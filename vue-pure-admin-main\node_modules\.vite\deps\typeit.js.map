{"version": 3, "sources": ["../../.pnpm/typeit@8.8.7/node_modules/typeit/dist/index.es.js"], "sourcesContent": ["// TypeIt by <PERSON> - https://typeitjs.com\nconst isArray = (thing) => Array.isArray(thing);\n\nconst asArray = (value) => isArray(value) ? value : [value];\n\nlet Queue = function(initialItems) {\n  let add = function(steps) {\n    asArray(steps).forEach((step) => {\n      return _q.set(Symbol(step.char?.innerText), buildQueueItem({ ...step }));\n    });\n    return this;\n  };\n  let getTypeable = () => rawValues().filter((value) => value.typeable);\n  let set = function(index, item) {\n    let keys = [..._q.keys()];\n    _q.set(keys[index], buildQueueItem(item));\n  };\n  let buildQueueItem = (queueItem) => {\n    queueItem.shouldPauseCursor = function() {\n      return Boolean(this.typeable || this.cursorable || this.deletable);\n    };\n    return queueItem;\n  };\n  let reset = function() {\n    _q.forEach((item) => delete item.done);\n  };\n  let wipe = function() {\n    _q = /* @__PURE__ */ new Map();\n    add(initialItems);\n  };\n  let getQueue = () => _q;\n  let rawValues = () => Array.from(_q.values());\n  let destroy = (key) => _q.delete(key);\n  let getPendingQueueItems = () => {\n    const pending = [];\n    for (let [, value] of getQueue()) {\n      if (!value.done) {\n        pending.push(value);\n      }\n    }\n    return pending;\n  };\n  let getItems = (all = false) => all ? rawValues() : rawValues().filter((i) => !i.done);\n  let done = (key, shouldDestroy = false) => shouldDestroy ? _q.delete(key) : _q.get(key).done = true;\n  let _q = /* @__PURE__ */ new Map();\n  add(initialItems);\n  return {\n    add,\n    set,\n    wipe,\n    done,\n    reset,\n    destroy,\n    getItems,\n    getQueue,\n    getTypeable,\n    getPendingQueueItems\n  };\n};\n\nconst DATA_ATTRIBUTE = \"data-typeit-id\";\nconst CURSOR_CLASS = \"ti-cursor\";\nconst END = \"END\";\nconst DEFAULT_STATUSES = {\n  started: false,\n  completed: false,\n  frozen: false,\n  destroyed: false\n};\nconst DEFAULT_OPTIONS = {\n  breakLines: true,\n  cursor: {\n    autoPause: true,\n    autoPauseDelay: 500,\n    animation: {\n      frames: [0, 0, 1].map((n) => {\n        return { opacity: n };\n      }),\n      options: {\n        iterations: Infinity,\n        easing: \"steps(2, start)\",\n        fill: \"forwards\"\n      }\n    }\n  },\n  cursorChar: \"|\",\n  cursorSpeed: 1e3,\n  deleteSpeed: null,\n  html: true,\n  lifeLike: true,\n  loop: false,\n  loopDelay: 750,\n  nextStringDelay: 750,\n  speed: 100,\n  startDelay: 250,\n  startDelete: false,\n  strings: [],\n  waitUntilVisible: false,\n  beforeString: () => {\n  },\n  afterString: () => {\n  },\n  beforeStep: () => {\n  },\n  afterStep: () => {\n  },\n  afterComplete: () => {\n  }\n};\nconst PLACEHOLDER_CSS = `[${DATA_ATTRIBUTE}]:before {content: '.'; display: inline-block; width: 0; visibility: hidden;}`;\n\nconst createElement = (el) => document.createElement(el);\n\nconst createTextNode = (content) => document.createTextNode(content);\n\nconst appendStyleBlock = (styles, id = \"\") => {\n  let styleBlock = createElement(\"style\");\n  styleBlock.id = id;\n  styleBlock.appendChild(createTextNode(styles));\n  document.head.appendChild(styleBlock);\n};\n\nconst calculateDelay = (delayArg) => {\n  if (!isArray(delayArg)) {\n    delayArg = [delayArg / 2, delayArg / 2];\n  }\n  return delayArg;\n};\n\nconst randomInRange = (value, range) => {\n  return Math.abs(\n    Math.random() * (value + range - (value - range)) + (value - range)\n  );\n};\n\nlet range = (val) => val / 2;\nfunction calculatePace(options) {\n  let { speed, deleteSpeed, lifeLike } = options;\n  deleteSpeed = deleteSpeed !== null ? deleteSpeed : speed / 3;\n  return lifeLike ? [\n    randomInRange(speed, range(speed)),\n    randomInRange(deleteSpeed, range(deleteSpeed))\n  ] : [speed, deleteSpeed];\n}\n\nconst toArray = (val) => Array.from(val);\n\nlet expandTextNodes = (element) => {\n  [...element.childNodes].forEach((child) => {\n    if (child.nodeValue) {\n      [...child.nodeValue].forEach((c) => {\n        child.parentNode.insertBefore(createTextNode(c), child);\n      });\n      child.remove();\n      return;\n    }\n    expandTextNodes(child);\n  });\n  return element;\n};\n\nconst getParsedBody = (content) => {\n  let doc = document.implementation.createHTMLDocument();\n  doc.body.innerHTML = content;\n  return expandTextNodes(doc.body);\n};\n\nfunction walkElementNodes(element, shouldReverse = false, shouldIncludeCursor = false) {\n  let cursor = element.querySelector(`.${CURSOR_CLASS}`);\n  let walker = document.createTreeWalker(element, NodeFilter.SHOW_ALL, {\n    acceptNode: (node) => {\n      if (cursor && shouldIncludeCursor) {\n        if (node.classList?.contains(CURSOR_CLASS)) {\n          return NodeFilter.FILTER_ACCEPT;\n        }\n        if (cursor.contains(node)) {\n          return NodeFilter.FILTER_REJECT;\n        }\n      }\n      return node.classList?.contains(CURSOR_CLASS) ? NodeFilter.FILTER_REJECT : NodeFilter.FILTER_ACCEPT;\n    }\n  });\n  let nextNode;\n  let nodes = [];\n  while (nextNode = walker.nextNode()) {\n    if (!nextNode.originalParent) {\n      nextNode.originalParent = nextNode.parentNode;\n    }\n    nodes.push(nextNode);\n  }\n  return shouldReverse ? nodes.reverse() : nodes;\n}\nfunction chunkStringAsHtml(string) {\n  return walkElementNodes(getParsedBody(string));\n}\nfunction maybeChunkStringAsHtml(str, asHtml = true) {\n  return asHtml ? chunkStringAsHtml(str) : toArray(str).map(createTextNode);\n}\n\nconst cleanUpSkipped = ({\n  index,\n  newIndex,\n  queueItems,\n  cleanUp\n}) => {\n  for (let i = index + 1; i < newIndex + 1; i++) {\n    cleanUp(queueItems[i][0]);\n  }\n};\n\nconst isNumber = (value) => Number.isInteger(value);\n\nconst countStepsToSelector = ({\n  queueItems,\n  selector,\n  cursorPosition,\n  to\n}) => {\n  if (isNumber(selector)) {\n    return selector * -1;\n  }\n  let isMovingToEnd = new RegExp(END, \"i\").test(to);\n  let selectorIndex = selector ? [...queueItems].reverse().findIndex(({ char }) => {\n    let parentElement = char.parentElement;\n    let parentMatches = parentElement.matches(selector);\n    if (isMovingToEnd && parentMatches) {\n      return true;\n    }\n    return parentMatches && parentElement.firstChild.isSameNode(char);\n  }) : -1;\n  if (selectorIndex < 0) {\n    selectorIndex = isMovingToEnd ? 0 : queueItems.length - 1;\n  }\n  let offset = isMovingToEnd ? 0 : 1;\n  return selectorIndex - cursorPosition + offset;\n};\n\nconst destroyTimeouts = (timeouts) => {\n  timeouts.forEach(clearTimeout);\n  return [];\n};\n\nconst duplicate = (value, times) => new Array(times).fill(value);\n\nlet beforePaint = (cb) => {\n  return new Promise((resolve) => {\n    requestAnimationFrame(async () => {\n      resolve(await cb());\n    });\n  });\n};\n\nlet getAnimationFromElement = (element) => {\n  return element?.getAnimations().find((animation) => {\n    return animation.id === element.dataset.tiAnimationId;\n  });\n};\n\nlet setCursorAnimation = ({\n  cursor,\n  frames,\n  options\n}) => {\n  let animation = cursor.animate(frames, options);\n  animation.pause();\n  animation.id = cursor.dataset.tiAnimationId;\n  beforePaint(() => {\n    beforePaint(() => {\n      animation.play();\n    });\n  });\n  return animation;\n};\n\nlet rebuildCursorAnimation = ({\n  cursor,\n  options,\n  cursorOptions\n}) => {\n  if (!cursor || !cursorOptions) return;\n  let animation = getAnimationFromElement(cursor);\n  let oldCurrentTime;\n  if (animation) {\n    options.delay = animation.effect.getComputedTiming().delay;\n    oldCurrentTime = animation.currentTime;\n    animation.cancel();\n  }\n  let newAnimation = setCursorAnimation({\n    cursor,\n    frames: cursorOptions.animation.frames,\n    options\n  });\n  if (oldCurrentTime) {\n    newAnimation.currentTime = oldCurrentTime;\n  }\n  return newAnimation;\n};\n\nlet execute = (queueItem) => queueItem.func?.call(null);\nlet fireItem = async ({\n  index,\n  queueItems,\n  wait,\n  cursor,\n  cursorOptions\n}) => {\n  let queueItem = queueItems[index][1];\n  let instantQueue = [];\n  let tempIndex = index;\n  let futureItem = queueItem;\n  let shouldBeGrouped = () => futureItem && !futureItem.delay;\n  let shouldPauseCursor = queueItem.shouldPauseCursor() && cursorOptions.autoPause;\n  while (shouldBeGrouped()) {\n    instantQueue.push(futureItem);\n    shouldBeGrouped() && tempIndex++;\n    futureItem = queueItems[tempIndex] ? queueItems[tempIndex][1] : null;\n  }\n  if (instantQueue.length) {\n    await beforePaint(async () => {\n      for (let q of instantQueue) {\n        await execute(q);\n      }\n    });\n    return tempIndex - 1;\n  }\n  let animation = getAnimationFromElement(cursor);\n  let options;\n  if (animation) {\n    options = {\n      ...animation.effect.getComputedTiming(),\n      delay: shouldPauseCursor ? cursorOptions.autoPauseDelay : 0\n    };\n  }\n  await wait(async () => {\n    if (animation && shouldPauseCursor) {\n      animation.cancel();\n    }\n    await beforePaint(() => {\n      execute(queueItem);\n    });\n  }, queueItem.delay);\n  await rebuildCursorAnimation({\n    cursor,\n    options,\n    cursorOptions\n  });\n  return index;\n};\n\nconst fireWhenVisible = (element, func) => {\n  let observer = new IntersectionObserver(\n    (entries, observer2) => {\n      entries.forEach((entry) => {\n        if (entry.isIntersecting) {\n          func();\n          observer2.unobserve(element);\n        }\n      });\n    },\n    { threshold: 1 }\n  );\n  observer.observe(element);\n};\n\nconst generateHash = () => Math.random().toString().substring(2, 9);\n\nconst isInput = (el) => {\n  return \"value\" in el;\n};\n\nlet getAllChars = (element) => {\n  if (isInput(element)) {\n    return toArray(element.value);\n  }\n  return walkElementNodes(element, true).filter(\n    (c) => !(c.childNodes.length > 0)\n  );\n};\n\nlet handleFunctionalArg = (arg) => {\n  return typeof arg === \"function\" ? arg() : arg;\n};\n\nlet select = (selector, element = document, all = false) => {\n  return element[`querySelector${all ? \"All\" : \"\"}`](selector);\n};\n\nlet isBodyElement = (node) => /body/i.test(node?.tagName);\n\nlet insertIntoElement = (originalTarget, character) => {\n  if (isInput(originalTarget)) {\n    originalTarget.value = `${originalTarget.value}${character.textContent}`;\n    return;\n  }\n  character.innerHTML = \"\";\n  let target = isBodyElement(character.originalParent) ? originalTarget : (\n    // If we add one-off fresh elements, there will be no\n    // \"originalParent\", so always fall back to the default target.\n    character.originalParent || originalTarget\n  );\n  let cursorNode = select(\".\" + CURSOR_CLASS, target) || null;\n  if (cursorNode && cursorNode.parentElement !== target) {\n    target = cursorNode.parentElement;\n  }\n  target.insertBefore(character, cursorNode);\n};\n\nconst isNonVoidElement = (el) => /<(.+)>(.*?)<\\/(.+)>/.test(el.outerHTML);\n\nconst merge = (originalObj, newObj) => Object.assign({}, originalObj, newObj);\n\nlet processCursorOptions = (cursorOptions) => {\n  if (typeof cursorOptions === \"object\") {\n    let newOptions = {};\n    let { frames: defaultFrames, options: defaultOptions } = DEFAULT_OPTIONS.cursor.animation;\n    newOptions.animation = cursorOptions.animation || {};\n    newOptions.animation.frames = cursorOptions.animation?.frames || defaultFrames;\n    newOptions.animation.options = merge(\n      defaultOptions,\n      cursorOptions.animation?.options || {}\n    );\n    newOptions.autoPause = cursorOptions.autoPause ?? DEFAULT_OPTIONS.cursor.autoPause;\n    newOptions.autoPauseDelay = cursorOptions.autoPauseDelay || DEFAULT_OPTIONS.cursor.autoPauseDelay;\n    return newOptions;\n  }\n  if (cursorOptions === true) {\n    return DEFAULT_OPTIONS.cursor;\n  }\n  return cursorOptions;\n};\n\nconst removeNode = (node, rootElement) => {\n  if (!node) return;\n  let nodeParent = node.parentNode;\n  let nodeToRemove = nodeParent.childNodes.length > 1 || nodeParent.isSameNode(rootElement) ? (\n    // This parent still needs to exist.\n    node\n  ) : (\n    // There's nothing else in there, so just delete the entire thing.\n    // By doing this, we clean up markup as we go along.\n    nodeParent\n  );\n  nodeToRemove.remove();\n};\n\nconst repositionCursor = (element, allChars, newCursorPosition) => {\n  let nodeToInsertBefore = allChars[newCursorPosition - 1];\n  let cursor = select(`.${CURSOR_CLASS}`, element);\n  element = nodeToInsertBefore?.parentNode || element;\n  element.insertBefore(cursor, nodeToInsertBefore || null);\n};\n\nfunction selectorToElement(thing) {\n  return typeof thing === \"string\" ? select(thing) : thing;\n}\n\nlet cursorFontStyles = {\n  \"font-family\": \"\",\n  \"font-weight\": \"\",\n  \"font-size\": \"\",\n  \"font-style\": \"\",\n  \"line-height\": \"\",\n  color: \"\",\n  transform: \"translateX(-.125em)\"\n};\nlet setCursorStyles = (id, element) => {\n  let rootSelector = `[${DATA_ATTRIBUTE}='${id}']`;\n  let cursorSelector = `${rootSelector} .${CURSOR_CLASS}`;\n  let computedStyles = getComputedStyle(element);\n  let customProperties = Object.entries(cursorFontStyles).reduce(\n    (accumulator, [item, value]) => {\n      return `${accumulator} ${item}: var(--ti-cursor-${item}, ${value || computedStyles[item]});`;\n    },\n    \"\"\n  );\n  appendStyleBlock(\n    `${cursorSelector} { display: inline-block; width: 0; ${customProperties} }`,\n    id\n  );\n};\n\nfunction splitOnBreak(str) {\n  return str.replace(/<!--(.+?)-->/g, \"\").trim().split(/<br(?:\\s*?)(?:\\/)?>/);\n}\n\nlet updateCursorPosition = (steps, cursorPosition, printedCharacters) => {\n  return Math.min(\n    Math.max(cursorPosition + steps, 0),\n    printedCharacters.length\n  );\n};\n\nlet wait = (callback, delay, timeouts) => {\n  return new Promise((resolve) => {\n    let cb = async () => {\n      await callback();\n      resolve();\n    };\n    timeouts.push(setTimeout(cb, delay || 0));\n  });\n};\n\nclass TypeIt {\n  element;\n  timeouts;\n  cursorPosition;\n  predictedCursorPosition;\n  statuses = {\n    started: false,\n    completed: false,\n    frozen: false,\n    destroyed: false,\n    firing: false\n  };\n  opts;\n  id;\n  queue;\n  cursor;\n  flushCallback = null;\n  unfreeze = () => {\n  };\n  constructor(element, options = {}) {\n    this.opts = merge(DEFAULT_OPTIONS, options);\n    this.element = selectorToElement(element);\n    this.timeouts = [];\n    this.cursorPosition = 0;\n    this.unfreeze = () => {\n    };\n    this.predictedCursorPosition = null;\n    this.statuses = merge({}, DEFAULT_STATUSES);\n    this.id = generateHash();\n    this.queue = Queue([{ delay: this.opts.startDelay }]);\n    this.#buildOptions(options);\n    this.cursor = this.#setUpCursor();\n    this.element.dataset.typeitId = this.id;\n    appendStyleBlock(PLACEHOLDER_CSS);\n    if (this.opts.strings.length) {\n      this.#generateQueue();\n    }\n  }\n  /**\n   * Can only be called once.\n   */\n  go() {\n    if (this.statuses.started) {\n      return this;\n    }\n    this.#attachCursor();\n    if (!this.opts.waitUntilVisible) {\n      this.#fire();\n      return this;\n    }\n    fireWhenVisible(this.element, this.#fire.bind(this));\n    return this;\n  }\n  destroy(shouldRemoveCursor = true) {\n    this.timeouts = destroyTimeouts(this.timeouts);\n    handleFunctionalArg(shouldRemoveCursor) && this.cursor && this.#removeNode(this.cursor);\n    this.statuses.destroyed = true;\n  }\n  reset(rebuild) {\n    !this.is(\"destroyed\") && this.destroy();\n    if (rebuild) {\n      this.queue.wipe();\n      rebuild(this);\n    } else {\n      this.queue.reset();\n    }\n    this.cursorPosition = 0;\n    for (let property in this.statuses) {\n      this.statuses[property] = false;\n    }\n    this.element[this.#elementIsInput() ? \"value\" : \"innerHTML\"] = \"\";\n    return this;\n  }\n  is = function(key) {\n    return this.statuses[key];\n  };\n  type(string, actionOpts = {}) {\n    string = handleFunctionalArg(string);\n    let { instant } = actionOpts;\n    let bookEndQueueItems = this.#generateTemporaryOptionQueueItems(actionOpts);\n    let chars = maybeChunkStringAsHtml(string, this.opts.html);\n    let charsAsQueueItems = chars.map((char) => {\n      return {\n        func: () => this.#type(char),\n        char,\n        delay: instant || isNonVoidElement(char) ? 0 : this.#getPace(),\n        typeable: char.nodeType === Node.TEXT_NODE\n      };\n    });\n    let itemsToQueue = [\n      bookEndQueueItems[0],\n      { func: async () => await this.opts.beforeString(string, this) },\n      ...charsAsQueueItems,\n      { func: async () => await this.opts.afterString(string, this) },\n      bookEndQueueItems[1]\n    ];\n    return this.#queueAndReturn(itemsToQueue, actionOpts);\n  }\n  break(actionOpts = {}) {\n    return this.#queueAndReturn(\n      {\n        func: () => this.#type(createElement(\"BR\")),\n        typeable: true\n      },\n      actionOpts\n    );\n  }\n  move(movementArg, actionOpts = {}) {\n    movementArg = handleFunctionalArg(movementArg);\n    let bookEndQueueItems = this.#generateTemporaryOptionQueueItems(actionOpts);\n    let { instant, to } = actionOpts;\n    let numberOfSteps = countStepsToSelector({\n      queueItems: this.queue.getTypeable(),\n      selector: movementArg === null ? \"\" : movementArg,\n      to,\n      cursorPosition: this.#derivedCursorPosition\n    });\n    let directionalStep = numberOfSteps < 0 ? -1 : 1;\n    this.predictedCursorPosition = this.#derivedCursorPosition + numberOfSteps;\n    return this.#queueAndReturn(\n      [\n        bookEndQueueItems[0],\n        ...duplicate(\n          {\n            func: () => this.#move(directionalStep),\n            delay: instant ? 0 : this.#getPace(),\n            cursorable: true\n          },\n          Math.abs(numberOfSteps)\n        ),\n        bookEndQueueItems[1]\n      ],\n      actionOpts\n    );\n  }\n  exec(func, actionOpts = {}) {\n    let bookEndQueueItems = this.#generateTemporaryOptionQueueItems(actionOpts);\n    return this.#queueAndReturn(\n      [bookEndQueueItems[0], { func: () => func(this) }, bookEndQueueItems[1]],\n      actionOpts\n    );\n  }\n  options(opts, actionOpts = {}) {\n    opts = handleFunctionalArg(opts);\n    this.#updateOptions(opts);\n    return this.#queueAndReturn({}, actionOpts);\n  }\n  pause(milliseconds, actionOpts = {}) {\n    return this.#queueAndReturn(\n      { delay: handleFunctionalArg(milliseconds) },\n      actionOpts\n    );\n  }\n  delete(numCharacters = null, actionOpts = {}) {\n    numCharacters = handleFunctionalArg(numCharacters);\n    let bookEndQueueItems = this.#generateTemporaryOptionQueueItems(actionOpts);\n    let num = numCharacters;\n    let { instant, to } = actionOpts;\n    let typeableQueueItems = this.queue.getTypeable();\n    let rounds = (() => {\n      if (num === null) {\n        return typeableQueueItems.length;\n      }\n      if (isNumber(num)) {\n        return num;\n      }\n      return countStepsToSelector({\n        queueItems: typeableQueueItems,\n        selector: num,\n        cursorPosition: this.#derivedCursorPosition,\n        to\n      });\n    })();\n    return this.#queueAndReturn(\n      [\n        bookEndQueueItems[0],\n        ...duplicate(\n          {\n            func: this.#delete.bind(this),\n            delay: instant ? 0 : this.#getPace(1),\n            deletable: true\n          },\n          rounds\n        ),\n        bookEndQueueItems[1]\n      ],\n      actionOpts\n    );\n  }\n  freeze() {\n    this.statuses.frozen = true;\n  }\n  /**\n   * Like `.go()`, but more... \"off the grid.\"\n   *\n   * - won't trigger `afterComplete` callback\n   * - items won't be replayed after `.reset()`\n   *\n   * When called, all non-done items will be \"flushed\" --\n   * that is, executed, but not remembered.\n   */\n  flush(cb = null) {\n    this.flushCallback = cb || this.flushCallback;\n    if (this.statuses.firing) {\n      return this;\n    }\n    this.#attachCursor();\n    this.#fire(false).then(() => {\n      if (this.queue.getPendingQueueItems().length > 0) {\n        return this.flush();\n      }\n      this.flushCallback();\n      this.flushCallback = null;\n    });\n    return this;\n  }\n  getQueue() {\n    return this.queue;\n  }\n  getOptions() {\n    return this.opts;\n  }\n  updateOptions(options) {\n    return this.#updateOptions(options);\n  }\n  getElement() {\n    return this.element;\n  }\n  empty(actionOpts = {}) {\n    return this.#queueAndReturn({ func: this.#empty.bind(this) }, actionOpts);\n  }\n  async #empty() {\n    if (this.#elementIsInput()) {\n      this.element.value = \"\";\n      return;\n    }\n    this.#allChars.forEach(this.#removeNode.bind(this));\n    return;\n  }\n  /**\n   * Execute items in the queue.\n   *\n   * @param remember If false, each queue item will be destroyed once executed.\n   * @returns\n   */\n  async #fire(remember = true) {\n    this.statuses.started = true;\n    this.statuses.firing = true;\n    let cleanUp = (qKey) => {\n      this.queue.done(qKey, !remember);\n    };\n    try {\n      let queueItems = [...this.queue.getQueue()];\n      for (let index = 0; index < queueItems.length; index++) {\n        let [queueKey, queueItem] = queueItems[index];\n        if (queueItem.done) continue;\n        if (!queueItem.deletable || queueItem.deletable && this.#allChars.length) {\n          let newIndex = await this.#fireItemWithContext(index, queueItems);\n          cleanUpSkipped({\n            index,\n            newIndex,\n            queueItems,\n            cleanUp\n          });\n          index = newIndex;\n        }\n        cleanUp(queueKey);\n      }\n      if (!remember) {\n        this.statuses.firing = false;\n        return this;\n      }\n      this.statuses.completed = true;\n      this.statuses.firing = false;\n      await this.opts.afterComplete(this);\n      if (!this.opts.loop) {\n        throw \"\";\n      }\n      let delay = this.opts.loopDelay;\n      this.#wait(async () => {\n        await this.#prepLoop(delay[0]);\n        this.#fire();\n      }, delay[1]);\n    } catch (e) {\n    }\n    this.statuses.firing = false;\n    return this;\n  }\n  async #move(step) {\n    this.cursorPosition = updateCursorPosition(\n      step,\n      this.cursorPosition,\n      this.#allChars\n    );\n    repositionCursor(this.element, this.#allChars, this.cursorPosition);\n  }\n  /**\n   * 1. Reset queue.\n   * 2. Reset initial pause.\n   */\n  async #prepLoop(delay) {\n    let derivedCursorPosition = this.#derivedCursorPosition;\n    derivedCursorPosition && await this.#move({ value: derivedCursorPosition });\n    let queueItems = this.#allChars.map((c) => {\n      return [\n        Symbol(),\n        {\n          func: this.#delete.bind(this),\n          delay: this.#getPace(1),\n          deletable: true,\n          shouldPauseCursor: () => true\n        }\n      ];\n    });\n    for (let index = 0; index < queueItems.length; index++) {\n      await this.#fireItemWithContext(index, queueItems);\n    }\n    this.queue.reset();\n    this.queue.set(0, { delay });\n  }\n  #fireItemWithContext(index, queueItems) {\n    return fireItem({\n      index,\n      queueItems,\n      wait: this.#wait.bind(this),\n      cursor: this.cursor,\n      cursorOptions: this.opts.cursor\n    });\n  }\n  async #wait(callback, delay, silent = false) {\n    if (this.statuses.frozen) {\n      await new Promise((resolve) => {\n        this.unfreeze = () => {\n          this.statuses.frozen = false;\n          resolve();\n        };\n      });\n    }\n    silent || await this.opts.beforeStep(this);\n    await wait(callback, delay, this.timeouts);\n    silent || await this.opts.afterStep(this);\n  }\n  /**\n   * Attach it to the DOM so, along with the required CSS transition.\n   */\n  async #attachCursor() {\n    !this.#elementIsInput() && this.cursor && this.element.appendChild(this.cursor);\n    if (this.#shouldRenderCursor) {\n      setCursorStyles(this.id, this.element);\n      this.cursor.dataset.tiAnimationId = this.id;\n      let { animation } = this.opts.cursor;\n      let { frames, options } = animation;\n      setCursorAnimation({\n        frames,\n        cursor: this.cursor,\n        options: {\n          duration: this.opts.cursorSpeed,\n          ...options\n        }\n      });\n    }\n  }\n  #elementIsInput() {\n    return isInput(this.element);\n  }\n  #queueAndReturn(steps, opts) {\n    this.queue.add(steps);\n    this.#maybeAppendPause(opts);\n    return this;\n  }\n  #maybeAppendPause(opts = {}) {\n    let delay = opts.delay;\n    delay && this.queue.add({ delay });\n  }\n  #generateTemporaryOptionQueueItems(newOptions = {}) {\n    return [\n      { func: () => this.#updateOptions(newOptions) },\n      { func: () => this.#updateOptions(this.opts) }\n    ];\n  }\n  async #updateOptions(opts) {\n    this.opts = merge(this.opts, opts);\n  }\n  /**\n   * Based on provided strings, generate a TypeIt queue\n   * to be fired for each character in the string.\n   */\n  #generateQueue() {\n    let strings = this.opts.strings.filter((string) => !!string);\n    strings.forEach((string, index) => {\n      this.type(string);\n      if (index + 1 === strings.length) {\n        return;\n      }\n      let splitItems = this.opts.breakLines ? [{ func: () => this.#type(createElement(\"BR\")), typeable: true }] : duplicate(\n        {\n          func: this.#delete.bind(this),\n          delay: this.#getPace(1)\n        },\n        this.queue.getTypeable().length\n      );\n      this.#addSplitPause(splitItems);\n    });\n  }\n  #buildOptions = (options) => {\n    this.opts.cursor = processCursorOptions(\n      options.cursor ?? DEFAULT_OPTIONS.cursor\n    );\n    this.opts.strings = this.#prependHardcodedStrings(\n      asArray(this.opts.strings)\n    );\n    this.opts = merge(this.opts, {\n      html: !this.#isInput && this.opts.html,\n      nextStringDelay: calculateDelay(this.opts.nextStringDelay),\n      loopDelay: calculateDelay(this.opts.loopDelay)\n    });\n  };\n  #prependHardcodedStrings(strings) {\n    let existingMarkup = this.element.innerHTML;\n    if (!existingMarkup) {\n      return strings;\n    }\n    this.element.innerHTML = \"\";\n    if (this.opts.startDelete) {\n      this.element.innerHTML = existingMarkup;\n      expandTextNodes(this.element);\n      this.#addSplitPause(\n        duplicate(\n          {\n            func: this.#delete.bind(this),\n            delay: this.#getPace(1),\n            deletable: true\n          },\n          this.#allChars.length\n        )\n      );\n      return strings;\n    }\n    return splitOnBreak(existingMarkup).concat(strings);\n  }\n  /**\n   * Provided it's a non-form element and the options is provided,\n   * set up the cursor element for the animation.\n   */\n  #setUpCursor() {\n    if (this.#isInput) {\n      return null;\n    }\n    let cursor = createElement(\"span\");\n    cursor.className = CURSOR_CLASS;\n    if (!this.#shouldRenderCursor) {\n      cursor.style.visibility = \"hidden\";\n      return cursor;\n    }\n    cursor.innerHTML = getParsedBody(this.opts.cursorChar).innerHTML;\n    return cursor;\n  }\n  #addSplitPause(items) {\n    let delay = this.opts.nextStringDelay;\n    this.queue.add([{ delay: delay[0] }, ...items, { delay: delay[1] }]);\n  }\n  #type(char) {\n    insertIntoElement(this.element, char);\n  }\n  #delete() {\n    if (!this.#allChars.length) return;\n    if (this.#isInput) {\n      this.element.value = this.element.value.slice(0, -1);\n    } else {\n      this.#removeNode(this.#allChars[this.cursorPosition]);\n    }\n  }\n  #removeNode(node) {\n    removeNode(node, this.element);\n  }\n  #getPace(index = 0) {\n    return calculatePace(this.opts)[index];\n  }\n  get #derivedCursorPosition() {\n    return this.predictedCursorPosition ?? this.cursorPosition;\n  }\n  get #isInput() {\n    return isInput(this.element);\n  }\n  get #shouldRenderCursor() {\n    return !!this.opts.cursor && !this.#isInput;\n  }\n  get #allChars() {\n    return getAllChars(this.element);\n  }\n}\n\nexport { TypeIt as default };\n"], "mappings": ";;;;;;;;AACA,IAAM,UAAU,CAAC,UAAU,MAAM,QAAQ,KAAK;AAE9C,IAAM,UAAU,CAAC,UAAU,QAAQ,KAAK,IAAI,QAAQ,CAAC,KAAK;AAE1D,IAAI,QAAQ,SAAS,cAAc;AACjC,MAAI,MAAM,SAAS,OAAO;AACxB,YAAQ,KAAK,EAAE,QAAQ,CAAC,SAAS;AAPrC;AAQM,aAAO,GAAG,IAAI,QAAO,UAAK,SAAL,mBAAW,SAAS,GAAG,eAAe,EAAE,GAAG,KAAK,CAAC,CAAC;AAAA,IACzE,CAAC;AACD,WAAO;AAAA,EACT;AACA,MAAI,cAAc,MAAM,UAAU,EAAE,OAAO,CAAC,UAAU,MAAM,QAAQ;AACpE,MAAI,MAAM,SAAS,OAAO,MAAM;AAC9B,QAAI,OAAO,CAAC,GAAG,GAAG,KAAK,CAAC;AACxB,OAAG,IAAI,KAAK,KAAK,GAAG,eAAe,IAAI,CAAC;AAAA,EAC1C;AACA,MAAI,iBAAiB,CAAC,cAAc;AAClC,cAAU,oBAAoB,WAAW;AACvC,aAAO,QAAQ,KAAK,YAAY,KAAK,cAAc,KAAK,SAAS;AAAA,IACnE;AACA,WAAO;AAAA,EACT;AACA,MAAI,QAAQ,WAAW;AACrB,OAAG,QAAQ,CAAC,SAAS,OAAO,KAAK,IAAI;AAAA,EACvC;AACA,MAAI,OAAO,WAAW;AACpB,SAAqB,oBAAI,IAAI;AAC7B,QAAI,YAAY;AAAA,EAClB;AACA,MAAI,WAAW,MAAM;AACrB,MAAI,YAAY,MAAM,MAAM,KAAK,GAAG,OAAO,CAAC;AAC5C,MAAI,UAAU,CAAC,QAAQ,GAAG,OAAO,GAAG;AACpC,MAAI,uBAAuB,MAAM;AAC/B,UAAM,UAAU,CAAC;AACjB,aAAS,CAAC,EAAE,KAAK,KAAK,SAAS,GAAG;AAChC,UAAI,CAAC,MAAM,MAAM;AACf,gBAAQ,KAAK,KAAK;AAAA,MACpB;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,MAAI,WAAW,CAAC,MAAM,UAAU,MAAM,UAAU,IAAI,UAAU,EAAE,OAAO,CAAC,MAAM,CAAC,EAAE,IAAI;AACrF,MAAI,OAAO,CAAC,KAAK,gBAAgB,UAAU,gBAAgB,GAAG,OAAO,GAAG,IAAI,GAAG,IAAI,GAAG,EAAE,OAAO;AAC/F,MAAI,KAAqB,oBAAI,IAAI;AACjC,MAAI,YAAY;AAChB,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,IAAM,iBAAiB;AACvB,IAAM,eAAe;AACrB,IAAM,MAAM;AACZ,IAAM,mBAAmB;AAAA,EACvB,SAAS;AAAA,EACT,WAAW;AAAA,EACX,QAAQ;AAAA,EACR,WAAW;AACb;AACA,IAAM,kBAAkB;AAAA,EACtB,YAAY;AAAA,EACZ,QAAQ;AAAA,IACN,WAAW;AAAA,IACX,gBAAgB;AAAA,IAChB,WAAW;AAAA,MACT,QAAQ,CAAC,GAAG,GAAG,CAAC,EAAE,IAAI,CAAC,MAAM;AAC3B,eAAO,EAAE,SAAS,EAAE;AAAA,MACtB,CAAC;AAAA,MACD,SAAS;AAAA,QACP,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACR;AAAA,IACF;AAAA,EACF;AAAA,EACA,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AAAA,EACb,MAAM;AAAA,EACN,UAAU;AAAA,EACV,MAAM;AAAA,EACN,WAAW;AAAA,EACX,iBAAiB;AAAA,EACjB,OAAO;AAAA,EACP,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,SAAS,CAAC;AAAA,EACV,kBAAkB;AAAA,EAClB,cAAc,MAAM;AAAA,EACpB;AAAA,EACA,aAAa,MAAM;AAAA,EACnB;AAAA,EACA,YAAY,MAAM;AAAA,EAClB;AAAA,EACA,WAAW,MAAM;AAAA,EACjB;AAAA,EACA,eAAe,MAAM;AAAA,EACrB;AACF;AACA,IAAM,kBAAkB,IAAI,cAAc;AAE1C,IAAM,gBAAgB,CAAC,OAAO,SAAS,cAAc,EAAE;AAEvD,IAAM,iBAAiB,CAAC,YAAY,SAAS,eAAe,OAAO;AAEnE,IAAM,mBAAmB,CAAC,QAAQ,KAAK,OAAO;AAC5C,MAAI,aAAa,cAAc,OAAO;AACtC,aAAW,KAAK;AAChB,aAAW,YAAY,eAAe,MAAM,CAAC;AAC7C,WAAS,KAAK,YAAY,UAAU;AACtC;AAEA,IAAM,iBAAiB,CAAC,aAAa;AACnC,MAAI,CAAC,QAAQ,QAAQ,GAAG;AACtB,eAAW,CAAC,WAAW,GAAG,WAAW,CAAC;AAAA,EACxC;AACA,SAAO;AACT;AAEA,IAAM,gBAAgB,CAAC,OAAOA,WAAU;AACtC,SAAO,KAAK;AAAA,IACV,KAAK,OAAO,KAAK,QAAQA,UAAS,QAAQA,YAAW,QAAQA;AAAA,EAC/D;AACF;AAEA,IAAI,QAAQ,CAAC,QAAQ,MAAM;AAC3B,SAAS,cAAc,SAAS;AAC9B,MAAI,EAAE,OAAO,aAAa,SAAS,IAAI;AACvC,gBAAc,gBAAgB,OAAO,cAAc,QAAQ;AAC3D,SAAO,WAAW;AAAA,IAChB,cAAc,OAAO,MAAM,KAAK,CAAC;AAAA,IACjC,cAAc,aAAa,MAAM,WAAW,CAAC;AAAA,EAC/C,IAAI,CAAC,OAAO,WAAW;AACzB;AAEA,IAAM,UAAU,CAAC,QAAQ,MAAM,KAAK,GAAG;AAEvC,IAAI,kBAAkB,CAAC,YAAY;AACjC,GAAC,GAAG,QAAQ,UAAU,EAAE,QAAQ,CAAC,UAAU;AACzC,QAAI,MAAM,WAAW;AACnB,OAAC,GAAG,MAAM,SAAS,EAAE,QAAQ,CAAC,MAAM;AAClC,cAAM,WAAW,aAAa,eAAe,CAAC,GAAG,KAAK;AAAA,MACxD,CAAC;AACD,YAAM,OAAO;AACb;AAAA,IACF;AACA,oBAAgB,KAAK;AAAA,EACvB,CAAC;AACD,SAAO;AACT;AAEA,IAAM,gBAAgB,CAAC,YAAY;AACjC,MAAI,MAAM,SAAS,eAAe,mBAAmB;AACrD,MAAI,KAAK,YAAY;AACrB,SAAO,gBAAgB,IAAI,IAAI;AACjC;AAEA,SAAS,iBAAiB,SAAS,gBAAgB,OAAO,sBAAsB,OAAO;AACrF,MAAI,SAAS,QAAQ,cAAc,IAAI,YAAY,EAAE;AACrD,MAAI,SAAS,SAAS,iBAAiB,SAAS,WAAW,UAAU;AAAA,IACnE,YAAY,CAAC,SAAS;AA1K1B;AA2KM,UAAI,UAAU,qBAAqB;AACjC,aAAI,UAAK,cAAL,mBAAgB,SAAS,eAAe;AAC1C,iBAAO,WAAW;AAAA,QACpB;AACA,YAAI,OAAO,SAAS,IAAI,GAAG;AACzB,iBAAO,WAAW;AAAA,QACpB;AAAA,MACF;AACA,eAAO,UAAK,cAAL,mBAAgB,SAAS,iBAAgB,WAAW,gBAAgB,WAAW;AAAA,IACxF;AAAA,EACF,CAAC;AACD,MAAI;AACJ,MAAI,QAAQ,CAAC;AACb,SAAO,WAAW,OAAO,SAAS,GAAG;AACnC,QAAI,CAAC,SAAS,gBAAgB;AAC5B,eAAS,iBAAiB,SAAS;AAAA,IACrC;AACA,UAAM,KAAK,QAAQ;AAAA,EACrB;AACA,SAAO,gBAAgB,MAAM,QAAQ,IAAI;AAC3C;AACA,SAAS,kBAAkB,QAAQ;AACjC,SAAO,iBAAiB,cAAc,MAAM,CAAC;AAC/C;AACA,SAAS,uBAAuB,KAAK,SAAS,MAAM;AAClD,SAAO,SAAS,kBAAkB,GAAG,IAAI,QAAQ,GAAG,EAAE,IAAI,cAAc;AAC1E;AAEA,IAAM,iBAAiB,CAAC;AAAA,EACtB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,MAAM;AACJ,WAAS,IAAI,QAAQ,GAAG,IAAI,WAAW,GAAG,KAAK;AAC7C,YAAQ,WAAW,CAAC,EAAE,CAAC,CAAC;AAAA,EAC1B;AACF;AAEA,IAAM,WAAW,CAAC,UAAU,OAAO,UAAU,KAAK;AAElD,IAAM,uBAAuB,CAAC;AAAA,EAC5B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,MAAM;AACJ,MAAI,SAAS,QAAQ,GAAG;AACtB,WAAO,WAAW;AAAA,EACpB;AACA,MAAI,gBAAgB,IAAI,OAAO,KAAK,GAAG,EAAE,KAAK,EAAE;AAChD,MAAI,gBAAgB,WAAW,CAAC,GAAG,UAAU,EAAE,QAAQ,EAAE,UAAU,CAAC,EAAE,KAAK,MAAM;AAC/E,QAAI,gBAAgB,KAAK;AACzB,QAAI,gBAAgB,cAAc,QAAQ,QAAQ;AAClD,QAAI,iBAAiB,eAAe;AAClC,aAAO;AAAA,IACT;AACA,WAAO,iBAAiB,cAAc,WAAW,WAAW,IAAI;AAAA,EAClE,CAAC,IAAI;AACL,MAAI,gBAAgB,GAAG;AACrB,oBAAgB,gBAAgB,IAAI,WAAW,SAAS;AAAA,EAC1D;AACA,MAAI,SAAS,gBAAgB,IAAI;AACjC,SAAO,gBAAgB,iBAAiB;AAC1C;AAEA,IAAM,kBAAkB,CAAC,aAAa;AACpC,WAAS,QAAQ,YAAY;AAC7B,SAAO,CAAC;AACV;AAEA,IAAM,YAAY,CAAC,OAAO,UAAU,IAAI,MAAM,KAAK,EAAE,KAAK,KAAK;AAE/D,IAAI,cAAc,CAAC,OAAO;AACxB,SAAO,IAAI,QAAQ,CAAC,YAAY;AAC9B,0BAAsB,YAAY;AAChC,cAAQ,MAAM,GAAG,CAAC;AAAA,IACpB,CAAC;AAAA,EACH,CAAC;AACH;AAEA,IAAI,0BAA0B,CAAC,YAAY;AACzC,SAAO,mCAAS,gBAAgB,KAAK,CAAC,cAAc;AAClD,WAAO,UAAU,OAAO,QAAQ,QAAQ;AAAA,EAC1C;AACF;AAEA,IAAI,qBAAqB,CAAC;AAAA,EACxB;AAAA,EACA;AAAA,EACA;AACF,MAAM;AACJ,MAAI,YAAY,OAAO,QAAQ,QAAQ,OAAO;AAC9C,YAAU,MAAM;AAChB,YAAU,KAAK,OAAO,QAAQ;AAC9B,cAAY,MAAM;AAChB,gBAAY,MAAM;AAChB,gBAAU,KAAK;AAAA,IACjB,CAAC;AAAA,EACH,CAAC;AACD,SAAO;AACT;AAEA,IAAI,yBAAyB,CAAC;AAAA,EAC5B;AAAA,EACA;AAAA,EACA;AACF,MAAM;AACJ,MAAI,CAAC,UAAU,CAAC;AAAe;AAC/B,MAAI,YAAY,wBAAwB,MAAM;AAC9C,MAAI;AACJ,MAAI,WAAW;AACb,YAAQ,QAAQ,UAAU,OAAO,kBAAkB,EAAE;AACrD,qBAAiB,UAAU;AAC3B,cAAU,OAAO;AAAA,EACnB;AACA,MAAI,eAAe,mBAAmB;AAAA,IACpC;AAAA,IACA,QAAQ,cAAc,UAAU;AAAA,IAChC;AAAA,EACF,CAAC;AACD,MAAI,gBAAgB;AAClB,iBAAa,cAAc;AAAA,EAC7B;AACA,SAAO;AACT;AAEA,IAAI,UAAU,CAAC,cAAW;AA1S1B;AA0S6B,yBAAU,SAAV,mBAAgB,KAAK;AAAA;AAClD,IAAI,WAAW,OAAO;AAAA,EACpB;AAAA,EACA;AAAA,EACA,MAAAC;AAAA,EACA;AAAA,EACA;AACF,MAAM;AACJ,MAAI,YAAY,WAAW,KAAK,EAAE,CAAC;AACnC,MAAI,eAAe,CAAC;AACpB,MAAI,YAAY;AAChB,MAAI,aAAa;AACjB,MAAI,kBAAkB,MAAM,cAAc,CAAC,WAAW;AACtD,MAAI,oBAAoB,UAAU,kBAAkB,KAAK,cAAc;AACvE,SAAO,gBAAgB,GAAG;AACxB,iBAAa,KAAK,UAAU;AAC5B,oBAAgB,KAAK;AACrB,iBAAa,WAAW,SAAS,IAAI,WAAW,SAAS,EAAE,CAAC,IAAI;AAAA,EAClE;AACA,MAAI,aAAa,QAAQ;AACvB,UAAM,YAAY,YAAY;AAC5B,eAAS,KAAK,cAAc;AAC1B,cAAM,QAAQ,CAAC;AAAA,MACjB;AAAA,IACF,CAAC;AACD,WAAO,YAAY;AAAA,EACrB;AACA,MAAI,YAAY,wBAAwB,MAAM;AAC9C,MAAI;AACJ,MAAI,WAAW;AACb,cAAU;AAAA,MACR,GAAG,UAAU,OAAO,kBAAkB;AAAA,MACtC,OAAO,oBAAoB,cAAc,iBAAiB;AAAA,IAC5D;AAAA,EACF;AACA,QAAMA,MAAK,YAAY;AACrB,QAAI,aAAa,mBAAmB;AAClC,gBAAU,OAAO;AAAA,IACnB;AACA,UAAM,YAAY,MAAM;AACtB,cAAQ,SAAS;AAAA,IACnB,CAAC;AAAA,EACH,GAAG,UAAU,KAAK;AAClB,QAAM,uBAAuB;AAAA,IAC3B;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACD,SAAO;AACT;AAEA,IAAM,kBAAkB,CAAC,SAAS,SAAS;AACzC,MAAI,WAAW,IAAI;AAAA,IACjB,CAAC,SAAS,cAAc;AACtB,cAAQ,QAAQ,CAAC,UAAU;AACzB,YAAI,MAAM,gBAAgB;AACxB,eAAK;AACL,oBAAU,UAAU,OAAO;AAAA,QAC7B;AAAA,MACF,CAAC;AAAA,IACH;AAAA,IACA,EAAE,WAAW,EAAE;AAAA,EACjB;AACA,WAAS,QAAQ,OAAO;AAC1B;AAEA,IAAM,eAAe,MAAM,KAAK,OAAO,EAAE,SAAS,EAAE,UAAU,GAAG,CAAC;AAElE,IAAM,UAAU,CAAC,OAAO;AACtB,SAAO,WAAW;AACpB;AAEA,IAAI,cAAc,CAAC,YAAY;AAC7B,MAAI,QAAQ,OAAO,GAAG;AACpB,WAAO,QAAQ,QAAQ,KAAK;AAAA,EAC9B;AACA,SAAO,iBAAiB,SAAS,IAAI,EAAE;AAAA,IACrC,CAAC,MAAM,EAAE,EAAE,WAAW,SAAS;AAAA,EACjC;AACF;AAEA,IAAI,sBAAsB,CAAC,QAAQ;AACjC,SAAO,OAAO,QAAQ,aAAa,IAAI,IAAI;AAC7C;AAEA,IAAI,SAAS,CAAC,UAAU,UAAU,UAAU,MAAM,UAAU;AAC1D,SAAO,QAAQ,gBAAgB,MAAM,QAAQ,EAAE,EAAE,EAAE,QAAQ;AAC7D;AAEA,IAAI,gBAAgB,CAAC,SAAS,QAAQ,KAAK,6BAAM,OAAO;AAExD,IAAI,oBAAoB,CAAC,gBAAgB,cAAc;AACrD,MAAI,QAAQ,cAAc,GAAG;AAC3B,mBAAe,QAAQ,GAAG,eAAe,KAAK,GAAG,UAAU,WAAW;AACtE;AAAA,EACF;AACA,YAAU,YAAY;AACtB,MAAI,SAAS,cAAc,UAAU,cAAc,IAAI;AAAA;AAAA;AAAA,IAGrD,UAAU,kBAAkB;AAAA;AAE9B,MAAI,aAAa,OAAO,MAAM,cAAc,MAAM,KAAK;AACvD,MAAI,cAAc,WAAW,kBAAkB,QAAQ;AACrD,aAAS,WAAW;AAAA,EACtB;AACA,SAAO,aAAa,WAAW,UAAU;AAC3C;AAEA,IAAM,mBAAmB,CAAC,OAAO,sBAAsB,KAAK,GAAG,SAAS;AAExE,IAAM,QAAQ,CAAC,aAAa,WAAW,OAAO,OAAO,CAAC,GAAG,aAAa,MAAM;AAE5E,IAAI,uBAAuB,CAAC,kBAAkB;AA3Z9C;AA4ZE,MAAI,OAAO,kBAAkB,UAAU;AACrC,QAAI,aAAa,CAAC;AAClB,QAAI,EAAE,QAAQ,eAAe,SAAS,eAAe,IAAI,gBAAgB,OAAO;AAChF,eAAW,YAAY,cAAc,aAAa,CAAC;AACnD,eAAW,UAAU,WAAS,mBAAc,cAAd,mBAAyB,WAAU;AACjE,eAAW,UAAU,UAAU;AAAA,MAC7B;AAAA,QACA,mBAAc,cAAd,mBAAyB,YAAW,CAAC;AAAA,IACvC;AACA,eAAW,YAAY,cAAc,aAAa,gBAAgB,OAAO;AACzE,eAAW,iBAAiB,cAAc,kBAAkB,gBAAgB,OAAO;AACnF,WAAO;AAAA,EACT;AACA,MAAI,kBAAkB,MAAM;AAC1B,WAAO,gBAAgB;AAAA,EACzB;AACA,SAAO;AACT;AAEA,IAAM,aAAa,CAAC,MAAM,gBAAgB;AACxC,MAAI,CAAC;AAAM;AACX,MAAI,aAAa,KAAK;AACtB,MAAI,eAAe,WAAW,WAAW,SAAS,KAAK,WAAW,WAAW,WAAW;AAAA;AAAA,IAEtF;AAAA;AAAA;AAAA;AAAA,IAIA;AAAA;AAEF,eAAa,OAAO;AACtB;AAEA,IAAM,mBAAmB,CAAC,SAAS,UAAU,sBAAsB;AACjE,MAAI,qBAAqB,SAAS,oBAAoB,CAAC;AACvD,MAAI,SAAS,OAAO,IAAI,YAAY,IAAI,OAAO;AAC/C,aAAU,yDAAoB,eAAc;AAC5C,UAAQ,aAAa,QAAQ,sBAAsB,IAAI;AACzD;AAEA,SAAS,kBAAkB,OAAO;AAChC,SAAO,OAAO,UAAU,WAAW,OAAO,KAAK,IAAI;AACrD;AAEA,IAAI,mBAAmB;AAAA,EACrB,eAAe;AAAA,EACf,eAAe;AAAA,EACf,aAAa;AAAA,EACb,cAAc;AAAA,EACd,eAAe;AAAA,EACf,OAAO;AAAA,EACP,WAAW;AACb;AACA,IAAI,kBAAkB,CAAC,IAAI,YAAY;AACrC,MAAI,eAAe,IAAI,cAAc,KAAK,EAAE;AAC5C,MAAI,iBAAiB,GAAG,YAAY,KAAK,YAAY;AACrD,MAAI,iBAAiB,iBAAiB,OAAO;AAC7C,MAAI,mBAAmB,OAAO,QAAQ,gBAAgB,EAAE;AAAA,IACtD,CAAC,aAAa,CAAC,MAAM,KAAK,MAAM;AAC9B,aAAO,GAAG,WAAW,IAAI,IAAI,qBAAqB,IAAI,KAAK,SAAS,eAAe,IAAI,CAAC;AAAA,IAC1F;AAAA,IACA;AAAA,EACF;AACA;AAAA,IACE,GAAG,cAAc,uCAAuC,gBAAgB;AAAA,IACxE;AAAA,EACF;AACF;AAEA,SAAS,aAAa,KAAK;AACzB,SAAO,IAAI,QAAQ,iBAAiB,EAAE,EAAE,KAAK,EAAE,MAAM,qBAAqB;AAC5E;AAEA,IAAI,uBAAuB,CAAC,OAAO,gBAAgB,sBAAsB;AACvE,SAAO,KAAK;AAAA,IACV,KAAK,IAAI,iBAAiB,OAAO,CAAC;AAAA,IAClC,kBAAkB;AAAA,EACpB;AACF;AAEA,IAAI,OAAO,CAAC,UAAU,OAAO,aAAa;AACxC,SAAO,IAAI,QAAQ,CAAC,YAAY;AAC9B,QAAI,KAAK,YAAY;AACnB,YAAM,SAAS;AACf,cAAQ;AAAA,IACV;AACA,aAAS,KAAK,WAAW,IAAI,SAAS,CAAC,CAAC;AAAA,EAC1C,CAAC;AACH;AApfA;AAsfA,IAAM,SAAN,MAAa;AAAA,EAmBX,YAAY,SAAS,UAAU,CAAC,GAAG;AAoNnC,uBAAM;AAcN;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBAAM;AA2CN,uBAAM;AAYN;AAAA;AAAA;AAAA;AAAA,uBAAM;AAoBN;AASA,uBAAM;AAgBN;AAAA;AAAA;AAAA,uBAAM;AAiBN;AAGA;AAKA;AAIA;AAMA,uBAAM;AAON;AAAA;AAAA;AAAA;AAAA;AA8BA;AA2BA;AAAA;AAAA;AAAA;AAAA;AAaA;AAIA;AAGA;AAQA;AAGA;AAGA,uBAAI;AAGJ,uBAAI;AAGJ,uBAAI;AAGJ,uBAAI;AAteJ;AACA;AACA;AACA;AACA,oCAAW;AAAA,MACT,SAAS;AAAA,MACT,WAAW;AAAA,MACX,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,QAAQ;AAAA,IACV;AACA;AACA;AACA;AACA;AACA,yCAAgB;AAChB,oCAAW,MAAM;AAAA,IACjB;AAuDA,8BAAK,SAAS,KAAK;AACjB,aAAO,KAAK,SAAS,GAAG;AAAA,IAC1B;AAyUA,sCAAgB,CAAC,YAAY;AAC3B,WAAK,KAAK,SAAS;AAAA,QACjB,QAAQ,UAAU,gBAAgB;AAAA,MACpC;AACA,WAAK,KAAK,UAAU,sBAAK,sDAAL,WAClB,QAAQ,KAAK,KAAK,OAAO;AAE3B,WAAK,OAAO,MAAM,KAAK,MAAM;AAAA,QAC3B,MAAM,CAAC,mBAAK,0BAAY,KAAK,KAAK;AAAA,QAClC,iBAAiB,eAAe,KAAK,KAAK,eAAe;AAAA,QACzD,WAAW,eAAe,KAAK,KAAK,SAAS;AAAA,MAC/C,CAAC;AAAA,IACH;AA5YE,SAAK,OAAO,MAAM,iBAAiB,OAAO;AAC1C,SAAK,UAAU,kBAAkB,OAAO;AACxC,SAAK,WAAW,CAAC;AACjB,SAAK,iBAAiB;AACtB,SAAK,WAAW,MAAM;AAAA,IACtB;AACA,SAAK,0BAA0B;AAC/B,SAAK,WAAW,MAAM,CAAC,GAAG,gBAAgB;AAC1C,SAAK,KAAK,aAAa;AACvB,SAAK,QAAQ,MAAM,CAAC,EAAE,OAAO,KAAK,KAAK,WAAW,CAAC,CAAC;AACpD,uBAAK,eAAL,WAAmB;AACnB,SAAK,SAAS,sBAAK,8BAAL;AACd,SAAK,QAAQ,QAAQ,WAAW,KAAK;AACrC,qBAAiB,eAAe;AAChC,QAAI,KAAK,KAAK,QAAQ,QAAQ;AAC5B,4BAAK,kCAAL;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,KAAK;AACH,QAAI,KAAK,SAAS,SAAS;AACzB,aAAO;AAAA,IACT;AACA,0BAAK,gCAAL;AACA,QAAI,CAAC,KAAK,KAAK,kBAAkB;AAC/B,4BAAK,gBAAL;AACA,aAAO;AAAA,IACT;AACA,oBAAgB,KAAK,SAAS,sBAAK,gBAAM,KAAK,IAAI,CAAC;AACnD,WAAO;AAAA,EACT;AAAA,EACA,QAAQ,qBAAqB,MAAM;AACjC,SAAK,WAAW,gBAAgB,KAAK,QAAQ;AAC7C,wBAAoB,kBAAkB,KAAK,KAAK,UAAU,sBAAK,4BAAL,WAAiB,KAAK;AAChF,SAAK,SAAS,YAAY;AAAA,EAC5B;AAAA,EACA,MAAM,SAAS;AACb,KAAC,KAAK,GAAG,WAAW,KAAK,KAAK,QAAQ;AACtC,QAAI,SAAS;AACX,WAAK,MAAM,KAAK;AAChB,cAAQ,IAAI;AAAA,IACd,OAAO;AACL,WAAK,MAAM,MAAM;AAAA,IACnB;AACA,SAAK,iBAAiB;AACtB,aAAS,YAAY,KAAK,UAAU;AAClC,WAAK,SAAS,QAAQ,IAAI;AAAA,IAC5B;AACA,SAAK,QAAQ,sBAAK,oCAAL,aAAyB,UAAU,WAAW,IAAI;AAC/D,WAAO;AAAA,EACT;AAAA,EAIA,KAAK,QAAQ,aAAa,CAAC,GAAG;AAC5B,aAAS,oBAAoB,MAAM;AACnC,QAAI,EAAE,QAAQ,IAAI;AAClB,QAAI,oBAAoB,sBAAK,0EAAL,WAAwC;AAChE,QAAI,QAAQ,uBAAuB,QAAQ,KAAK,KAAK,IAAI;AACzD,QAAI,oBAAoB,MAAM,IAAI,CAAC,SAAS;AAC1C,aAAO;AAAA,QACL,MAAM,MAAM,sBAAK,gBAAL,WAAW;AAAA,QACvB;AAAA,QACA,OAAO,WAAW,iBAAiB,IAAI,IAAI,IAAI,sBAAK,sBAAL;AAAA,QAC/C,UAAU,KAAK,aAAa,KAAK;AAAA,MACnC;AAAA,IACF,CAAC;AACD,QAAI,eAAe;AAAA,MACjB,kBAAkB,CAAC;AAAA,MACnB,EAAE,MAAM,YAAY,MAAM,KAAK,KAAK,aAAa,QAAQ,IAAI,EAAE;AAAA,MAC/D,GAAG;AAAA,MACH,EAAE,MAAM,YAAY,MAAM,KAAK,KAAK,YAAY,QAAQ,IAAI,EAAE;AAAA,MAC9D,kBAAkB,CAAC;AAAA,IACrB;AACA,WAAO,sBAAK,oCAAL,WAAqB,cAAc;AAAA,EAC5C;AAAA,EACA,MAAM,aAAa,CAAC,GAAG;AACrB,WAAO,sBAAK,oCAAL,WACL;AAAA,MACE,MAAM,MAAM,sBAAK,gBAAL,WAAW,cAAc,IAAI;AAAA,MACzC,UAAU;AAAA,IACZ,GACA;AAAA,EAEJ;AAAA,EACA,KAAK,aAAa,aAAa,CAAC,GAAG;AACjC,kBAAc,oBAAoB,WAAW;AAC7C,QAAI,oBAAoB,sBAAK,0EAAL,WAAwC;AAChE,QAAI,EAAE,SAAS,GAAG,IAAI;AACtB,QAAI,gBAAgB,qBAAqB;AAAA,MACvC,YAAY,KAAK,MAAM,YAAY;AAAA,MACnC,UAAU,gBAAgB,OAAO,KAAK;AAAA,MACtC;AAAA,MACA,gBAAgB,mBAAK;AAAA,IACvB,CAAC;AACD,QAAI,kBAAkB,gBAAgB,IAAI,KAAK;AAC/C,SAAK,0BAA0B,mBAAK,qDAAyB;AAC7D,WAAO,sBAAK,oCAAL,WACL;AAAA,MACE,kBAAkB,CAAC;AAAA,MACnB,GAAG;AAAA,QACD;AAAA,UACE,MAAM,MAAM,sBAAK,gBAAL,WAAW;AAAA,UACvB,OAAO,UAAU,IAAI,sBAAK,sBAAL;AAAA,UACrB,YAAY;AAAA,QACd;AAAA,QACA,KAAK,IAAI,aAAa;AAAA,MACxB;AAAA,MACA,kBAAkB,CAAC;AAAA,IACrB,GACA;AAAA,EAEJ;AAAA,EACA,KAAK,MAAM,aAAa,CAAC,GAAG;AAC1B,QAAI,oBAAoB,sBAAK,0EAAL,WAAwC;AAChE,WAAO,sBAAK,oCAAL,WACL,CAAC,kBAAkB,CAAC,GAAG,EAAE,MAAM,MAAM,KAAK,IAAI,EAAE,GAAG,kBAAkB,CAAC,CAAC,GACvE;AAAA,EAEJ;AAAA,EACA,QAAQ,MAAM,aAAa,CAAC,GAAG;AAC7B,WAAO,oBAAoB,IAAI;AAC/B,0BAAK,kCAAL,WAAoB;AACpB,WAAO,sBAAK,oCAAL,WAAqB,CAAC,GAAG;AAAA,EAClC;AAAA,EACA,MAAM,cAAc,aAAa,CAAC,GAAG;AACnC,WAAO,sBAAK,oCAAL,WACL,EAAE,OAAO,oBAAoB,YAAY,EAAE,GAC3C;AAAA,EAEJ;AAAA,EACA,OAAO,gBAAgB,MAAM,aAAa,CAAC,GAAG;AAC5C,oBAAgB,oBAAoB,aAAa;AACjD,QAAI,oBAAoB,sBAAK,0EAAL,WAAwC;AAChE,QAAI,MAAM;AACV,QAAI,EAAE,SAAS,GAAG,IAAI;AACtB,QAAI,qBAAqB,KAAK,MAAM,YAAY;AAChD,QAAI,UAAU,MAAM;AAClB,UAAI,QAAQ,MAAM;AAChB,eAAO,mBAAmB;AAAA,MAC5B;AACA,UAAI,SAAS,GAAG,GAAG;AACjB,eAAO;AAAA,MACT;AACA,aAAO,qBAAqB;AAAA,QAC1B,YAAY;AAAA,QACZ,UAAU;AAAA,QACV,gBAAgB,mBAAK;AAAA,QACrB;AAAA,MACF,CAAC;AAAA,IACH,GAAG;AACH,WAAO,sBAAK,oCAAL,WACL;AAAA,MACE,kBAAkB,CAAC;AAAA,MACnB,GAAG;AAAA,QACD;AAAA,UACE,MAAM,sBAAK,oBAAQ,KAAK,IAAI;AAAA,UAC5B,OAAO,UAAU,IAAI,sBAAK,sBAAL,WAAc;AAAA,UACnC,WAAW;AAAA,QACb;AAAA,QACA;AAAA,MACF;AAAA,MACA,kBAAkB,CAAC;AAAA,IACrB,GACA;AAAA,EAEJ;AAAA,EACA,SAAS;AACP,SAAK,SAAS,SAAS;AAAA,EACzB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,MAAM,KAAK,MAAM;AACf,SAAK,gBAAgB,MAAM,KAAK;AAChC,QAAI,KAAK,SAAS,QAAQ;AACxB,aAAO;AAAA,IACT;AACA,0BAAK,gCAAL;AACA,0BAAK,gBAAL,WAAW,OAAO,KAAK,MAAM;AAC3B,UAAI,KAAK,MAAM,qBAAqB,EAAE,SAAS,GAAG;AAChD,eAAO,KAAK,MAAM;AAAA,MACpB;AACA,WAAK,cAAc;AACnB,WAAK,gBAAgB;AAAA,IACvB,CAAC;AACD,WAAO;AAAA,EACT;AAAA,EACA,WAAW;AACT,WAAO,KAAK;AAAA,EACd;AAAA,EACA,aAAa;AACX,WAAO,KAAK;AAAA,EACd;AAAA,EACA,cAAc,SAAS;AACrB,WAAO,sBAAK,kCAAL,WAAoB;AAAA,EAC7B;AAAA,EACA,aAAa;AACX,WAAO,KAAK;AAAA,EACd;AAAA,EACA,MAAM,aAAa,CAAC,GAAG;AACrB,WAAO,sBAAK,oCAAL,WAAqB,EAAE,MAAM,sBAAK,kBAAO,KAAK,IAAI,EAAE,GAAG;AAAA,EAChE;AAoQF;AAnQQ;AAAA,WAAM,iBAAG;AACb,MAAI,sBAAK,oCAAL,YAAwB;AAC1B,SAAK,QAAQ,QAAQ;AACrB;AAAA,EACF;AACA,qBAAK,yBAAU,QAAQ,sBAAK,4BAAY,KAAK,IAAI,CAAC;AAClD;AACF;AAOM;AAAA,UAAK,eAAC,WAAW,MAAM;AAC3B,OAAK,SAAS,UAAU;AACxB,OAAK,SAAS,SAAS;AACvB,MAAI,UAAU,CAAC,SAAS;AACtB,SAAK,MAAM,KAAK,MAAM,CAAC,QAAQ;AAAA,EACjC;AACA,MAAI;AACF,QAAI,aAAa,CAAC,GAAG,KAAK,MAAM,SAAS,CAAC;AAC1C,aAAS,QAAQ,GAAG,QAAQ,WAAW,QAAQ,SAAS;AACtD,UAAI,CAAC,UAAU,SAAS,IAAI,WAAW,KAAK;AAC5C,UAAI,UAAU;AAAM;AACpB,UAAI,CAAC,UAAU,aAAa,UAAU,aAAa,mBAAK,yBAAU,QAAQ;AACxE,YAAI,WAAW,MAAM,sBAAK,8CAAL,WAA0B,OAAO;AACtD,uBAAe;AAAA,UACb;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF,CAAC;AACD,gBAAQ;AAAA,MACV;AACA,cAAQ,QAAQ;AAAA,IAClB;AACA,QAAI,CAAC,UAAU;AACb,WAAK,SAAS,SAAS;AACvB,aAAO;AAAA,IACT;AACA,SAAK,SAAS,YAAY;AAC1B,SAAK,SAAS,SAAS;AACvB,UAAM,KAAK,KAAK,cAAc,IAAI;AAClC,QAAI,CAAC,KAAK,KAAK,MAAM;AACnB,YAAM;AAAA,IACR;AACA,QAAI,QAAQ,KAAK,KAAK;AACtB,0BAAK,gBAAL,WAAW,YAAY;AACrB,YAAM,sBAAK,wBAAL,WAAe,MAAM,CAAC;AAC5B,4BAAK,gBAAL;AAAA,IACF,GAAG,MAAM,CAAC;AAAA,EACZ,SAAS,GAAG;AAAA,EACZ;AACA,OAAK,SAAS,SAAS;AACvB,SAAO;AACT;AACM;AAAA,UAAK,eAAC,MAAM;AAChB,OAAK,iBAAiB;AAAA,IACpB;AAAA,IACA,KAAK;AAAA,IACL,mBAAK;AAAA,EACP;AACA,mBAAiB,KAAK,SAAS,mBAAK,0BAAW,KAAK,cAAc;AACpE;AAKM;AAAA,cAAS,eAAC,OAAO;AACrB,MAAI,wBAAwB,mBAAK;AACjC,2BAAyB,MAAM,sBAAK,gBAAL,WAAW,EAAE,OAAO,sBAAsB;AACzE,MAAI,aAAa,mBAAK,yBAAU,IAAI,CAAC,MAAM;AACzC,WAAO;AAAA,MACL,OAAO;AAAA,MACP;AAAA,QACE,MAAM,sBAAK,oBAAQ,KAAK,IAAI;AAAA,QAC5B,OAAO,sBAAK,sBAAL,WAAc;AAAA,QACrB,WAAW;AAAA,QACX,mBAAmB,MAAM;AAAA,MAC3B;AAAA,IACF;AAAA,EACF,CAAC;AACD,WAAS,QAAQ,GAAG,QAAQ,WAAW,QAAQ,SAAS;AACtD,UAAM,sBAAK,8CAAL,WAA0B,OAAO;AAAA,EACzC;AACA,OAAK,MAAM,MAAM;AACjB,OAAK,MAAM,IAAI,GAAG,EAAE,MAAM,CAAC;AAC7B;AACA;AAAA,yBAAoB,SAAC,OAAO,YAAY;AACtC,SAAO,SAAS;AAAA,IACd;AAAA,IACA;AAAA,IACA,MAAM,sBAAK,gBAAM,KAAK,IAAI;AAAA,IAC1B,QAAQ,KAAK;AAAA,IACb,eAAe,KAAK,KAAK;AAAA,EAC3B,CAAC;AACH;AACM;AAAA,UAAK,eAAC,UAAU,OAAO,SAAS,OAAO;AAC3C,MAAI,KAAK,SAAS,QAAQ;AACxB,UAAM,IAAI,QAAQ,CAAC,YAAY;AAC7B,WAAK,WAAW,MAAM;AACpB,aAAK,SAAS,SAAS;AACvB,gBAAQ;AAAA,MACV;AAAA,IACF,CAAC;AAAA,EACH;AACA,YAAU,MAAM,KAAK,KAAK,WAAW,IAAI;AACzC,QAAM,KAAK,UAAU,OAAO,KAAK,QAAQ;AACzC,YAAU,MAAM,KAAK,KAAK,UAAU,IAAI;AAC1C;AAIM;AAAA,kBAAa,iBAAG;AACpB,GAAC,sBAAK,oCAAL,cAA0B,KAAK,UAAU,KAAK,QAAQ,YAAY,KAAK,MAAM;AAC9E,MAAI,mBAAK,8CAAqB;AAC5B,oBAAgB,KAAK,IAAI,KAAK,OAAO;AACrC,SAAK,OAAO,QAAQ,gBAAgB,KAAK;AACzC,QAAI,EAAE,UAAU,IAAI,KAAK,KAAK;AAC9B,QAAI,EAAE,QAAQ,QAAQ,IAAI;AAC1B,uBAAmB;AAAA,MACjB;AAAA,MACA,QAAQ,KAAK;AAAA,MACb,SAAS;AAAA,QACP,UAAU,KAAK,KAAK;AAAA,QACpB,GAAG;AAAA,MACL;AAAA,IACF,CAAC;AAAA,EACH;AACF;AACA;AAAA,oBAAe,WAAG;AAChB,SAAO,QAAQ,KAAK,OAAO;AAC7B;AACA;AAAA,oBAAe,SAAC,OAAO,MAAM;AAC3B,OAAK,MAAM,IAAI,KAAK;AACpB,wBAAK,wCAAL,WAAuB;AACvB,SAAO;AACT;AACA;AAAA,sBAAiB,SAAC,OAAO,CAAC,GAAG;AAC3B,MAAI,QAAQ,KAAK;AACjB,WAAS,KAAK,MAAM,IAAI,EAAE,MAAM,CAAC;AACnC;AACA;AAAA,uCAAkC,SAAC,aAAa,CAAC,GAAG;AAClD,SAAO;AAAA,IACL,EAAE,MAAM,MAAM,sBAAK,kCAAL,WAAoB,YAAY;AAAA,IAC9C,EAAE,MAAM,MAAM,sBAAK,kCAAL,WAAoB,KAAK,MAAM;AAAA,EAC/C;AACF;AACM;AAAA,mBAAc,eAAC,MAAM;AACzB,OAAK,OAAO,MAAM,KAAK,MAAM,IAAI;AACnC;AAKA;AAAA,mBAAc,WAAG;AACf,MAAI,UAAU,KAAK,KAAK,QAAQ,OAAO,CAAC,WAAW,CAAC,CAAC,MAAM;AAC3D,UAAQ,QAAQ,CAAC,QAAQ,UAAU;AACjC,SAAK,KAAK,MAAM;AAChB,QAAI,QAAQ,MAAM,QAAQ,QAAQ;AAChC;AAAA,IACF;AACA,QAAI,aAAa,KAAK,KAAK,aAAa,CAAC,EAAE,MAAM,MAAM,sBAAK,gBAAL,WAAW,cAAc,IAAI,IAAI,UAAU,KAAK,CAAC,IAAI;AAAA,MAC1G;AAAA,QACE,MAAM,sBAAK,oBAAQ,KAAK,IAAI;AAAA,QAC5B,OAAO,sBAAK,sBAAL,WAAc;AAAA,MACvB;AAAA,MACA,KAAK,MAAM,YAAY,EAAE;AAAA,IAC3B;AACA,0BAAK,kCAAL,WAAoB;AAAA,EACtB,CAAC;AACH;AACA;AAaA;AAAA,6BAAwB,SAAC,SAAS;AAChC,MAAI,iBAAiB,KAAK,QAAQ;AAClC,MAAI,CAAC,gBAAgB;AACnB,WAAO;AAAA,EACT;AACA,OAAK,QAAQ,YAAY;AACzB,MAAI,KAAK,KAAK,aAAa;AACzB,SAAK,QAAQ,YAAY;AACzB,oBAAgB,KAAK,OAAO;AAC5B,0BAAK,kCAAL,WACE;AAAA,MACE;AAAA,QACE,MAAM,sBAAK,oBAAQ,KAAK,IAAI;AAAA,QAC5B,OAAO,sBAAK,sBAAL,WAAc;AAAA,QACrB,WAAW;AAAA,MACb;AAAA,MACA,mBAAK,yBAAU;AAAA,IACjB;AAEF,WAAO;AAAA,EACT;AACA,SAAO,aAAa,cAAc,EAAE,OAAO,OAAO;AACpD;AAKA;AAAA,iBAAY,WAAG;AACb,MAAI,mBAAK,wBAAU;AACjB,WAAO;AAAA,EACT;AACA,MAAI,SAAS,cAAc,MAAM;AACjC,SAAO,YAAY;AACnB,MAAI,CAAC,mBAAK,8CAAqB;AAC7B,WAAO,MAAM,aAAa;AAC1B,WAAO;AAAA,EACT;AACA,SAAO,YAAY,cAAc,KAAK,KAAK,UAAU,EAAE;AACvD,SAAO;AACT;AACA;AAAA,mBAAc,SAAC,OAAO;AACpB,MAAI,QAAQ,KAAK,KAAK;AACtB,OAAK,MAAM,IAAI,CAAC,EAAE,OAAO,MAAM,CAAC,EAAE,GAAG,GAAG,OAAO,EAAE,OAAO,MAAM,CAAC,EAAE,CAAC,CAAC;AACrE;AACA;AAAA,UAAK,SAAC,MAAM;AACV,oBAAkB,KAAK,SAAS,IAAI;AACtC;AACA;AAAA,YAAO,WAAG;AACR,MAAI,CAAC,mBAAK,yBAAU;AAAQ;AAC5B,MAAI,mBAAK,wBAAU;AACjB,SAAK,QAAQ,QAAQ,KAAK,QAAQ,MAAM,MAAM,GAAG,EAAE;AAAA,EACrD,OAAO;AACL,0BAAK,4BAAL,WAAiB,mBAAK,yBAAU,KAAK,cAAc;AAAA,EACrD;AACF;AACA;AAAA,gBAAW,SAAC,MAAM;AAChB,aAAW,MAAM,KAAK,OAAO;AAC/B;AACA;AAAA,aAAQ,SAAC,QAAQ,GAAG;AAClB,SAAO,cAAc,KAAK,IAAI,EAAE,KAAK;AACvC;AACI;AAAA,4BAAsB,WAAG;AAC3B,SAAO,KAAK,2BAA2B,KAAK;AAC9C;AACI;AAAA,cAAQ,WAAG;AACb,SAAO,QAAQ,KAAK,OAAO;AAC7B;AACI;AAAA,yBAAmB,WAAG;AACxB,SAAO,CAAC,CAAC,KAAK,KAAK,UAAU,CAAC,mBAAK;AACrC;AACI;AAAA,eAAS,WAAG;AACd,SAAO,YAAY,KAAK,OAAO;AACjC;", "names": ["range", "wait"]}
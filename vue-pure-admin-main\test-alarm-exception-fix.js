/**
 * 測試例外設定載入修復效果
 * 
 * 問題描述：
 * 新系統沒有正確從 GetTagList API 響應中載入例外設定數據
 * 
 * 根本原因：
 * 新系統錯誤地從測點根層級載入例外設定，但實際數據在 Alarm.AlarmException 物件中
 * 
 * 修復方案：
 * 從 originalData.Alarm.AlarmException 物件中正確載入例外設定數據
 */

console.log('🧪 例外設定載入修復測試開始...')

// 模擬 GetTagList API 響應的數據結構
function simulateApiResponse() {
  console.log('\n📋 模擬 GetTagList API 響應結構:')
  
  const apiResponse = {
    Id: '01fa0f04-260e-45c3-9bb6-155934b619b9',
    Name: '高雄火車站商業大樓電力管理系統通道.MB-B51電表.KW',
    SimpleName: 'KW',
    Description: 'MB-B51電表即時功率',
    // ... 其他測點屬性
    Alarm: {
      Status: 1,
      Audio: false,
      Sop: '<p>1221323432</p>',
      NotifyGroup: [],
      HHStatus: true,
      HHValue: '8888',
      HHContent: '',
      // ... 其他警報屬性
      AlarmException: {
        Status: true,
        StartAt: '01:30:20',  // HH:mm:ss 格式
        EndAt: '01:32:39',    // HH:mm:ss 格式
        Until: {
          Id: 2,
          Name: '延續至次日'
        },
        Action: {
          Id: 1,
          Name: '時段內不處理警報'
        }
      }
    }
  }
  
  console.log('API 響應結構:', {
    測點ID: apiResponse.Id,
    測點名稱: apiResponse.SimpleName,
    警報物件存在: !!apiResponse.Alarm,
    例外設定物件存在: !!apiResponse.Alarm?.AlarmException,
    例外設定詳情: apiResponse.Alarm?.AlarmException
  })
  
  return apiResponse
}

// 模擬修復前的錯誤載入邏輯
function simulateBeforeFix(apiResponse) {
  console.log('\n❌ 修復前的錯誤載入邏輯:')
  
  const tagForm = {}
  
  // 修復前：錯誤地從根層級載入
  tagForm.exceptionEnabled = apiResponse.AlarmExceptionStatus || false
  tagForm.exceptionStartTime = apiResponse.AlarmExceptionStartAt || null
  tagForm.exceptionEndTime = apiResponse.AlarmExceptionEndAt || null
  tagForm.exceptionUntil = apiResponse.AlarmExceptionUntil || 1
  tagForm.exceptionAction = apiResponse.AlarmExceptionAction || 0
  
  console.log('修復前載入結果:', {
    例外啟用: tagForm.exceptionEnabled,
    開始時間: tagForm.exceptionStartTime,
    結束時間: tagForm.exceptionEndTime,
    延續類型: tagForm.exceptionUntil,
    例外動作: tagForm.exceptionAction
  })
  
  console.log('❌ 問題：所有例外設定都是預設值，因為根層級沒有這些屬性')
  
  return tagForm
}

// 模擬修復後的正確載入邏輯
function simulateAfterFix(apiResponse) {
  console.log('\n✅ 修復後的正確載入邏輯:')
  
  const tagForm = {}
  
  // 修復後：正確地從 Alarm.AlarmException 物件載入
  const alarmException = apiResponse.Alarm?.AlarmException
  if (alarmException) {
    tagForm.exceptionEnabled = alarmException.Status || false
    
    // 處理例外開始時間（"HH:mm:ss" 格式轉換為 Date 物件）
    if (alarmException.StartAt) {
      const startTime = new Date()
      const [hours, minutes, seconds] = alarmException.StartAt.split(':')
      startTime.setHours(parseInt(hours), parseInt(minutes), parseInt(seconds || 0))
      tagForm.exceptionStartTime = startTime
    } else {
      tagForm.exceptionStartTime = null
    }
    
    // 處理例外結束時間（"HH:mm:ss" 格式轉換為 Date 物件）
    if (alarmException.EndAt) {
      const endTime = new Date()
      const [hours, minutes, seconds] = alarmException.EndAt.split(':')
      endTime.setHours(parseInt(hours), parseInt(minutes), parseInt(seconds || 0))
      tagForm.exceptionEndTime = endTime
    } else {
      tagForm.exceptionEndTime = null
    }
    
    tagForm.exceptionUntil = alarmException.Until?.Id || 1
    tagForm.exceptionAction = alarmException.Action?.Id || 0
  } else {
    // 如果沒有 AlarmException 物件，設為預設值
    tagForm.exceptionEnabled = false
    tagForm.exceptionStartTime = null
    tagForm.exceptionEndTime = null
    tagForm.exceptionUntil = 1
    tagForm.exceptionAction = 0
  }
  
  console.log('修復後載入結果:', {
    例外啟用: tagForm.exceptionEnabled,
    開始時間: tagForm.exceptionStartTime?.toString(),
    結束時間: tagForm.exceptionEndTime?.toString(),
    延續類型: tagForm.exceptionUntil,
    例外動作: tagForm.exceptionAction
  })
  
  console.log('✅ 成功：正確載入了所有例外設定數據')
  
  return tagForm
}

// 執行測試
const apiResponse = simulateApiResponse()
const beforeFix = simulateBeforeFix(apiResponse)
const afterFix = simulateAfterFix(apiResponse)

console.log('\n🎯 修復對比總結:')
console.log('修復前 vs 修復後:')
console.log(`- 例外啟用: ${beforeFix.exceptionEnabled} → ${afterFix.exceptionEnabled}`)
console.log(`- 開始時間: ${beforeFix.exceptionStartTime || '無'} → ${afterFix.exceptionStartTime ? '有效時間' : '無'}`)
console.log(`- 結束時間: ${beforeFix.exceptionEndTime || '無'} → ${afterFix.exceptionEndTime ? '有效時間' : '無'}`)
console.log(`- 延續類型: ${beforeFix.exceptionUntil} → ${afterFix.exceptionUntil}`)
console.log(`- 例外動作: ${beforeFix.exceptionAction} → ${afterFix.exceptionAction}`)

console.log('\n📋 修復要點:')
console.log('1. 從 originalData.Alarm.AlarmException 物件載入例外設定')
console.log('2. 將 "HH:mm:ss" 格式轉換為 Date 物件')
console.log('3. 正確處理 Until.Id 和 Action.Id 屬性')
console.log('4. 與舊系統載入邏輯完全一致')

console.log('\n✅ 例外設定載入修復測試完成！')

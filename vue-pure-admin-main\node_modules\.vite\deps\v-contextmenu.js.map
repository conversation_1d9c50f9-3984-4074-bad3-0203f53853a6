{"version": 3, "sources": ["../../.pnpm/v-contextmenu@3.2.0_vue@3.5.18_typescript@5.8.3_/node_modules/v-contextmenu/dist/index.esm.js"], "sourcesContent": ["import { isRef, defineComponent, ref, computed, reactive, watch, onBeforeUnmount, provide, withDirectives, createVNode, vShow, Teleport, isVNode, nextTick, inject } from 'vue';\n\nvar bind = function bind(el, binding) {\n  var _binding$instance;\n  var contextmenuKey = binding.arg;\n  if (!contextmenuKey) {\n    console.error('参数有误');\n    return;\n  }\n  var contextmenuOptions = binding.value;\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  var contextmenuInstance = isRef(contextmenuKey) ? contextmenuKey.value : (_binding$instance = binding.instance) === null || _binding$instance === void 0 ? void 0 : _binding$instance.$refs[contextmenuKey];\n  if (!contextmenuInstance) {\n    console.error(\"\\u6CA1\\u6709\\u627E\\u5230 \".concat(contextmenuKey, \" \\u5BF9\\u5E94\\u7684\\u5B9E\\u4F8B\"));\n    return;\n  }\n  if (typeof contextmenuInstance.addReference !== 'function') {\n    console.error(\"\".concat(contextmenuKey, \" \\u5BF9\\u5E94\\u7684\\u5B9E\\u4F8B\\u4E0D\\u662F VContextmenu\"));\n    return;\n  }\n  el.$contextmenuKey = contextmenuKey;\n  contextmenuInstance.addReference(el, contextmenuOptions);\n};\nvar unbind = function unbind(el, binding) {\n  var _binding$instance2;\n  var contextmenuKey = el.$contextmenuKey;\n  if (!contextmenuKey) return;\n\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  var contextmenuInstance = isRef(contextmenuKey) ? contextmenuKey.value : (_binding$instance2 = binding.instance) === null || _binding$instance2 === void 0 ? void 0 : _binding$instance2.$refs[contextmenuKey];\n  if (!contextmenuInstance) {\n    console.error(\"\\u6CA1\\u6709\\u627E\\u5230 \".concat(contextmenuKey, \" \\u5BF9\\u5E94\\u7684\\u5B9E\\u4F8B\"));\n    return;\n  }\n  if (typeof contextmenuInstance.removeReference !== 'function') {\n    console.error(\"\".concat(contextmenuKey, \" \\u5BF9\\u5E94\\u7684\\u5B9E\\u4F8B\\u4E0D\\u662F VContextmenu\"));\n    return;\n  }\n  contextmenuInstance.removeReference(el);\n};\nvar rebind = function rebind(el, binding) {\n  unbind(el, binding);\n  bind(el, binding);\n};\nvar contextmenuDirective = {\n  mounted: bind,\n  updated: rebind,\n  beforeUnmount: unbind\n};\n\nvar CLASSES = {\n  contextmenu: 'v-contextmenu',\n  // 根元素\n  contextmenuIcon: 'v-contextmenu-icon',\n  // icon\n  contextmenuInner: 'v-contextmenu-inner',\n  // 菜单根元素\n  contextmenuDivider: 'v-contextmenu-divider',\n  // 分割线\n\n  contextmenuItem: 'v-contextmenu-item',\n  // 单个菜单\n  contextmenuItemHover: 'v-contextmenu-item--hover',\n  // 单个菜单激活状态\n  contextmenuItemDisabled: 'v-contextmenu-item--disabled',\n  // 单个菜单禁用状态\n\n  contextmenuGroup: 'v-contextmenu-group',\n  // 按钮组根元素\n  contextmenuGroupTitle: 'v-contextmenu-group__title',\n  // 按钮组标题\n  contextmenuGroupMenus: 'v-contextmenu-group__menus',\n  // 按钮组按钮容器\n\n  contextmenuSubmenu: 'v-contextmenu-submenu',\n  // 子菜单容器\n  contextmenuSubmenuTitle: 'v-contextmenu-submenu__title',\n  // 子菜单标题\n  contextmenuSubmenuMenus: 'v-contextmenu-submenu__menus',\n  // 子菜单\n  contextmenuSubmenuMenusTop: 'v-contextmenu-submenu__menus--top',\n  // 子菜单 Top\n  contextmenuSubmenuMenusRight: 'v-contextmenu-submenu__menus--right',\n  // 子菜单 Right\n  contextmenuSubmenuMenusBottom: 'v-contextmenu-submenu__menus--bottom',\n  // 子菜 Bottom单\n  contextmenuSubmenuMenusLeft: 'v-contextmenu-submenu__menus--left',\n  // 子菜单 Left\n  contextmenuSubmenuArrow: 'v-contextmenu-submenu__arrow' // 子菜单标题 icon\n};\n\nfunction _isSlot(s) {\n  return typeof s === 'function' || Object.prototype.toString.call(s) === '[object Object]' && !isVNode(s);\n}\nvar DEFAULT_REFERENCE_OPTIONS = {\n  trigger: ['contextmenu']\n};\nvar Contextmenu = defineComponent({\n  name: 'VContextmenu',\n  props: {\n    modelValue: {\n      type: Boolean,\n      default: false\n    },\n    autoAdjustPlacement: {\n      type: Boolean,\n      default: true\n    },\n    disabled: {\n      type: Boolean,\n      default: false\n    },\n    teleport: {\n      type: [String, Object],\n      default: function _default() {\n        return 'body';\n      }\n    },\n    preventContextmenu: {\n      type: Boolean,\n      default: true\n    }\n    // destroyOnHide: {\n    //   type: Boolean,\n    //   default: false,\n    // },\n  },\n  emits: ['show', 'hide', 'update:modelValue', 'contextmenu'],\n  setup: function setup(props, _ref) {\n    var emit = _ref.emit;\n    var contextmenuRef = ref(null);\n    var visible = ref(props.modelValue || false);\n    var toggle = function toggle(value) {\n      visible.value = value;\n      emit('update:modelValue', value);\n    };\n    var position = ref({\n      top: 0,\n      left: 0\n    });\n    var style = computed(function () {\n      return {\n        top: \"\".concat(position.value.top, \"px\"),\n        left: \"\".concat(position.value.left, \"px\")\n      };\n    });\n    var currentOptions = ref(null);\n    var show = function show(evt, options) {\n      var targetOptions = evt instanceof Event ? options : evt;\n      var autoAdjustPlacement = (targetOptions === null || targetOptions === void 0 ? void 0 : targetOptions.autoAdjustPlacement) || props.autoAdjustPlacement;\n      var targetPosition = {\n        top: (targetOptions === null || targetOptions === void 0 ? void 0 : targetOptions.top) || 0,\n        left: (targetOptions === null || targetOptions === void 0 ? void 0 : targetOptions.left) || 0\n      };\n      if (evt instanceof Event) {\n        var _targetOptions$top, _targetOptions$left;\n        evt.preventDefault();\n        targetPosition.top = (_targetOptions$top = targetOptions === null || targetOptions === void 0 ? void 0 : targetOptions.top) !== null && _targetOptions$top !== void 0 ? _targetOptions$top : evt.pageY;\n        targetPosition.left = (_targetOptions$left = targetOptions === null || targetOptions === void 0 ? void 0 : targetOptions.left) !== null && _targetOptions$left !== void 0 ? _targetOptions$left : evt.pageX;\n      }\n      toggle(true);\n      nextTick(function () {\n        if (autoAdjustPlacement) {\n          var el = contextmenuRef.value;\n          if (!el) return;\n          var width = el.clientWidth;\n          var height = el.clientHeight;\n          if (height + targetPosition.top >= window.innerHeight + window.scrollY) {\n            var targetTop = targetPosition.top - height;\n            if (targetTop > window.scrollY) {\n              targetPosition.top = targetTop;\n            }\n          }\n          if (width + targetPosition.left >= window.innerWidth + window.scrollX) {\n            var targetWidth = targetPosition.left - width;\n            if (targetWidth > window.scrollX) {\n              targetPosition.left = targetWidth;\n            }\n          }\n        }\n        position.value = targetPosition;\n\n        // TODO: 添加回调参数\n        emit('show');\n      });\n    };\n    var hide = function hide() {\n      currentOptions.value = null;\n      toggle(false);\n\n      // TODO: 添加回调参数\n      emit('hide');\n    };\n    var references = reactive(new Map());\n    var currentReference = ref();\n    var currentReferenceOptions = computed(function () {\n      return currentReference.value && references.get(currentReference.value);\n    });\n    var addReference = function addReference(el, options) {\n      var triggers = function () {\n        if (options !== null && options !== void 0 && options.trigger) {\n          return Array.isArray(options.trigger) ? options.trigger : [options.trigger];\n        }\n        return DEFAULT_REFERENCE_OPTIONS.trigger;\n      }();\n      var handler = function handler(evt) {\n        if (props.disabled) return;\n        currentReference.value = el;\n        show(evt, {});\n      };\n      triggers.forEach(function (eventType) {\n        el.addEventListener(eventType, handler);\n      });\n      references.set(el, {\n        triggers: triggers,\n        handler: handler\n      });\n    };\n    var removeReference = function removeReference(el) {\n      var options = references.get(el);\n      if (!options) return;\n      options.triggers.forEach(function (eventType) {\n        el.removeEventListener(eventType, options.handler);\n      });\n      references.delete(el);\n    };\n    var onBodyClick = function onBodyClick(evt) {\n      if (!evt.target || !contextmenuRef.value || !currentReference.value) return;\n      var notOutside = contextmenuRef.value.contains(evt.target) || currentReferenceOptions.value && currentReferenceOptions.value.triggers.includes('click') && currentReference.value.contains(evt.target);\n      if (!notOutside) {\n        toggle(false);\n      }\n    };\n\n    // watch(props.modelValue, (value) => {\n    //   if (value !== visible.value) {\n    //     toggle(value);\n    //   }\n    // });\n    watch(visible, function (value) {\n      if (value) {\n        document.addEventListener('click', onBodyClick);\n      } else {\n        document.removeEventListener('click', onBodyClick);\n      }\n    });\n    onBeforeUnmount(function () {\n      document.removeEventListener('click', onBodyClick);\n    });\n    provide('visible', visible);\n    provide('autoAdjustPlacement', props.autoAdjustPlacement);\n    provide('show', show);\n    provide('hide', hide);\n    return {\n      visible: visible,\n      style: style,\n      currentReferenceOptions: currentReferenceOptions,\n      currentOptions: currentOptions,\n      contextmenuRef: contextmenuRef,\n      addReference: addReference,\n      removeReference: removeReference,\n      toggle: toggle,\n      show: show,\n      hide: hide\n    };\n  },\n  methods: {\n    renderContent: function renderContent() {\n      var _this = this,\n        _this$$slots$default,\n        _this$$slots;\n      return withDirectives(createVNode(\"div\", {\n        \"class\": CLASSES.contextmenu,\n        \"ref\": \"contextmenuRef\",\n        \"style\": this.style,\n        \"onContextmenu\": function onContextmenu(evt) {\n          if (_this.$props.preventContextmenu) {\n            evt.preventDefault();\n          }\n          _this.$emit('contextmenu', evt);\n        }\n      }, [createVNode(\"ul\", {\n        \"class\": CLASSES.contextmenuInner\n      }, [(_this$$slots$default = (_this$$slots = this.$slots).default) === null || _this$$slots$default === void 0 ? void 0 : _this$$slots$default.call(_this$$slots, {\n        triggerOptions: 'currentReferenceOptions',\n        options: 'currentOptions'\n      })])]), [[vShow, \"visible\"]]);\n    }\n  },\n  render: function render() {\n    var _slot;\n    if (!this.visible) return null;\n    return this.teleport ? createVNode(Teleport, {\n      \"to\": this.teleport\n    }, _isSlot(_slot = this.renderContent()) ? _slot : {\n      default: function _default() {\n        return [_slot];\n      }\n    }) : this.renderContent();\n  }\n});\n\nfunction _toPrimitive(t, r) {\n  if (\"object\" != typeof t || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != typeof i) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nfunction _toPropertyKey(t) {\n  var i = _toPrimitive(t, \"string\");\n  return \"symbol\" == typeof i ? i : String(i);\n}\nfunction _defineProperty(obj, key, value) {\n  key = _toPropertyKey(key);\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\n\nvar ContextmenuItem = defineComponent({\n  name: 'VContextmenuItem',\n  props: {\n    disabled: {\n      type: Boolean,\n      default: false\n    },\n    hideOnClick: {\n      type: Boolean,\n      default: true\n    },\n    contextmenuAsClick: {\n      type: Boolean,\n      default: true\n    }\n  },\n  emits: ['click', 'contextmenu', 'mouseenter', 'mouseleave'],\n  setup: function setup(props, _ref) {\n    var emit = _ref.emit;\n    var rootHide = inject('hide');\n    var hover = ref(false);\n    var classes = computed(function () {\n      return _defineProperty(_defineProperty(_defineProperty({}, CLASSES.contextmenuItem, true), CLASSES.contextmenuItemDisabled, props.disabled), CLASSES.contextmenuItemHover, hover.value);\n    });\n    var handleClick = function handleClick(evt) {\n      emit('click', evt);\n      if (props.disabled) return;\n      props.hideOnClick && (rootHide === null || rootHide === void 0 ? void 0 : rootHide());\n    };\n    var handleContextmenu = function handleContextmenu(evt) {\n      emit('contextmenu', evt);\n      if (props.contextmenuAsClick) {\n        if (props.disabled) return;\n        props.hideOnClick && (rootHide === null || rootHide === void 0 ? void 0 : rootHide());\n      }\n    };\n    var handleMouseenter = function handleMouseenter(evt) {\n      emit('mouseenter', evt);\n      if (props.disabled) return;\n      hover.value = true;\n    };\n    var handleMouseleave = function handleMouseleave(evt) {\n      emit('mouseleave', evt);\n      if (props.disabled) return;\n      hover.value = false;\n    };\n    return {\n      classes: classes,\n      handleClick: handleClick,\n      handleContextmenu: handleContextmenu,\n      handleMouseenter: handleMouseenter,\n      handleMouseleave: handleMouseleave\n    };\n  },\n  render: function render() {\n    var _this$$slots$default, _this$$slots;\n    return createVNode(\"li\", {\n      \"class\": this.classes,\n      \"onClick\": this.handleClick,\n      \"onContextmenu\": this.handleContextmenu,\n      \"onMouseenter\": this.handleMouseenter,\n      \"onMouseleave\": this.handleMouseleave\n    }, [(_this$$slots$default = (_this$$slots = this.$slots).default) === null || _this$$slots$default === void 0 ? void 0 : _this$$slots$default.call(_this$$slots)]);\n  }\n});\n\nvar ContextmenuDivider = defineComponent({\n  name: 'VContextmenuDivider',\n  render: function render() {\n    return createVNode(\"li\", {\n      \"class\": CLASSES.contextmenuDivider\n    }, null);\n  }\n});\n\nvar ContextmenuIcon = defineComponent({\n  name: 'VContextmenuIcon',\n  props: {\n    name: {\n      type: String,\n      required: true\n    }\n  },\n  render: function render() {\n    return createVNode(\"i\", {\n      \"class\": [CLASSES.contextmenuIcon, \"\".concat(CLASSES.contextmenuIcon, \"-\").concat(this.name)]\n    }, null);\n  }\n});\n\nvar ContextmenuSubmenu = defineComponent({\n  name: 'VContextmenuSubmenu',\n  props: {\n    title: {\n      type: String,\n      required: true\n    },\n    disabled: {\n      type: Boolean,\n      default: false\n    }\n  },\n  emits: ['mouseenter', 'mouseleave'],\n  setup: function setup(props, _ref) {\n    var emit = _ref.emit;\n    var submenuRef = ref(null);\n    var autoAdjustPlacement = inject('autoAdjustPlacement');\n    var placements = ref(['top', 'right']);\n    var hover = ref(false);\n    var handleMouseenter = function handleMouseenter(evt) {\n      if (props.disabled) return;\n      hover.value = true;\n      emit('mouseenter', evt);\n      nextTick(function () {\n        var targetPlacements = [];\n        if (autoAdjustPlacement) {\n          var target = evt.target;\n          var targetDimension = target.getBoundingClientRect();\n          if (!submenuRef.value) return;\n          var submenuWidth = submenuRef.value.clientWidth;\n          var submenuHeight = submenuRef.value.clientHeight;\n          if (targetDimension.right + submenuWidth >= window.innerWidth) {\n            targetPlacements.push('left');\n          } else {\n            targetPlacements.push('right');\n          }\n          if (targetDimension.bottom + submenuHeight >= window.innerHeight) {\n            targetPlacements.push('bottom');\n          } else {\n            targetPlacements.push('top');\n          }\n        }\n        placements.value = targetPlacements;\n      });\n    };\n    var handleMouseleave = function handleMouseleave(evt) {\n      if (props.disabled) return;\n      hover.value = false;\n      emit('mouseleave', evt);\n    };\n    var titleClasses = computed(function () {\n      return _defineProperty(_defineProperty(_defineProperty(_defineProperty({}, CLASSES.contextmenuItem, true), CLASSES.contextmenuSubmenuTitle, true), CLASSES.contextmenuItemHover, hover.value), CLASSES.contextmenuItemDisabled, props.disabled);\n    });\n    var menusClasses = computed(function () {\n      return _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty({}, CLASSES.contextmenu, true), CLASSES.contextmenuSubmenuMenus, true), CLASSES.contextmenuSubmenuMenusTop, placements.value.includes('top')), CLASSES.contextmenuSubmenuMenusRight, placements.value.includes('right')), CLASSES.contextmenuSubmenuMenusBottom, placements.value.includes('bottom')), CLASSES.contextmenuSubmenuMenusLeft, placements.value.includes('left'));\n    });\n    return {\n      hover: hover,\n      submenuRef: submenuRef,\n      titleClasses: titleClasses,\n      menusClasses: menusClasses,\n      handleMouseenter: handleMouseenter,\n      handleMouseleave: handleMouseleave\n    };\n  },\n  render: function render() {\n    var _this$$slots$title, _this$$slots, _this$$slots$default, _this$$slots2;\n    return createVNode(\"li\", {\n      \"class\": CLASSES.contextmenuSubmenu,\n      \"onMouseenter\": this.handleMouseenter,\n      \"onMouseleave\": this.handleMouseleave\n    }, [createVNode(\"div\", {\n      \"class\": this.titleClasses\n    }, [((_this$$slots$title = (_this$$slots = this.$slots).title) === null || _this$$slots$title === void 0 ? void 0 : _this$$slots$title.call(_this$$slots)) || this.title, createVNode(\"span\", {\n      \"class\": CLASSES.contextmenuSubmenuArrow\n    }, [createVNode(ContextmenuIcon, {\n      \"name\": \"right-arrow\"\n    }, null)])]), this.hover ? createVNode(\"div\", {\n      \"ref\": \"submenuRef\",\n      \"class\": this.menusClasses\n    }, [createVNode(\"ul\", {\n      \"class\": CLASSES.contextmenuInner\n    }, [(_this$$slots$default = (_this$$slots2 = this.$slots).default) === null || _this$$slots$default === void 0 ? void 0 : _this$$slots$default.call(_this$$slots2)])]) : null]);\n  }\n});\n\nvar ContextmenuGroup = defineComponent({\n  name: 'VContextmenuGroup',\n  props: {\n    title: {\n      type: String,\n      default: undefined\n    },\n    maxWidth: {\n      type: [Number, String],\n      default: undefined\n    }\n  },\n  setup: function setup(props) {\n    var style = computed(function () {\n      if (!props.maxWidth) return;\n      return {\n        'max-width': typeof props.maxWidth === 'number' ? \"\".concat(props.maxWidth, \"px\") : props.maxWidth,\n        'overflow-x': 'auto'\n      };\n    });\n    return {\n      style: style\n    };\n  },\n  methods: {\n    renderTitle: function renderTitle() {\n      var _this$$slots$title, _this$$slots;\n      var content = ((_this$$slots$title = (_this$$slots = this.$slots).title) === null || _this$$slots$title === void 0 ? void 0 : _this$$slots$title.call(_this$$slots)) || this.title;\n      return content ? createVNode(\"div\", {\n        \"class\": CLASSES.contextmenuGroupTitle\n      }, [content]) : null;\n    }\n  },\n  render: function render() {\n    var _this$$slots$default, _this$$slots2;\n    return createVNode(\"li\", {\n      \"class\": CLASSES.contextmenuGroup\n    }, [this.renderTitle(), createVNode(\"ul\", {\n      \"style\": this.style,\n      \"class\": CLASSES.contextmenuGroupMenus\n    }, [(_this$$slots$default = (_this$$slots2 = this.$slots).default) === null || _this$$slots$default === void 0 ? void 0 : _this$$slots$default.call(_this$$slots2)])]);\n  }\n});\n\nvar version = \"3.2.0\";\n\nvar install = function install(app) {\n  app.directive('contextmenu', contextmenuDirective);\n  app.component(Contextmenu.name, Contextmenu);\n  app.component(ContextmenuItem.name, ContextmenuItem);\n  app.component(ContextmenuDivider.name, ContextmenuDivider);\n  app.component(ContextmenuSubmenu.name, ContextmenuSubmenu);\n  app.component(ContextmenuGroup.name, ContextmenuGroup);\n};\nvar VContextmenu = {\n  install: install,\n  version: version\n};\n\nexport { Contextmenu, ContextmenuDivider, ContextmenuGroup, ContextmenuItem, ContextmenuSubmenu, VContextmenu as default, contextmenuDirective as directive, install, version };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAEA,IAAI,OAAO,SAASA,MAAK,IAAI,SAAS;AACpC,MAAI;AACJ,MAAI,iBAAiB,QAAQ;AAC7B,MAAI,CAAC,gBAAgB;AACnB,YAAQ,MAAM,MAAM;AACpB;AAAA,EACF;AACA,MAAI,qBAAqB,QAAQ;AAEjC,MAAI,sBAAsB,MAAM,cAAc,IAAI,eAAe,SAAS,oBAAoB,QAAQ,cAAc,QAAQ,sBAAsB,SAAS,SAAS,kBAAkB,MAAM,cAAc;AAC1M,MAAI,CAAC,qBAAqB;AACxB,YAAQ,MAAM,QAA4B,OAAO,gBAAgB,QAAiC,CAAC;AACnG;AAAA,EACF;AACA,MAAI,OAAO,oBAAoB,iBAAiB,YAAY;AAC1D,YAAQ,MAAM,GAAG,OAAO,gBAAgB,uBAA0D,CAAC;AACnG;AAAA,EACF;AACA,KAAG,kBAAkB;AACrB,sBAAoB,aAAa,IAAI,kBAAkB;AACzD;AACA,IAAI,SAAS,SAASC,QAAO,IAAI,SAAS;AACxC,MAAI;AACJ,MAAI,iBAAiB,GAAG;AACxB,MAAI,CAAC;AAAgB;AAGrB,MAAI,sBAAsB,MAAM,cAAc,IAAI,eAAe,SAAS,qBAAqB,QAAQ,cAAc,QAAQ,uBAAuB,SAAS,SAAS,mBAAmB,MAAM,cAAc;AAC7M,MAAI,CAAC,qBAAqB;AACxB,YAAQ,MAAM,QAA4B,OAAO,gBAAgB,QAAiC,CAAC;AACnG;AAAA,EACF;AACA,MAAI,OAAO,oBAAoB,oBAAoB,YAAY;AAC7D,YAAQ,MAAM,GAAG,OAAO,gBAAgB,uBAA0D,CAAC;AACnG;AAAA,EACF;AACA,sBAAoB,gBAAgB,EAAE;AACxC;AACA,IAAI,SAAS,SAASC,QAAO,IAAI,SAAS;AACxC,SAAO,IAAI,OAAO;AAClB,OAAK,IAAI,OAAO;AAClB;AACA,IAAI,uBAAuB;AAAA,EACzB,SAAS;AAAA,EACT,SAAS;AAAA,EACT,eAAe;AACjB;AAEA,IAAI,UAAU;AAAA,EACZ,aAAa;AAAA;AAAA,EAEb,iBAAiB;AAAA;AAAA,EAEjB,kBAAkB;AAAA;AAAA,EAElB,oBAAoB;AAAA;AAAA,EAGpB,iBAAiB;AAAA;AAAA,EAEjB,sBAAsB;AAAA;AAAA,EAEtB,yBAAyB;AAAA;AAAA,EAGzB,kBAAkB;AAAA;AAAA,EAElB,uBAAuB;AAAA;AAAA,EAEvB,uBAAuB;AAAA;AAAA,EAGvB,oBAAoB;AAAA;AAAA,EAEpB,yBAAyB;AAAA;AAAA,EAEzB,yBAAyB;AAAA;AAAA,EAEzB,4BAA4B;AAAA;AAAA,EAE5B,8BAA8B;AAAA;AAAA,EAE9B,+BAA+B;AAAA;AAAA,EAE/B,6BAA6B;AAAA;AAAA,EAE7B,yBAAyB;AAAA;AAC3B;AAEA,SAAS,QAAQ,GAAG;AAClB,SAAO,OAAO,MAAM,cAAc,OAAO,UAAU,SAAS,KAAK,CAAC,MAAM,qBAAqB,CAAC,QAAQ,CAAC;AACzG;AACA,IAAI,4BAA4B;AAAA,EAC9B,SAAS,CAAC,aAAa;AACzB;AACA,IAAI,cAAc,gBAAgB;AAAA,EAChC,MAAM;AAAA,EACN,OAAO;AAAA,IACL,YAAY;AAAA,MACV,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,qBAAqB;AAAA,MACnB,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,UAAU;AAAA,MACR,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,UAAU;AAAA,MACR,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,SAAS,WAAW;AAC3B,eAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA;AAAA;AAAA;AAAA;AAAA,EAKF;AAAA,EACA,OAAO,CAAC,QAAQ,QAAQ,qBAAqB,aAAa;AAAA,EAC1D,OAAO,SAAS,MAAM,OAAO,MAAM;AACjC,QAAI,OAAO,KAAK;AAChB,QAAI,iBAAiB,IAAI,IAAI;AAC7B,QAAI,UAAU,IAAI,MAAM,cAAc,KAAK;AAC3C,QAAI,SAAS,SAASC,QAAO,OAAO;AAClC,cAAQ,QAAQ;AAChB,WAAK,qBAAqB,KAAK;AAAA,IACjC;AACA,QAAI,WAAW,IAAI;AAAA,MACjB,KAAK;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AACD,QAAI,QAAQ,SAAS,WAAY;AAC/B,aAAO;AAAA,QACL,KAAK,GAAG,OAAO,SAAS,MAAM,KAAK,IAAI;AAAA,QACvC,MAAM,GAAG,OAAO,SAAS,MAAM,MAAM,IAAI;AAAA,MAC3C;AAAA,IACF,CAAC;AACD,QAAI,iBAAiB,IAAI,IAAI;AAC7B,QAAI,OAAO,SAASC,MAAK,KAAK,SAAS;AACrC,UAAI,gBAAgB,eAAe,QAAQ,UAAU;AACrD,UAAI,uBAAuB,kBAAkB,QAAQ,kBAAkB,SAAS,SAAS,cAAc,wBAAwB,MAAM;AACrI,UAAI,iBAAiB;AAAA,QACnB,MAAM,kBAAkB,QAAQ,kBAAkB,SAAS,SAAS,cAAc,QAAQ;AAAA,QAC1F,OAAO,kBAAkB,QAAQ,kBAAkB,SAAS,SAAS,cAAc,SAAS;AAAA,MAC9F;AACA,UAAI,eAAe,OAAO;AACxB,YAAI,oBAAoB;AACxB,YAAI,eAAe;AACnB,uBAAe,OAAO,qBAAqB,kBAAkB,QAAQ,kBAAkB,SAAS,SAAS,cAAc,SAAS,QAAQ,uBAAuB,SAAS,qBAAqB,IAAI;AACjM,uBAAe,QAAQ,sBAAsB,kBAAkB,QAAQ,kBAAkB,SAAS,SAAS,cAAc,UAAU,QAAQ,wBAAwB,SAAS,sBAAsB,IAAI;AAAA,MACxM;AACA,aAAO,IAAI;AACX,eAAS,WAAY;AACnB,YAAI,qBAAqB;AACvB,cAAI,KAAK,eAAe;AACxB,cAAI,CAAC;AAAI;AACT,cAAI,QAAQ,GAAG;AACf,cAAI,SAAS,GAAG;AAChB,cAAI,SAAS,eAAe,OAAO,OAAO,cAAc,OAAO,SAAS;AACtE,gBAAI,YAAY,eAAe,MAAM;AACrC,gBAAI,YAAY,OAAO,SAAS;AAC9B,6BAAe,MAAM;AAAA,YACvB;AAAA,UACF;AACA,cAAI,QAAQ,eAAe,QAAQ,OAAO,aAAa,OAAO,SAAS;AACrE,gBAAI,cAAc,eAAe,OAAO;AACxC,gBAAI,cAAc,OAAO,SAAS;AAChC,6BAAe,OAAO;AAAA,YACxB;AAAA,UACF;AAAA,QACF;AACA,iBAAS,QAAQ;AAGjB,aAAK,MAAM;AAAA,MACb,CAAC;AAAA,IACH;AACA,QAAI,OAAO,SAASC,QAAO;AACzB,qBAAe,QAAQ;AACvB,aAAO,KAAK;AAGZ,WAAK,MAAM;AAAA,IACb;AACA,QAAI,aAAa,SAAS,oBAAI,IAAI,CAAC;AACnC,QAAI,mBAAmB,IAAI;AAC3B,QAAI,0BAA0B,SAAS,WAAY;AACjD,aAAO,iBAAiB,SAAS,WAAW,IAAI,iBAAiB,KAAK;AAAA,IACxE,CAAC;AACD,QAAI,eAAe,SAASC,cAAa,IAAI,SAAS;AACpD,UAAI,WAAW,WAAY;AACzB,YAAI,YAAY,QAAQ,YAAY,UAAU,QAAQ,SAAS;AAC7D,iBAAO,MAAM,QAAQ,QAAQ,OAAO,IAAI,QAAQ,UAAU,CAAC,QAAQ,OAAO;AAAA,QAC5E;AACA,eAAO,0BAA0B;AAAA,MACnC,EAAE;AACF,UAAI,UAAU,SAASC,SAAQ,KAAK;AAClC,YAAI,MAAM;AAAU;AACpB,yBAAiB,QAAQ;AACzB,aAAK,KAAK,CAAC,CAAC;AAAA,MACd;AACA,eAAS,QAAQ,SAAU,WAAW;AACpC,WAAG,iBAAiB,WAAW,OAAO;AAAA,MACxC,CAAC;AACD,iBAAW,IAAI,IAAI;AAAA,QACjB;AAAA,QACA;AAAA,MACF,CAAC;AAAA,IACH;AACA,QAAI,kBAAkB,SAASC,iBAAgB,IAAI;AACjD,UAAI,UAAU,WAAW,IAAI,EAAE;AAC/B,UAAI,CAAC;AAAS;AACd,cAAQ,SAAS,QAAQ,SAAU,WAAW;AAC5C,WAAG,oBAAoB,WAAW,QAAQ,OAAO;AAAA,MACnD,CAAC;AACD,iBAAW,OAAO,EAAE;AAAA,IACtB;AACA,QAAI,cAAc,SAASC,aAAY,KAAK;AAC1C,UAAI,CAAC,IAAI,UAAU,CAAC,eAAe,SAAS,CAAC,iBAAiB;AAAO;AACrE,UAAI,aAAa,eAAe,MAAM,SAAS,IAAI,MAAM,KAAK,wBAAwB,SAAS,wBAAwB,MAAM,SAAS,SAAS,OAAO,KAAK,iBAAiB,MAAM,SAAS,IAAI,MAAM;AACrM,UAAI,CAAC,YAAY;AACf,eAAO,KAAK;AAAA,MACd;AAAA,IACF;AAOA,UAAM,SAAS,SAAU,OAAO;AAC9B,UAAI,OAAO;AACT,iBAAS,iBAAiB,SAAS,WAAW;AAAA,MAChD,OAAO;AACL,iBAAS,oBAAoB,SAAS,WAAW;AAAA,MACnD;AAAA,IACF,CAAC;AACD,oBAAgB,WAAY;AAC1B,eAAS,oBAAoB,SAAS,WAAW;AAAA,IACnD,CAAC;AACD,YAAQ,WAAW,OAAO;AAC1B,YAAQ,uBAAuB,MAAM,mBAAmB;AACxD,YAAQ,QAAQ,IAAI;AACpB,YAAQ,QAAQ,IAAI;AACpB,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA,EACA,SAAS;AAAA,IACP,eAAe,SAAS,gBAAgB;AACtC,UAAI,QAAQ,MACV,sBACA;AACF,aAAO,eAAe,YAAY,OAAO;AAAA,QACvC,SAAS,QAAQ;AAAA,QACjB,OAAO;AAAA,QACP,SAAS,KAAK;AAAA,QACd,iBAAiB,SAAS,cAAc,KAAK;AAC3C,cAAI,MAAM,OAAO,oBAAoB;AACnC,gBAAI,eAAe;AAAA,UACrB;AACA,gBAAM,MAAM,eAAe,GAAG;AAAA,QAChC;AAAA,MACF,GAAG,CAAC,YAAY,MAAM;AAAA,QACpB,SAAS,QAAQ;AAAA,MACnB,GAAG,EAAE,wBAAwB,eAAe,KAAK,QAAQ,aAAa,QAAQ,yBAAyB,SAAS,SAAS,qBAAqB,KAAK,cAAc;AAAA,QAC/J,gBAAgB;AAAA,QAChB,SAAS;AAAA,MACX,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,OAAO,SAAS,CAAC,CAAC;AAAA,IAC9B;AAAA,EACF;AAAA,EACA,QAAQ,SAAS,SAAS;AACxB,QAAI;AACJ,QAAI,CAAC,KAAK;AAAS,aAAO;AAC1B,WAAO,KAAK,WAAW,YAAY,UAAU;AAAA,MAC3C,MAAM,KAAK;AAAA,IACb,GAAG,QAAQ,QAAQ,KAAK,cAAc,CAAC,IAAI,QAAQ;AAAA,MACjD,SAAS,SAASC,YAAW;AAC3B,eAAO,CAAC,KAAK;AAAA,MACf;AAAA,IACF,CAAC,IAAI,KAAK,cAAc;AAAA,EAC1B;AACF,CAAC;AAED,SAAS,aAAa,GAAG,GAAG;AAC1B,MAAI,YAAY,OAAO,KAAK,CAAC;AAAG,WAAO;AACvC,MAAI,IAAI,EAAE,OAAO,WAAW;AAC5B,MAAI,WAAW,GAAG;AAChB,QAAI,IAAI,EAAE,KAAK,GAAG,KAAK,SAAS;AAChC,QAAI,YAAY,OAAO;AAAG,aAAO;AACjC,UAAM,IAAI,UAAU,8CAA8C;AAAA,EACpE;AACA,UAAQ,aAAa,IAAI,SAAS,QAAQ,CAAC;AAC7C;AACA,SAAS,eAAe,GAAG;AACzB,MAAI,IAAI,aAAa,GAAG,QAAQ;AAChC,SAAO,YAAY,OAAO,IAAI,IAAI,OAAO,CAAC;AAC5C;AACA,SAAS,gBAAgB,KAAK,KAAK,OAAO;AACxC,QAAM,eAAe,GAAG;AACxB,MAAI,OAAO,KAAK;AACd,WAAO,eAAe,KAAK,KAAK;AAAA,MAC9B;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,OAAO;AACL,QAAI,GAAG,IAAI;AAAA,EACb;AACA,SAAO;AACT;AAEA,IAAI,kBAAkB,gBAAgB;AAAA,EACpC,MAAM;AAAA,EACN,OAAO;AAAA,IACL,UAAU;AAAA,MACR,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,aAAa;AAAA,MACX,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,oBAAoB;AAAA,MAClB,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,EACF;AAAA,EACA,OAAO,CAAC,SAAS,eAAe,cAAc,YAAY;AAAA,EAC1D,OAAO,SAASC,OAAM,OAAO,MAAM;AACjC,QAAI,OAAO,KAAK;AAChB,QAAI,WAAW,OAAO,MAAM;AAC5B,QAAI,QAAQ,IAAI,KAAK;AACrB,QAAI,UAAU,SAAS,WAAY;AACjC,aAAO,gBAAgB,gBAAgB,gBAAgB,CAAC,GAAG,QAAQ,iBAAiB,IAAI,GAAG,QAAQ,yBAAyB,MAAM,QAAQ,GAAG,QAAQ,sBAAsB,MAAM,KAAK;AAAA,IACxL,CAAC;AACD,QAAI,cAAc,SAASC,aAAY,KAAK;AAC1C,WAAK,SAAS,GAAG;AACjB,UAAI,MAAM;AAAU;AACpB,YAAM,gBAAgB,aAAa,QAAQ,aAAa,SAAS,SAAS,SAAS;AAAA,IACrF;AACA,QAAI,oBAAoB,SAASC,mBAAkB,KAAK;AACtD,WAAK,eAAe,GAAG;AACvB,UAAI,MAAM,oBAAoB;AAC5B,YAAI,MAAM;AAAU;AACpB,cAAM,gBAAgB,aAAa,QAAQ,aAAa,SAAS,SAAS,SAAS;AAAA,MACrF;AAAA,IACF;AACA,QAAI,mBAAmB,SAASC,kBAAiB,KAAK;AACpD,WAAK,cAAc,GAAG;AACtB,UAAI,MAAM;AAAU;AACpB,YAAM,QAAQ;AAAA,IAChB;AACA,QAAI,mBAAmB,SAASC,kBAAiB,KAAK;AACpD,WAAK,cAAc,GAAG;AACtB,UAAI,MAAM;AAAU;AACpB,YAAM,QAAQ;AAAA,IAChB;AACA,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA,EACA,QAAQ,SAASC,UAAS;AACxB,QAAI,sBAAsB;AAC1B,WAAO,YAAY,MAAM;AAAA,MACvB,SAAS,KAAK;AAAA,MACd,WAAW,KAAK;AAAA,MAChB,iBAAiB,KAAK;AAAA,MACtB,gBAAgB,KAAK;AAAA,MACrB,gBAAgB,KAAK;AAAA,IACvB,GAAG,EAAE,wBAAwB,eAAe,KAAK,QAAQ,aAAa,QAAQ,yBAAyB,SAAS,SAAS,qBAAqB,KAAK,YAAY,CAAC,CAAC;AAAA,EACnK;AACF,CAAC;AAED,IAAI,qBAAqB,gBAAgB;AAAA,EACvC,MAAM;AAAA,EACN,QAAQ,SAASA,UAAS;AACxB,WAAO,YAAY,MAAM;AAAA,MACvB,SAAS,QAAQ;AAAA,IACnB,GAAG,IAAI;AAAA,EACT;AACF,CAAC;AAED,IAAI,kBAAkB,gBAAgB;AAAA,EACpC,MAAM;AAAA,EACN,OAAO;AAAA,IACL,MAAM;AAAA,MACJ,MAAM;AAAA,MACN,UAAU;AAAA,IACZ;AAAA,EACF;AAAA,EACA,QAAQ,SAASA,UAAS;AACxB,WAAO,YAAY,KAAK;AAAA,MACtB,SAAS,CAAC,QAAQ,iBAAiB,GAAG,OAAO,QAAQ,iBAAiB,GAAG,EAAE,OAAO,KAAK,IAAI,CAAC;AAAA,IAC9F,GAAG,IAAI;AAAA,EACT;AACF,CAAC;AAED,IAAI,qBAAqB,gBAAgB;AAAA,EACvC,MAAM;AAAA,EACN,OAAO;AAAA,IACL,OAAO;AAAA,MACL,MAAM;AAAA,MACN,UAAU;AAAA,IACZ;AAAA,IACA,UAAU;AAAA,MACR,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,EACF;AAAA,EACA,OAAO,CAAC,cAAc,YAAY;AAAA,EAClC,OAAO,SAASL,OAAM,OAAO,MAAM;AACjC,QAAI,OAAO,KAAK;AAChB,QAAI,aAAa,IAAI,IAAI;AACzB,QAAI,sBAAsB,OAAO,qBAAqB;AACtD,QAAI,aAAa,IAAI,CAAC,OAAO,OAAO,CAAC;AACrC,QAAI,QAAQ,IAAI,KAAK;AACrB,QAAI,mBAAmB,SAASG,kBAAiB,KAAK;AACpD,UAAI,MAAM;AAAU;AACpB,YAAM,QAAQ;AACd,WAAK,cAAc,GAAG;AACtB,eAAS,WAAY;AACnB,YAAI,mBAAmB,CAAC;AACxB,YAAI,qBAAqB;AACvB,cAAI,SAAS,IAAI;AACjB,cAAI,kBAAkB,OAAO,sBAAsB;AACnD,cAAI,CAAC,WAAW;AAAO;AACvB,cAAI,eAAe,WAAW,MAAM;AACpC,cAAI,gBAAgB,WAAW,MAAM;AACrC,cAAI,gBAAgB,QAAQ,gBAAgB,OAAO,YAAY;AAC7D,6BAAiB,KAAK,MAAM;AAAA,UAC9B,OAAO;AACL,6BAAiB,KAAK,OAAO;AAAA,UAC/B;AACA,cAAI,gBAAgB,SAAS,iBAAiB,OAAO,aAAa;AAChE,6BAAiB,KAAK,QAAQ;AAAA,UAChC,OAAO;AACL,6BAAiB,KAAK,KAAK;AAAA,UAC7B;AAAA,QACF;AACA,mBAAW,QAAQ;AAAA,MACrB,CAAC;AAAA,IACH;AACA,QAAI,mBAAmB,SAASC,kBAAiB,KAAK;AACpD,UAAI,MAAM;AAAU;AACpB,YAAM,QAAQ;AACd,WAAK,cAAc,GAAG;AAAA,IACxB;AACA,QAAI,eAAe,SAAS,WAAY;AACtC,aAAO,gBAAgB,gBAAgB,gBAAgB,gBAAgB,CAAC,GAAG,QAAQ,iBAAiB,IAAI,GAAG,QAAQ,yBAAyB,IAAI,GAAG,QAAQ,sBAAsB,MAAM,KAAK,GAAG,QAAQ,yBAAyB,MAAM,QAAQ;AAAA,IAChP,CAAC;AACD,QAAI,eAAe,SAAS,WAAY;AACtC,aAAO,gBAAgB,gBAAgB,gBAAgB,gBAAgB,gBAAgB,gBAAgB,CAAC,GAAG,QAAQ,aAAa,IAAI,GAAG,QAAQ,yBAAyB,IAAI,GAAG,QAAQ,4BAA4B,WAAW,MAAM,SAAS,KAAK,CAAC,GAAG,QAAQ,8BAA8B,WAAW,MAAM,SAAS,OAAO,CAAC,GAAG,QAAQ,+BAA+B,WAAW,MAAM,SAAS,QAAQ,CAAC,GAAG,QAAQ,6BAA6B,WAAW,MAAM,SAAS,MAAM,CAAC;AAAA,IACtd,CAAC;AACD,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA,EACA,QAAQ,SAASC,UAAS;AACxB,QAAI,oBAAoB,cAAc,sBAAsB;AAC5D,WAAO,YAAY,MAAM;AAAA,MACvB,SAAS,QAAQ;AAAA,MACjB,gBAAgB,KAAK;AAAA,MACrB,gBAAgB,KAAK;AAAA,IACvB,GAAG,CAAC,YAAY,OAAO;AAAA,MACrB,SAAS,KAAK;AAAA,IAChB,GAAG,GAAG,sBAAsB,eAAe,KAAK,QAAQ,WAAW,QAAQ,uBAAuB,SAAS,SAAS,mBAAmB,KAAK,YAAY,MAAM,KAAK,OAAO,YAAY,QAAQ;AAAA,MAC5L,SAAS,QAAQ;AAAA,IACnB,GAAG,CAAC,YAAY,iBAAiB;AAAA,MAC/B,QAAQ;AAAA,IACV,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,QAAQ,YAAY,OAAO;AAAA,MAC5C,OAAO;AAAA,MACP,SAAS,KAAK;AAAA,IAChB,GAAG,CAAC,YAAY,MAAM;AAAA,MACpB,SAAS,QAAQ;AAAA,IACnB,GAAG,EAAE,wBAAwB,gBAAgB,KAAK,QAAQ,aAAa,QAAQ,yBAAyB,SAAS,SAAS,qBAAqB,KAAK,aAAa,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC;AAAA,EAChL;AACF,CAAC;AAED,IAAI,mBAAmB,gBAAgB;AAAA,EACrC,MAAM;AAAA,EACN,OAAO;AAAA,IACL,OAAO;AAAA,MACL,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,UAAU;AAAA,MACR,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS;AAAA,IACX;AAAA,EACF;AAAA,EACA,OAAO,SAASL,OAAM,OAAO;AAC3B,QAAI,QAAQ,SAAS,WAAY;AAC/B,UAAI,CAAC,MAAM;AAAU;AACrB,aAAO;AAAA,QACL,aAAa,OAAO,MAAM,aAAa,WAAW,GAAG,OAAO,MAAM,UAAU,IAAI,IAAI,MAAM;AAAA,QAC1F,cAAc;AAAA,MAChB;AAAA,IACF,CAAC;AACD,WAAO;AAAA,MACL;AAAA,IACF;AAAA,EACF;AAAA,EACA,SAAS;AAAA,IACP,aAAa,SAAS,cAAc;AAClC,UAAI,oBAAoB;AACxB,UAAI,YAAY,sBAAsB,eAAe,KAAK,QAAQ,WAAW,QAAQ,uBAAuB,SAAS,SAAS,mBAAmB,KAAK,YAAY,MAAM,KAAK;AAC7K,aAAO,UAAU,YAAY,OAAO;AAAA,QAClC,SAAS,QAAQ;AAAA,MACnB,GAAG,CAAC,OAAO,CAAC,IAAI;AAAA,IAClB;AAAA,EACF;AAAA,EACA,QAAQ,SAASK,UAAS;AACxB,QAAI,sBAAsB;AAC1B,WAAO,YAAY,MAAM;AAAA,MACvB,SAAS,QAAQ;AAAA,IACnB,GAAG,CAAC,KAAK,YAAY,GAAG,YAAY,MAAM;AAAA,MACxC,SAAS,KAAK;AAAA,MACd,SAAS,QAAQ;AAAA,IACnB,GAAG,EAAE,wBAAwB,gBAAgB,KAAK,QAAQ,aAAa,QAAQ,yBAAyB,SAAS,SAAS,qBAAqB,KAAK,aAAa,CAAC,CAAC,CAAC,CAAC;AAAA,EACvK;AACF,CAAC;AAED,IAAI,UAAU;AAEd,IAAI,UAAU,SAASC,SAAQ,KAAK;AAClC,MAAI,UAAU,eAAe,oBAAoB;AACjD,MAAI,UAAU,YAAY,MAAM,WAAW;AAC3C,MAAI,UAAU,gBAAgB,MAAM,eAAe;AACnD,MAAI,UAAU,mBAAmB,MAAM,kBAAkB;AACzD,MAAI,UAAU,mBAAmB,MAAM,kBAAkB;AACzD,MAAI,UAAU,iBAAiB,MAAM,gBAAgB;AACvD;AACA,IAAI,eAAe;AAAA,EACjB;AAAA,EACA;AACF;", "names": ["bind", "unbind", "rebind", "toggle", "show", "hide", "addReference", "handler", "removeReference", "onBodyClick", "_default", "setup", "handleClick", "handleContextmenu", "handleMouseenter", "handleMouseleave", "render", "install"]}
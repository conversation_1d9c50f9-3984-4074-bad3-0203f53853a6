{"version": 3, "sources": ["../../.pnpm/vue-resize@2.0.0-alpha.1_vue@3.5.18_typescript@5.8.3_/node_modules/vue-resize/src/utils/compatibility.js", "../../.pnpm/vue-resize@2.0.0-alpha.1_vue@3.5.18_typescript@5.8.3_/node_modules/vue-resize/src/components/ResizeObserver.vue", "../../.pnpm/vue-resize@2.0.0-alpha.1_vue@3.5.18_typescript@5.8.3_/node_modules/vue-resize/src/components/ResizeObserver.vue?vue&type=template&id=b329ee4c&lang.js", "../../.pnpm/vue-resize@2.0.0-alpha.1_vue@3.5.18_typescript@5.8.3_/node_modules/vue-resize/src/index.js", "../../.pnpm/vue-observe-visibility@2.0._ddf9073488828b840c31ea5b94a35276/node_modules/vue-observe-visibility/dist/vue-observe-visibility.esm.js", "../../.pnpm/mitt@2.1.0/node_modules/mitt/src/index.ts", "../../.pnpm/vue-virtual-scroller@2.0.0-_b48791c03294d7c2b531af578b1f8594/node_modules/vue-virtual-scroller/src/config.js", "../../.pnpm/vue-virtual-scroller@2.0.0-_b48791c03294d7c2b531af578b1f8594/node_modules/vue-virtual-scroller/src/scrollparent.js", "../../.pnpm/vue-virtual-scroller@2.0.0-_b48791c03294d7c2b531af578b1f8594/node_modules/vue-virtual-scroller/src/components/common.js", "../../.pnpm/vue-virtual-scroller@2.0.0-_b48791c03294d7c2b531af578b1f8594/node_modules/vue-virtual-scroller/src/utils.js", "../../.pnpm/vue-virtual-scroller@2.0.0-_b48791c03294d7c2b531af578b1f8594/node_modules/vue-virtual-scroller/src/components/RecycleScroller.vue", "../../.pnpm/vue-virtual-scroller@2.0.0-_b48791c03294d7c2b531af578b1f8594/node_modules/vue-virtual-scroller/src/components/RecycleScroller.vue?vue&type=template&id=093a936d&lang.js", "../../.pnpm/vue-virtual-scroller@2.0.0-_b48791c03294d7c2b531af578b1f8594/node_modules/vue-virtual-scroller/src/components/DynamicScroller.vue", "../../.pnpm/vue-virtual-scroller@2.0.0-_b48791c03294d7c2b531af578b1f8594/node_modules/vue-virtual-scroller/src/components/DynamicScroller.vue?vue&type=template&id=76e15f19&lang.js", "../../.pnpm/vue-virtual-scroller@2.0.0-_b48791c03294d7c2b531af578b1f8594/node_modules/vue-virtual-scroller/src/components/DynamicScrollerItem.vue", "../../.pnpm/vue-virtual-scroller@2.0.0-_b48791c03294d7c2b531af578b1f8594/node_modules/vue-virtual-scroller/src/mixins/IdState.js", "../../.pnpm/vue-virtual-scroller@2.0.0-_b48791c03294d7c2b531af578b1f8594/node_modules/vue-virtual-scroller/src/index.js"], "sourcesContent": ["export function getInternetExplorerVersion () {\n  const ua = window.navigator.userAgent\n\n  const msie = ua.indexOf('MSIE ')\n  if (msie > 0) {\n    // IE 10 or older => return version number\n    return parseInt(ua.substring(msie + 5, ua.indexOf('.', msie)), 10)\n  }\n\n  const trident = ua.indexOf('Trident/')\n  if (trident > 0) {\n    // IE 11 => return version number\n    const rv = ua.indexOf('rv:')\n    return parseInt(ua.substring(rv + 3, ua.indexOf('.', rv)), 10)\n  }\n\n  const edge = ua.indexOf('Edge/')\n  if (edge > 0) {\n    // Edge (IE 12+) => return version number\n    return parseInt(ua.substring(edge + 5, ua.indexOf('.', edge)), 10)\n  }\n\n  // other browser\n  return -1\n}\n", "<template>\n  <div\n    class=\"resize-observer\"\n    tabindex=\"-1\"\n  />\n</template>\n\n<script>\nimport { nextTick } from 'vue'\nimport { getInternetExplorerVersion } from '../utils/compatibility'\n\nlet isIE\n\nfunction initCompat () {\n  if (!initCompat.init) {\n    initCompat.init = true\n    isIE = getInternetExplorerVersion() !== -1\n  }\n}\n\nexport default {\n  name: 'ResizeObserver',\n\n  props: {\n    emitOnMount: {\n      type: Boolean,\n      default: false,\n    },\n\n    ignoreWidth: {\n      type: Boolean,\n      default: false,\n    },\n\n    ignoreHeight: {\n      type: Boolean,\n      default: false,\n    },\n  },\n\n  emits: [\n    'notify',\n  ],\n\n  mounted () {\n    initCompat()\n    nextTick(() => {\n      this._w = this.$el.offsetWidth\n      this._h = this.$el.offsetHeight\n      if (this.emitOnMount) {\n        this.emitSize()\n      }\n    })\n    const object = document.createElement('object')\n    this._resizeObject = object\n    object.setAttribute('aria-hidden', 'true')\n    object.setAttribute('tabindex', -1)\n    object.onload = this.addResizeHandlers\n    object.type = 'text/html'\n    if (isIE) {\n      this.$el.appendChild(object)\n    }\n    object.data = 'about:blank'\n    if (!isIE) {\n      this.$el.appendChild(object)\n    }\n  },\n\n  beforeUnmount () {\n    this.removeResizeHandlers()\n  },\n\n  methods: {\n    compareAndNotify () {\n      if ((!this.ignoreWidth && this._w !== this.$el.offsetWidth) || (!this.ignoreHeight && this._h !== this.$el.offsetHeight)) {\n        this._w = this.$el.offsetWidth\n        this._h = this.$el.offsetHeight\n        this.emitSize()\n      }\n    },\n\n    emitSize () {\n      this.$emit('notify', {\n        width: this._w,\n        height: this._h,\n      })\n    },\n\n    addResizeHandlers () {\n      this._resizeObject.contentDocument.defaultView.addEventListener('resize', this.compareAndNotify)\n      this.compareAndNotify()\n    },\n\n    removeResizeHandlers () {\n      if (this._resizeObject && this._resizeObject.onload) {\n        if (!isIE && this._resizeObject.contentDocument) {\n          this._resizeObject.contentDocument.defaultView.removeEventListener('resize', this.compareAndNotify)\n        }\n        this.$el.removeChild(this._resizeObject)\n        this._resizeObject.onload = null\n        this._resizeObject = null\n      }\n    },\n  },\n}\n</script>\n\n<style scoped>\n.resize-observer {\n  position: absolute;\n  top: 0;\n  left: 0;\n  z-index: -1;\n  width: 100%;\n  height: 100%;\n  border: none;\n  background-color: transparent;\n  pointer-events: none;\n  display: block;\n  overflow: hidden;\n  opacity: 0;\n}\n\n.resize-observer:deep(object) {\n  display: block;\n  position: absolute;\n  top: 0;\n  left: 0;\n  height: 100%;\n  width: 100%;\n  overflow: hidden;\n  pointer-events: none;\n  z-index: -1;\n}\n</style>\n", "<template>\n  <div\n    class=\"resize-observer\"\n    tabindex=\"-1\"\n  />\n</template>\n\n<script>\nimport { nextTick } from 'vue'\nimport { getInternetExplorerVersion } from '../utils/compatibility'\n\nlet isIE\n\nfunction initCompat () {\n  if (!initCompat.init) {\n    initCompat.init = true\n    isIE = getInternetExplorerVersion() !== -1\n  }\n}\n\nexport default {\n  name: 'ResizeObserver',\n\n  props: {\n    emitOnMount: {\n      type: Boolean,\n      default: false,\n    },\n\n    ignoreWidth: {\n      type: Boolean,\n      default: false,\n    },\n\n    ignoreHeight: {\n      type: Boolean,\n      default: false,\n    },\n  },\n\n  emits: [\n    'notify',\n  ],\n\n  mounted () {\n    initCompat()\n    nextTick(() => {\n      this._w = this.$el.offsetWidth\n      this._h = this.$el.offsetHeight\n      if (this.emitOnMount) {\n        this.emitSize()\n      }\n    })\n    const object = document.createElement('object')\n    this._resizeObject = object\n    object.setAttribute('aria-hidden', 'true')\n    object.setAttribute('tabindex', -1)\n    object.onload = this.addResizeHandlers\n    object.type = 'text/html'\n    if (isIE) {\n      this.$el.appendChild(object)\n    }\n    object.data = 'about:blank'\n    if (!isIE) {\n      this.$el.appendChild(object)\n    }\n  },\n\n  beforeUnmount () {\n    this.removeResizeHandlers()\n  },\n\n  methods: {\n    compareAndNotify () {\n      if ((!this.ignoreWidth && this._w !== this.$el.offsetWidth) || (!this.ignoreHeight && this._h !== this.$el.offsetHeight)) {\n        this._w = this.$el.offsetWidth\n        this._h = this.$el.offsetHeight\n        this.emitSize()\n      }\n    },\n\n    emitSize () {\n      this.$emit('notify', {\n        width: this._w,\n        height: this._h,\n      })\n    },\n\n    addResizeHandlers () {\n      this._resizeObject.contentDocument.defaultView.addEventListener('resize', this.compareAndNotify)\n      this.compareAndNotify()\n    },\n\n    removeResizeHandlers () {\n      if (this._resizeObject && this._resizeObject.onload) {\n        if (!isIE && this._resizeObject.contentDocument) {\n          this._resizeObject.contentDocument.defaultView.removeEventListener('resize', this.compareAndNotify)\n        }\n        this.$el.removeChild(this._resizeObject)\n        this._resizeObject.onload = null\n        this._resizeObject = null\n      }\n    },\n  },\n}\n</script>\n\n<style scoped>\n.resize-observer {\n  position: absolute;\n  top: 0;\n  left: 0;\n  z-index: -1;\n  width: 100%;\n  height: 100%;\n  border: none;\n  background-color: transparent;\n  pointer-events: none;\n  display: block;\n  overflow: hidden;\n  opacity: 0;\n}\n\n.resize-observer:deep(object) {\n  display: block;\n  position: absolute;\n  top: 0;\n  left: 0;\n  height: 100%;\n  width: 100%;\n  overflow: hidden;\n  pointer-events: none;\n  z-index: -1;\n}\n</style>\n", "import ResizeObserver from './components/ResizeObserver.vue'\n\n// Install the components\nexport function install (app) {\n  // eslint-disable-next-line vue/component-definition-name-casing\n  app.component('resize-observer', ResizeObserver)\n  app.component('ResizeObserver', ResizeObserver)\n}\n\nexport {\n  ResizeObserver,\n}\n\n// Plugin\nconst plugin = {\n  // eslint-disable-next-line no-undef\n  version: VERSION,\n  install,\n}\n\nexport default plugin\n", "import { nextTick } from 'vue';\n\nfunction _typeof(obj) {\n  \"@babel/helpers - typeof\";\n\n  if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") {\n    _typeof = function (obj) {\n      return typeof obj;\n    };\n  } else {\n    _typeof = function (obj) {\n      return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n    };\n  }\n\n  return _typeof(obj);\n}\n\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\n\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, descriptor.key, descriptor);\n  }\n}\n\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  return Constructor;\n}\n\nfunction _toConsumableArray(arr) {\n  return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread();\n}\n\nfunction _arrayWithoutHoles(arr) {\n  if (Array.isArray(arr)) return _arrayLikeToArray(arr);\n}\n\nfunction _iterableToArray(iter) {\n  if (typeof Symbol !== \"undefined\" && Symbol.iterator in Object(iter)) return Array.from(iter);\n}\n\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\n\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n\n  for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];\n\n  return arr2;\n}\n\nfunction _nonIterableSpread() {\n  throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\n\nfunction processOptions(value) {\n  var options;\n\n  if (typeof value === 'function') {\n    // Simple options (callback-only)\n    options = {\n      callback: value\n    };\n  } else {\n    // Options object\n    options = value;\n  }\n\n  return options;\n}\nfunction throttle(callback, delay) {\n  var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n  var timeout;\n  var lastState;\n  var currentArgs;\n\n  var throttled = function throttled(state) {\n    for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n      args[_key - 1] = arguments[_key];\n    }\n\n    currentArgs = args;\n    if (timeout && state === lastState) return;\n    var leading = options.leading;\n\n    if (typeof leading === 'function') {\n      leading = leading(state, lastState);\n    }\n\n    if ((!timeout || state !== lastState) && leading) {\n      callback.apply(void 0, [state].concat(_toConsumableArray(currentArgs)));\n    }\n\n    lastState = state;\n    clearTimeout(timeout);\n    timeout = setTimeout(function () {\n      callback.apply(void 0, [state].concat(_toConsumableArray(currentArgs)));\n      timeout = 0;\n    }, delay);\n  };\n\n  throttled._clear = function () {\n    clearTimeout(timeout);\n    timeout = null;\n  };\n\n  return throttled;\n}\nfunction deepEqual(val1, val2) {\n  if (val1 === val2) return true;\n\n  if (_typeof(val1) === 'object') {\n    for (var key in val1) {\n      if (!deepEqual(val1[key], val2[key])) {\n        return false;\n      }\n    }\n\n    return true;\n  }\n\n  return false;\n}\n\nvar VisibilityState = /*#__PURE__*/function () {\n  function VisibilityState(el, options, vnode) {\n    _classCallCheck(this, VisibilityState);\n\n    this.el = el;\n    this.observer = null;\n    this.frozen = false;\n    this.createObserver(options, vnode);\n  }\n\n  _createClass(VisibilityState, [{\n    key: \"createObserver\",\n    value: function createObserver(options, vnode) {\n      var _this = this;\n\n      if (this.observer) {\n        this.destroyObserver();\n      }\n\n      if (this.frozen) return;\n      this.options = processOptions(options);\n\n      this.callback = function (result, entry) {\n        _this.options.callback(result, entry);\n\n        if (result && _this.options.once) {\n          _this.frozen = true;\n\n          _this.destroyObserver();\n        }\n      }; // Throttle\n\n\n      if (this.callback && this.options.throttle) {\n        var _ref = this.options.throttleOptions || {},\n            _leading = _ref.leading;\n\n        this.callback = throttle(this.callback, this.options.throttle, {\n          leading: function leading(state) {\n            return _leading === 'both' || _leading === 'visible' && state || _leading === 'hidden' && !state;\n          }\n        });\n      }\n\n      this.oldResult = undefined;\n      this.observer = new IntersectionObserver(function (entries) {\n        var entry = entries[0];\n\n        if (entries.length > 1) {\n          var intersectingEntry = entries.find(function (e) {\n            return e.isIntersecting;\n          });\n\n          if (intersectingEntry) {\n            entry = intersectingEntry;\n          }\n        }\n\n        if (_this.callback) {\n          // Use isIntersecting if possible because browsers can report isIntersecting as true, but intersectionRatio as 0, when something very slowly enters the viewport.\n          var result = entry.isIntersecting && entry.intersectionRatio >= _this.threshold;\n          if (result === _this.oldResult) return;\n          _this.oldResult = result;\n\n          _this.callback(result, entry);\n        }\n      }, this.options.intersection); // Wait for the element to be in document\n\n      nextTick(function () {\n        if (_this.observer) {\n          _this.observer.observe(_this.el);\n        }\n      });\n    }\n  }, {\n    key: \"destroyObserver\",\n    value: function destroyObserver() {\n      if (this.observer) {\n        this.observer.disconnect();\n        this.observer = null;\n      } // Cancel throttled call\n\n\n      if (this.callback && this.callback._clear) {\n        this.callback._clear();\n\n        this.callback = null;\n      }\n    }\n  }, {\n    key: \"threshold\",\n    get: function get() {\n      return this.options.intersection && typeof this.options.intersection.threshold === 'number' ? this.options.intersection.threshold : 0;\n    }\n  }]);\n\n  return VisibilityState;\n}();\n\nfunction beforeMount(el, _ref2, vnode) {\n  var value = _ref2.value;\n  if (!value) return;\n\n  if (typeof IntersectionObserver === 'undefined') {\n    console.warn('[vue-observe-visibility] IntersectionObserver API is not available in your browser. Please install this polyfill: https://github.com/w3c/IntersectionObserver/tree/master/polyfill');\n  } else {\n    var state = new VisibilityState(el, value, vnode);\n    el._vue_visibilityState = state;\n  }\n}\n\nfunction updated(el, _ref3, vnode) {\n  var value = _ref3.value,\n      oldValue = _ref3.oldValue;\n  if (deepEqual(value, oldValue)) return;\n  var state = el._vue_visibilityState;\n\n  if (!value) {\n    unmounted(el);\n    return;\n  }\n\n  if (state) {\n    state.createObserver(value, vnode);\n  } else {\n    beforeMount(el, {\n      value: value\n    }, vnode);\n  }\n}\n\nfunction unmounted(el) {\n  var state = el._vue_visibilityState;\n\n  if (state) {\n    state.destroyObserver();\n    delete el._vue_visibilityState;\n  }\n}\n\nvar ObserveVisibility = {\n  beforeMount: beforeMount,\n  updated: updated,\n  unmounted: unmounted\n};\n\nfunction install(app) {\n  app.directive('observe-visibility', ObserveVisibility);\n  /* -- Add more components here -- */\n}\n/* -- Plugin definition & Auto-install -- */\n\n/* You shouldn't have to modify the code below */\n// Plugin\n\nvar plugin = {\n  // eslint-disable-next-line no-undef\n  version: \"2.0.0-alpha.1\",\n  install: install\n};\n\nexport default plugin;\nexport { ObserveVisibility, install };\n", "export type EventType = string | symbol;\n\n// An event handler can take an optional event argument\n// and should not return a value\nexport type Handler<T = any> = (event?: T) => void;\nexport type WildcardHandler = (type: EventType, event?: any) => void;\n\n// An array of all currently registered event handlers for a type\nexport type EventHandlerList = Array<Handler>;\nexport type WildCardEventHandlerList = Array<WildcardHandler>;\n\n// A map of event types and their corresponding event handlers.\nexport type EventHandlerMap = Map<EventType, EventHandlerList | WildCardEventHandlerList>;\n\nexport interface Emitter {\n\tall: EventHandlerMap;\n\n\ton<T = any>(type: EventType, handler: Handler<T>): void;\n\ton(type: '*', handler: WildcardHandler): void;\n\n\toff<T = any>(type: EventType, handler: Handler<T>): void;\n\toff(type: '*', handler: WildcardHandler): void;\n\n\temit<T = any>(type: EventType, event?: T): void;\n\temit(type: '*', event?: any): void;\n}\n\n/**\n * Mitt: Tiny (~200b) functional event emitter / pubsub.\n * @name mitt\n * @returns {Mitt}\n */\nexport default function mitt(all?: EventHandlerMap): Emitter {\n\tall = all || new Map();\n\n\treturn {\n\n\t\t/**\n\t\t * A Map of event names to registered handler functions.\n\t\t */\n\t\tall,\n\n\t\t/**\n\t\t * Register an event handler for the given type.\n\t\t * @param {string|symbol} type Type of event to listen for, or `\"*\"` for all events\n\t\t * @param {Function} handler Function to call in response to given event\n\t\t * @memberOf mitt\n\t\t */\n\t\ton<T = any>(type: EventType, handler: Handler<T>) {\n\t\t\tconst handlers = all.get(type);\n\t\t\tconst added = handlers && handlers.push(handler);\n\t\t\tif (!added) {\n\t\t\t\tall.set(type, [handler]);\n\t\t\t}\n\t\t},\n\n\t\t/**\n\t\t * Remove an event handler for the given type.\n\t\t * @param {string|symbol} type Type of event to unregister `handler` from, or `\"*\"`\n\t\t * @param {Function} handler Handler function to remove\n\t\t * @memberOf mitt\n\t\t */\n\t\toff<T = any>(type: EventType, handler: Handler<T>) {\n\t\t\tconst handlers = all.get(type);\n\t\t\tif (handlers) {\n\t\t\t\thandlers.splice(handlers.indexOf(handler) >>> 0, 1);\n\t\t\t}\n\t\t},\n\n\t\t/**\n\t\t * Invoke all handlers for the given type.\n\t\t * If present, `\"*\"` handlers are invoked after type-matched handlers.\n\t\t *\n\t\t * Note: Manually firing \"*\" handlers is not supported.\n\t\t *\n\t\t * @param {string|symbol} type The event type to invoke\n\t\t * @param {Any} [evt] Any value (object is recommended and powerful), passed to each handler\n\t\t * @memberOf mitt\n\t\t */\n\t\temit<T = any>(type: EventType, evt: T) {\n\t\t\t((all.get(type) || []) as EventHandlerList).slice().map((handler) => { handler(evt); });\n\t\t\t((all.get('*') || []) as WildCardEventHandlerList).slice().map((handler) => { handler(type, evt); });\n\t\t}\n\t};\n}\n", "export default {\n  itemsLimit: 1000,\n}\n", "// Fork of https://github.com/olahol/scrollparent.js to be able to build with Rollup\n\nconst regex = /(auto|scroll)/\n\nfunction parents (node, ps) {\n  if (node.parentNode === null) { return ps }\n\n  return parents(node.parentNode, ps.concat([node]))\n}\n\nconst style = function (node, prop) {\n  return getComputedStyle(node, null).getPropertyValue(prop)\n}\n\nconst overflow = function (node) {\n  return style(node, 'overflow') + style(node, 'overflow-y') + style(node, 'overflow-x')\n}\n\nconst scroll = function (node) {\n  return regex.test(overflow(node))\n}\n\nexport function getScrollParent (node) {\n  if (!(node instanceof HTMLElement || node instanceof SVGElement)) {\n    return\n  }\n\n  const ps = parents(node.parentNode, [])\n\n  for (let i = 0; i < ps.length; i += 1) {\n    if (scroll(ps[i])) {\n      return ps[i]\n    }\n  }\n\n  return document.scrollingElement || document.documentElement\n}\n", "export const props = {\n  items: {\n    type: Array,\n    required: true,\n  },\n\n  keyField: {\n    type: String,\n    default: 'id',\n  },\n\n  direction: {\n    type: String,\n    default: 'vertical',\n    validator: (value) => ['vertical', 'horizontal'].includes(value),\n  },\n\n  listTag: {\n    type: String,\n    default: 'div',\n  },\n\n  itemTag: {\n    type: String,\n    default: 'div',\n  },\n}\n\nexport function simpleArray () {\n  return this.items.length && typeof this.items[0] !== 'object'\n}\n", "export let supportsPassive = false\n\nif (typeof window !== 'undefined') {\n  supportsPassive = false\n  try {\n    const opts = Object.defineProperty({}, 'passive', {\n      get () {\n        supportsPassive = true\n      },\n    })\n    window.addEventListener('test', null, opts)\n  } catch (e) {}\n}\n", "<template>\n  <div\n    v-observe-visibility=\"handleVisibilityChange\"\n    class=\"vue-recycle-scroller\"\n    :class=\"{\n      ready,\n      'page-mode': pageMode,\n      [`direction-${direction}`]: true,\n    }\"\n    @scroll.passive=\"handleScroll\"\n  >\n    <div\n      v-if=\"$slots.before\"\n      ref=\"before\"\n      class=\"vue-recycle-scroller__slot\"\n    >\n      <slot\n        name=\"before\"\n      />\n    </div>\n\n    <component\n      :is=\"listTag\"\n      ref=\"wrapper\"\n      :style=\"{ [direction === 'vertical' ? 'minHeight' : 'minWidth']: totalSize + 'px' }\"\n      class=\"vue-recycle-scroller__item-wrapper\"\n      :class=\"listClass\"\n    >\n      <component\n        :is=\"itemTag\"\n        v-for=\"view of pool\"\n        :key=\"view.nr.id\"\n        :style=\"ready ? {\n          transform: `translate${direction === 'vertical' ? 'Y' : 'X'}(${view.position}px) translate${direction === 'vertical' ? 'X' : 'Y'}(${view.offset}px)`,\n          width: gridItems ? `${direction === 'vertical' ? itemSecondarySize || itemSize : itemSize}px` : undefined,\n          height: gridItems ? `${direction === 'horizontal' ? itemSecondarySize || itemSize : itemSize}px` : undefined,\n        } : null\"\n        class=\"vue-recycle-scroller__item-view\"\n        :class=\"[\n          itemClass,\n          {\n            hover: !skipHover && hoverKey === view.nr.key\n          },\n        ]\"\n        v-on=\"skipHover ? {} : {\n          mouseenter: () => { hoverKey = view.nr.key },\n          mouseleave: () => { hoverKey = null },\n        }\"\n      >\n        <slot\n          :item=\"view.item\"\n          :index=\"view.nr.index\"\n          :active=\"view.nr.used\"\n        />\n      </component>\n\n      <slot\n        name=\"empty\"\n      />\n    </component>\n\n    <div\n      v-if=\"$slots.after\"\n      ref=\"after\"\n      class=\"vue-recycle-scroller__slot\"\n    >\n      <slot\n        name=\"after\"\n      />\n    </div>\n\n    <ResizeObserver @notify=\"handleResize\" />\n  </div>\n</template>\n\n<script>\nimport { shallowReactive, markRaw } from 'vue'\nimport { ResizeObserver } from 'vue-resize'\nimport { ObserveVisibility } from 'vue-observe-visibility'\nimport { getScrollParent } from '../scrollparent'\nimport config from '../config'\nimport { props, simpleArray } from './common'\nimport { supportsPassive } from '../utils'\n\nlet uid = 0\n\nexport default {\n  name: 'RecycleScroller',\n\n  components: {\n    ResizeObserver,\n  },\n\n  directives: {\n    ObserveVisibility,\n  },\n\n  props: {\n    ...props,\n\n    itemSize: {\n      type: Number,\n      default: null,\n    },\n\n    gridItems: {\n      type: Number,\n      default: undefined,\n    },\n\n    itemSecondarySize: {\n      type: Number,\n      default: undefined,\n    },\n\n    minItemSize: {\n      type: [Number, String],\n      default: null,\n    },\n\n    sizeField: {\n      type: String,\n      default: 'size',\n    },\n\n    typeField: {\n      type: String,\n      default: 'type',\n    },\n\n    buffer: {\n      type: Number,\n      default: 200,\n    },\n\n    pageMode: {\n      type: Boolean,\n      default: false,\n    },\n\n    prerender: {\n      type: Number,\n      default: 0,\n    },\n\n    emitUpdate: {\n      type: Boolean,\n      default: false,\n    },\n\n    updateInterval: {\n      type: Number,\n      default: 0,\n    },\n\n    skipHover: {\n      type: Boolean,\n      default: false,\n    },\n\n    listTag: {\n      type: String,\n      default: 'div',\n    },\n\n    itemTag: {\n      type: String,\n      default: 'div',\n    },\n\n    listClass: {\n      type: [String, Object, Array],\n      default: '',\n    },\n\n    itemClass: {\n      type: [String, Object, Array],\n      default: '',\n    },\n  },\n\n  emits: [\n    'resize',\n    'visible',\n    'hidden',\n    'update',\n    'scroll-start',\n    'scroll-end',\n  ],\n\n  data () {\n    return {\n      pool: [],\n      totalSize: 0,\n      ready: false,\n      hoverKey: null,\n    }\n  },\n\n  computed: {\n    sizes () {\n      if (this.itemSize === null) {\n        const sizes = {\n          '-1': { accumulator: 0 },\n        }\n        const items = this.items\n        const field = this.sizeField\n        const minItemSize = this.minItemSize\n        let computedMinSize = 10000\n        let accumulator = 0\n        let current\n        for (let i = 0, l = items.length; i < l; i++) {\n          current = items[i][field] || minItemSize\n          if (current < computedMinSize) {\n            computedMinSize = current\n          }\n          accumulator += current\n          sizes[i] = { accumulator, size: current }\n        }\n        // eslint-disable-next-line\n        this.$_computedMinItemSize = computedMinSize\n        return sizes\n      }\n      return []\n    },\n\n    simpleArray,\n\n    itemIndexByKey () {\n      const { keyField, items } = this\n      const result = {}\n      for (let i = 0, l = items.length; i < l; i++) {\n        result[items[i][keyField]] = i\n      }\n      return result\n    },\n  },\n\n  watch: {\n    items () {\n      this.updateVisibleItems(true)\n    },\n\n    pageMode () {\n      this.applyPageMode()\n      this.updateVisibleItems(false)\n    },\n\n    sizes: {\n      handler () {\n        this.updateVisibleItems(false)\n      },\n      deep: true,\n    },\n\n    gridItems () {\n      this.updateVisibleItems(true)\n    },\n\n    itemSecondarySize () {\n      this.updateVisibleItems(true)\n    },\n  },\n\n  created () {\n    this.$_startIndex = 0\n    this.$_endIndex = 0\n    this.$_views = new Map()\n    this.$_unusedViews = new Map()\n    this.$_scrollDirty = false\n    this.$_lastUpdateScrollPosition = 0\n\n    // In SSR mode, we also prerender the same number of item for the first render\n    // to avoir mismatch between server and client templates\n    if (this.prerender) {\n      this.$_prerender = true\n      this.updateVisibleItems(false)\n    }\n\n    if (this.gridItems && !this.itemSize) {\n      console.error('[vue-recycle-scroller] You must provide an itemSize when using gridItems')\n    }\n  },\n\n  mounted () {\n    this.applyPageMode()\n    this.$nextTick(() => {\n      // In SSR mode, render the real number of visible items\n      this.$_prerender = false\n      this.updateVisibleItems(true)\n      this.ready = true\n    })\n  },\n\n  activated () {\n    const lastPosition = this.$_lastUpdateScrollPosition\n    if (typeof lastPosition === 'number') {\n      this.$nextTick(() => {\n        this.scrollToPosition(lastPosition)\n      })\n    }\n  },\n\n  beforeUnmount () {\n    this.removeListeners()\n  },\n\n  methods: {\n    addView (pool, index, item, key, type) {\n      const nr = markRaw({\n        id: uid++,\n        index,\n        used: true,\n        key,\n        type,\n      })\n      const view = shallowReactive({\n        item,\n        position: 0,\n        nr,\n      })\n      pool.push(view)\n      return view\n    },\n\n    unuseView (view, fake = false) {\n      const unusedViews = this.$_unusedViews\n      const type = view.nr.type\n      let unusedPool = unusedViews.get(type)\n      if (!unusedPool) {\n        unusedPool = []\n        unusedViews.set(type, unusedPool)\n      }\n      unusedPool.push(view)\n      if (!fake) {\n        view.nr.used = false\n        view.position = -9999\n      }\n    },\n\n    handleResize () {\n      this.$emit('resize')\n      if (this.ready) this.updateVisibleItems(false)\n    },\n\n    handleScroll (event) {\n      if (!this.$_scrollDirty) {\n        this.$_scrollDirty = true\n        if (this.$_updateTimeout) return\n\n        const requestUpdate = () => requestAnimationFrame(() => {\n          this.$_scrollDirty = false\n          const { continuous } = this.updateVisibleItems(false, true)\n\n          // It seems sometimes chrome doesn't fire scroll event :/\n          // When non continous scrolling is ending, we force a refresh\n          if (!continuous) {\n            clearTimeout(this.$_refreshTimout)\n            this.$_refreshTimout = setTimeout(this.handleScroll, this.updateInterval + 100)\n          }\n        })\n\n        requestUpdate()\n\n        // Schedule the next update with throttling\n        if (this.updateInterval) {\n          this.$_updateTimeout = setTimeout(() => {\n            this.$_updateTimeout = 0\n            if (this.$_scrollDirty) requestUpdate()\n          }, this.updateInterval)\n        }\n      }\n    },\n\n    handleVisibilityChange (isVisible, entry) {\n      if (this.ready) {\n        if (isVisible || entry.boundingClientRect.width !== 0 || entry.boundingClientRect.height !== 0) {\n          this.$emit('visible')\n          requestAnimationFrame(() => {\n            this.updateVisibleItems(false)\n          })\n        } else {\n          this.$emit('hidden')\n        }\n      }\n    },\n\n    updateVisibleItems (checkItem, checkPositionDiff = false) {\n      const itemSize = this.itemSize\n      const gridItems = this.gridItems || 1\n      const itemSecondarySize = this.itemSecondarySize || itemSize\n      const minItemSize = this.$_computedMinItemSize\n      const typeField = this.typeField\n      const keyField = this.simpleArray ? null : this.keyField\n      const items = this.items\n      const count = items.length\n      const sizes = this.sizes\n      const views = this.$_views\n      const unusedViews = this.$_unusedViews\n      const pool = this.pool\n      const itemIndexByKey = this.itemIndexByKey\n      let startIndex, endIndex\n      let totalSize\n      let visibleStartIndex, visibleEndIndex\n\n      if (!count) {\n        startIndex = endIndex = visibleStartIndex = visibleEndIndex = totalSize = 0\n      } else if (this.$_prerender) {\n        startIndex = visibleStartIndex = 0\n        endIndex = visibleEndIndex = Math.min(this.prerender, items.length)\n        totalSize = null\n      } else {\n        const scroll = this.getScroll()\n\n        // Skip update if use hasn't scrolled enough\n        if (checkPositionDiff) {\n          let positionDiff = scroll.start - this.$_lastUpdateScrollPosition\n          if (positionDiff < 0) positionDiff = -positionDiff\n          if ((itemSize === null && positionDiff < minItemSize) || positionDiff < itemSize) {\n            return {\n              continuous: true,\n            }\n          }\n        }\n        this.$_lastUpdateScrollPosition = scroll.start\n\n        const buffer = this.buffer\n        scroll.start -= buffer\n        scroll.end += buffer\n\n        // account for leading slot\n        let beforeSize = 0\n        if (this.$refs.before) {\n          beforeSize = this.$refs.before.scrollHeight\n          scroll.start -= beforeSize\n        }\n\n        // account for trailing slot\n        if (this.$refs.after) {\n          const afterSize = this.$refs.after.scrollHeight\n          scroll.end += afterSize\n        }\n\n        // Variable size mode\n        if (itemSize === null) {\n          let h\n          let a = 0\n          let b = count - 1\n          let i = ~~(count / 2)\n          let oldI\n\n          // Searching for startIndex\n          do {\n            oldI = i\n            h = sizes[i].accumulator\n            if (h < scroll.start) {\n              a = i\n            } else if (i < count - 1 && sizes[i + 1].accumulator > scroll.start) {\n              b = i\n            }\n            i = ~~((a + b) / 2)\n          } while (i !== oldI)\n          i < 0 && (i = 0)\n          startIndex = i\n\n          // For container style\n          totalSize = sizes[count - 1].accumulator\n\n          // Searching for endIndex\n          for (endIndex = i; endIndex < count && sizes[endIndex].accumulator < scroll.end; endIndex++);\n          if (endIndex === -1) {\n            endIndex = items.length - 1\n          } else {\n            endIndex++\n            // Bounds\n            endIndex > count && (endIndex = count)\n          }\n\n          // search visible startIndex\n          for (visibleStartIndex = startIndex; visibleStartIndex < count && (beforeSize + sizes[visibleStartIndex].accumulator) < scroll.start; visibleStartIndex++);\n\n          // search visible endIndex\n          for (visibleEndIndex = visibleStartIndex; visibleEndIndex < count && (beforeSize + sizes[visibleEndIndex].accumulator) < scroll.end; visibleEndIndex++);\n        } else {\n          // Fixed size mode\n          startIndex = ~~(scroll.start / itemSize * gridItems)\n          const remainer = startIndex % gridItems\n          startIndex -= remainer\n          endIndex = Math.ceil(scroll.end / itemSize * gridItems)\n          visibleStartIndex = Math.max(0, Math.floor((scroll.start - beforeSize) / itemSize * gridItems))\n          visibleEndIndex = Math.floor((scroll.end - beforeSize) / itemSize * gridItems)\n\n          // Bounds\n          startIndex < 0 && (startIndex = 0)\n          endIndex > count && (endIndex = count)\n          visibleStartIndex < 0 && (visibleStartIndex = 0)\n          visibleEndIndex > count && (visibleEndIndex = count)\n\n          totalSize = Math.ceil(count / gridItems) * itemSize\n        }\n      }\n\n      if (endIndex - startIndex > config.itemsLimit) {\n        this.itemsLimitError()\n      }\n\n      this.totalSize = totalSize\n\n      let view\n\n      const continuous = startIndex <= this.$_endIndex && endIndex >= this.$_startIndex\n\n      // Unuse views that are no longer visible\n      if (continuous) {\n        for (let i = 0, l = pool.length; i < l; i++) {\n          view = pool[i]\n          if (view.nr.used) {\n            // Update view item index\n            if (checkItem) {\n              view.nr.index = itemIndexByKey[view.item[keyField]]\n            }\n\n            // Check if index is still in visible range\n            if (\n              view.nr.index == null ||\n              view.nr.index < startIndex ||\n              view.nr.index >= endIndex\n            ) {\n              this.unuseView(view)\n            }\n          }\n        }\n      }\n\n      const unusedIndex = continuous ? null : new Map()\n\n      let item, type\n      let v\n      for (let i = startIndex; i < endIndex; i++) {\n        item = items[i]\n        const key = keyField ? item[keyField] : item\n        if (key == null) {\n          throw new Error(`Key is ${key} on item (keyField is '${keyField}')`)\n        }\n        view = views.get(key)\n\n        if (!itemSize && !sizes[i].size) {\n          if (view) this.unuseView(view)\n          continue\n        }\n\n        type = item[typeField]\n\n        let unusedPool = unusedViews.get(type)\n        let newlyUsedView = false\n\n        // No view assigned to item\n        if (!view) {\n          if (continuous) {\n            // Reuse existing view\n            if (unusedPool && unusedPool.length) {\n              view = unusedPool.pop()\n            } else {\n              view = this.addView(pool, i, item, key, type)\n            }\n          } else {\n            // Use existing view\n            // We don't care if they are already used\n            // because we are not in continous scrolling\n            v = unusedIndex.get(type) || 0\n\n            if (!unusedPool || v >= unusedPool.length) {\n              view = this.addView(pool, i, item, key, type)\n              this.unuseView(view, true)\n              unusedPool = unusedViews.get(type)\n            }\n\n            view = unusedPool[v]\n            unusedIndex.set(type, v + 1)\n          }\n\n          // Assign view to item\n          views.delete(view.nr.key)\n          view.nr.used = true\n          view.nr.index = i\n          view.nr.key = key\n          view.nr.type = type\n          views.set(key, view)\n\n          newlyUsedView = true\n        } else {\n          // View already assigned to item\n          if (!view.nr.used) {\n            view.nr.used = true\n            newlyUsedView = true\n            if (unusedPool) {\n              const index = unusedPool.indexOf(view)\n              if (index !== -1) unusedPool.splice(index, 1)\n            }\n          }\n        }\n\n        // Always set item in case it's a new object with the same key\n        view.item = item\n\n        if (newlyUsedView) {\n          if (i === items.length - 1) this.$emit('scroll-end')\n          if (i === 0) this.$emit('scroll-start')\n        }\n\n        // Update position\n        if (itemSize === null) {\n          view.position = sizes[i - 1].accumulator\n          view.offset = 0\n        } else {\n          view.position = Math.floor(i / gridItems) * itemSize\n          view.offset = (i % gridItems) * itemSecondarySize\n        }\n      }\n\n      this.$_startIndex = startIndex\n      this.$_endIndex = endIndex\n\n      if (this.emitUpdate) this.$emit('update', startIndex, endIndex, visibleStartIndex, visibleEndIndex)\n\n      // After the user has finished scrolling\n      // Sort views so text selection is correct\n      clearTimeout(this.$_sortTimer)\n      this.$_sortTimer = setTimeout(this.sortViews, this.updateInterval + 300)\n\n      return {\n        continuous,\n      }\n    },\n\n    getListenerTarget () {\n      let target = getScrollParent(this.$el)\n      // Fix global scroll target for Chrome and Safari\n      if (window.document && (target === window.document.documentElement || target === window.document.body)) {\n        target = window\n      }\n      return target\n    },\n\n    getScroll () {\n      const { $el: el, direction } = this\n      const isVertical = direction === 'vertical'\n      let scrollState\n\n      if (this.pageMode) {\n        const bounds = el.getBoundingClientRect()\n        const boundsSize = isVertical ? bounds.height : bounds.width\n        let start = -(isVertical ? bounds.top : bounds.left)\n        let size = isVertical ? window.innerHeight : window.innerWidth\n        if (start < 0) {\n          size += start\n          start = 0\n        }\n        if (start + size > boundsSize) {\n          size = boundsSize - start\n        }\n        scrollState = {\n          start,\n          end: start + size,\n        }\n      } else if (isVertical) {\n        scrollState = {\n          start: el.scrollTop,\n          end: el.scrollTop + el.clientHeight,\n        }\n      } else {\n        scrollState = {\n          start: el.scrollLeft,\n          end: el.scrollLeft + el.clientWidth,\n        }\n      }\n\n      return scrollState\n    },\n\n    applyPageMode () {\n      if (this.pageMode) {\n        this.addListeners()\n      } else {\n        this.removeListeners()\n      }\n    },\n\n    addListeners () {\n      this.listenerTarget = this.getListenerTarget()\n      this.listenerTarget.addEventListener('scroll', this.handleScroll, supportsPassive\n        ? {\n            passive: true,\n          }\n        : false)\n      this.listenerTarget.addEventListener('resize', this.handleResize)\n    },\n\n    removeListeners () {\n      if (!this.listenerTarget) {\n        return\n      }\n\n      this.listenerTarget.removeEventListener('scroll', this.handleScroll)\n      this.listenerTarget.removeEventListener('resize', this.handleResize)\n\n      this.listenerTarget = null\n    },\n\n    scrollToItem (index) {\n      let scroll\n      const gridItems = this.gridItems || 1\n      if (this.itemSize === null) {\n        scroll = index > 0 ? this.sizes[index - 1].accumulator : 0\n      } else {\n        scroll = Math.floor(index / gridItems) * this.itemSize\n      }\n      this.scrollToPosition(scroll)\n    },\n\n    scrollToPosition (position) {\n      const direction = this.direction === 'vertical'\n        ? { scroll: 'scrollTop', start: 'top' }\n        : { scroll: 'scrollLeft', start: 'left' }\n\n      let viewport\n      let scrollDirection\n      let scrollDistance\n\n      if (this.pageMode) {\n        const viewportEl = getScrollParent(this.$el)\n        // HTML doesn't overflow like other elements\n        const scrollTop = viewportEl.tagName === 'HTML' ? 0 : viewportEl[direction.scroll]\n        const bounds = viewportEl.getBoundingClientRect()\n\n        const scroller = this.$el.getBoundingClientRect()\n        const scrollerPosition = scroller[direction.start] - bounds[direction.start]\n\n        viewport = viewportEl\n        scrollDirection = direction.scroll\n        scrollDistance = position + scrollTop + scrollerPosition\n      } else {\n        viewport = this.$el\n        scrollDirection = direction.scroll\n        scrollDistance = position\n      }\n\n      viewport[scrollDirection] = scrollDistance\n    },\n\n    itemsLimitError () {\n      setTimeout(() => {\n        console.log('It seems the scroller element isn\\'t scrolling, so it tries to render all the items at once.', 'Scroller:', this.$el)\n        console.log('Make sure the scroller has a fixed height (or width) and \\'overflow-y\\' (or \\'overflow-x\\') set to \\'auto\\' so it can scroll correctly and only render the items visible in the scroll viewport.')\n      })\n      throw new Error('Rendered items limit reached')\n    },\n\n    sortViews () {\n      this.pool.sort((viewA, viewB) => viewA.nr.index - viewB.nr.index)\n    },\n  },\n}\n</script>\n\n<style>\n.vue-recycle-scroller {\n  position: relative;\n}\n\n.vue-recycle-scroller.direction-vertical:not(.page-mode) {\n  overflow-y: auto;\n}\n\n.vue-recycle-scroller.direction-horizontal:not(.page-mode) {\n  overflow-x: auto;\n}\n\n.vue-recycle-scroller.direction-horizontal {\n  display: flex;\n}\n\n.vue-recycle-scroller__slot {\n  flex: auto 0 0;\n}\n\n.vue-recycle-scroller__item-wrapper {\n  flex: 1;\n  box-sizing: border-box;\n  overflow: hidden;\n  position: relative;\n}\n\n.vue-recycle-scroller.ready .vue-recycle-scroller__item-view {\n  position: absolute;\n  top: 0;\n  left: 0;\n  will-change: transform;\n}\n\n.vue-recycle-scroller.direction-vertical .vue-recycle-scroller__item-wrapper {\n  width: 100%;\n}\n\n.vue-recycle-scroller.direction-horizontal .vue-recycle-scroller__item-wrapper {\n  height: 100%;\n}\n\n.vue-recycle-scroller.ready.direction-vertical .vue-recycle-scroller__item-view {\n  width: 100%;\n}\n\n.vue-recycle-scroller.ready.direction-horizontal .vue-recycle-scroller__item-view {\n  height: 100%;\n}\n</style>\n", "<template>\n  <div\n    v-observe-visibility=\"handleVisibilityChange\"\n    class=\"vue-recycle-scroller\"\n    :class=\"{\n      ready,\n      'page-mode': pageMode,\n      [`direction-${direction}`]: true,\n    }\"\n    @scroll.passive=\"handleScroll\"\n  >\n    <div\n      v-if=\"$slots.before\"\n      ref=\"before\"\n      class=\"vue-recycle-scroller__slot\"\n    >\n      <slot\n        name=\"before\"\n      />\n    </div>\n\n    <component\n      :is=\"listTag\"\n      ref=\"wrapper\"\n      :style=\"{ [direction === 'vertical' ? 'minHeight' : 'minWidth']: totalSize + 'px' }\"\n      class=\"vue-recycle-scroller__item-wrapper\"\n      :class=\"listClass\"\n    >\n      <component\n        :is=\"itemTag\"\n        v-for=\"view of pool\"\n        :key=\"view.nr.id\"\n        :style=\"ready ? {\n          transform: `translate${direction === 'vertical' ? 'Y' : 'X'}(${view.position}px) translate${direction === 'vertical' ? 'X' : 'Y'}(${view.offset}px)`,\n          width: gridItems ? `${direction === 'vertical' ? itemSecondarySize || itemSize : itemSize}px` : undefined,\n          height: gridItems ? `${direction === 'horizontal' ? itemSecondarySize || itemSize : itemSize}px` : undefined,\n        } : null\"\n        class=\"vue-recycle-scroller__item-view\"\n        :class=\"[\n          itemClass,\n          {\n            hover: !skipHover && hoverKey === view.nr.key\n          },\n        ]\"\n        v-on=\"skipHover ? {} : {\n          mouseenter: () => { hoverKey = view.nr.key },\n          mouseleave: () => { hoverKey = null },\n        }\"\n      >\n        <slot\n          :item=\"view.item\"\n          :index=\"view.nr.index\"\n          :active=\"view.nr.used\"\n        />\n      </component>\n\n      <slot\n        name=\"empty\"\n      />\n    </component>\n\n    <div\n      v-if=\"$slots.after\"\n      ref=\"after\"\n      class=\"vue-recycle-scroller__slot\"\n    >\n      <slot\n        name=\"after\"\n      />\n    </div>\n\n    <ResizeObserver @notify=\"handleResize\" />\n  </div>\n</template>\n\n<script>\nimport { shallowReactive, markRaw } from 'vue'\nimport { ResizeObserver } from 'vue-resize'\nimport { ObserveVisibility } from 'vue-observe-visibility'\nimport { getScrollParent } from '../scrollparent'\nimport config from '../config'\nimport { props, simpleArray } from './common'\nimport { supportsPassive } from '../utils'\n\nlet uid = 0\n\nexport default {\n  name: 'RecycleScroller',\n\n  components: {\n    ResizeObserver,\n  },\n\n  directives: {\n    ObserveVisibility,\n  },\n\n  props: {\n    ...props,\n\n    itemSize: {\n      type: Number,\n      default: null,\n    },\n\n    gridItems: {\n      type: Number,\n      default: undefined,\n    },\n\n    itemSecondarySize: {\n      type: Number,\n      default: undefined,\n    },\n\n    minItemSize: {\n      type: [Number, String],\n      default: null,\n    },\n\n    sizeField: {\n      type: String,\n      default: 'size',\n    },\n\n    typeField: {\n      type: String,\n      default: 'type',\n    },\n\n    buffer: {\n      type: Number,\n      default: 200,\n    },\n\n    pageMode: {\n      type: Boolean,\n      default: false,\n    },\n\n    prerender: {\n      type: Number,\n      default: 0,\n    },\n\n    emitUpdate: {\n      type: Boolean,\n      default: false,\n    },\n\n    updateInterval: {\n      type: Number,\n      default: 0,\n    },\n\n    skipHover: {\n      type: Boolean,\n      default: false,\n    },\n\n    listTag: {\n      type: String,\n      default: 'div',\n    },\n\n    itemTag: {\n      type: String,\n      default: 'div',\n    },\n\n    listClass: {\n      type: [String, Object, Array],\n      default: '',\n    },\n\n    itemClass: {\n      type: [String, Object, Array],\n      default: '',\n    },\n  },\n\n  emits: [\n    'resize',\n    'visible',\n    'hidden',\n    'update',\n    'scroll-start',\n    'scroll-end',\n  ],\n\n  data () {\n    return {\n      pool: [],\n      totalSize: 0,\n      ready: false,\n      hoverKey: null,\n    }\n  },\n\n  computed: {\n    sizes () {\n      if (this.itemSize === null) {\n        const sizes = {\n          '-1': { accumulator: 0 },\n        }\n        const items = this.items\n        const field = this.sizeField\n        const minItemSize = this.minItemSize\n        let computedMinSize = 10000\n        let accumulator = 0\n        let current\n        for (let i = 0, l = items.length; i < l; i++) {\n          current = items[i][field] || minItemSize\n          if (current < computedMinSize) {\n            computedMinSize = current\n          }\n          accumulator += current\n          sizes[i] = { accumulator, size: current }\n        }\n        // eslint-disable-next-line\n        this.$_computedMinItemSize = computedMinSize\n        return sizes\n      }\n      return []\n    },\n\n    simpleArray,\n\n    itemIndexByKey () {\n      const { keyField, items } = this\n      const result = {}\n      for (let i = 0, l = items.length; i < l; i++) {\n        result[items[i][keyField]] = i\n      }\n      return result\n    },\n  },\n\n  watch: {\n    items () {\n      this.updateVisibleItems(true)\n    },\n\n    pageMode () {\n      this.applyPageMode()\n      this.updateVisibleItems(false)\n    },\n\n    sizes: {\n      handler () {\n        this.updateVisibleItems(false)\n      },\n      deep: true,\n    },\n\n    gridItems () {\n      this.updateVisibleItems(true)\n    },\n\n    itemSecondarySize () {\n      this.updateVisibleItems(true)\n    },\n  },\n\n  created () {\n    this.$_startIndex = 0\n    this.$_endIndex = 0\n    this.$_views = new Map()\n    this.$_unusedViews = new Map()\n    this.$_scrollDirty = false\n    this.$_lastUpdateScrollPosition = 0\n\n    // In SSR mode, we also prerender the same number of item for the first render\n    // to avoir mismatch between server and client templates\n    if (this.prerender) {\n      this.$_prerender = true\n      this.updateVisibleItems(false)\n    }\n\n    if (this.gridItems && !this.itemSize) {\n      console.error('[vue-recycle-scroller] You must provide an itemSize when using gridItems')\n    }\n  },\n\n  mounted () {\n    this.applyPageMode()\n    this.$nextTick(() => {\n      // In SSR mode, render the real number of visible items\n      this.$_prerender = false\n      this.updateVisibleItems(true)\n      this.ready = true\n    })\n  },\n\n  activated () {\n    const lastPosition = this.$_lastUpdateScrollPosition\n    if (typeof lastPosition === 'number') {\n      this.$nextTick(() => {\n        this.scrollToPosition(lastPosition)\n      })\n    }\n  },\n\n  beforeUnmount () {\n    this.removeListeners()\n  },\n\n  methods: {\n    addView (pool, index, item, key, type) {\n      const nr = markRaw({\n        id: uid++,\n        index,\n        used: true,\n        key,\n        type,\n      })\n      const view = shallowReactive({\n        item,\n        position: 0,\n        nr,\n      })\n      pool.push(view)\n      return view\n    },\n\n    unuseView (view, fake = false) {\n      const unusedViews = this.$_unusedViews\n      const type = view.nr.type\n      let unusedPool = unusedViews.get(type)\n      if (!unusedPool) {\n        unusedPool = []\n        unusedViews.set(type, unusedPool)\n      }\n      unusedPool.push(view)\n      if (!fake) {\n        view.nr.used = false\n        view.position = -9999\n      }\n    },\n\n    handleResize () {\n      this.$emit('resize')\n      if (this.ready) this.updateVisibleItems(false)\n    },\n\n    handleScroll (event) {\n      if (!this.$_scrollDirty) {\n        this.$_scrollDirty = true\n        if (this.$_updateTimeout) return\n\n        const requestUpdate = () => requestAnimationFrame(() => {\n          this.$_scrollDirty = false\n          const { continuous } = this.updateVisibleItems(false, true)\n\n          // It seems sometimes chrome doesn't fire scroll event :/\n          // When non continous scrolling is ending, we force a refresh\n          if (!continuous) {\n            clearTimeout(this.$_refreshTimout)\n            this.$_refreshTimout = setTimeout(this.handleScroll, this.updateInterval + 100)\n          }\n        })\n\n        requestUpdate()\n\n        // Schedule the next update with throttling\n        if (this.updateInterval) {\n          this.$_updateTimeout = setTimeout(() => {\n            this.$_updateTimeout = 0\n            if (this.$_scrollDirty) requestUpdate()\n          }, this.updateInterval)\n        }\n      }\n    },\n\n    handleVisibilityChange (isVisible, entry) {\n      if (this.ready) {\n        if (isVisible || entry.boundingClientRect.width !== 0 || entry.boundingClientRect.height !== 0) {\n          this.$emit('visible')\n          requestAnimationFrame(() => {\n            this.updateVisibleItems(false)\n          })\n        } else {\n          this.$emit('hidden')\n        }\n      }\n    },\n\n    updateVisibleItems (checkItem, checkPositionDiff = false) {\n      const itemSize = this.itemSize\n      const gridItems = this.gridItems || 1\n      const itemSecondarySize = this.itemSecondarySize || itemSize\n      const minItemSize = this.$_computedMinItemSize\n      const typeField = this.typeField\n      const keyField = this.simpleArray ? null : this.keyField\n      const items = this.items\n      const count = items.length\n      const sizes = this.sizes\n      const views = this.$_views\n      const unusedViews = this.$_unusedViews\n      const pool = this.pool\n      const itemIndexByKey = this.itemIndexByKey\n      let startIndex, endIndex\n      let totalSize\n      let visibleStartIndex, visibleEndIndex\n\n      if (!count) {\n        startIndex = endIndex = visibleStartIndex = visibleEndIndex = totalSize = 0\n      } else if (this.$_prerender) {\n        startIndex = visibleStartIndex = 0\n        endIndex = visibleEndIndex = Math.min(this.prerender, items.length)\n        totalSize = null\n      } else {\n        const scroll = this.getScroll()\n\n        // Skip update if use hasn't scrolled enough\n        if (checkPositionDiff) {\n          let positionDiff = scroll.start - this.$_lastUpdateScrollPosition\n          if (positionDiff < 0) positionDiff = -positionDiff\n          if ((itemSize === null && positionDiff < minItemSize) || positionDiff < itemSize) {\n            return {\n              continuous: true,\n            }\n          }\n        }\n        this.$_lastUpdateScrollPosition = scroll.start\n\n        const buffer = this.buffer\n        scroll.start -= buffer\n        scroll.end += buffer\n\n        // account for leading slot\n        let beforeSize = 0\n        if (this.$refs.before) {\n          beforeSize = this.$refs.before.scrollHeight\n          scroll.start -= beforeSize\n        }\n\n        // account for trailing slot\n        if (this.$refs.after) {\n          const afterSize = this.$refs.after.scrollHeight\n          scroll.end += afterSize\n        }\n\n        // Variable size mode\n        if (itemSize === null) {\n          let h\n          let a = 0\n          let b = count - 1\n          let i = ~~(count / 2)\n          let oldI\n\n          // Searching for startIndex\n          do {\n            oldI = i\n            h = sizes[i].accumulator\n            if (h < scroll.start) {\n              a = i\n            } else if (i < count - 1 && sizes[i + 1].accumulator > scroll.start) {\n              b = i\n            }\n            i = ~~((a + b) / 2)\n          } while (i !== oldI)\n          i < 0 && (i = 0)\n          startIndex = i\n\n          // For container style\n          totalSize = sizes[count - 1].accumulator\n\n          // Searching for endIndex\n          for (endIndex = i; endIndex < count && sizes[endIndex].accumulator < scroll.end; endIndex++);\n          if (endIndex === -1) {\n            endIndex = items.length - 1\n          } else {\n            endIndex++\n            // Bounds\n            endIndex > count && (endIndex = count)\n          }\n\n          // search visible startIndex\n          for (visibleStartIndex = startIndex; visibleStartIndex < count && (beforeSize + sizes[visibleStartIndex].accumulator) < scroll.start; visibleStartIndex++);\n\n          // search visible endIndex\n          for (visibleEndIndex = visibleStartIndex; visibleEndIndex < count && (beforeSize + sizes[visibleEndIndex].accumulator) < scroll.end; visibleEndIndex++);\n        } else {\n          // Fixed size mode\n          startIndex = ~~(scroll.start / itemSize * gridItems)\n          const remainer = startIndex % gridItems\n          startIndex -= remainer\n          endIndex = Math.ceil(scroll.end / itemSize * gridItems)\n          visibleStartIndex = Math.max(0, Math.floor((scroll.start - beforeSize) / itemSize * gridItems))\n          visibleEndIndex = Math.floor((scroll.end - beforeSize) / itemSize * gridItems)\n\n          // Bounds\n          startIndex < 0 && (startIndex = 0)\n          endIndex > count && (endIndex = count)\n          visibleStartIndex < 0 && (visibleStartIndex = 0)\n          visibleEndIndex > count && (visibleEndIndex = count)\n\n          totalSize = Math.ceil(count / gridItems) * itemSize\n        }\n      }\n\n      if (endIndex - startIndex > config.itemsLimit) {\n        this.itemsLimitError()\n      }\n\n      this.totalSize = totalSize\n\n      let view\n\n      const continuous = startIndex <= this.$_endIndex && endIndex >= this.$_startIndex\n\n      // Unuse views that are no longer visible\n      if (continuous) {\n        for (let i = 0, l = pool.length; i < l; i++) {\n          view = pool[i]\n          if (view.nr.used) {\n            // Update view item index\n            if (checkItem) {\n              view.nr.index = itemIndexByKey[view.item[keyField]]\n            }\n\n            // Check if index is still in visible range\n            if (\n              view.nr.index == null ||\n              view.nr.index < startIndex ||\n              view.nr.index >= endIndex\n            ) {\n              this.unuseView(view)\n            }\n          }\n        }\n      }\n\n      const unusedIndex = continuous ? null : new Map()\n\n      let item, type\n      let v\n      for (let i = startIndex; i < endIndex; i++) {\n        item = items[i]\n        const key = keyField ? item[keyField] : item\n        if (key == null) {\n          throw new Error(`Key is ${key} on item (keyField is '${keyField}')`)\n        }\n        view = views.get(key)\n\n        if (!itemSize && !sizes[i].size) {\n          if (view) this.unuseView(view)\n          continue\n        }\n\n        type = item[typeField]\n\n        let unusedPool = unusedViews.get(type)\n        let newlyUsedView = false\n\n        // No view assigned to item\n        if (!view) {\n          if (continuous) {\n            // Reuse existing view\n            if (unusedPool && unusedPool.length) {\n              view = unusedPool.pop()\n            } else {\n              view = this.addView(pool, i, item, key, type)\n            }\n          } else {\n            // Use existing view\n            // We don't care if they are already used\n            // because we are not in continous scrolling\n            v = unusedIndex.get(type) || 0\n\n            if (!unusedPool || v >= unusedPool.length) {\n              view = this.addView(pool, i, item, key, type)\n              this.unuseView(view, true)\n              unusedPool = unusedViews.get(type)\n            }\n\n            view = unusedPool[v]\n            unusedIndex.set(type, v + 1)\n          }\n\n          // Assign view to item\n          views.delete(view.nr.key)\n          view.nr.used = true\n          view.nr.index = i\n          view.nr.key = key\n          view.nr.type = type\n          views.set(key, view)\n\n          newlyUsedView = true\n        } else {\n          // View already assigned to item\n          if (!view.nr.used) {\n            view.nr.used = true\n            newlyUsedView = true\n            if (unusedPool) {\n              const index = unusedPool.indexOf(view)\n              if (index !== -1) unusedPool.splice(index, 1)\n            }\n          }\n        }\n\n        // Always set item in case it's a new object with the same key\n        view.item = item\n\n        if (newlyUsedView) {\n          if (i === items.length - 1) this.$emit('scroll-end')\n          if (i === 0) this.$emit('scroll-start')\n        }\n\n        // Update position\n        if (itemSize === null) {\n          view.position = sizes[i - 1].accumulator\n          view.offset = 0\n        } else {\n          view.position = Math.floor(i / gridItems) * itemSize\n          view.offset = (i % gridItems) * itemSecondarySize\n        }\n      }\n\n      this.$_startIndex = startIndex\n      this.$_endIndex = endIndex\n\n      if (this.emitUpdate) this.$emit('update', startIndex, endIndex, visibleStartIndex, visibleEndIndex)\n\n      // After the user has finished scrolling\n      // Sort views so text selection is correct\n      clearTimeout(this.$_sortTimer)\n      this.$_sortTimer = setTimeout(this.sortViews, this.updateInterval + 300)\n\n      return {\n        continuous,\n      }\n    },\n\n    getListenerTarget () {\n      let target = getScrollParent(this.$el)\n      // Fix global scroll target for Chrome and Safari\n      if (window.document && (target === window.document.documentElement || target === window.document.body)) {\n        target = window\n      }\n      return target\n    },\n\n    getScroll () {\n      const { $el: el, direction } = this\n      const isVertical = direction === 'vertical'\n      let scrollState\n\n      if (this.pageMode) {\n        const bounds = el.getBoundingClientRect()\n        const boundsSize = isVertical ? bounds.height : bounds.width\n        let start = -(isVertical ? bounds.top : bounds.left)\n        let size = isVertical ? window.innerHeight : window.innerWidth\n        if (start < 0) {\n          size += start\n          start = 0\n        }\n        if (start + size > boundsSize) {\n          size = boundsSize - start\n        }\n        scrollState = {\n          start,\n          end: start + size,\n        }\n      } else if (isVertical) {\n        scrollState = {\n          start: el.scrollTop,\n          end: el.scrollTop + el.clientHeight,\n        }\n      } else {\n        scrollState = {\n          start: el.scrollLeft,\n          end: el.scrollLeft + el.clientWidth,\n        }\n      }\n\n      return scrollState\n    },\n\n    applyPageMode () {\n      if (this.pageMode) {\n        this.addListeners()\n      } else {\n        this.removeListeners()\n      }\n    },\n\n    addListeners () {\n      this.listenerTarget = this.getListenerTarget()\n      this.listenerTarget.addEventListener('scroll', this.handleScroll, supportsPassive\n        ? {\n            passive: true,\n          }\n        : false)\n      this.listenerTarget.addEventListener('resize', this.handleResize)\n    },\n\n    removeListeners () {\n      if (!this.listenerTarget) {\n        return\n      }\n\n      this.listenerTarget.removeEventListener('scroll', this.handleScroll)\n      this.listenerTarget.removeEventListener('resize', this.handleResize)\n\n      this.listenerTarget = null\n    },\n\n    scrollToItem (index) {\n      let scroll\n      const gridItems = this.gridItems || 1\n      if (this.itemSize === null) {\n        scroll = index > 0 ? this.sizes[index - 1].accumulator : 0\n      } else {\n        scroll = Math.floor(index / gridItems) * this.itemSize\n      }\n      this.scrollToPosition(scroll)\n    },\n\n    scrollToPosition (position) {\n      const direction = this.direction === 'vertical'\n        ? { scroll: 'scrollTop', start: 'top' }\n        : { scroll: 'scrollLeft', start: 'left' }\n\n      let viewport\n      let scrollDirection\n      let scrollDistance\n\n      if (this.pageMode) {\n        const viewportEl = getScrollParent(this.$el)\n        // HTML doesn't overflow like other elements\n        const scrollTop = viewportEl.tagName === 'HTML' ? 0 : viewportEl[direction.scroll]\n        const bounds = viewportEl.getBoundingClientRect()\n\n        const scroller = this.$el.getBoundingClientRect()\n        const scrollerPosition = scroller[direction.start] - bounds[direction.start]\n\n        viewport = viewportEl\n        scrollDirection = direction.scroll\n        scrollDistance = position + scrollTop + scrollerPosition\n      } else {\n        viewport = this.$el\n        scrollDirection = direction.scroll\n        scrollDistance = position\n      }\n\n      viewport[scrollDirection] = scrollDistance\n    },\n\n    itemsLimitError () {\n      setTimeout(() => {\n        console.log('It seems the scroller element isn\\'t scrolling, so it tries to render all the items at once.', 'Scroller:', this.$el)\n        console.log('Make sure the scroller has a fixed height (or width) and \\'overflow-y\\' (or \\'overflow-x\\') set to \\'auto\\' so it can scroll correctly and only render the items visible in the scroll viewport.')\n      })\n      throw new Error('Rendered items limit reached')\n    },\n\n    sortViews () {\n      this.pool.sort((viewA, viewB) => viewA.nr.index - viewB.nr.index)\n    },\n  },\n}\n</script>\n\n<style>\n.vue-recycle-scroller {\n  position: relative;\n}\n\n.vue-recycle-scroller.direction-vertical:not(.page-mode) {\n  overflow-y: auto;\n}\n\n.vue-recycle-scroller.direction-horizontal:not(.page-mode) {\n  overflow-x: auto;\n}\n\n.vue-recycle-scroller.direction-horizontal {\n  display: flex;\n}\n\n.vue-recycle-scroller__slot {\n  flex: auto 0 0;\n}\n\n.vue-recycle-scroller__item-wrapper {\n  flex: 1;\n  box-sizing: border-box;\n  overflow: hidden;\n  position: relative;\n}\n\n.vue-recycle-scroller.ready .vue-recycle-scroller__item-view {\n  position: absolute;\n  top: 0;\n  left: 0;\n  will-change: transform;\n}\n\n.vue-recycle-scroller.direction-vertical .vue-recycle-scroller__item-wrapper {\n  width: 100%;\n}\n\n.vue-recycle-scroller.direction-horizontal .vue-recycle-scroller__item-wrapper {\n  height: 100%;\n}\n\n.vue-recycle-scroller.ready.direction-vertical .vue-recycle-scroller__item-view {\n  width: 100%;\n}\n\n.vue-recycle-scroller.ready.direction-horizontal .vue-recycle-scroller__item-view {\n  height: 100%;\n}\n</style>\n", "<template>\n  <RecycleScroller\n    ref=\"scroller\"\n    :items=\"itemsWithSize\"\n    :min-item-size=\"minItemSize\"\n    :direction=\"direction\"\n    key-field=\"id\"\n    :list-tag=\"listTag\"\n    :item-tag=\"itemTag\"\n    v-bind=\"$attrs\"\n    @resize=\"onScrollerResize\"\n    @visible=\"onScrollerVisible\"\n  >\n    <template #default=\"{ item: itemWithSize, index, active }\">\n      <slot\n        v-bind=\"{\n          item: itemWithSize.item,\n          index,\n          active,\n          itemWithSize\n        }\"\n      />\n    </template>\n    <template #before>\n      <slot name=\"before\" />\n    </template>\n    <template #after>\n      <slot name=\"after\" />\n    </template>\n    <template #empty>\n      <slot name=\"empty\" />\n    </template>\n  </RecycleScroller>\n</template>\n\n<script>\nimport mitt from 'mitt'\nimport RecycleScroller from './RecycleScroller.vue'\nimport { props, simpleArray } from './common'\n\nexport default {\n  name: 'DynamicScroller',\n\n  components: {\n    RecycleScroller,\n  },\n\n  provide () {\n    if (typeof ResizeObserver !== 'undefined') {\n      this.$_resizeObserver = new ResizeObserver(entries => {\n        requestAnimationFrame(() => {\n          if (!Array.isArray(entries)) {\n            return\n          }\n          for (const entry of entries) {\n            if (entry.target && entry.target.$_vs_onResize) {\n              let width, height\n              if (entry.borderBoxSize) {\n                const resizeObserverSize = entry.borderBoxSize[0]\n                width = resizeObserverSize.inlineSize\n                height = resizeObserverSize.blockSize\n              } else {\n                // @TODO remove when contentRect is deprecated\n                width = entry.contentRect.width\n                height = entry.contentRect.height\n              }\n              entry.target.$_vs_onResize(entry.target.$_vs_id, width, height)\n            }\n          }\n        })\n      })\n    }\n\n    return {\n      vscrollData: this.vscrollData,\n      vscrollParent: this,\n      vscrollResizeObserver: this.$_resizeObserver,\n    }\n  },\n\n  inheritAttrs: false,\n\n  props: {\n    ...props,\n\n    minItemSize: {\n      type: [Number, String],\n      required: true,\n    },\n  },\n\n  emits: [\n    'resize',\n    'visible',\n  ],\n\n  data () {\n    return {\n      vscrollData: {\n        active: true,\n        sizes: {},\n        keyField: this.keyField,\n        simpleArray: false,\n      },\n    }\n  },\n\n  computed: {\n    simpleArray,\n\n    itemsWithSize () {\n      const result = []\n      const { items, keyField, simpleArray } = this\n      const sizes = this.vscrollData.sizes\n      const l = items.length\n      for (let i = 0; i < l; i++) {\n        const item = items[i]\n        const id = simpleArray ? i : item[keyField]\n        let size = sizes[id]\n        if (typeof size === 'undefined' && !this.$_undefinedMap[id]) {\n          size = 0\n        }\n        result.push({\n          item,\n          id,\n          size,\n        })\n      }\n      return result\n    },\n  },\n\n  watch: {\n    items () {\n      this.forceUpdate()\n    },\n\n    simpleArray: {\n      handler (value) {\n        this.vscrollData.simpleArray = value\n      },\n      immediate: true,\n    },\n\n    direction (value) {\n      this.forceUpdate(true)\n    },\n\n    itemsWithSize (next, prev) {\n      const scrollTop = this.$el.scrollTop\n\n      // Calculate total diff between prev and next sizes\n      // over current scroll top. Then add it to scrollTop to\n      // avoid jumping the contents that the user is seeing.\n      let prevActiveTop = 0; let activeTop = 0\n      const length = Math.min(next.length, prev.length)\n      for (let i = 0; i < length; i++) {\n        if (prevActiveTop >= scrollTop) {\n          break\n        }\n        prevActiveTop += prev[i].size || this.minItemSize\n        activeTop += next[i].size || this.minItemSize\n      }\n      const offset = activeTop - prevActiveTop\n\n      if (offset === 0) {\n        return\n      }\n\n      this.$el.scrollTop += offset\n    },\n  },\n\n  beforeCreate () {\n    this.$_updates = []\n    this.$_undefinedSizes = 0\n    this.$_undefinedMap = {}\n    this.$_events = mitt()\n  },\n\n  activated () {\n    this.vscrollData.active = true\n  },\n\n  deactivated () {\n    this.vscrollData.active = false\n  },\n\n  unmounted () {\n    this.$_events.all.clear()\n  },\n\n  methods: {\n    onScrollerResize () {\n      const scroller = this.$refs.scroller\n      if (scroller) {\n        this.forceUpdate()\n      }\n      this.$emit('resize')\n    },\n\n    onScrollerVisible () {\n      this.$_events.emit('vscroll:update', { force: false })\n      this.$emit('visible')\n    },\n\n    forceUpdate (clear = false) {\n      if (clear || this.simpleArray) {\n        this.vscrollData.sizes = {}\n      }\n      this.$_events.emit('vscroll:update', { force: true })\n    },\n\n    scrollToItem (index) {\n      const scroller = this.$refs.scroller\n      if (scroller) scroller.scrollToItem(index)\n    },\n\n    getItemSize (item, index = undefined) {\n      const id = this.simpleArray ? (index != null ? index : this.items.indexOf(item)) : item[this.keyField]\n      return this.vscrollData.sizes[id] || 0\n    },\n\n    scrollToBottom () {\n      if (this.$_scrollingToBottom) return\n      this.$_scrollingToBottom = true\n      const el = this.$el\n      // Item is inserted to the DOM\n      this.$nextTick(() => {\n        el.scrollTop = el.scrollHeight + 5000\n        // Item sizes are computed\n        const cb = () => {\n          el.scrollTop = el.scrollHeight + 5000\n          requestAnimationFrame(() => {\n            el.scrollTop = el.scrollHeight + 5000\n            if (this.$_undefinedSizes === 0) {\n              this.$_scrollingToBottom = false\n            } else {\n              requestAnimationFrame(cb)\n            }\n          })\n        }\n        requestAnimationFrame(cb)\n      })\n    },\n  },\n}\n</script>\n", "<template>\n  <RecycleScroller\n    ref=\"scroller\"\n    :items=\"itemsWithSize\"\n    :min-item-size=\"minItemSize\"\n    :direction=\"direction\"\n    key-field=\"id\"\n    :list-tag=\"listTag\"\n    :item-tag=\"itemTag\"\n    v-bind=\"$attrs\"\n    @resize=\"onScrollerResize\"\n    @visible=\"onScrollerVisible\"\n  >\n    <template #default=\"{ item: itemWithSize, index, active }\">\n      <slot\n        v-bind=\"{\n          item: itemWithSize.item,\n          index,\n          active,\n          itemWithSize\n        }\"\n      />\n    </template>\n    <template #before>\n      <slot name=\"before\" />\n    </template>\n    <template #after>\n      <slot name=\"after\" />\n    </template>\n    <template #empty>\n      <slot name=\"empty\" />\n    </template>\n  </RecycleScroller>\n</template>\n\n<script>\nimport mitt from 'mitt'\nimport RecycleScroller from './RecycleScroller.vue'\nimport { props, simpleArray } from './common'\n\nexport default {\n  name: 'DynamicScroller',\n\n  components: {\n    RecycleScroller,\n  },\n\n  provide () {\n    if (typeof ResizeObserver !== 'undefined') {\n      this.$_resizeObserver = new ResizeObserver(entries => {\n        requestAnimationFrame(() => {\n          if (!Array.isArray(entries)) {\n            return\n          }\n          for (const entry of entries) {\n            if (entry.target && entry.target.$_vs_onResize) {\n              let width, height\n              if (entry.borderBoxSize) {\n                const resizeObserverSize = entry.borderBoxSize[0]\n                width = resizeObserverSize.inlineSize\n                height = resizeObserverSize.blockSize\n              } else {\n                // @TODO remove when contentRect is deprecated\n                width = entry.contentRect.width\n                height = entry.contentRect.height\n              }\n              entry.target.$_vs_onResize(entry.target.$_vs_id, width, height)\n            }\n          }\n        })\n      })\n    }\n\n    return {\n      vscrollData: this.vscrollData,\n      vscrollParent: this,\n      vscrollResizeObserver: this.$_resizeObserver,\n    }\n  },\n\n  inheritAttrs: false,\n\n  props: {\n    ...props,\n\n    minItemSize: {\n      type: [Number, String],\n      required: true,\n    },\n  },\n\n  emits: [\n    'resize',\n    'visible',\n  ],\n\n  data () {\n    return {\n      vscrollData: {\n        active: true,\n        sizes: {},\n        keyField: this.keyField,\n        simpleArray: false,\n      },\n    }\n  },\n\n  computed: {\n    simpleArray,\n\n    itemsWithSize () {\n      const result = []\n      const { items, keyField, simpleArray } = this\n      const sizes = this.vscrollData.sizes\n      const l = items.length\n      for (let i = 0; i < l; i++) {\n        const item = items[i]\n        const id = simpleArray ? i : item[keyField]\n        let size = sizes[id]\n        if (typeof size === 'undefined' && !this.$_undefinedMap[id]) {\n          size = 0\n        }\n        result.push({\n          item,\n          id,\n          size,\n        })\n      }\n      return result\n    },\n  },\n\n  watch: {\n    items () {\n      this.forceUpdate()\n    },\n\n    simpleArray: {\n      handler (value) {\n        this.vscrollData.simpleArray = value\n      },\n      immediate: true,\n    },\n\n    direction (value) {\n      this.forceUpdate(true)\n    },\n\n    itemsWithSize (next, prev) {\n      const scrollTop = this.$el.scrollTop\n\n      // Calculate total diff between prev and next sizes\n      // over current scroll top. Then add it to scrollTop to\n      // avoid jumping the contents that the user is seeing.\n      let prevActiveTop = 0; let activeTop = 0\n      const length = Math.min(next.length, prev.length)\n      for (let i = 0; i < length; i++) {\n        if (prevActiveTop >= scrollTop) {\n          break\n        }\n        prevActiveTop += prev[i].size || this.minItemSize\n        activeTop += next[i].size || this.minItemSize\n      }\n      const offset = activeTop - prevActiveTop\n\n      if (offset === 0) {\n        return\n      }\n\n      this.$el.scrollTop += offset\n    },\n  },\n\n  beforeCreate () {\n    this.$_updates = []\n    this.$_undefinedSizes = 0\n    this.$_undefinedMap = {}\n    this.$_events = mitt()\n  },\n\n  activated () {\n    this.vscrollData.active = true\n  },\n\n  deactivated () {\n    this.vscrollData.active = false\n  },\n\n  unmounted () {\n    this.$_events.all.clear()\n  },\n\n  methods: {\n    onScrollerResize () {\n      const scroller = this.$refs.scroller\n      if (scroller) {\n        this.forceUpdate()\n      }\n      this.$emit('resize')\n    },\n\n    onScrollerVisible () {\n      this.$_events.emit('vscroll:update', { force: false })\n      this.$emit('visible')\n    },\n\n    forceUpdate (clear = false) {\n      if (clear || this.simpleArray) {\n        this.vscrollData.sizes = {}\n      }\n      this.$_events.emit('vscroll:update', { force: true })\n    },\n\n    scrollToItem (index) {\n      const scroller = this.$refs.scroller\n      if (scroller) scroller.scrollToItem(index)\n    },\n\n    getItemSize (item, index = undefined) {\n      const id = this.simpleArray ? (index != null ? index : this.items.indexOf(item)) : item[this.keyField]\n      return this.vscrollData.sizes[id] || 0\n    },\n\n    scrollToBottom () {\n      if (this.$_scrollingToBottom) return\n      this.$_scrollingToBottom = true\n      const el = this.$el\n      // Item is inserted to the DOM\n      this.$nextTick(() => {\n        el.scrollTop = el.scrollHeight + 5000\n        // Item sizes are computed\n        const cb = () => {\n          el.scrollTop = el.scrollHeight + 5000\n          requestAnimationFrame(() => {\n            el.scrollTop = el.scrollHeight + 5000\n            if (this.$_undefinedSizes === 0) {\n              this.$_scrollingToBottom = false\n            } else {\n              requestAnimationFrame(cb)\n            }\n          })\n        }\n        requestAnimationFrame(cb)\n      })\n    },\n  },\n}\n</script>\n", "<script>\nimport { h } from 'vue'\n\nexport default {\n  name: 'DynamicScrollerItem',\n\n  inject: [\n    'vscrollData',\n    'vscrollParent',\n    'vscrollResizeObserver',\n  ],\n\n  props: {\n    // eslint-disable-next-line vue/require-prop-types\n    item: {\n      required: true,\n    },\n\n    watchData: {\n      type: Boolean,\n      default: false,\n    },\n\n    /**\n     * Indicates if the view is actively used to display an item.\n     */\n    active: {\n      type: Boolean,\n      required: true,\n    },\n\n    index: {\n      type: Number,\n      default: undefined,\n    },\n\n    sizeDependencies: {\n      type: [Array, Object],\n      default: null,\n    },\n\n    emitResize: {\n      type: Boolean,\n      default: false,\n    },\n\n    tag: {\n      type: String,\n      default: 'div',\n    },\n  },\n\n  emits: [\n    'resize',\n  ],\n\n  computed: {\n    id () {\n      if (this.vscrollData.simpleArray) return this.index\n      // eslint-disable-next-line no-prototype-builtins\n      if (this.vscrollData.keyField in this.item) return this.item[this.vscrollData.keyField]\n      throw new Error(`keyField '${this.vscrollData.keyField}' not found in your item. You should set a valid keyField prop on your Scroller`)\n    },\n\n    size () {\n      return this.vscrollData.sizes[this.id] || 0\n    },\n\n    finalActive () {\n      return this.active && this.vscrollData.active\n    },\n  },\n\n  watch: {\n    watchData: 'updateWatchData',\n\n    id (value, oldValue) {\n      this.$el.$_vs_id = this.id\n      if (!this.size) {\n        this.onDataUpdate()\n      }\n\n      if (this.$_sizeObserved) {\n        // In case the old item had the same size, it won't trigger the ResizeObserver\n        // since we are reusing the same DOM node\n        const oldSize = this.vscrollData.sizes[oldValue]\n        const size = this.vscrollData.sizes[value]\n        if (oldSize != null && oldSize !== size) {\n          this.applySize(oldSize)\n        }\n      }\n    },\n\n    finalActive (value) {\n      if (!this.size) {\n        if (value) {\n          if (!this.vscrollParent.$_undefinedMap[this.id]) {\n            this.vscrollParent.$_undefinedSizes++\n            this.vscrollParent.$_undefinedMap[this.id] = true\n          }\n        } else {\n          if (this.vscrollParent.$_undefinedMap[this.id]) {\n            this.vscrollParent.$_undefinedSizes--\n            this.vscrollParent.$_undefinedMap[this.id] = false\n          }\n        }\n      }\n\n      if (this.vscrollResizeObserver) {\n        if (value) {\n          this.observeSize()\n        } else {\n          this.unobserveSize()\n        }\n      } else if (value && this.$_pendingVScrollUpdate === this.id) {\n        this.updateSize()\n      }\n    },\n  },\n\n  created () {\n    if (this.$isServer) return\n\n    this.$_forceNextVScrollUpdate = null\n    this.updateWatchData()\n\n    if (!this.vscrollResizeObserver) {\n      for (const k in this.sizeDependencies) {\n        this.$watch(() => this.sizeDependencies[k], this.onDataUpdate)\n      }\n\n      this.vscrollParent.$_events.on('vscroll:update', this.onVscrollUpdate)\n    }\n  },\n\n  mounted () {\n    if (this.finalActive) {\n      this.updateSize()\n      this.observeSize()\n    }\n  },\n\n  beforeUnmount () {\n    this.vscrollParent.$_events.off('vscroll:update', this.onVscrollUpdate)\n    this.unobserveSize()\n  },\n\n  methods: {\n    updateSize () {\n      if (this.finalActive) {\n        if (this.$_pendingSizeUpdate !== this.id) {\n          this.$_pendingSizeUpdate = this.id\n          this.$_forceNextVScrollUpdate = null\n          this.$_pendingVScrollUpdate = null\n          this.computeSize(this.id)\n        }\n      } else {\n        this.$_forceNextVScrollUpdate = this.id\n      }\n    },\n\n    updateWatchData () {\n      if (this.watchData && !this.vscrollResizeObserver) {\n        this.$_watchData = this.$watch('item', () => {\n          this.onDataUpdate()\n        }, {\n          deep: true,\n        })\n      } else if (this.$_watchData) {\n        this.$_watchData()\n        this.$_watchData = null\n      }\n    },\n\n    onVscrollUpdate ({ force }) {\n      // If not active, sechedule a size update when it becomes active\n      if (!this.finalActive && force) {\n        this.$_pendingVScrollUpdate = this.id\n      }\n\n      if (this.$_forceNextVScrollUpdate === this.id || force || !this.size) {\n        this.updateSize()\n      }\n    },\n\n    onDataUpdate () {\n      this.updateSize()\n    },\n\n    computeSize (id) {\n      this.$nextTick(() => {\n        if (this.id === id) {\n          const width = this.$el.offsetWidth\n          const height = this.$el.offsetHeight\n          this.applyWidthHeight(width, height)\n        }\n        this.$_pendingSizeUpdate = null\n      })\n    },\n\n    applyWidthHeight (width, height) {\n      const size = ~~(this.vscrollParent.direction === 'vertical' ? height : width)\n      if (size && this.size !== size) {\n        this.applySize(size)\n      }\n    },\n\n    applySize (size) {\n      if (this.vscrollParent.$_undefinedMap[this.id]) {\n        this.vscrollParent.$_undefinedSizes--\n        this.vscrollParent.$_undefinedMap[this.id] = undefined\n      }\n      this.vscrollData.sizes[this.id] = size\n      if (this.emitResize) this.$emit('resize', this.id)\n    },\n\n    observeSize () {\n      if (!this.vscrollResizeObserver) return\n      if (this.$_sizeObserved) return\n      this.vscrollResizeObserver.observe(this.$el)\n      this.$el.$_vs_id = this.id\n      this.$el.$_vs_onResize = this.onResize\n      this.$_sizeObserved = true\n    },\n\n    unobserveSize () {\n      if (!this.vscrollResizeObserver) return\n      if (!this.$_sizeObserved) return\n      this.vscrollResizeObserver.unobserve(this.$el)\n      this.$el.$_vs_onResize = undefined\n      this.$_sizeObserved = false\n    },\n\n    onResize (id, width, height) {\n      if (this.id === id) {\n        this.applyWidthHeight(width, height)\n      }\n    },\n  },\n\n  render () {\n    return h(this.tag, this.$slots.default())\n  },\n}\n</script>\n", "import { reactive } from 'vue'\n\nexport default function ({\n  idProp = vm => vm.item.id,\n} = {}) {\n  const store = reactive({})\n\n  // @vue/component\n  return {\n    data () {\n      return {\n        idState: null,\n      }\n    },\n\n    created () {\n      this.$_id = null\n      if (typeof idProp === 'function') {\n        this.$_getId = () => idProp.call(this, this)\n      } else {\n        this.$_getId = () => this[idProp]\n      }\n      this.$watch(this.$_getId, {\n        handler (value) {\n          this.$nextTick(() => {\n            this.$_id = value\n          })\n        },\n        immediate: true,\n      })\n      this.$_updateIdState()\n    },\n\n    beforeUpdate () {\n      this.$_updateIdState()\n    },\n\n    methods: {\n      /**\n       * Initialize an idState\n       * @param {number|string} id Unique id for the data\n       */\n      $_idStateInit (id) {\n        const factory = this.$options.idState\n        if (typeof factory === 'function') {\n          const data = factory.call(this, this)\n          store[id] = data\n          this.$_id = id\n          return data\n        } else {\n          throw new Error('[mixin IdState] Missing `idState` function on component definition.')\n        }\n      },\n\n      /**\n       * Ensure idState is created and up-to-date\n       */\n      $_updateIdState () {\n        const id = this.$_getId()\n        if (id == null) {\n          console.warn(`No id found for IdState with idProp: '${idProp}'.`)\n        }\n        if (id !== this.$_id) {\n          if (!store[id]) {\n            this.$_idStateInit(id)\n          }\n          this.idState = store[id]\n        }\n      },\n    },\n  }\n}\n", "import config from './config'\n\nimport RecycleScroller from './components/RecycleScroller.vue'\nimport DynamicScroller from './components/DynamicScroller.vue'\nimport DynamicScrollerItem from './components/DynamicScrollerItem.vue'\n\nexport { default as IdState } from './mixins/IdState'\n\nexport {\n  RecycleScroller,\n  DynamicScroller,\n  DynamicScrollerItem,\n}\n\nfunction registerComponents (app, prefix) {\n  app.component(`${prefix}recycle-scroller`, RecycleScroller)\n  app.component(`${prefix}RecycleScroller`, RecycleScroller)\n  app.component(`${prefix}dynamic-scroller`, DynamicScroller)\n  app.component(`${prefix}DynamicScroller`, DynamicScroller)\n  app.component(`${prefix}dynamic-scroller-item`, DynamicScrollerItem)\n  app.component(`${prefix}DynamicScrollerItem`, DynamicScrollerItem)\n}\n\nconst plugin = {\n  // eslint-disable-next-line no-undef\n  version: VERSION,\n  install (app, options) {\n    const finalOptions = Object.assign({}, {\n      installComponents: true,\n      componentsPrefix: '',\n    }, options)\n\n    for (const key in finalOptions) {\n      if (typeof finalOptions[key] !== 'undefined') {\n        config[key] = finalOptions[key]\n      }\n    }\n\n    if (finalOptions.installComponents) {\n      registerComponents(app, finalOptions.componentsPrefix)\n    }\n  },\n}\n\nexport default plugin\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAO,SAASA,6BAA8B;AAC5C,MAAMC,KAAKC,OAAOC,UAAUC;AAE5B,MAAMC,OAAOJ,GAAGK,QAAQ,OAAX;AACb,MAAID,OAAO,GAAG;AAEZ,WAAOE,SAASN,GAAGO,UAAUH,OAAO,GAAGJ,GAAGK,QAAQ,KAAKD,IAAhB,CAAvB,GAA+C,EAAhD;EAChB;AAED,MAAMI,UAAUR,GAAGK,QAAQ,UAAX;AAChB,MAAIG,UAAU,GAAG;AAEf,QAAMC,KAAKT,GAAGK,QAAQ,KAAX;AACX,WAAOC,SAASN,GAAGO,UAAUE,KAAK,GAAGT,GAAGK,QAAQ,KAAKI,EAAhB,CAArB,GAA2C,EAA5C;EAChB;AAED,MAAMC,OAAOV,GAAGK,QAAQ,OAAX;AACb,MAAIK,OAAO,GAAG;AAEZ,WAAOJ,SAASN,GAAGO,UAAUG,OAAO,GAAGV,GAAGK,QAAQ,KAAKK,IAAhB,CAAvB,GAA+C,EAAhD;EAChB;AAGD,SAAO;AACR;ACbD,IAAI;AAEJ,SAAS,aAAc;AACrB,MAAI,CAAC,WAAW,MAAM;AACpB,eAAW,OAAO;AAClB,WAAO,2BAA0B,MAAO;;AAE5C;AAEA,IAAA,SAAe;EACb,MAAM;EAEN,OAAO;IACL,aAAa;MACX,MAAM;MACN,SAAS;;IAGX,aAAa;MACX,MAAM;MACN,SAAS;;IAGX,cAAc;MACZ,MAAM;MACN,SAAS;;;EAIb,OAAO;IACL;;EAGF,UAAW;AACT,eAAU;AACV,aAAS,MAAM;AACb,WAAK,KAAK,KAAK,IAAI;AACnB,WAAK,KAAK,KAAK,IAAI;AACnB,UAAI,KAAK,aAAa;AACpB,aAAK,SAAQ;;KAEhB;AACD,UAAM,SAAS,SAAS,cAAc,QAAQ;AAC9C,SAAK,gBAAgB;AACrB,WAAO,aAAa,eAAe,MAAM;AACzC,WAAO,aAAa,YAAY,EAAE;AAClC,WAAO,SAAS,KAAK;AACrB,WAAO,OAAO;AACd,QAAI,MAAM;AACR,WAAK,IAAI,YAAY,MAAM;;AAE7B,WAAO,OAAO;AACd,QAAI,CAAC,MAAM;AACT,WAAK,IAAI,YAAY,MAAM;;;EAI/B,gBAAiB;AACf,SAAK,qBAAoB;;EAG3B,SAAS;IACP,mBAAoB;AAClB,UAAK,CAAC,KAAK,eAAe,KAAK,OAAO,KAAK,IAAI,eAAiB,CAAC,KAAK,gBAAgB,KAAK,OAAO,KAAK,IAAI,cAAe;AACxH,aAAK,KAAK,KAAK,IAAI;AACnB,aAAK,KAAK,KAAK,IAAI;AACnB,aAAK,SAAQ;;;IAIjB,WAAY;AACV,WAAK,MAAM,UAAU;QACnB,OAAO,KAAK;QACZ,QAAQ,KAAK;OACd;;IAGH,oBAAqB;AACnB,WAAK,cAAc,gBAAgB,YAAY,iBAAiB,UAAU,KAAK,gBAAgB;AAC/F,WAAK,iBAAgB;;IAGvB,uBAAwB;AACtB,UAAI,KAAK,iBAAiB,KAAK,cAAc,QAAQ;AACnD,YAAI,CAAC,QAAQ,KAAK,cAAc,iBAAiB;AAC/C,eAAK,cAAc,gBAAgB,YAAY,oBAAoB,UAAU,KAAK,gBAAgB;;AAEpG,aAAK,IAAI,YAAY,KAAK,aAAa;AACvC,aAAK,cAAc,SAAS;AAC5B,aAAK,gBAAgB;;;;AAI7B;;;;ECtGI,OAAM;EACN,UAAS;;;;sBAFXC,YAAAA,OAAA,UAAA;;;;;;;AECF,SAAS,QAAQ,KAAK;AACpB;AAEA,MAAI,OAAO,WAAW,cAAc,OAAO,OAAO,aAAa,UAAU;AACvE,cAAU,SAAUC,MAAK;AACvB,aAAO,OAAOA;AAAA,IAChB;AAAA,EACF,OAAO;AACL,cAAU,SAAUA,MAAK;AACvB,aAAOA,QAAO,OAAO,WAAW,cAAcA,KAAI,gBAAgB,UAAUA,SAAQ,OAAO,YAAY,WAAW,OAAOA;AAAA,IAC3H;AAAA,EACF;AAEA,SAAO,QAAQ,GAAG;AACpB;AAEA,SAAS,gBAAgB,UAAU,aAAa;AAC9C,MAAI,EAAE,oBAAoB,cAAc;AACtC,UAAM,IAAI,UAAU,mCAAmC;AAAA,EACzD;AACF;AAEA,SAAS,kBAAkB,QAAQC,QAAO;AACxC,WAAS,IAAI,GAAG,IAAIA,OAAM,QAAQ,KAAK;AACrC,QAAI,aAAaA,OAAM,CAAC;AACxB,eAAW,aAAa,WAAW,cAAc;AACjD,eAAW,eAAe;AAC1B,QAAI,WAAW;AAAY,iBAAW,WAAW;AACjD,WAAO,eAAe,QAAQ,WAAW,KAAK,UAAU;AAAA,EAC1D;AACF;AAEA,SAAS,aAAa,aAAa,YAAY,aAAa;AAC1D,MAAI;AAAY,sBAAkB,YAAY,WAAW,UAAU;AACnE,MAAI;AAAa,sBAAkB,aAAa,WAAW;AAC3D,SAAO;AACT;AAEA,SAAS,mBAAmB,KAAK;AAC/B,SAAO,mBAAmB,GAAG,KAAK,iBAAiB,GAAG,KAAK,4BAA4B,GAAG,KAAK,mBAAmB;AACpH;AAEA,SAAS,mBAAmB,KAAK;AAC/B,MAAI,MAAM,QAAQ,GAAG;AAAG,WAAO,kBAAkB,GAAG;AACtD;AAEA,SAAS,iBAAiB,MAAM;AAC9B,MAAI,OAAO,WAAW,eAAe,OAAO,YAAY,OAAO,IAAI;AAAG,WAAO,MAAM,KAAK,IAAI;AAC9F;AAEA,SAAS,4BAA4B,GAAG,QAAQ;AAC9C,MAAI,CAAC;AAAG;AACR,MAAI,OAAO,MAAM;AAAU,WAAO,kBAAkB,GAAG,MAAM;AAC7D,MAAI,IAAI,OAAO,UAAU,SAAS,KAAK,CAAC,EAAE,MAAM,GAAG,EAAE;AACrD,MAAI,MAAM,YAAY,EAAE;AAAa,QAAI,EAAE,YAAY;AACvD,MAAI,MAAM,SAAS,MAAM;AAAO,WAAO,MAAM,KAAK,CAAC;AACnD,MAAI,MAAM,eAAe,2CAA2C,KAAK,CAAC;AAAG,WAAO,kBAAkB,GAAG,MAAM;AACjH;AAEA,SAAS,kBAAkB,KAAK,KAAK;AACnC,MAAI,OAAO,QAAQ,MAAM,IAAI;AAAQ,UAAM,IAAI;AAE/C,WAAS,IAAI,GAAG,OAAO,IAAI,MAAM,GAAG,GAAG,IAAI,KAAK;AAAK,SAAK,CAAC,IAAI,IAAI,CAAC;AAEpE,SAAO;AACT;AAEA,SAAS,qBAAqB;AAC5B,QAAM,IAAI,UAAU,sIAAsI;AAC5J;AAEA,SAAS,eAAe,OAAO;AAC7B,MAAI;AAEJ,MAAI,OAAO,UAAU,YAAY;AAE/B,cAAU;AAAA,MACR,UAAU;AAAA,IACZ;AAAA,EACF,OAAO;AAEL,cAAU;AAAA,EACZ;AAEA,SAAO;AACT;AACA,SAAS,SAAS,UAAU,OAAO;AACjC,MAAI,UAAU,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AACnF,MAAI;AACJ,MAAI;AACJ,MAAI;AAEJ,MAAI,YAAY,SAASC,WAAU,OAAO;AACxC,aAAS,OAAO,UAAU,QAAQ,OAAO,IAAI,MAAM,OAAO,IAAI,OAAO,IAAI,CAAC,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AAC1G,WAAK,OAAO,CAAC,IAAI,UAAU,IAAI;AAAA,IACjC;AAEA,kBAAc;AACd,QAAI,WAAW,UAAU;AAAW;AACpC,QAAI,UAAU,QAAQ;AAEtB,QAAI,OAAO,YAAY,YAAY;AACjC,gBAAU,QAAQ,OAAO,SAAS;AAAA,IACpC;AAEA,SAAK,CAAC,WAAW,UAAU,cAAc,SAAS;AAChD,eAAS,MAAM,QAAQ,CAAC,KAAK,EAAE,OAAO,mBAAmB,WAAW,CAAC,CAAC;AAAA,IACxE;AAEA,gBAAY;AACZ,iBAAa,OAAO;AACpB,cAAU,WAAW,WAAY;AAC/B,eAAS,MAAM,QAAQ,CAAC,KAAK,EAAE,OAAO,mBAAmB,WAAW,CAAC,CAAC;AACtE,gBAAU;AAAA,IACZ,GAAG,KAAK;AAAA,EACV;AAEA,YAAU,SAAS,WAAY;AAC7B,iBAAa,OAAO;AACpB,cAAU;AAAA,EACZ;AAEA,SAAO;AACT;AACA,SAAS,UAAU,MAAM,MAAM;AAC7B,MAAI,SAAS;AAAM,WAAO;AAE1B,MAAI,QAAQ,IAAI,MAAM,UAAU;AAC9B,aAAS,OAAO,MAAM;AACpB,UAAI,CAAC,UAAU,KAAK,GAAG,GAAG,KAAK,GAAG,CAAC,GAAG;AACpC,eAAO;AAAA,MACT;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAEA,IAAI,kBAA+B,WAAY;AAC7C,WAASC,iBAAgB,IAAI,SAAS,OAAO;AAC3C,oBAAgB,MAAMA,gBAAe;AAErC,SAAK,KAAK;AACV,SAAK,WAAW;AAChB,SAAK,SAAS;AACd,SAAK,eAAe,SAAS,KAAK;AAAA,EACpC;AAEA,eAAaA,kBAAiB,CAAC;AAAA,IAC7B,KAAK;AAAA,IACL,OAAO,SAAS,eAAe,SAAS,OAAO;AAC7C,UAAI,QAAQ;AAEZ,UAAI,KAAK,UAAU;AACjB,aAAK,gBAAgB;AAAA,MACvB;AAEA,UAAI,KAAK;AAAQ;AACjB,WAAK,UAAU,eAAe,OAAO;AAErC,WAAK,WAAW,SAAU,QAAQ,OAAO;AACvC,cAAM,QAAQ,SAAS,QAAQ,KAAK;AAEpC,YAAI,UAAU,MAAM,QAAQ,MAAM;AAChC,gBAAM,SAAS;AAEf,gBAAM,gBAAgB;AAAA,QACxB;AAAA,MACF;AAGA,UAAI,KAAK,YAAY,KAAK,QAAQ,UAAU;AAC1C,YAAI,OAAO,KAAK,QAAQ,mBAAmB,CAAC,GACxC,WAAW,KAAK;AAEpB,aAAK,WAAW,SAAS,KAAK,UAAU,KAAK,QAAQ,UAAU;AAAA,UAC7D,SAAS,SAAS,QAAQ,OAAO;AAC/B,mBAAO,aAAa,UAAU,aAAa,aAAa,SAAS,aAAa,YAAY,CAAC;AAAA,UAC7F;AAAA,QACF,CAAC;AAAA,MACH;AAEA,WAAK,YAAY;AACjB,WAAK,WAAW,IAAI,qBAAqB,SAAU,SAAS;AAC1D,YAAI,QAAQ,QAAQ,CAAC;AAErB,YAAI,QAAQ,SAAS,GAAG;AACtB,cAAI,oBAAoB,QAAQ,KAAK,SAAU,GAAG;AAChD,mBAAO,EAAE;AAAA,UACX,CAAC;AAED,cAAI,mBAAmB;AACrB,oBAAQ;AAAA,UACV;AAAA,QACF;AAEA,YAAI,MAAM,UAAU;AAElB,cAAI,SAAS,MAAM,kBAAkB,MAAM,qBAAqB,MAAM;AACtE,cAAI,WAAW,MAAM;AAAW;AAChC,gBAAM,YAAY;AAElB,gBAAM,SAAS,QAAQ,KAAK;AAAA,QAC9B;AAAA,MACF,GAAG,KAAK,QAAQ,YAAY;AAE5B,eAAS,WAAY;AACnB,YAAI,MAAM,UAAU;AAClB,gBAAM,SAAS,QAAQ,MAAM,EAAE;AAAA,QACjC;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,kBAAkB;AAChC,UAAI,KAAK,UAAU;AACjB,aAAK,SAAS,WAAW;AACzB,aAAK,WAAW;AAAA,MAClB;AAGA,UAAI,KAAK,YAAY,KAAK,SAAS,QAAQ;AACzC,aAAK,SAAS,OAAO;AAErB,aAAK,WAAW;AAAA,MAClB;AAAA,IACF;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,KAAK,SAAS,MAAM;AAClB,aAAO,KAAK,QAAQ,gBAAgB,OAAO,KAAK,QAAQ,aAAa,cAAc,WAAW,KAAK,QAAQ,aAAa,YAAY;AAAA,IACtI;AAAA,EACF,CAAC,CAAC;AAEF,SAAOA;AACT,EAAE;AAEF,SAAS,YAAY,IAAI,OAAO,OAAO;AACrC,MAAI,QAAQ,MAAM;AAClB,MAAI,CAAC;AAAO;AAEZ,MAAI,OAAO,yBAAyB,aAAa;AAC/C,YAAQ,KAAK,oLAAoL;AAAA,EACnM,OAAO;AACL,QAAI,QAAQ,IAAI,gBAAgB,IAAI,OAAO,KAAK;AAChD,OAAG,uBAAuB;AAAA,EAC5B;AACF;AAEA,SAAS,QAAQ,IAAI,OAAO,OAAO;AACjC,MAAI,QAAQ,MAAM,OACd,WAAW,MAAM;AACrB,MAAI,UAAU,OAAO,QAAQ;AAAG;AAChC,MAAI,QAAQ,GAAG;AAEf,MAAI,CAAC,OAAO;AACV,cAAU,EAAE;AACZ;AAAA,EACF;AAEA,MAAI,OAAO;AACT,UAAM,eAAe,OAAO,KAAK;AAAA,EACnC,OAAO;AACL,gBAAY,IAAI;AAAA,MACd;AAAA,IACF,GAAG,KAAK;AAAA,EACV;AACF;AAEA,SAAS,UAAU,IAAI;AACrB,MAAI,QAAQ,GAAG;AAEf,MAAI,OAAO;AACT,UAAM,gBAAgB;AACtB,WAAO,GAAG;AAAA,EACZ;AACF;AAEA,IAAI,oBAAoB;AAAA,EACtB;AAAA,EACA;AAAA,EACA;AACF;;;yBC9P6BC,GAAAA;AAG5B,SAAO,EAKNA,KAPDA,IAAMA,KAAO,oBAAIC,OAehBC,IAAAA,SAAYC,GAAiBC,GAAAA;AAC5B,QAAMC,IAAWL,EAAIM,IAAIH,CAAAA;AACXE,SAAYA,EAASE,KAAKH,CAAAA,KAEvCJ,EAAIQ,IAAIL,GAAM,CAACC,CAAAA,CAAAA;EAAAA,GAUjBK,KAAAA,SAAaN,GAAiBC,GAAAA;AAC7B,QAAMC,IAAWL,EAAIM,IAAIH,CAAAA;AACrBE,SACHA,EAASK,OAAOL,EAASM,QAAQP,CAAAA,MAAa,GAAG,CAAA;EAAA,GAcnDQ,MAAAA,SAAcT,GAAiBU,GAAAA;AAAAA,KAC5Bb,EAAIM,IAAIH,CAAAA,KAAS,CAAA,GAAyBW,MAAAA,EAAQC,IAAI,SAACX,IAAAA;AAAcA,MAAAA,GAAQS,CAAAA;IAAAA,CAAAA,IAC7Eb,EAAIM,IAAI,GAAA,KAAQ,CAAA,GAAiCQ,MAAAA,EAAQC,IAAI,SAACX,IAAAA;AAAcA,MAAAA,GAAQD,GAAMU,CAAAA;IAAAA,CAAAA;EAAAA,EAAAA;AAAAA;;;ACjF/F,IAAA,SAAe;EACbG,YAAY;AACd;ACAA,IAAMC,QAAQ;AAEd,SAASC,QAASC,MAAMC,IAAI;AAC1B,MAAID,KAAKE,eAAe,MAAM;AAAE,WAAOD;EAAG;AAE1C,SAAOF,QAAQC,KAAKE,YAAYD,GAAGE,OAAO,CAACH,IAAI,CAAC,CAAC;AACnD;AAEA,IAAMI,QAAQ,SAARA,OAAkBJ,MAAMK,MAAM;AAClC,SAAOC,iBAAiBN,MAAM,IAAI,EAAEO,iBAAiBF,IAAI;AAC3D;AAEA,IAAMG,WAAW,SAAXA,UAAqBR,MAAM;AAC/B,SAAOI,MAAMJ,MAAM,UAAU,IAAII,MAAMJ,MAAM,YAAY,IAAII,MAAMJ,MAAM,YAAY;AACvF;AAEA,IAAMS,SAAS,SAATA,QAAmBT,MAAM;AAC7B,SAAOF,MAAMY,KAAKF,SAASR,IAAI,CAAC;AAClC;AAEO,SAASW,gBAAiBX,MAAM;AACrC,MAAI,EAAEA,gBAAgBY,eAAeZ,gBAAgBa,aAAa;AAChE;EACF;AAEA,MAAMZ,KAAKF,QAAQC,KAAKE,YAAY,CAAA,CAAE;AAEtC,WAASY,IAAI,GAAGA,IAAIb,GAAGc,QAAQD,KAAK,GAAG;AACrC,QAAIL,OAAOR,GAAGa,CAAC,CAAC,GAAG;AACjB,aAAOb,GAAGa,CAAC;IACb;EACF;AAEA,SAAOE,SAASC,oBAAoBD,SAASE;AAC/C;;;;;;;;;ACpCO,IAAMC,QAAQ;EACnBC,OAAO;IACLC,MAAMC;IACNC,UAAU;;EAGZC,UAAU;IACRH,MAAMI;IACNC,SAAS;;EAGXC,WAAW;IACTN,MAAMI;IACNC,SAAS;IACTE,WAAW,SAAA,UAACC,OAAK;AAAA,aAAK,CAAC,YAAY,YAAY,EAAEC,SAASD,KAAK;IAAC;;EAGlEE,SAAS;IACPV,MAAMI;IACNC,SAAS;;EAGXM,SAAS;IACPX,MAAMI;IACNC,SAAS;EACX;AACF;AAEO,SAASO,cAAe;AAC7B,SAAO,KAAKb,MAAML,UAAUmB,SAAO,KAAKd,MAAM,CAAC,CAAC,MAAK;AACvD;AC9BO,IAAIe,kBAAkB;AAE7B,IAAI,OAAOC,WAAW,aAAa;AACjCD,oBAAkB;AAClB,MAAI;AACIE,WAAOC,OAAOC,eAAe,CAAA,GAAI,WAAW;MAChDC,KAAO,SAAA,MAAA;AACLL,0BAAkB;MACpB;IACF,CAAC;AACDC,WAAOK,iBAAiB,QAAQ,MAAMJ,IAAI;EAC5C,SAASK,GAAG;EAAA;AACd;AAPUL;AC+EV,IAAI,MAAM;AAEV,IAAA,WAAe;EACb,MAAM;EAEN,YAAY;IACV,gBAAAM;;EAGF,YAAY;IACV;;EAGF,OAAO;IACL,GAAG;IAEH,UAAU;MACR,MAAM;MACN,SAAS;;IAGX,WAAW;MACT,MAAM;MACN,SAAS;;IAGX,mBAAmB;MACjB,MAAM;MACN,SAAS;;IAGX,aAAa;MACX,MAAM,CAAC,QAAQ,MAAM;MACrB,SAAS;;IAGX,WAAW;MACT,MAAM;MACN,SAAS;;IAGX,WAAW;MACT,MAAM;MACN,SAAS;;IAGX,QAAQ;MACN,MAAM;MACN,SAAS;;IAGX,UAAU;MACR,MAAM;MACN,SAAS;;IAGX,WAAW;MACT,MAAM;MACN,SAAS;;IAGX,YAAY;MACV,MAAM;MACN,SAAS;;IAGX,gBAAgB;MACd,MAAM;MACN,SAAS;;IAGX,WAAW;MACT,MAAM;MACN,SAAS;;IAGX,SAAS;MACP,MAAM;MACN,SAAS;;IAGX,SAAS;MACP,MAAM;MACN,SAAS;;IAGX,WAAW;MACT,MAAM,CAAC,QAAQ,QAAQ,KAAK;MAC5B,SAAS;;IAGX,WAAW;MACT,MAAM,CAAC,QAAQ,QAAQ,KAAK;MAC5B,SAAS;;;EAIb,OAAO;IACL;IACA;IACA;IACA;IACA;IACA;;EAGF,OAAQ;AACN,WAAO;MACL,MAAM,CAAA;MACN,WAAW;MACX,OAAO;MACP,UAAU;IACZ;;EAGF,UAAU;IACR,QAAS;AACP,UAAI,KAAK,aAAa,MAAM;AAC1B,cAAM,QAAQ;UACZ,MAAM,EAAE,aAAa,EAAA;QACvB;AACA,cAAM,QAAQ,KAAK;AACnB,cAAM,QAAQ,KAAK;AACnB,cAAM,cAAc,KAAK;AACzB,YAAI,kBAAkB;AACtB,YAAI,cAAc;AAClB,YAAI;AACJ,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,IAAI,GAAG,KAAK;AAC5C,oBAAU,MAAM,CAAC,EAAE,KAAK,KAAK;AAC7B,cAAI,UAAU,iBAAiB;AAC7B,8BAAkB;UACpB;AACA,yBAAe;AACf,gBAAM,CAAC,IAAI,EAAE,aAAa,MAAM,QAAQ;QAC1C;AAEA,aAAK,wBAAwB;AAC7B,eAAO;MACT;AACA,aAAO,CAAA;;IAGT;IAEA,iBAAkB;AAChB,YAAM,EAAE,UAAU,MAAA,IAAU;AAC5B,YAAM,SAAS,CAAA;AACf,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,IAAI,GAAG,KAAK;AAC5C,eAAO,MAAM,CAAC,EAAE,QAAQ,CAAC,IAAI;MAC/B;AACA,aAAO;;;EAIX,OAAO;IACL,QAAS;AACP,WAAK,mBAAmB,IAAI;;IAG9B,WAAY;AACV,WAAK,cAAa;AAClB,WAAK,mBAAmB,KAAK;;IAG/B,OAAO;MACL,UAAW;AACT,aAAK,mBAAmB,KAAK;;MAE/B,MAAM;;IAGR,YAAa;AACX,WAAK,mBAAmB,IAAI;;IAG9B,oBAAqB;AACnB,WAAK,mBAAmB,IAAI;;;EAIhC,UAAW;AACT,SAAK,eAAe;AACpB,SAAK,aAAa;AAClB,SAAK,UAAU,oBAAI,IAAG;AACtB,SAAK,gBAAgB,oBAAI,IAAG;AAC5B,SAAK,gBAAgB;AACrB,SAAK,6BAA6B;AAIlC,QAAI,KAAK,WAAW;AAClB,WAAK,cAAc;AACnB,WAAK,mBAAmB,KAAK;IAC/B;AAEA,QAAI,KAAK,aAAa,CAAC,KAAK,UAAU;AACpC,cAAQ,MAAM,0EAA0E;IAC1F;;EAGF,UAAW;AACT,SAAK,cAAa;AAClB,SAAK,UAAU,MAAM;AAEnB,WAAK,cAAc;AACnB,WAAK,mBAAmB,IAAI;AAC5B,WAAK,QAAQ;KACd;;EAGH,YAAa;AACX,UAAM,eAAe,KAAK;AAC1B,QAAI,OAAO,iBAAiB,UAAU;AACpC,WAAK,UAAU,MAAM;AACnB,aAAK,iBAAiB,YAAY;OACnC;IACH;;EAGF,gBAAiB;AACf,SAAK,gBAAe;;EAGtB,SAAS;IACP,QAAS,MAAM,OAAO,MAAM,KAAK,MAAM;AACrC,YAAM,KAAK,QAAQ;QACjB,IAAI;QACJ;QACA,MAAM;QACN;QACA;OACD;AACD,YAAM,OAAO,gBAAgB;QAC3B;QACA,UAAU;QACV;OACD;AACD,WAAK,KAAK,IAAI;AACd,aAAO;;IAGT,UAAW,MAAM,OAAO,OAAO;AAC7B,YAAM,cAAc,KAAK;AACzB,YAAM,OAAO,KAAK,GAAG;AACrB,UAAI,aAAa,YAAY,IAAI,IAAI;AACrC,UAAI,CAAC,YAAY;AACf,qBAAa,CAAA;AACb,oBAAY,IAAI,MAAM,UAAU;MAClC;AACA,iBAAW,KAAK,IAAI;AACpB,UAAI,CAAC,MAAM;AACT,aAAK,GAAG,OAAO;AACf,aAAK,WAAW;MAClB;;IAGF,eAAgB;AACd,WAAK,MAAM,QAAQ;AACnB,UAAI,KAAK;AAAO,aAAK,mBAAmB,KAAK;;IAG/C,aAAc,OAAO;AACnB,UAAI,CAAC,KAAK,eAAe;AACvB,aAAK,gBAAgB;AACrB,YAAI,KAAK;AAAiB;AAE1B,cAAM,gBAAgB,MAAM,sBAAsB,MAAM;AACtD,eAAK,gBAAgB;AACrB,gBAAM,EAAE,WAAW,IAAI,KAAK,mBAAmB,OAAO,IAAI;AAI1D,cAAI,CAAC,YAAY;AACf,yBAAa,KAAK,eAAe;AACjC,iBAAK,kBAAkB,WAAW,KAAK,cAAc,KAAK,iBAAiB,GAAG;UAChF;SACD;AAED,sBAAa;AAGb,YAAI,KAAK,gBAAgB;AACvB,eAAK,kBAAkB,WAAW,MAAM;AACtC,iBAAK,kBAAkB;AACvB,gBAAI,KAAK;AAAe,4BAAa;UACvC,GAAG,KAAK,cAAc;QACxB;MACF;;IAGF,uBAAwB,WAAW,OAAO;AACxC,UAAI,KAAK,OAAO;AACd,YAAI,aAAa,MAAM,mBAAmB,UAAU,KAAK,MAAM,mBAAmB,WAAW,GAAG;AAC9F,eAAK,MAAM,SAAS;AACpB,gCAAsB,MAAM;AAC1B,iBAAK,mBAAmB,KAAK;WAC9B;eACI;AACL,eAAK,MAAM,QAAQ;QACrB;MACF;;IAGF,mBAAoB,WAAW,oBAAoB,OAAO;AACxD,YAAM,WAAW,KAAK;AACtB,YAAM,YAAY,KAAK,aAAa;AACpC,YAAM,oBAAoB,KAAK,qBAAqB;AACpD,YAAM,cAAc,KAAK;AACzB,YAAM,YAAY,KAAK;AACvB,YAAM,WAAW,KAAK,cAAc,OAAO,KAAK;AAChD,YAAM,QAAQ,KAAK;AACnB,YAAM,QAAQ,MAAM;AACpB,YAAM,QAAQ,KAAK;AACnB,YAAM,QAAQ,KAAK;AACnB,YAAM,cAAc,KAAK;AACzB,YAAM,OAAO,KAAK;AAClB,YAAM,iBAAiB,KAAK;AAC5B,UAAI,YAAY;AAChB,UAAI;AACJ,UAAI,mBAAmB;AAEvB,UAAI,CAAC,OAAO;AACV,qBAAa,WAAW,oBAAoB,kBAAkB,YAAY;iBACjE,KAAK,aAAa;AAC3B,qBAAa,oBAAoB;AACjC,mBAAW,kBAAkB,KAAK,IAAI,KAAK,WAAW,MAAM,MAAM;AAClE,oBAAY;aACP;AACL,cAAMlC,UAAS,KAAK,UAAS;AAG7B,YAAI,mBAAmB;AACrB,cAAI,eAAeA,QAAO,QAAQ,KAAK;AACvC,cAAI,eAAe;AAAG,2BAAe,CAAC;AACtC,cAAK,aAAa,QAAQ,eAAe,eAAgB,eAAe,UAAU;AAChF,mBAAO;cACL,YAAY;YACd;UACF;QACF;AACA,aAAK,6BAA6BA,QAAO;AAEzC,cAAM,SAAS,KAAK;AACpB,QAAAA,QAAO,SAAS;AAChB,QAAAA,QAAO,OAAO;AAGd,YAAI,aAAa;AACjB,YAAI,KAAK,MAAM,QAAQ;AACrB,uBAAa,KAAK,MAAM,OAAO;AAC/B,UAAAA,QAAO,SAAS;QAClB;AAGA,YAAI,KAAK,MAAM,OAAO;AACpB,gBAAM,YAAY,KAAK,MAAM,MAAM;AACnC,UAAAA,QAAO,OAAO;QAChB;AAGA,YAAI,aAAa,MAAM;AACrB,cAAImC;AACJ,cAAI,IAAI;AACR,cAAI,IAAI,QAAQ;AAChB,cAAI,IAAI,CAAC,EAAE,QAAQ;AACnB,cAAI;AAGJ,aAAG;AACD,mBAAO;AACP,YAAAA,KAAI,MAAM,CAAC,EAAE;AACb,gBAAIA,KAAInC,QAAO,OAAO;AACpB,kBAAI;uBACK,IAAI,QAAQ,KAAK,MAAM,IAAI,CAAC,EAAE,cAAcA,QAAO,OAAO;AACnE,kBAAI;YACN;AACA,gBAAI,CAAC,GAAG,IAAI,KAAK;UACnB,SAAS,MAAM;AACf,cAAI,MAAM,IAAI;AACd,uBAAa;AAGb,sBAAY,MAAM,QAAQ,CAAC,EAAE;AAG7B,eAAK,WAAW,GAAG,WAAW,SAAS,MAAM,QAAQ,EAAE,cAAcA,QAAO,KAAK;AAAW;AAC5F,cAAI,aAAa,IAAI;AACnB,uBAAW,MAAM,SAAS;iBACrB;AACL;AAEA,uBAAW,UAAU,WAAW;UAClC;AAGA,eAAK,oBAAoB,YAAY,oBAAoB,SAAU,aAAa,MAAM,iBAAiB,EAAE,cAAeA,QAAO,OAAO;AAAoB;AAG1J,eAAK,kBAAkB,mBAAmB,kBAAkB,SAAU,aAAa,MAAM,eAAe,EAAE,cAAeA,QAAO,KAAK;AAAkB;eAClJ;AAEL,uBAAa,CAAC,EAAEA,QAAO,QAAQ,WAAW;AAC1C,gBAAM,WAAW,aAAa;AAC9B,wBAAc;AACd,qBAAW,KAAK,KAAKA,QAAO,MAAM,WAAW,SAAS;AACtD,8BAAoB,KAAK,IAAI,GAAG,KAAK,OAAOA,QAAO,QAAQ,cAAc,WAAW,SAAS,CAAC;AAC9F,4BAAkB,KAAK,OAAOA,QAAO,MAAM,cAAc,WAAW,SAAS;AAG7E,uBAAa,MAAM,aAAa;AAChC,qBAAW,UAAU,WAAW;AAChC,8BAAoB,MAAM,oBAAoB;AAC9C,4BAAkB,UAAU,kBAAkB;AAE9C,sBAAY,KAAK,KAAK,QAAQ,SAAS,IAAI;QAC7C;MACF;AAEA,UAAI,WAAW,aAAa,OAAO,YAAY;AAC7C,aAAK,gBAAe;MACtB;AAEA,WAAK,YAAY;AAEjB,UAAI;AAEJ,YAAM,aAAa,cAAc,KAAK,cAAc,YAAY,KAAK;AAGrE,UAAI,YAAY;AACd,iBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,IAAI,GAAG,KAAK;AAC3C,iBAAO,KAAK,CAAC;AACb,cAAI,KAAK,GAAG,MAAM;AAEhB,gBAAI,WAAW;AACb,mBAAK,GAAG,QAAQ,eAAe,KAAK,KAAK,QAAQ,CAAC;YACpD;AAGA,gBACE,KAAK,GAAG,SAAS,QACjB,KAAK,GAAG,QAAQ,cAChB,KAAK,GAAG,SAAS,UACjB;AACA,mBAAK,UAAU,IAAI;YACrB;UACF;QACF;MACF;AAEA,YAAM,cAAc,aAAa,OAAO,oBAAI,IAAG;AAE/C,UAAI,MAAM;AACV,UAAI;AACJ,eAAS,IAAI,YAAY,IAAI,UAAU,KAAK;AAC1C,eAAO,MAAM,CAAC;AACd,cAAM,MAAM,WAAW,KAAK,QAAQ,IAAI;AACxC,YAAI,OAAO,MAAM;AACf,gBAAM,IAAI,MAAM,UAAU,GAAG,0BAA0B,QAAQ,IAAI;QACrE;AACA,eAAO,MAAM,IAAI,GAAG;AAEpB,YAAI,CAAC,YAAY,CAAC,MAAM,CAAC,EAAE,MAAM;AAC/B,cAAI;AAAM,iBAAK,UAAU,IAAI;AAC7B;QACF;AAEA,eAAO,KAAK,SAAS;AAErB,YAAI,aAAa,YAAY,IAAI,IAAI;AACrC,YAAI,gBAAgB;AAGpB,YAAI,CAAC,MAAM;AACT,cAAI,YAAY;AAEd,gBAAI,cAAc,WAAW,QAAQ;AACnC,qBAAO,WAAW,IAAG;mBAChB;AACL,qBAAO,KAAK,QAAQ,MAAM,GAAG,MAAM,KAAK,IAAI;YAC9C;iBACK;AAIL,gBAAI,YAAY,IAAI,IAAI,KAAK;AAE7B,gBAAI,CAAC,cAAc,KAAK,WAAW,QAAQ;AACzC,qBAAO,KAAK,QAAQ,MAAM,GAAG,MAAM,KAAK,IAAI;AAC5C,mBAAK,UAAU,MAAM,IAAI;AACzB,2BAAa,YAAY,IAAI,IAAI;YACnC;AAEA,mBAAO,WAAW,CAAC;AACnB,wBAAY,IAAI,MAAM,IAAI,CAAC;UAC7B;AAGA,gBAAM,OAAO,KAAK,GAAG,GAAG;AACxB,eAAK,GAAG,OAAO;AACf,eAAK,GAAG,QAAQ;AAChB,eAAK,GAAG,MAAM;AACd,eAAK,GAAG,OAAO;AACf,gBAAM,IAAI,KAAK,IAAI;AAEnB,0BAAgB;eACX;AAEL,cAAI,CAAC,KAAK,GAAG,MAAM;AACjB,iBAAK,GAAG,OAAO;AACf,4BAAgB;AAChB,gBAAI,YAAY;AACd,oBAAM,QAAQ,WAAW,QAAQ,IAAI;AACrC,kBAAI,UAAU;AAAI,2BAAW,OAAO,OAAO,CAAC;YAC9C;UACF;QACF;AAGA,aAAK,OAAO;AAEZ,YAAI,eAAe;AACjB,cAAI,MAAM,MAAM,SAAS;AAAG,iBAAK,MAAM,YAAY;AACnD,cAAI,MAAM;AAAG,iBAAK,MAAM,cAAc;QACxC;AAGA,YAAI,aAAa,MAAM;AACrB,eAAK,WAAW,MAAM,IAAI,CAAC,EAAE;AAC7B,eAAK,SAAS;eACT;AACL,eAAK,WAAW,KAAK,MAAM,IAAI,SAAS,IAAI;AAC5C,eAAK,SAAU,IAAI,YAAa;QAClC;MACF;AAEA,WAAK,eAAe;AACpB,WAAK,aAAa;AAElB,UAAI,KAAK;AAAY,aAAK,MAAM,UAAU,YAAY,UAAU,mBAAmB,eAAe;AAIlG,mBAAa,KAAK,WAAW;AAC7B,WAAK,cAAc,WAAW,KAAK,WAAW,KAAK,iBAAiB,GAAG;AAEvE,aAAO;QACL;MACF;;IAGF,oBAAqB;AACnB,UAAI,SAAS,gBAAgB,KAAK,GAAG;AAErC,UAAI,OAAO,aAAa,WAAW,OAAO,SAAS,mBAAmB,WAAW,OAAO,SAAS,OAAO;AACtG,iBAAS;MACX;AACA,aAAO;;IAGT,YAAa;AACX,YAAM,EAAE,KAAK,IAAI,UAAQ,IAAM;AAC/B,YAAM,aAAa,cAAc;AACjC,UAAI;AAEJ,UAAI,KAAK,UAAU;AACjB,cAAM,SAAS,GAAG,sBAAqB;AACvC,cAAM,aAAa,aAAa,OAAO,SAAS,OAAO;AACvD,YAAI,QAAQ,EAAE,aAAa,OAAO,MAAM,OAAO;AAC/C,YAAI,OAAO,aAAa,OAAO,cAAc,OAAO;AACpD,YAAI,QAAQ,GAAG;AACb,kBAAQ;AACR,kBAAQ;QACV;AACA,YAAI,QAAQ,OAAO,YAAY;AAC7B,iBAAO,aAAa;QACtB;AACA,sBAAc;UACZ;UACA,KAAK,QAAQ;QACf;MACF,WAAW,YAAY;AACrB,sBAAc;UACZ,OAAO,GAAG;UACV,KAAK,GAAG,YAAY,GAAG;QACzB;aACK;AACL,sBAAc;UACZ,OAAO,GAAG;UACV,KAAK,GAAG,aAAa,GAAG;QAC1B;MACF;AAEA,aAAO;;IAGT,gBAAiB;AACf,UAAI,KAAK,UAAU;AACjB,aAAK,aAAY;aACZ;AACL,aAAK,gBAAe;MACtB;;IAGF,eAAgB;AACd,WAAK,iBAAiB,KAAK,kBAAiB;AAC5C,WAAK,eAAe,iBAAiB,UAAU,KAAK,cAAc,kBAC9D;QACE,SAAS;MACX,IACA,KAAK;AACT,WAAK,eAAe,iBAAiB,UAAU,KAAK,YAAY;;IAGlE,kBAAmB;AACjB,UAAI,CAAC,KAAK,gBAAgB;AACxB;MACF;AAEA,WAAK,eAAe,oBAAoB,UAAU,KAAK,YAAY;AACnE,WAAK,eAAe,oBAAoB,UAAU,KAAK,YAAY;AAEnE,WAAK,iBAAiB;;IAGxB,aAAc,OAAO;AACnB,UAAIA;AACJ,YAAM,YAAY,KAAK,aAAa;AACpC,UAAI,KAAK,aAAa,MAAM;AAC1B,QAAAA,UAAS,QAAQ,IAAI,KAAK,MAAM,QAAQ,CAAC,EAAE,cAAc;aACpD;AACL,QAAAA,UAAS,KAAK,MAAM,QAAQ,SAAS,IAAI,KAAK;MAChD;AACA,WAAK,iBAAiBA,OAAM;;IAG9B,iBAAkB,UAAU;AAC1B,YAAM,YAAY,KAAK,cAAc,aACjC,EAAE,QAAQ,aAAa,OAAO,MAAM,IACpC,EAAE,QAAQ,cAAc,OAAO,OAAO;AAE1C,UAAI;AACJ,UAAI;AACJ,UAAI;AAEJ,UAAI,KAAK,UAAU;AACjB,cAAM,aAAa,gBAAgB,KAAK,GAAG;AAE3C,cAAM,YAAY,WAAW,YAAY,SAAS,IAAI,WAAW,UAAU,MAAM;AACjF,cAAM,SAAS,WAAW,sBAAqB;AAE/C,cAAM,WAAW,KAAK,IAAI,sBAAqB;AAC/C,cAAM,mBAAmB,SAAS,UAAU,KAAK,IAAI,OAAO,UAAU,KAAK;AAE3E,mBAAW;AACX,0BAAkB,UAAU;AAC5B,yBAAiB,WAAW,YAAY;aACnC;AACL,mBAAW,KAAK;AAChB,0BAAkB,UAAU;AAC5B,yBAAiB;MACnB;AAEA,eAAS,eAAe,IAAI;;IAG9B,kBAAmB;AACjB,iBAAW,MAAM;AACf,gBAAQ,IAAI,+FAAgG,aAAa,KAAK,GAAG;AACjI,gBAAQ,IAAI,4LAAkM;OAC/M;AACD,YAAM,IAAI,MAAM,8BAA8B;;IAGhD,YAAa;AACX,WAAK,KAAK,KAAK,CAAC,OAAO,UAAU,MAAM,GAAG,QAAQ,MAAM,GAAG,KAAK;;;AAGtE;;;EC7uBM,KAAI;EACJ,OAAM;;;;EAiDN,KAAI;EACJ,OAAM;;;;;sCA/DVoC;IAuEK;IAAA;MArEH,OAAK,eAAA,CAAC,wBAAqB;eACnB,MAAA;qBAAA,OAAA;sBAAA,KAAA,SAAA,EAAA,GAAA;;8DAKS,SAAY,gBAAA,SAAA,aAAA,GAAA,IAAA;;;MAGrB,KAAA,OAAO,UADfC,UAAA,GAAAD;QAQK;QARLE;QAQK;UAHHC,WAEC,KAAA,QAAA,QAAA;;;;;OAGHF,UAAA,GAAAG,YAsCWC,wBArCJ,OAAO,OAAA,GAAA;QACZ,KAAI;QACH,OAAKC,eAAA,EAAA,CAAK,KAAQ,cAAA,aAAA,cAAA,UAAA,GAA8C,MAAA,YAAA,KAAA,CAAA;QACjE,OAAKC,eAAA,CAAC,sCACE,OAAS,SAAA,CAAA;;yBAIf,MAAmB;4BAFrBP;YA0BWQ;YAAA;YAAAC,WAxBM,MAAI,MAAA,CAAZ,SAAA;kCAFTL,YA0BWC,wBAzBJ,OAAO,OAAA,GADdK,WA0BW;gBAvBR,KAAK,KAAK,GAAG;gBACb,OAAO,MAAI,QAAA;yCAAI,KAAA,cAAA,aAAA,MAAA,GAAA,IAAA,KAAA,QAAA,gBAAA,KAAA,cAAA,aAAA,MAAA,GAAA,IAAA,KAAA,MAAA;yBAAA,OAAA,YAAA,GAAA,KAAA,cAAA,aAAA,OAAA,qBAAA,OAAA,WAAA,OAAA,QAAA,OAAA;0BAAA,OAAA,YAAA,GAAA,KAAA,cAAA,eAAA,OAAA,qBAAA,OAAA,WAAA,OAAA,QAAA,OAAA;;gBAKhB,OAAK,CAAC,mCAAgC;kBAC9B,OAAA;;oBAAA,OAAA,CAAA,OAAA,aAAA,MAAA,aAAA,KAAA,GAAA;;;cAMR,GAAAC,WAAM,OAAA,YAAA,CAAA,IAAA;gBAAiB,YAAA,MAAA;AAAA,wBAAA,WAAA,KAAA,GAAA;gBAAA;;AAAA,wBAAA,WAAA;gBAAA;;iCAKvB,MAIC;kBAJDR,WAIC,KAAA,QAAA,WAAA;oBAHE,MAAM,KAAK;oBACX,OAAO,KAAK,GAAG;oBACf,QAAQ,KAAK,GAAG;;;;;;;;;;UAIrBA,WAEC,KAAA,QAAA,OAAA;;;;;MAIK,KAAA,OAAO,SADfF,UAAA,GAAAD;QAQK;QARL;QAQK;UAHHG,WAEC,KAAA,QAAA,OAAA;;;;;MAGHS,YAAwC,2BAAA,EAAvB,UAAQ,SAAY,aAAA,GAAA,MAAA,GAAA,CAAA,UAAA,CAAA;;;;;oCArEf,SAAsB,sBAAA;;;;;ACsChD,IAAA,WAAe;EACb,MAAM;EAEN,YAAY;IACV,iBAAAC;;EAGF,UAAW;AACT,QAAI,OAAO,mBAAmB,aAAa;AACzC,WAAK,mBAAmB,IAAI,eAAe,aAAW;AACpD,8BAAsB,MAAM;AAC1B,cAAI,CAAC,MAAM,QAAQ,OAAO,GAAG;AAC3B;UACF;AACA,qBAAW,SAAS,SAAS;AAC3B,gBAAI,MAAM,UAAU,MAAM,OAAO,eAAe;AAC9C,kBAAI,OAAO;AACX,kBAAI,MAAM,eAAe;AACvB,sBAAM,qBAAqB,MAAM,cAAc,CAAC;AAChD,wBAAQ,mBAAmB;AAC3B,yBAAS,mBAAmB;qBACvB;AAEL,wBAAQ,MAAM,YAAY;AAC1B,yBAAS,MAAM,YAAY;cAC7B;AACA,oBAAM,OAAO,cAAc,MAAM,OAAO,SAAS,OAAO,MAAM;YAChE;UACF;SACD;OACF;IACH;AAEA,WAAO;MACL,aAAa,KAAK;MAClB,eAAe;MACf,uBAAuB,KAAK;IAC9B;;EAGF,cAAc;EAEd,OAAO;IACL,GAAG;IAEH,aAAa;MACX,MAAM,CAAC,QAAQ,MAAM;MACrB,UAAU;;;EAId,OAAO;IACL;IACA;;EAGF,OAAQ;AACN,WAAO;MACL,aAAa;QACX,QAAQ;QACR,OAAO,CAAA;QACP,UAAU,KAAK;QACf,aAAa;;IAEjB;;EAGF,UAAU;IACR;IAEA,gBAAiB;AACf,YAAM,SAAS,CAAA;AACf,YAAM,EAAE,OAAO,UAAU,aAAAzB,aAAY,IAAI;AACzC,YAAM,QAAQ,KAAK,YAAY;AAC/B,YAAM,IAAI,MAAM;AAChB,eAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,cAAM,OAAO,MAAM,CAAC;AACpB,cAAM,KAAKA,eAAc,IAAI,KAAK,QAAQ;AAC1C,YAAI,OAAO,MAAM,EAAE;AACnB,YAAI,OAAO,SAAS,eAAe,CAAC,KAAK,eAAe,EAAE,GAAG;AAC3D,iBAAO;QACT;AACA,eAAO,KAAK;UACV;UACA;UACA;SACD;MACH;AACA,aAAO;;;EAIX,OAAO;IACL,QAAS;AACP,WAAK,YAAW;;IAGlB,aAAa;MACX,QAAS,OAAO;AACd,aAAK,YAAY,cAAc;;MAEjC,WAAW;;IAGb,UAAW,OAAO;AAChB,WAAK,YAAY,IAAI;;IAGvB,cAAe,MAAM,MAAM;AACzB,YAAM,YAAY,KAAK,IAAI;AAK3B,UAAI,gBAAgB;AAAG,UAAI,YAAY;AACvC,YAAM,SAAS,KAAK,IAAI,KAAK,QAAQ,KAAK,MAAM;AAChD,eAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,YAAI,iBAAiB,WAAW;AAC9B;QACF;AACA,yBAAiB,KAAK,CAAC,EAAE,QAAQ,KAAK;AACtC,qBAAa,KAAK,CAAC,EAAE,QAAQ,KAAK;MACpC;AACA,YAAM,SAAS,YAAY;AAE3B,UAAI,WAAW,GAAG;AAChB;MACF;AAEA,WAAK,IAAI,aAAa;;;EAI1B,eAAgB;AACd,SAAK,YAAY,CAAA;AACjB,SAAK,mBAAmB;AACxB,SAAK,iBAAiB,CAAA;AACtB,SAAK,WAAW,gBAAI;;EAGtB,YAAa;AACX,SAAK,YAAY,SAAS;;EAG5B,cAAe;AACb,SAAK,YAAY,SAAS;;EAG5B,YAAa;AACX,SAAK,SAAS,IAAI,MAAK;;EAGzB,SAAS;IACP,mBAAoB;AAClB,YAAM,WAAW,KAAK,MAAM;AAC5B,UAAI,UAAU;AACZ,aAAK,YAAW;MAClB;AACA,WAAK,MAAM,QAAQ;;IAGrB,oBAAqB;AACnB,WAAK,SAAS,KAAK,kBAAkB,EAAE,OAAO,MAAI,CAAG;AACrD,WAAK,MAAM,SAAS;;IAGtB,YAAa,QAAQ,OAAO;AAC1B,UAAI,SAAS,KAAK,aAAa;AAC7B,aAAK,YAAY,QAAQ,CAAA;MAC3B;AACA,WAAK,SAAS,KAAK,kBAAkB,EAAE,OAAO,KAAG,CAAG;;IAGtD,aAAc,OAAO;AACnB,YAAM,WAAW,KAAK,MAAM;AAC5B,UAAI;AAAU,iBAAS,aAAa,KAAK;;IAG3C,YAAa,MAAM,QAAQ,QAAW;AACpC,YAAM,KAAK,KAAK,cAAe,SAAS,OAAO,QAAQ,KAAK,MAAM,QAAQ,IAAI,IAAK,KAAK,KAAK,QAAQ;AACrG,aAAO,KAAK,YAAY,MAAM,EAAE,KAAK;;IAGvC,iBAAkB;AAChB,UAAI,KAAK;AAAqB;AAC9B,WAAK,sBAAsB;AAC3B,YAAM,KAAK,KAAK;AAEhB,WAAK,UAAU,MAAM;AACnB,WAAG,YAAY,GAAG,eAAe;AAEjC,cAAM,KAAK,MAAM;AACf,aAAG,YAAY,GAAG,eAAe;AACjC,gCAAsB,MAAM;AAC1B,eAAG,YAAY,GAAG,eAAe;AACjC,gBAAI,KAAK,qBAAqB,GAAG;AAC/B,mBAAK,sBAAsB;mBACtB;AACL,oCAAsB,EAAE;YAC1B;WACD;QACH;AACA,8BAAsB,EAAE;OACzB;;;AAGP;;;ACrPE,SAAAa,UAAA,GAAAG,YA+BiB,4BA/BjBM,WA+BiB;IA9Bf,KAAI;IACH,OAAO,SAAa;IACpB,iBAAe,OAAW;IAC1B,WAAW,KAAS;IACrB,aAAU;IACT,YAAU,KAAO;IACjB,YAAU,KAAO;KACV,KAAM,QAAA;IACb,UAAQ,SAAgB;IACxB,WAAS,SAAiB;;IAEhB,SAAO,QAChB,CAOC,EAAA,MARyB,cAAc,OAAO,OAAO,MAAA;MACtDP,WAOC,KAAA,QAAA,WAAAW,eAAAC,mBAAA;QANS,MAAA,aAAA;QAAA;QAAA;QAAA;;;IAQD,QAAM,QACf,MAAqB;MAArBZ,WAAqB,KAAA,QAAA,QAAA;;IAEZ,OAAK,QACd,MAAoB;MAApBA,WAAoB,KAAA,QAAA,OAAA;;IAEX,OAAK,QACd,MAAoB;MAApBA,WAAoB,KAAA,QAAA,OAAA;;;;;;;;AC3B1B,IAAAa,UAAe;EACb,MAAM;EAEN,QAAQ;IACN;IACA;IACA;;EAGF,OAAO;;IAEL,MAAM;MACJ,UAAU;;IAGZ,WAAW;MACT,MAAM;MACN,SAAS;;;;;IAMX,QAAQ;MACN,MAAM;MACN,UAAU;;IAGZ,OAAO;MACL,MAAM;MACN,SAAS;;IAGX,kBAAkB;MAChB,MAAM,CAAC,OAAO,MAAM;MACpB,SAAS;;IAGX,YAAY;MACV,MAAM;MACN,SAAS;;IAGX,KAAK;MACH,MAAM;MACN,SAAS;;;EAIb,OAAO;IACL;;EAGF,UAAU;IACR,KAAM;AACJ,UAAI,KAAK,YAAY;AAAa,eAAO,KAAK;AAE9C,UAAI,KAAK,YAAY,YAAY,KAAK;AAAM,eAAO,KAAK,KAAK,KAAK,YAAY,QAAQ;AACtF,YAAM,IAAI,MAAM,aAAa,KAAK,YAAY,QAAQ,iFAAiF;;IAGzI,OAAQ;AACN,aAAO,KAAK,YAAY,MAAM,KAAK,EAAE,KAAK;;IAG5C,cAAe;AACb,aAAO,KAAK,UAAU,KAAK,YAAY;;;EAI3C,OAAO;IACL,WAAW;IAEX,GAAI,OAAO,UAAU;AACnB,WAAK,IAAI,UAAU,KAAK;AACxB,UAAI,CAAC,KAAK,MAAM;AACd,aAAK,aAAY;MACnB;AAEA,UAAI,KAAK,gBAAgB;AAGvB,cAAM,UAAU,KAAK,YAAY,MAAM,QAAQ;AAC/C,cAAM,OAAO,KAAK,YAAY,MAAM,KAAK;AACzC,YAAI,WAAW,QAAQ,YAAY,MAAM;AACvC,eAAK,UAAU,OAAO;QACxB;MACF;;IAGF,YAAa,OAAO;AAClB,UAAI,CAAC,KAAK,MAAM;AACd,YAAI,OAAO;AACT,cAAI,CAAC,KAAK,cAAc,eAAe,KAAK,EAAE,GAAG;AAC/C,iBAAK,cAAc;AACnB,iBAAK,cAAc,eAAe,KAAK,EAAE,IAAI;UAC/C;eACK;AACL,cAAI,KAAK,cAAc,eAAe,KAAK,EAAE,GAAG;AAC9C,iBAAK,cAAc;AACnB,iBAAK,cAAc,eAAe,KAAK,EAAE,IAAI;UAC/C;QACF;MACF;AAEA,UAAI,KAAK,uBAAuB;AAC9B,YAAI,OAAO;AACT,eAAK,YAAW;eACX;AACL,eAAK,cAAa;QACpB;iBACS,SAAS,KAAK,2BAA2B,KAAK,IAAI;AAC3D,aAAK,WAAU;MACjB;;;EAIJ,UAAW;AACT,QAAI,KAAK;AAAW;AAEpB,SAAK,2BAA2B;AAChC,SAAK,gBAAe;AAEpB,QAAI,CAAC,KAAK,uBAAuB;AAC/B,iBAAW,KAAK,KAAK,kBAAkB;AACrC,aAAK,OAAO,MAAM,KAAK,iBAAiB,CAAC,GAAG,KAAK,YAAY;MAC/D;AAEA,WAAK,cAAc,SAAS,GAAG,kBAAkB,KAAK,eAAe;IACvE;;EAGF,UAAW;AACT,QAAI,KAAK,aAAa;AACpB,WAAK,WAAU;AACf,WAAK,YAAW;IAClB;;EAGF,gBAAiB;AACf,SAAK,cAAc,SAAS,IAAI,kBAAkB,KAAK,eAAe;AACtE,SAAK,cAAa;;EAGpB,SAAS;IACP,aAAc;AACZ,UAAI,KAAK,aAAa;AACpB,YAAI,KAAK,wBAAwB,KAAK,IAAI;AACxC,eAAK,sBAAsB,KAAK;AAChC,eAAK,2BAA2B;AAChC,eAAK,yBAAyB;AAC9B,eAAK,YAAY,KAAK,EAAE;QAC1B;aACK;AACL,aAAK,2BAA2B,KAAK;MACvC;;IAGF,kBAAmB;AACjB,UAAI,KAAK,aAAa,CAAC,KAAK,uBAAuB;AACjD,aAAK,cAAc,KAAK,OAAO,QAAQ,MAAM;AAC3C,eAAK,aAAY;QACnB,GAAG;UACD,MAAM;SACP;iBACQ,KAAK,aAAa;AAC3B,aAAK,YAAW;AAChB,aAAK,cAAc;MACrB;;IAGF,gBAAiB,EAAE,MAAM,GAAG;AAE1B,UAAI,CAAC,KAAK,eAAe,OAAO;AAC9B,aAAK,yBAAyB,KAAK;MACrC;AAEA,UAAI,KAAK,6BAA6B,KAAK,MAAM,SAAS,CAAC,KAAK,MAAM;AACpE,aAAK,WAAU;MACjB;;IAGF,eAAgB;AACd,WAAK,WAAU;;IAGjB,YAAa,IAAI;AACf,WAAK,UAAU,MAAM;AACnB,YAAI,KAAK,OAAO,IAAI;AAClB,gBAAM,QAAQ,KAAK,IAAI;AACvB,gBAAM,SAAS,KAAK,IAAI;AACxB,eAAK,iBAAiB,OAAO,MAAM;QACrC;AACA,aAAK,sBAAsB;OAC5B;;IAGH,iBAAkB,OAAO,QAAQ;AAC/B,YAAM,OAAO,CAAC,EAAE,KAAK,cAAc,cAAc,aAAa,SAAS;AACvE,UAAI,QAAQ,KAAK,SAAS,MAAM;AAC9B,aAAK,UAAU,IAAI;MACrB;;IAGF,UAAW,MAAM;AACf,UAAI,KAAK,cAAc,eAAe,KAAK,EAAE,GAAG;AAC9C,aAAK,cAAc;AACnB,aAAK,cAAc,eAAe,KAAK,EAAE,IAAI;MAC/C;AACA,WAAK,YAAY,MAAM,KAAK,EAAE,IAAI;AAClC,UAAI,KAAK;AAAY,aAAK,MAAM,UAAU,KAAK,EAAE;;IAGnD,cAAe;AACb,UAAI,CAAC,KAAK;AAAuB;AACjC,UAAI,KAAK;AAAgB;AACzB,WAAK,sBAAsB,QAAQ,KAAK,GAAG;AAC3C,WAAK,IAAI,UAAU,KAAK;AACxB,WAAK,IAAI,gBAAgB,KAAK;AAC9B,WAAK,iBAAiB;;IAGxB,gBAAiB;AACf,UAAI,CAAC,KAAK;AAAuB;AACjC,UAAI,CAAC,KAAK;AAAgB;AAC1B,WAAK,sBAAsB,UAAU,KAAK,GAAG;AAC7C,WAAK,IAAI,gBAAgB;AACzB,WAAK,iBAAiB;;IAGxB,SAAU,IAAI,OAAO,QAAQ;AAC3B,UAAI,KAAK,OAAO,IAAI;AAClB,aAAK,iBAAiB,OAAO,MAAM;MACrC;;;EAIJ,SAAU;AACR,WAAO,EAAE,KAAK,KAAK,KAAK,OAAO,QAAO,CAAE;;AAE5C;;ACjPe,SAAA,UAEP;AAAA,MAAA,OAAA,UAAA,SAAA,KAAA,UAAA,CAAA,MAAA,SAAA,UAAA,CAAA,IAAJ,CAAA,GAAE,cAAA,KADJC,QAAAA,SAAM,gBAAA,SAAG,SAAAC,IAAE;AAAA,WAAIA,GAAGC,KAAKC;EAAE,IAAA;AAEzB,MAAMC,QAAQC,SAAS,CAAA,CAAE;AAGzB,SAAO;IACLC,MAAQ,SAAA,OAAA;AACN,aAAO;QACLC,SAAS;;;IAIbC,SAAW,SAAA,UAAA;AAAA,UAAA,QAAA;AACT,WAAKC,OAAO;AACZ,UAAI,OAAOT,WAAW,YAAY;AAChC,aAAKU,UAAU,WAAA;AAAA,iBAAMV,OAAOW,KAAK,OAAM,KAAI;QAAC;MAC9C,OAAO;AACL,aAAKD,UAAU,WAAA;AAAA,iBAAM,MAAKV,MAAM;QAAC;MACnC;AACA,WAAKY,OAAO,KAAKF,SAAS;QACxBG,SAAO,SAAA,QAAE9C,OAAO;AAAA,cAAA,SAAA;AACd,eAAK+C,UAAU,WAAM;AACnB,mBAAKL,OAAO1C;UACd,CAAC;;QAEHgD,WAAW;MACb,CAAC;AACD,WAAKC,gBAAe;;IAGtBC,cAAgB,SAAA,eAAA;AACd,WAAKD,gBAAe;;IAGtBE,SAAS;;;;;MAKPC,eAAehB,SAAAA,cAAAA,IAAI;AACjB,YAAMiB,UAAU,KAAKC,SAASd;AAC9B,YAAI,OAAOa,YAAY,YAAY;AACjC,cAAMd,OAAOc,QAAQT,KAAK,MAAM,IAAI;AACpCP,gBAAMD,EAAE,IAAIG;AACZ,eAAKG,OAAON;AACZ,iBAAOG;QACT,OAAO;AACL,gBAAM,IAAIgB,MAAM,qEAAqE;QACvF;;;;;MAMFN,iBAAmB,SAAA,kBAAA;AACjB,YAAMb,KAAK,KAAKO,QAAO;AACvB,YAAIP,MAAM,MAAM;AACdoB,kBAAQC,KAA8CxB,yCAAAA,OAAAA,QAAW,IAAA,CAAA;QACnE;AACA,YAAIG,OAAO,KAAKM,MAAM;AACpB,cAAI,CAACL,MAAMD,EAAE,GAAG;AACd,iBAAKgB,cAAchB,EAAE;UACvB;AACA,eAAKI,UAAUH,MAAMD,EAAE;QACzB;MACF;IACF;;AAEJ;ACzDA,SAASsB,mBAAoBC,KAAKC,QAAQ;AACxCD,MAAIE,UAAS,GAAA,OAAID,QAAM,kBAAA,GAAoB/B,QAAe;AAC1D8B,MAAIE,UAAS,GAAA,OAAID,QAAM,iBAAA,GAAmB/B,QAAe;AACzD8B,MAAIE,UAAS,GAAA,OAAID,QAAM,kBAAA,GAAoBE,QAAe;AAC1DH,MAAIE,UAAS,GAAA,OAAID,QAAM,iBAAA,GAAmBE,QAAe;AACzDH,MAAIE,UAAS,GAAA,OAAID,QAAM,uBAAA,GAAyBG,OAAmB;AACnEJ,MAAIE,UAAS,GAAA,OAAID,QAAM,qBAAA,GAAuBG,OAAmB;AACnE;AAEA,IAAMC,SAAS;;EAEbC,SAASC;EACTC,SAASR,SAAAA,QAAAA,KAAKS,SAAS;AACrB,QAAMC,eAAe5D,OAAO6D,OAAO,CAAA,GAAI;MACrCC,mBAAmB;MACnBC,kBAAkB;OACjBJ,OAAO;AAEV,aAAWK,OAAOJ,cAAc;AAC9B,UAAI,OAAOA,aAAaI,GAAG,MAAM,aAAa;AAC5CC,eAAOD,GAAG,IAAIJ,aAAaI,GAAG;MAChC;IACF;AAEA,QAAIJ,aAAaE,mBAAmB;AAClCb,yBAAmBC,KAAKU,aAAaG,gBAAgB;IACvD;EACF;AACF;", "names": ["getInternetExplorerVersion", "ua", "window", "navigator", "userAgent", "msie", "indexOf", "parseInt", "substring", "trident", "rv", "edge", "_createBlock", "obj", "props", "throttled", "VisibilityState", "all", "Map", "on", "type", "handler", "handlers", "get", "push", "set", "off", "splice", "indexOf", "emit", "evt", "slice", "map", "itemsLimit", "regex", "parents", "node", "ps", "parentNode", "concat", "style", "prop", "getComputedStyle", "getPropertyValue", "overflow", "scroll", "test", "getScrollParent", "HTMLElement", "SVGElement", "i", "length", "document", "scrollingElement", "documentElement", "props", "items", "type", "Array", "required", "keyField", "String", "default", "direction", "validator", "value", "includes", "listTag", "itemTag", "simpleArray", "_typeof", "supportsPassive", "window", "opts", "Object", "defineProperty", "get", "addEventListener", "e", "ResizeObserver", "h", "_createElementBlock", "_openBlock", "_hoisted_1", "_renderSlot", "_createBlock", "_resolveDynamicComponent", "_normalizeStyle", "_normalizeClass", "_Fragment", "_renderList", "_mergeProps", "_toHandlers", "_createVNode", "RecycleScroller", "_normalizeProps", "_guardReactiveProps", "script", "idProp", "vm", "item", "id", "store", "reactive", "data", "idState", "created", "$_id", "$_getId", "call", "$watch", "handler", "$nextTick", "immediate", "$_updateIdState", "beforeUpdate", "methods", "$_idStateInit", "factory", "$options", "Error", "console", "warn", "registerComponents", "app", "prefix", "component", "DynamicScroller", "DynamicScrollerItem", "plugin", "version", "VERSION", "install", "options", "finalOptions", "assign", "installComponents", "componentsPrefix", "key", "config"]}
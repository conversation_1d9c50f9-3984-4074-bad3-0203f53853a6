import {
  computed,
  createApp,
  createBaseVNode,
  createElementBlock,
  defineComponent,
  h,
  nextTick,
  normalizeClass,
  onBeforeUnmount,
  onMounted,
  openBlock,
  reactive,
  ref,
  renderSlot
} from "./chunk-JBQXOB42.js";
import "./chunk-PRH6DGNM.js";

// node_modules/.pnpm/vue3-danmaku@1.6.6_vue@3.5.18_typescript@5.8.3_/node_modules/vue3-danmaku/dist/vue3-danmaku.esm.js
var f = defineComponent({ name: "vue3-danmaku", components: {}, props: { danmus: { type: Array, required: true, default: () => [] }, channels: { type: Number, default: 0 }, autoplay: { type: Boolean, default: true }, loop: { type: Boolean, default: false }, useSlot: { type: Boolean, default: false }, debounce: { type: Number, default: 100 }, speeds: { type: Number, default: 200 }, randomChannel: { type: <PERSON><PERSON><PERSON>, default: false }, fontSize: { type: Number, default: 18 }, top: { type: Number, default: 4 }, right: { type: Number, default: 0 }, isSuspend: { type: Boolean, default: false }, extraStyle: { type: String, default: "" } }, emits: ["list-end", "play-end", "dm-over", "dm-out", "update:danmus"], setup(t, { emit: d, slots: r }) {
  let m = ref(document.createElement("div")), v = ref(document.createElement("div"));
  const c = ref(0), f2 = ref(0);
  let p2 = 0;
  const h2 = ref(0), y = ref(0), g = ref(0), w = ref(false), b = ref(false), x = ref({}), S = function(t2, n, a = "modelValue") {
    return computed({ get: () => t2[a], set: (e) => {
      n(`update:${a}`, e);
    } });
  }(t, d, "danmus"), k = reactive({ channels: computed(() => t.channels || h2.value), autoplay: computed(() => t.autoplay), loop: computed(() => t.loop), useSlot: computed(() => t.useSlot), debounce: computed(() => t.debounce), randomChannel: computed(() => t.randomChannel) }), C = reactive({ height: computed(() => y.value), fontSize: computed(() => t.fontSize), speeds: computed(() => t.speeds), top: computed(() => t.top), right: computed(() => t.right) });
  function L() {
    E(), t.isSuspend && function() {
      let e = [];
      v.value.addEventListener("mouseover", (t2) => {
        let n = t2.target;
        n.className.includes("dm") || (n = n.closest(".dm") || n), n.className.includes("dm") && (e.includes(n) || (d("dm-over", { el: n }), n.classList.add("pause"), e.push(n)));
      }), v.value.addEventListener("mouseout", (t2) => {
        let n = t2.target;
        n.className.includes("dm") || (n = n.closest(".dm") || n), n.className.includes("dm") && (d("dm-out", { el: n }), n.classList.remove("pause"), e.forEach((e2) => {
          e2.classList.remove("pause");
        }), e = []);
      });
    }(), k.autoplay && N();
  }
  function E() {
    if (c.value = m.value.offsetWidth, f2.value = m.value.offsetHeight, 0 === c.value || 0 === f2.value)
      throw new Error("获取不到容器宽高");
  }
  function N() {
    b.value = false, p2 || (p2 = window.setInterval(() => function() {
      if (!b.value && S.value.length)
        if (g.value > S.value.length - 1) {
          const e = v.value.children.length;
          k.loop && (e < g.value && (d("list-end"), g.value = 0), B());
        } else
          B();
    }(), k.debounce));
  }
  function B(e) {
    const n = k.loop ? g.value % S.value.length : g.value, a = e || S.value[n];
    let l = document.createElement("div");
    k.useSlot ? l = function(e2, t2) {
      const n2 = createApp({ render: () => h("div", {}, [r.dm && r.dm({ danmu: e2, index: t2 })]) });
      return n2.mount(document.createElement("div"));
    }(a, n).$el : (l.innerHTML = a, l.setAttribute("style", t.extraStyle), l.style.fontSize = `${C.fontSize}px`, l.style.lineHeight = `${C.fontSize}px`), l.classList.add("dm"), v.value.appendChild(l), l.style.opacity = "0", nextTick(() => {
      C.height || (y.value = l.offsetHeight), k.channels || (h2.value = Math.floor(f2.value / (C.height + C.top)));
      let e2 = function(e3) {
        let t2 = [...Array(k.channels).keys()];
        k.randomChannel && (t2 = t2.sort(() => 0.5 - Math.random()));
        for (let n2 of t2) {
          const t3 = x.value[n2];
          if (!t3 || !t3.length)
            return x.value[n2] = [e3], e3.addEventListener("animationend", () => x.value[n2].splice(0, 1)), n2 % k.channels;
          for (let a2 = 0; a2 < t3.length; a2++) {
            const l2 = $(t3[a2]) - 10;
            if (l2 <= 0.88 * (e3.offsetWidth - t3[a2].offsetWidth) || l2 <= 0)
              break;
            if (a2 === t3.length - 1)
              return x.value[n2].push(e3), e3.addEventListener("animationend", () => x.value[n2].splice(0, 1)), n2 % k.channels;
          }
        }
        return -1;
      }(l);
      if (e2 >= 0) {
        const t2 = l.offsetWidth, a2 = C.height;
        l.classList.add("move"), l.dataset.index = `${n}`, l.dataset.channel = e2.toString(), l.style.opacity = "1", l.style.top = e2 * (a2 + C.top) + "px", l.style.width = t2 + C.right + "px", l.style.setProperty("--dm-scroll-width", `-${c.value + t2}px`), l.style.left = `${c.value}px`, l.style.animationDuration = c.value / C.speeds + "s", l.addEventListener("animationend", () => {
          Number(l.dataset.index) !== S.value.length - 1 || k.loop || d("play-end", l.dataset.index), v.value && v.value.removeChild(l);
        }), g.value++;
      } else
        v.value.removeChild(l);
    });
  }
  function $(e) {
    const t2 = e.offsetWidth || parseInt(e.style.width), n = e.getBoundingClientRect().right || v.value.getBoundingClientRect().right + t2;
    return v.value.getBoundingClientRect().right - n;
  }
  function z() {
    clearInterval(p2), p2 = 0, g.value = 0;
  }
  return onMounted(() => {
    L();
  }), onBeforeUnmount(() => {
    z();
  }), { container: m, dmContainer: v, hidden: w, paused: b, danmuList: S, getPlayState: function() {
    return !b.value;
  }, resize: function() {
    E();
    const e = v.value.getElementsByClassName("dm");
    for (let t2 = 0; t2 < e.length; t2++) {
      const n = e[t2];
      n.style.setProperty("--dm-scroll-width", `-${c.value + n.offsetWidth}px`), n.style.left = `${c.value}px`, n.style.animationDuration = c.value / C.speeds + "s";
    }
  }, play: N, pause: function() {
    b.value = true;
  }, stop: function() {
    x.value = {}, v.value.innerHTML = "", b.value = true, w.value = false, z();
  }, show: function() {
    w.value = false;
  }, hide: function() {
    w.value = true;
  }, reset: function() {
    y.value = 0, L();
  }, add: function(e) {
    if (g.value === S.value.length)
      return S.value.push(e), S.value.length - 1;
    {
      const t2 = g.value % S.value.length;
      return S.value.splice(t2, 0, e), t2 + 1;
    }
  }, push: function(e) {
    return S.value.push(e), S.value.length - 1;
  }, insert: B };
} });
var p = { ref: "container", class: "vue-danmaku" };
!function(e, t) {
  void 0 === t && (t = {});
  var n = t.insertAt;
  if ("undefined" != typeof document) {
    var a = document.head || document.getElementsByTagName("head")[0], l = document.createElement("style");
    l.type = "text/css", "top" === n && a.firstChild ? a.insertBefore(l, a.firstChild) : a.appendChild(l), l.styleSheet ? l.styleSheet.cssText = e : l.appendChild(document.createTextNode(e));
  }
}(".vue-danmaku {\n  position: relative;\n  overflow: hidden;\n}\n.vue-danmaku .danmus {\n  position: absolute;\n  left: 0;\n  top: 0;\n  width: 100%;\n  height: 100%;\n  opacity: 0;\n  -webkit-transition: all 0.3s;\n  transition: all 0.3s;\n}\n.vue-danmaku .danmus.show {\n  opacity: 1;\n}\n.vue-danmaku .danmus.paused .dm.move {\n  animation-play-state: paused;\n}\n.vue-danmaku .danmus .dm {\n  position: absolute;\n  font-size: 20px;\n  color: #ddd;\n  white-space: pre;\n  transform: translateX(0);\n  transform-style: preserve-3d;\n}\n.vue-danmaku .danmus .dm.move {\n  will-change: transform;\n  animation-name: moveLeft;\n  animation-timing-function: linear;\n  animation-play-state: running;\n}\n.vue-danmaku .danmus .dm.pause {\n  animation-play-state: paused;\n  z-index: 100;\n}\n@keyframes moveLeft {\n  from {\n    transform: translateX(0);\n  }\n  to {\n    transform: translateX(var(--dm-scroll-width));\n  }\n}\n@-webkit-keyframes moveLeft {\n  from {\n    -webkit-transform: translateX(0);\n  }\n  to {\n    -webkit-transform: translateX(var(--dm-scroll-width));\n  }\n}"), f.render = function(e, t, n, a, l, u) {
  return openBlock(), createElementBlock("div", p, [createBaseVNode("div", { ref: "dmContainer", class: normalizeClass(["danmus", { show: !e.hidden }, { paused: e.paused }]) }, null, 2), renderSlot(e.$slots, "default")], 512);
}, f.__file = "src/lib/Danmaku.vue";
export {
  f as default
};
//# sourceMappingURL=vue3-danmaku.js.map

import {
  Fragment,
  Teleport,
  computed,
  createBaseVNode,
  createBlock,
  createCommentVNode,
  createElementBlock,
  createVNode,
  defineComponent,
  mergeProps,
  nextTick,
  normalizeClass,
  normalizeStyle,
  onUnmounted,
  openBlock,
  ref,
  render,
  renderList,
  resolveDynamicComponent,
  toDisplayString,
  toHandler<PERSON>ey,
  vShow,
  watch,
  withDirectives,
  withModifiers
} from "./chunk-JBQXOB42.js";
import "./chunk-PRH6DGNM.js";

// node_modules/.pnpm/@howdyjs+mouse-menu@2.1.7_vue@3.5.18_typescript@5.8.3_/node_modules/@howdyjs/mouse-menu/dist/index.es.js
var clone = function(obj) {
  let newObj = Array.isArray(obj) ? [] : {};
  if (obj && typeof obj === "object") {
    for (let key in obj) {
      if (obj.hasOwnProperty(key)) {
        if (obj[key] && typeof obj[key] === "object") {
          newObj[key] = clone(obj[key]);
        } else {
          newObj[key] = obj[key];
        }
      }
    }
  }
  return newObj;
};
var script = defineComponent({
  name: "MouseMenu",
  props: {
    appendToBody: {
      type: Boolean,
      default: true
    },
    menuWidth: {
      type: Number,
      default: 200
    },
    menuList: {
      type: Array,
      required: true
    },
    menuHiddenFn: {
      type: Function
    },
    hasIcon: {
      type: Boolean,
      default: false
    },
    iconType: {
      type: String,
      default: "font-icon"
    },
    menuWrapperCss: Object,
    menuItemCss: Object,
    el: {
      type: Object,
      required: true
    },
    params: {
      type: [String, Number, Array, Object]
    },
    useLongPressInMobile: Boolean,
    longPressDuration: Number,
    longPressPreventDefault: [Function, Boolean],
    injectCloseListener: {
      type: Boolean,
      default: true
    },
    customClass: String,
    disabled: {
      type: Function
    }
  },
  emits: ["open", "close"],
  expose: ["show", "close", "showMenu"],
  setup(props, { emit }) {
    const subLeft = ref(0);
    const subTop = ref(0);
    const hoverFlag = ref(false);
    const menuTop = ref(0);
    const menuLeft = ref(0);
    const showMenu = ref(false);
    const clickDomEl = ref(null);
    const calcMenuList = ref([]);
    const hasSubMenu = computed(() => props.menuList.some((item) => item.children && item.children.length > 0));
    const arrowSize = ref(10);
    const MenuWrapper = ref();
    watch(showMenu, async (val) => {
      var _a, _b;
      if (val) {
        await nextTick();
        let el = MenuWrapper.value;
        if (props.menuWrapperCss) {
          Object.keys(props.menuWrapperCss).map((item) => {
            el.style.setProperty(`--menu-${item}`, props.menuWrapperCss && props.menuWrapperCss[item]);
          });
        }
        if (props.menuItemCss) {
          Object.keys(props.menuItemCss).map((item) => {
            el.style.setProperty(`--menu-item-${item}`, props.menuItemCss && props.menuItemCss[item]);
          });
        }
        let _arrowSize = (_b = (_a = props.menuItemCss) == null ? void 0 : _a.arrowSize) == null ? void 0 : _b.match(/\d+/);
        if (_arrowSize) {
          arrowSize.value = ~~_arrowSize[0] || 10;
        } else {
          arrowSize.value = 10;
        }
        el.style.setProperty("--menu-item-arrowRealSize", arrowSize.value / 2 + "px");
        emit("open", props.params, clickDomEl.value, props.el);
      } else {
        emit("close", props.params, clickDomEl.value, props.el);
      }
    });
    const handleMenuItemClick = (item, $event) => {
      if (item.disabled)
        return;
      if (item.fn && typeof item.fn === "function") {
        const flag = item.fn(props.params, clickDomEl.value, props.el, $event);
        if (flag === false)
          return;
      }
      showMenu.value = false;
    };
    const handleSubMenuItemClick = (subItem, $event) => {
      if (subItem.disabled)
        return;
      if (subItem.fn && typeof subItem.fn === "function" && !subItem.disabled) {
        const flag = subItem.fn(props.params, clickDomEl.value, props.el, $event);
        if (flag === false)
          return;
        hoverFlag.value = false;
      }
      showMenu.value = false;
    };
    const handleMenuMouseEnter = async ($event, item) => {
      if (item.children && !item.disabled) {
        hoverFlag.value = true;
        await nextTick();
        const el = $event.currentTarget;
        if (!el)
          return;
        const { offsetWidth } = el;
        const subEl = el.querySelector(".__menu__sub__wrapper");
        if (!subEl)
          return;
        const { offsetWidth: subOffsetWidth, offsetHeight: subOffsetHeight } = subEl;
        const { innerWidth: windowWidth, innerHeight: windowHeight } = window;
        const { top, left } = el.getBoundingClientRect();
        if (left + offsetWidth + subOffsetWidth > windowWidth - 5) {
          subLeft.value = left - subOffsetWidth + 5;
        } else {
          subLeft.value = left + offsetWidth;
        }
        if (top + subOffsetHeight > windowHeight - 5) {
          subTop.value = windowHeight - subOffsetHeight;
        } else {
          subTop.value = top + 5;
        }
      }
    };
    const formatterFnOption = (list, clickDomEl2, el, params) => {
      return list.map((item) => {
        if (item.children) {
          item.children = formatterFnOption(item.children, clickDomEl2, el, params);
        }
        if (item.label && typeof item.label === "function") {
          item.label = item.label(params, clickDomEl2, el);
        }
        if (item.tips && typeof item.tips === "function") {
          item.tips = item.tips(params, clickDomEl2, el);
        }
        if (item.icon && typeof item.icon === "function") {
          item.icon = item.icon(params, clickDomEl2, el);
        }
        if (item.hidden && typeof item.hidden === "function") {
          item.hidden = item.hidden(params, clickDomEl2, el);
        }
        if (item.disabled && typeof item.disabled === "function") {
          item.disabled = item.disabled(params, clickDomEl2, el);
        }
        return item;
      });
    };
    const show = async (x = 0, y = 0) => {
      clickDomEl.value = document.elementFromPoint(x - 1, y - 1);
      if (props.menuHiddenFn) {
        showMenu.value = !props.menuHiddenFn(props.params, clickDomEl.value, props.el);
      } else {
        showMenu.value = true;
      }
      if (!showMenu.value)
        return;
      calcMenuList.value = clone(props.menuList);
      calcMenuList.value = formatterFnOption(calcMenuList.value, clickDomEl.value, props.el, props.params);
      await nextTick();
      const { innerWidth: windowWidth, innerHeight: windowHeight } = window;
      const menu = MenuWrapper.value;
      const menuHeight = menu.offsetHeight;
      const menuWidth = props.menuWidth || 200;
      menuLeft.value = x + menuWidth + 1 > windowWidth ? windowWidth - menuWidth - 5 : x + 1;
      menuTop.value = y + menuHeight + 1 > windowHeight ? windowHeight - menuHeight - 5 : y + 1;
    };
    const close = () => {
      showMenu.value = false;
    };
    const clickEventKey = computed(() => props.useLongPressInMobile && "ontouchstart" in window ? "touchstart" : "mousedown");
    const listenerFn = (e) => {
      if (MenuWrapper.value && !MenuWrapper.value.contains(e.currentTarget)) {
        showMenu.value = false;
        document.oncontextmenu = null;
      }
    };
    watch(() => props.injectCloseListener, (val) => {
      if (val) {
        document.addEventListener(clickEventKey.value, listenerFn);
      } else {
        document.removeEventListener(clickEventKey.value, listenerFn);
      }
    }, {
      immediate: true
    });
    onUnmounted(() => {
      document.removeEventListener(clickEventKey.value, listenerFn);
    });
    return {
      subLeft,
      subTop,
      hoverFlag,
      menuTop,
      menuLeft,
      showMenu,
      clickDomEl,
      calcMenuList,
      arrowSize,
      hasSubMenu,
      MenuWrapper,
      handleMenuItemClick,
      handleSubMenuItemClick,
      handleMenuMouseEnter,
      show,
      close,
      clickEventKey
    };
  }
});
var _hoisted_1 = ["onMouseenter"];
var _hoisted_2 = {
  key: 0,
  class: "__menu__item-icon"
};
var _hoisted_3 = ["innerHTML"];
var _hoisted_4 = { class: "__menu__item-label" };
var _hoisted_5 = { class: "__menu__item-tips" };
var _hoisted_6 = { class: "__menu__item-arrow-after" };
var _hoisted_7 = {
  key: 0,
  class: "__menu__item-icon"
};
var _hoisted_8 = ["innerHTML"];
var _hoisted_9 = { class: "__menu__sub__item-label" };
var _hoisted_10 = { class: "__menu__sub__item-tips" };
function render2(_ctx, _cache, $props, $setup, $data, $options) {
  return openBlock(), createBlock(Teleport, {
    to: "body",
    disabled: !_ctx.appendToBody
  }, [
    _ctx.showMenu ? (openBlock(), createElementBlock(
      "div",
      {
        key: 0,
        ref: "MenuWrapper",
        class: normalizeClass(["__menu__wrapper", _ctx.customClass]),
        style: normalizeStyle({ width: `${_ctx.menuWidth}px`, top: `${_ctx.menuTop}px`, left: `${_ctx.menuLeft}px` })
      },
      [
        (openBlock(true), createElementBlock(
          Fragment,
          null,
          renderList(_ctx.calcMenuList, (item, index) => {
            return openBlock(), createElementBlock(
              Fragment,
              null,
              [
                !item.hidden && !item.line ? (openBlock(), createElementBlock("div", mergeProps({
                  key: index,
                  class: ["__menu__item", item.disabled && "disabled", item.customClass]
                }, {
                  [toHandlerKey(_ctx.clickEventKey)]: withModifiers(($event) => _ctx.handleMenuItemClick(item, $event), ["stop"])
                }, {
                  onMouseenter: ($event) => _ctx.handleMenuMouseEnter($event, item)
                }), [
                  _ctx.hasIcon ? (openBlock(), createElementBlock("div", _hoisted_2, [
                    _ctx.iconType === "font-icon" ? withDirectives((openBlock(), createElementBlock(
                      "i",
                      {
                        key: 0,
                        class: normalizeClass(item.icon)
                      },
                      null,
                      2
                      /* CLASS */
                    )), [
                      [vShow, item.icon]
                    ]) : _ctx.iconType === "svg-icon" ? withDirectives((openBlock(), createElementBlock("div", {
                      key: 1,
                      class: "__menu__item-icon-svg",
                      innerHTML: item.icon
                    }, null, 8, _hoisted_3)), [
                      [vShow, item.icon]
                    ]) : _ctx.iconType === "vnode-icon" ? (openBlock(), createBlock(resolveDynamicComponent(item.icon), { key: 2 })) : createCommentVNode("v-if", true)
                  ])) : createCommentVNode("v-if", true),
                  createBaseVNode(
                    "span",
                    _hoisted_4,
                    toDisplayString(item.label),
                    1
                    /* TEXT */
                  ),
                  createBaseVNode(
                    "span",
                    _hoisted_5,
                    toDisplayString(item.tips || ""),
                    1
                    /* TEXT */
                  ),
                  _ctx.hasSubMenu ? (openBlock(), createElementBlock(
                    "span",
                    {
                      key: 1,
                      class: normalizeClass(["__menu__item-arrow", { show: _ctx.hasSubMenu && item.children }]),
                      style: normalizeStyle({ width: _ctx.arrowSize + "px", height: _ctx.arrowSize + "px" })
                    },
                    [
                      withDirectives(createBaseVNode(
                        "span",
                        _hoisted_6,
                        null,
                        512
                        /* NEED_PATCH */
                      ), [
                        [vShow, _ctx.hasSubMenu && item.children]
                      ])
                    ],
                    6
                    /* CLASS, STYLE */
                  )) : createCommentVNode("v-if", true),
                  item.children && item.children.length > 0 && !item.disabled ? withDirectives((openBlock(), createElementBlock(
                    "div",
                    {
                      key: 2,
                      class: "__menu__sub__wrapper",
                      style: normalizeStyle({ width: `${_ctx.menuWidth}px`, top: `${_ctx.subTop}px`, left: `${_ctx.subLeft}px` })
                    },
                    [
                      (openBlock(true), createElementBlock(
                        Fragment,
                        null,
                        renderList(item.children, (subItem, subIndex) => {
                          return openBlock(), createElementBlock(
                            Fragment,
                            null,
                            [
                              !subItem.hidden && !subItem.line ? (openBlock(), createElementBlock(
                                "div",
                                mergeProps({
                                  key: subIndex,
                                  class: ["__menu__sub__item", subItem.disabled && "disabled", subItem.customClass]
                                }, {
                                  [toHandlerKey(_ctx.clickEventKey)]: withModifiers(($event) => _ctx.handleSubMenuItemClick(subItem, $event), ["stop"])
                                }),
                                [
                                  _ctx.hasIcon ? (openBlock(), createElementBlock("div", _hoisted_7, [
                                    _ctx.iconType === "font-icon" ? withDirectives((openBlock(), createElementBlock(
                                      "i",
                                      {
                                        key: 0,
                                        class: normalizeClass(subItem.icon)
                                      },
                                      null,
                                      2
                                      /* CLASS */
                                    )), [
                                      [vShow, subItem.icon]
                                    ]) : _ctx.iconType === "svg-icon" ? withDirectives((openBlock(), createElementBlock("div", {
                                      key: 1,
                                      class: "__menu__item-icon-svg",
                                      innerHTML: subItem.icon
                                    }, null, 8, _hoisted_8)), [
                                      [vShow, subItem.icon]
                                    ]) : _ctx.iconType === "vnode-icon" ? (openBlock(), createBlock(resolveDynamicComponent(subItem.icon), { key: 2 })) : createCommentVNode("v-if", true)
                                  ])) : createCommentVNode("v-if", true),
                                  createBaseVNode(
                                    "span",
                                    _hoisted_9,
                                    toDisplayString(subItem.label),
                                    1
                                    /* TEXT */
                                  ),
                                  createBaseVNode(
                                    "span",
                                    _hoisted_10,
                                    toDisplayString(subItem.tips || ""),
                                    1
                                    /* TEXT */
                                  )
                                ],
                                16
                                /* FULL_PROPS */
                              )) : createCommentVNode("v-if", true),
                              subItem.line ? (openBlock(), createElementBlock("div", {
                                key: subIndex,
                                class: "__menu__line"
                              })) : createCommentVNode("v-if", true)
                            ],
                            64
                            /* STABLE_FRAGMENT */
                          );
                        }),
                        256
                        /* UNKEYED_FRAGMENT */
                      ))
                    ],
                    4
                    /* STYLE */
                  )), [
                    [vShow, _ctx.hoverFlag]
                  ]) : createCommentVNode("v-if", true)
                ], 16, _hoisted_1)) : createCommentVNode("v-if", true),
                !item.hidden && item.line ? (openBlock(), createElementBlock("div", {
                  key: index,
                  class: "__menu__line"
                })) : createCommentVNode("v-if", true)
              ],
              64
              /* STABLE_FRAGMENT */
            );
          }),
          256
          /* UNKEYED_FRAGMENT */
        ))
      ],
      6
      /* CLASS, STYLE */
    )) : createCommentVNode("v-if", true)
  ], 8, ["disabled"]);
}
function styleInject(css, ref2) {
  if (ref2 === void 0)
    ref2 = {};
  var insertAt = ref2.insertAt;
  if (!css || typeof document === "undefined") {
    return;
  }
  var head = document.head || document.getElementsByTagName("head")[0];
  var style = document.createElement("style");
  style.type = "text/css";
  if (insertAt === "top") {
    if (head.firstChild) {
      head.insertBefore(style, head.firstChild);
    } else {
      head.appendChild(style);
    }
  } else {
    head.appendChild(style);
  }
  if (style.styleSheet) {
    style.styleSheet.cssText = css;
  } else {
    style.appendChild(document.createTextNode(css));
  }
}
var css_248z = ".__menu__mask[data-v-3d21bc0a] {\n  position: fixed;\n  top: 0;\n  left: 0;\n  bottom: 0;\n  right: 0;\n  z-index: 999;\n}\n.__menu__wrapper[data-v-3d21bc0a] {\n  --menu-background: #c8f2f0;\n  --menu-boxShadow: 0 1px 5px #888;\n  --menu-padding: 5px 0;\n  --menu-borderRadius: 0;\n  --menu-item-height: 30px;\n  --menu-item-padding: 0 10px;\n  --menu-item-iconSize: 20px;\n  --menu-item-iconFontSize: 14px;\n  --menu-item-iconColor: #484852;\n  --menu-item-labelColor: #484852;\n  --menu-item-labelFontSize: 14px;\n  --menu-item-tipsColor: #889;\n  --menu-item-tipsFontSize: 12px;\n  --menu-item-arrowColor: #484852;\n  --menu-item-disabledColor: #bcc;\n  --menu-item-hoverBackground: rgba(255, 255, 255, 0.8);\n  --menu-item-hoverIconColor: inherit;\n  --menu-item-hoverLabelColor: inherit;\n  --menu-item-hoverTipsColor: inherit;\n  --menu-item-hoverArrowColor: inherit;\n  --menu-lineColor: #ccc;\n  --menu-lineMargin: 5px 0;\n}\n.__menu__wrapper[data-v-3d21bc0a] {\n  position: fixed;\n  width: 200px;\n  background: var(--menu-background);\n  box-shadow: var(--menu-boxShadow);\n  padding: var(--menu-padding);\n  border-radius: var(--menu-borderRadius);\n  z-index: 99999;\n}\n.__menu__line[data-v-3d21bc0a],\n.__menu__sub__line[data-v-3d21bc0a] {\n  border-top: 1px solid var(--menu-lineColor);\n  margin: var(--menu-lineMargin);\n}\n.__menu__item[data-v-3d21bc0a],\n.__menu__sub__item[data-v-3d21bc0a] {\n  display: flex;\n  height: var(--menu-item-height);\n  align-items: center;\n  cursor: pointer;\n  padding: var(--menu-item-padding);\n}\n.__menu__item .__menu__item-icon[data-v-3d21bc0a],\n.__menu__sub__item .__menu__item-icon[data-v-3d21bc0a] {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: var(--menu-item-iconColor);\n  width: var(--menu-item-iconSize);\n  height: var(--menu-item-iconSize);\n}\n.__menu__item .__menu__item-icon i[data-v-3d21bc0a],\n.__menu__sub__item .__menu__item-icon i[data-v-3d21bc0a] {\n  font-size: var(--menu-item-iconFontSize);\n}\n.__menu__item .__menu__item-icon .__menu__item-icon-svg[data-v-3d21bc0a],\n.__menu__sub__item .__menu__item-icon .__menu__item-icon-svg[data-v-3d21bc0a] {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 100%;\n  height: 100%;\n}\n.__menu__item .__menu__item-label[data-v-3d21bc0a],\n.__menu__item .__menu__sub__item-label[data-v-3d21bc0a],\n.__menu__sub__item .__menu__item-label[data-v-3d21bc0a],\n.__menu__sub__item .__menu__sub__item-label[data-v-3d21bc0a] {\n  width: 100%;\n  max-height: 100%;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n  flex: 1;\n  font-size: var(--menu-item-labelFontSize);\n  color: var(--menu-item-labelColor);\n  margin-right: 5px;\n  overflow: hidden;\n}\n.__menu__item .__menu__item-tips[data-v-3d21bc0a],\n.__menu__item .__menu__sub__item-tips[data-v-3d21bc0a],\n.__menu__sub__item .__menu__item-tips[data-v-3d21bc0a],\n.__menu__sub__item .__menu__sub__item-tips[data-v-3d21bc0a] {\n  font-size: var(--menu-item-tipsFontSize);\n  color: var(--menu-item-tipsColor);\n}\n.__menu__item .__menu__item-arrow[data-v-3d21bc0a],\n.__menu__sub__item .__menu__item-arrow[data-v-3d21bc0a] {\n  width: 10px;\n  height: 10px;\n  margin-left: 5px;\n  position: relative;\n}\n.__menu__item.disabled[data-v-3d21bc0a],\n.__menu__sub__item.disabled[data-v-3d21bc0a] {\n  cursor: not-allowed;\n}\n.__menu__item.disabled .__menu__item-icon[data-v-3d21bc0a],\n.__menu__item.disabled .__menu__item-label[data-v-3d21bc0a],\n.__menu__item.disabled .__menu__sub__item-label[data-v-3d21bc0a],\n.__menu__item.disabled .__menu__item-tips[data-v-3d21bc0a],\n.__menu__item.disabled .__menu__sub__item-tips[data-v-3d21bc0a],\n.__menu__sub__item.disabled .__menu__item-icon[data-v-3d21bc0a],\n.__menu__sub__item.disabled .__menu__item-label[data-v-3d21bc0a],\n.__menu__sub__item.disabled .__menu__sub__item-label[data-v-3d21bc0a],\n.__menu__sub__item.disabled .__menu__item-tips[data-v-3d21bc0a],\n.__menu__sub__item.disabled .__menu__sub__item-tips[data-v-3d21bc0a] {\n  color: var(--menu-item-disabledColor);\n}\n.__menu__item.disabled .__menu__item-arrow .__menu__item-arrow-after[data-v-3d21bc0a],\n.__menu__sub__item.disabled .__menu__item-arrow .__menu__item-arrow-after[data-v-3d21bc0a] {\n  border-left: var(--menu-item-arrowRealSize) solid var(--menu-item-disabledColor);\n}\n.__menu__item[data-v-3d21bc0a]:not(.disabled):hover {\n  background: var(--menu-item-hoverBackground);\n}\n.__menu__item:not(.disabled):hover .__menu__item-icon[data-v-3d21bc0a] {\n  color: var(--menu-item-hoverIconColor);\n}\n.__menu__item:not(.disabled):hover .__menu__item-label[data-v-3d21bc0a] {\n  color: var(--menu-item-hoverLabelColor);\n}\n.__menu__item:not(.disabled):hover .__menu__item-tips[data-v-3d21bc0a] {\n  color: var(--menu-item-hoverTipsColor);\n}\n.__menu__item:not(.disabled):hover .__menu__item-arrow[data-v-3d21bc0a] {\n  color: var(--menu-item-hoverArrowColor);\n}\n.__menu__sub__item[data-v-3d21bc0a]:not(.disabled):hover {\n  background: var(--menu-item-hoverBackground);\n}\n.__menu__sub__item:not(.disabled):hover .__menu__sub__item-label[data-v-3d21bc0a] {\n  color: var(--menu-item-hoverLabelColor);\n}\n.__menu__sub__item:not(.disabled):hover .__menu__sub__item-tips[data-v-3d21bc0a] {\n  color: var(--menu-item-hoverTipsColor);\n}\n.__menu__item-icon[data-v-3d21bc0a] {\n  width: 20px;\n  height: 20px;\n  text-align: center;\n  line-height: 20px;\n  margin-right: 4px;\n}\n.__menu__item-arrow.show .__menu__item-arrow-after[data-v-3d21bc0a] {\n  position: absolute;\n  width: 0;\n  height: 0;\n  left: 8px;\n  border-left: var(--menu-item-arrowRealSize) solid var(--menu-item-arrowColor);\n  border-top: var(--menu-item-arrowRealSize) solid transparent;\n  border-bottom: var(--menu-item-arrowRealSize) solid transparent;\n}\n.__menu__sub__wrapper[data-v-3d21bc0a] {\n  position: fixed;\n  visibility: hidden;\n  width: 200px;\n  background: var(--menu-background);\n  box-shadow: var(--menu-boxShadow);\n  padding: var(--menu-padding);\n  border-radius: var(--menu-borderRadius);\n}\n.__menu__item:hover .__menu__sub__wrapper[data-v-3d21bc0a] {\n  visibility: visible;\n}";
styleInject(css_248z);
script.render = render2;
script.__scopeId = "data-v-3d21bc0a";
script.__file = "packages/mouse-menu/mouse-menu.vue";
function createClassDom(tag, className, innerText) {
  let el = document.createElement(tag);
  el.setAttribute("class", className);
  if (innerText)
    el.innerText = innerText;
  return el;
}
script.install = (app) => {
  app.component(script.name, script);
};
function CustomMouseMenu(options) {
  var _a;
  const className = "__mouse__menu__container";
  let container;
  if (document.querySelector(`.${className}`)) {
    container = document.querySelector(`.${className}`);
  } else {
    container = createClassDom("div", className);
  }
  const vm = createVNode(script, options);
  render(vm, container);
  document.body.appendChild(container);
  return (_a = vm.component) == null ? void 0 : _a.proxy;
}
var MouseMenuCtx;
var longPressTimer;
var longPressTouchStart;
var longPressTouchEnd;
function addLongPressListener(el, fn, longPressDuration = 500, longPressPreventDefault) {
  longPressTouchStart = (e) => {
    MouseMenuCtx && MouseMenuCtx.close();
    if (typeof longPressPreventDefault === "function") {
      if (longPressPreventDefault(e, el)) {
        e.preventDefault();
      }
    } else if (typeof longPressPreventDefault === "boolean") {
      if (longPressPreventDefault) {
        e.preventDefault();
      }
    }
    if (longPressTimer)
      clearTimeout(longPressTimer);
    longPressTimer = window.setTimeout(() => {
      fn(e);
    }, longPressDuration);
  };
  longPressTouchEnd = () => {
    clearTimeout(longPressTimer);
  };
  el.addEventListener("touchstart", longPressTouchStart);
  el.addEventListener("touchmove", longPressTouchEnd);
  el.addEventListener("touchend", longPressTouchEnd);
  el.addEventListener("touchcancel", longPressTouchEnd);
}
function removeLongPressListener(el) {
  el.removeEventListener("touchstart", longPressTouchStart);
  el.removeEventListener("touchmove", longPressTouchEnd);
  el.removeEventListener("touchend", longPressTouchEnd);
  el.removeEventListener("touchcancel", longPressTouchEnd);
}
var contextMenuEvent;
var longPressEvent;
var mounted = (el, binding) => {
  const { value } = binding;
  if (value.menuList.length > 0) {
    contextMenuEvent = (e) => {
      if (typeof value.disabled === "function" && value.disabled(value.params))
        return;
      e.preventDefault();
      MouseMenuCtx = CustomMouseMenu({
        el,
        ...value
      });
      const { x, y } = e;
      MouseMenuCtx.show(x, y);
    };
    el.removeEventListener("contextmenu", contextMenuEvent);
    el.addEventListener("contextmenu", contextMenuEvent);
    if (value.useLongPressInMobile && "ontouchstart" in window) {
      longPressEvent = (e) => {
        if (typeof value.disabled === "function" && value.disabled(value.params))
          return;
        MouseMenuCtx = CustomMouseMenu({
          el,
          ...value
        });
        const { touches } = e;
        const { clientX, clientY } = touches[0];
        MouseMenuCtx.show(clientX, clientY);
        document.onmousedown = null;
        el.onmousedown = null;
        setTimeout(() => {
          document.onmousedown = () => MouseMenuCtx.close();
          el.onmousedown = () => MouseMenuCtx.close();
        }, 500);
      };
      removeLongPressListener(el);
      addLongPressListener(
        el,
        longPressEvent,
        value.longPressDuration || 500,
        value.longPressPreventDefault
      );
    }
  } else {
    throw new Error("At least set one menu list!");
  }
};
var unmounted = (el) => {
  el.removeEventListener("contextmenu", contextMenuEvent);
  if ("touchstart" in window) {
    removeLongPressListener(el);
  }
};
var MouseMenuDirective = {
  mounted,
  unmounted
};
export {
  CustomMouseMenu,
  MouseMenuCtx,
  MouseMenuDirective,
  script as default
};
//# sourceMappingURL=@howdyjs_mouse-menu.js.map

// node_modules/.pnpm/plus-pro-components@0.1.26__58f148a1d363315c5590851fc69d8998/node_modules/plus-pro-components/es/locale/lang/en.mjs
var English = {
  name: "en",
  plus: {
    dialog: {
      confirmText: "Yes",
      cancelText: "No",
      title: "Dialog"
    },
    datepicker: {
      startPlaceholder: "Please select start time",
      endPlaceholder: "Please select end time"
    },
    dialogForm: {
      title: "Dialog form"
    },
    drawerForm: {
      title: "Drawer form",
      confirmText: "Yes",
      cancelText: "No"
    },
    form: {
      submitText: "Submit",
      resetText: "Reset",
      errorTip: "Please complete the form and submit again!"
    },
    field: {
      pleaseEnter: "Please enter ",
      pleaseSelect: "Please select "
    },
    popover: {
      confirmText: "Yes",
      cancelText: "No"
    },
    search: {
      searchText: "Search",
      resetText: "Reset",
      expand: "Expand",
      retract: "Retract"
    },
    table: {
      title: "Table",
      density: "Density",
      refresh: "Refresh",
      columnSettings: "Column settings",
      selectAll: "Select all",
      default: "Default",
      loose: "Loose",
      compact: "Compact",
      action: "Action",
      more: "More",
      confirmToPerformThisOperation: "Confirm to perform this operation?",
      prompt: "Prompt",
      sort: "Sort",
      resetText: "Reset"
    },
    stepsForm: {
      nextText: "Next step",
      preText: "Previous step",
      submitText: "Submit"
    },
    inputTag: {
      placeholder: "Please enter keywords and press enter or space key"
    },
    header: {
      logout: "logout"
    }
  }
};

export {
  English
};
//# sourceMappingURL=chunk-2U7NUUN5.js.map

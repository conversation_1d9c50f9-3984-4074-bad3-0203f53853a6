{"version": 3, "sources": ["../../.pnpm/codemirror@5.65.19/node_modules/codemirror/addon/fold/foldcode.js", "../../.pnpm/codemirror@5.65.19/node_modules/codemirror/addon/fold/foldgutter.js", "../../.pnpm/codemirror@5.65.19/node_modules/codemirror/addon/fold/brace-fold.js", "../../.pnpm/codemirror@5.65.19/node_modules/codemirror/addon/selection/active-line.js", "../../.pnpm/codemirror@5.65.19/node_modules/codemirror/addon/merge/merge.js", "../../.pnpm/diff-match-patch@1.0.5/node_modules/diff-match-patch/index.js", "../../.pnpm/codemirror@5.65.19/node_modules/codemirror/addon/mode/simple.js", "../../.pnpm/codemirror-editor-vue3@2.8._1ed1da616f7b7611da7216c3164f14f1/node_modules/codemirror-editor-vue3/dist/codemirror-editor-vue3.js"], "sourcesContent": ["// CodeMirror, copyright (c) by <PERSON><PERSON> and others\n// Distributed under an MIT license: https://codemirror.net/5/LICENSE\n\n(function(mod) {\n  if (typeof exports == \"object\" && typeof module == \"object\") // CommonJS\n    mod(require(\"../../lib/codemirror\"));\n  else if (typeof define == \"function\" && define.amd) // AMD\n    define([\"../../lib/codemirror\"], mod);\n  else // Plain browser env\n    mod(CodeMirror);\n})(function(CodeMirror) {\n  \"use strict\";\n\n  function doFold(cm, pos, options, force) {\n    if (options && options.call) {\n      var finder = options;\n      options = null;\n    } else {\n      var finder = getOption(cm, options, \"rangeFinder\");\n    }\n    if (typeof pos == \"number\") pos = CodeMirror.Pos(pos, 0);\n    var minSize = getOption(cm, options, \"minFoldSize\");\n\n    function getRange(allowFolded) {\n      var range = finder(cm, pos);\n      if (!range || range.to.line - range.from.line < minSize) return null;\n      if (force === \"fold\") return range;\n\n      var marks = cm.findMarksAt(range.from);\n      for (var i = 0; i < marks.length; ++i) {\n        if (marks[i].__isFold) {\n          if (!allowFolded) return null;\n          range.cleared = true;\n          marks[i].clear();\n        }\n      }\n      return range;\n    }\n\n    var range = getRange(true);\n    if (getOption(cm, options, \"scanUp\")) while (!range && pos.line > cm.firstLine()) {\n      pos = CodeMirror.Pos(pos.line - 1, 0);\n      range = getRange(false);\n    }\n    if (!range || range.cleared || force === \"unfold\") return;\n\n    var myWidget = makeWidget(cm, options, range);\n    CodeMirror.on(myWidget, \"mousedown\", function(e) {\n      myRange.clear();\n      CodeMirror.e_preventDefault(e);\n    });\n    var myRange = cm.markText(range.from, range.to, {\n      replacedWith: myWidget,\n      clearOnEnter: getOption(cm, options, \"clearOnEnter\"),\n      __isFold: true\n    });\n    myRange.on(\"clear\", function(from, to) {\n      CodeMirror.signal(cm, \"unfold\", cm, from, to);\n    });\n    CodeMirror.signal(cm, \"fold\", cm, range.from, range.to);\n  }\n\n  function makeWidget(cm, options, range) {\n    var widget = getOption(cm, options, \"widget\");\n\n    if (typeof widget == \"function\") {\n      widget = widget(range.from, range.to);\n    }\n\n    if (typeof widget == \"string\") {\n      var text = document.createTextNode(widget);\n      widget = document.createElement(\"span\");\n      widget.appendChild(text);\n      widget.className = \"CodeMirror-foldmarker\";\n    } else if (widget) {\n      widget = widget.cloneNode(true)\n    }\n    return widget;\n  }\n\n  // Clumsy backwards-compatible interface\n  CodeMirror.newFoldFunction = function(rangeFinder, widget) {\n    return function(cm, pos) { doFold(cm, pos, {rangeFinder: rangeFinder, widget: widget}); };\n  };\n\n  // New-style interface\n  CodeMirror.defineExtension(\"foldCode\", function(pos, options, force) {\n    doFold(this, pos, options, force);\n  });\n\n  CodeMirror.defineExtension(\"isFolded\", function(pos) {\n    var marks = this.findMarksAt(pos);\n    for (var i = 0; i < marks.length; ++i)\n      if (marks[i].__isFold) return true;\n  });\n\n  CodeMirror.commands.toggleFold = function(cm) {\n    cm.foldCode(cm.getCursor());\n  };\n  CodeMirror.commands.fold = function(cm) {\n    cm.foldCode(cm.getCursor(), null, \"fold\");\n  };\n  CodeMirror.commands.unfold = function(cm) {\n    cm.foldCode(cm.getCursor(), { scanUp: false }, \"unfold\");\n  };\n  CodeMirror.commands.foldAll = function(cm) {\n    cm.operation(function() {\n      for (var i = cm.firstLine(), e = cm.lastLine(); i <= e; i++)\n        cm.foldCode(CodeMirror.Pos(i, 0), { scanUp: false }, \"fold\");\n    });\n  };\n  CodeMirror.commands.unfoldAll = function(cm) {\n    cm.operation(function() {\n      for (var i = cm.firstLine(), e = cm.lastLine(); i <= e; i++)\n        cm.foldCode(CodeMirror.Pos(i, 0), { scanUp: false }, \"unfold\");\n    });\n  };\n\n  CodeMirror.registerHelper(\"fold\", \"combine\", function() {\n    var funcs = Array.prototype.slice.call(arguments, 0);\n    return function(cm, start) {\n      for (var i = 0; i < funcs.length; ++i) {\n        var found = funcs[i](cm, start);\n        if (found) return found;\n      }\n    };\n  });\n\n  CodeMirror.registerHelper(\"fold\", \"auto\", function(cm, start) {\n    var helpers = cm.getHelpers(start, \"fold\");\n    for (var i = 0; i < helpers.length; i++) {\n      var cur = helpers[i](cm, start);\n      if (cur) return cur;\n    }\n  });\n\n  var defaultOptions = {\n    rangeFinder: CodeMirror.fold.auto,\n    widget: \"\\u2194\",\n    minFoldSize: 0,\n    scanUp: false,\n    clearOnEnter: true\n  };\n\n  CodeMirror.defineOption(\"foldOptions\", null);\n\n  function getOption(cm, options, name) {\n    if (options && options[name] !== undefined)\n      return options[name];\n    var editorOptions = cm.options.foldOptions;\n    if (editorOptions && editorOptions[name] !== undefined)\n      return editorOptions[name];\n    return defaultOptions[name];\n  }\n\n  CodeMirror.defineExtension(\"foldOption\", function(options, name) {\n    return getOption(this, options, name);\n  });\n});\n", "// CodeMirror, copyright (c) by <PERSON><PERSON> and others\n// Distributed under an MIT license: https://codemirror.net/5/LICENSE\n\n(function(mod) {\n  if (typeof exports == \"object\" && typeof module == \"object\") // CommonJS\n    mod(require(\"../../lib/codemirror\"), require(\"./foldcode\"));\n  else if (typeof define == \"function\" && define.amd) // AMD\n    define([\"../../lib/codemirror\", \"./foldcode\"], mod);\n  else // Plain browser env\n    mod(CodeMirror);\n})(function(CodeMirror) {\n  \"use strict\";\n\n  CodeMirror.defineOption(\"foldGutter\", false, function(cm, val, old) {\n    if (old && old != CodeMirror.Init) {\n      cm.clearGutter(cm.state.foldGutter.options.gutter);\n      cm.state.foldGutter = null;\n      cm.off(\"gutterClick\", onGutterClick);\n      cm.off(\"changes\", onChange);\n      cm.off(\"viewportChange\", onViewportChange);\n      cm.off(\"fold\", onFold);\n      cm.off(\"unfold\", onFold);\n      cm.off(\"swapDoc\", onChange);\n      cm.off(\"optionChange\", optionChange);\n    }\n    if (val) {\n      cm.state.foldGutter = new State(parseOptions(val));\n      updateInViewport(cm);\n      cm.on(\"gutterClick\", onGutterClick);\n      cm.on(\"changes\", onChange);\n      cm.on(\"viewportChange\", onViewportChange);\n      cm.on(\"fold\", onFold);\n      cm.on(\"unfold\", onFold);\n      cm.on(\"swapDoc\", onChange);\n      cm.on(\"optionChange\", optionChange);\n    }\n  });\n\n  var Pos = CodeMirror.Pos;\n\n  function State(options) {\n    this.options = options;\n    this.from = this.to = 0;\n  }\n\n  function parseOptions(opts) {\n    if (opts === true) opts = {};\n    if (opts.gutter == null) opts.gutter = \"CodeMirror-foldgutter\";\n    if (opts.indicatorOpen == null) opts.indicatorOpen = \"CodeMirror-foldgutter-open\";\n    if (opts.indicatorFolded == null) opts.indicatorFolded = \"CodeMirror-foldgutter-folded\";\n    return opts;\n  }\n\n  function isFolded(cm, line) {\n    var marks = cm.findMarks(Pos(line, 0), Pos(line + 1, 0));\n    for (var i = 0; i < marks.length; ++i) {\n      if (marks[i].__isFold) {\n        var fromPos = marks[i].find(-1);\n        if (fromPos && fromPos.line === line)\n          return marks[i];\n      }\n    }\n  }\n\n  function marker(spec) {\n    if (typeof spec == \"string\") {\n      var elt = document.createElement(\"div\");\n      elt.className = spec + \" CodeMirror-guttermarker-subtle\";\n      return elt;\n    } else {\n      return spec.cloneNode(true);\n    }\n  }\n\n  function updateFoldInfo(cm, from, to) {\n    var opts = cm.state.foldGutter.options, cur = from - 1;\n    var minSize = cm.foldOption(opts, \"minFoldSize\");\n    var func = cm.foldOption(opts, \"rangeFinder\");\n    // we can reuse the built-in indicator element if its className matches the new state\n    var clsFolded = typeof opts.indicatorFolded == \"string\" && classTest(opts.indicatorFolded);\n    var clsOpen = typeof opts.indicatorOpen == \"string\" && classTest(opts.indicatorOpen);\n    cm.eachLine(from, to, function(line) {\n      ++cur;\n      var mark = null;\n      var old = line.gutterMarkers;\n      if (old) old = old[opts.gutter];\n      if (isFolded(cm, cur)) {\n        if (clsFolded && old && clsFolded.test(old.className)) return;\n        mark = marker(opts.indicatorFolded);\n      } else {\n        var pos = Pos(cur, 0);\n        var range = func && func(cm, pos);\n        if (range && range.to.line - range.from.line >= minSize) {\n          if (clsOpen && old && clsOpen.test(old.className)) return;\n          mark = marker(opts.indicatorOpen);\n        }\n      }\n      if (!mark && !old) return;\n      cm.setGutterMarker(line, opts.gutter, mark);\n    });\n  }\n\n  // copied from CodeMirror/src/util/dom.js\n  function classTest(cls) { return new RegExp(\"(^|\\\\s)\" + cls + \"(?:$|\\\\s)\\\\s*\") }\n\n  function updateInViewport(cm) {\n    var vp = cm.getViewport(), state = cm.state.foldGutter;\n    if (!state) return;\n    cm.operation(function() {\n      updateFoldInfo(cm, vp.from, vp.to);\n    });\n    state.from = vp.from; state.to = vp.to;\n  }\n\n  function onGutterClick(cm, line, gutter) {\n    var state = cm.state.foldGutter;\n    if (!state) return;\n    var opts = state.options;\n    if (gutter != opts.gutter) return;\n    var folded = isFolded(cm, line);\n    if (folded) folded.clear();\n    else cm.foldCode(Pos(line, 0), opts);\n  }\n\n  function optionChange(cm, option) {\n    if (option == \"mode\") onChange(cm)\n  }\n\n  function onChange(cm) {\n    var state = cm.state.foldGutter;\n    if (!state) return;\n    var opts = state.options;\n    state.from = state.to = 0;\n    clearTimeout(state.changeUpdate);\n    state.changeUpdate = setTimeout(function() { updateInViewport(cm); }, opts.foldOnChangeTimeSpan || 600);\n  }\n\n  function onViewportChange(cm) {\n    var state = cm.state.foldGutter;\n    if (!state) return;\n    var opts = state.options;\n    clearTimeout(state.changeUpdate);\n    state.changeUpdate = setTimeout(function() {\n      var vp = cm.getViewport();\n      if (state.from == state.to || vp.from - state.to > 20 || state.from - vp.to > 20) {\n        updateInViewport(cm);\n      } else {\n        cm.operation(function() {\n          if (vp.from < state.from) {\n            updateFoldInfo(cm, vp.from, state.from);\n            state.from = vp.from;\n          }\n          if (vp.to > state.to) {\n            updateFoldInfo(cm, state.to, vp.to);\n            state.to = vp.to;\n          }\n        });\n      }\n    }, opts.updateViewportTimeSpan || 400);\n  }\n\n  function onFold(cm, from) {\n    var state = cm.state.foldGutter;\n    if (!state) return;\n    var line = from.line;\n    if (line >= state.from && line < state.to)\n      updateFoldInfo(cm, line, line + 1);\n  }\n});\n", "// CodeMirror, copyright (c) by <PERSON><PERSON> and others\n// Distributed under an MIT license: https://codemirror.net/5/LICENSE\n\n(function(mod) {\n  if (typeof exports == \"object\" && typeof module == \"object\") // CommonJS\n    mod(require(\"../../lib/codemirror\"));\n  else if (typeof define == \"function\" && define.amd) // AMD\n    define([\"../../lib/codemirror\"], mod);\n  else // Plain browser env\n    mod(CodeMirror);\n})(function(CodeMirror) {\n\"use strict\";\n\nfunction bracketFolding(pairs) {\n  return function(cm, start) {\n    var line = start.line, lineText = cm.getLine(line);\n\n    function findOpening(pair) {\n      var tokenType;\n      for (var at = start.ch, pass = 0;;) {\n        var found = at <= 0 ? -1 : lineText.lastIndexOf(pair[0], at - 1);\n        if (found == -1) {\n          if (pass == 1) break;\n          pass = 1;\n          at = lineText.length;\n          continue;\n        }\n        if (pass == 1 && found < start.ch) break;\n        tokenType = cm.getTokenTypeAt(CodeMirror.Pos(line, found + 1));\n        if (!/^(comment|string)/.test(tokenType)) return {ch: found + 1, tokenType: tokenType, pair: pair};\n        at = found - 1;\n      }\n    }\n\n    function findRange(found) {\n      var count = 1, lastLine = cm.lastLine(), end, startCh = found.ch, endCh\n      outer: for (var i = line; i <= lastLine; ++i) {\n        var text = cm.getLine(i), pos = i == line ? startCh : 0;\n        for (;;) {\n          var nextOpen = text.indexOf(found.pair[0], pos), nextClose = text.indexOf(found.pair[1], pos);\n          if (nextOpen < 0) nextOpen = text.length;\n          if (nextClose < 0) nextClose = text.length;\n          pos = Math.min(nextOpen, nextClose);\n          if (pos == text.length) break;\n          if (cm.getTokenTypeAt(CodeMirror.Pos(i, pos + 1)) == found.tokenType) {\n            if (pos == nextOpen) ++count;\n            else if (!--count) { end = i; endCh = pos; break outer; }\n          }\n          ++pos;\n        }\n      }\n\n      if (end == null || line == end) return null\n      return {from: CodeMirror.Pos(line, startCh),\n              to: CodeMirror.Pos(end, endCh)};\n    }\n\n    var found = []\n    for (var i = 0; i < pairs.length; i++) {\n      var open = findOpening(pairs[i])\n      if (open) found.push(open)\n    }\n    found.sort(function(a, b) { return a.ch - b.ch })\n    for (var i = 0; i < found.length; i++) {\n      var range = findRange(found[i])\n      if (range) return range\n    }\n    return null\n  }\n}\n\nCodeMirror.registerHelper(\"fold\", \"brace\", bracketFolding([[\"{\", \"}\"], [\"[\", \"]\"]]));\n\nCodeMirror.registerHelper(\"fold\", \"brace-paren\", bracketFolding([[\"{\", \"}\"], [\"[\", \"]\"], [\"(\", \")\"]]));\n\nCodeMirror.registerHelper(\"fold\", \"import\", function(cm, start) {\n  function hasImport(line) {\n    if (line < cm.firstLine() || line > cm.lastLine()) return null;\n    var start = cm.getTokenAt(CodeMirror.Pos(line, 1));\n    if (!/\\S/.test(start.string)) start = cm.getTokenAt(CodeMirror.Pos(line, start.end + 1));\n    if (start.type != \"keyword\" || start.string != \"import\") return null;\n    // Now find closing semicolon, return its position\n    for (var i = line, e = Math.min(cm.lastLine(), line + 10); i <= e; ++i) {\n      var text = cm.getLine(i), semi = text.indexOf(\";\");\n      if (semi != -1) return {startCh: start.end, end: CodeMirror.Pos(i, semi)};\n    }\n  }\n\n  var startLine = start.line, has = hasImport(startLine), prev;\n  if (!has || hasImport(startLine - 1) || ((prev = hasImport(startLine - 2)) && prev.end.line == startLine - 1))\n    return null;\n  for (var end = has.end;;) {\n    var next = hasImport(end.line + 1);\n    if (next == null) break;\n    end = next.end;\n  }\n  return {from: cm.clipPos(CodeMirror.Pos(startLine, has.startCh + 1)), to: end};\n});\n\nCodeMirror.registerHelper(\"fold\", \"include\", function(cm, start) {\n  function hasInclude(line) {\n    if (line < cm.firstLine() || line > cm.lastLine()) return null;\n    var start = cm.getTokenAt(CodeMirror.Pos(line, 1));\n    if (!/\\S/.test(start.string)) start = cm.getTokenAt(CodeMirror.Pos(line, start.end + 1));\n    if (start.type == \"meta\" && start.string.slice(0, 8) == \"#include\") return start.start + 8;\n  }\n\n  var startLine = start.line, has = hasInclude(startLine);\n  if (has == null || hasInclude(startLine - 1) != null) return null;\n  for (var end = startLine;;) {\n    var next = hasInclude(end + 1);\n    if (next == null) break;\n    ++end;\n  }\n  return {from: CodeMirror.Pos(startLine, has + 1),\n          to: cm.clipPos(CodeMirror.Pos(end))};\n});\n\n});\n", "// CodeMirror, copyright (c) by <PERSON><PERSON> and others\n// Distributed under an MIT license: https://codemirror.net/5/LICENSE\n\n(function(mod) {\n  if (typeof exports == \"object\" && typeof module == \"object\") // CommonJS\n    mod(require(\"../../lib/codemirror\"));\n  else if (typeof define == \"function\" && define.amd) // AMD\n    define([\"../../lib/codemirror\"], mod);\n  else // Plain browser env\n    mod(CodeMirror);\n})(function(CodeMirror) {\n  \"use strict\";\n  var WRAP_CLASS = \"CodeMirror-activeline\";\n  var BACK_CLASS = \"CodeMirror-activeline-background\";\n  var GUTT_CLASS = \"CodeMirror-activeline-gutter\";\n\n  CodeMirror.defineOption(\"styleActiveLine\", false, function(cm, val, old) {\n    var prev = old == CodeMirror.Init ? false : old;\n    if (val == prev) return\n    if (prev) {\n      cm.off(\"beforeSelectionChange\", selectionChange);\n      clearActiveLines(cm);\n      delete cm.state.activeLines;\n    }\n    if (val) {\n      cm.state.activeLines = [];\n      updateActiveLines(cm, cm.listSelections());\n      cm.on(\"beforeSelectionChange\", selectionChange);\n    }\n  });\n\n  function clearActiveLines(cm) {\n    for (var i = 0; i < cm.state.activeLines.length; i++) {\n      cm.removeLineClass(cm.state.activeLines[i], \"wrap\", WRAP_CLASS);\n      cm.removeLineClass(cm.state.activeLines[i], \"background\", BACK_CLASS);\n      cm.removeLineClass(cm.state.activeLines[i], \"gutter\", GUTT_CLASS);\n    }\n  }\n\n  function sameArray(a, b) {\n    if (a.length != b.length) return false;\n    for (var i = 0; i < a.length; i++)\n      if (a[i] != b[i]) return false;\n    return true;\n  }\n\n  function updateActiveLines(cm, ranges) {\n    var active = [];\n    for (var i = 0; i < ranges.length; i++) {\n      var range = ranges[i];\n      var option = cm.getOption(\"styleActiveLine\");\n      if (typeof option == \"object\" && option.nonEmpty ? range.anchor.line != range.head.line : !range.empty())\n        continue\n      var line = cm.getLineHandleVisualStart(range.head.line);\n      if (active[active.length - 1] != line) active.push(line);\n    }\n    if (sameArray(cm.state.activeLines, active)) return;\n    cm.operation(function() {\n      clearActiveLines(cm);\n      for (var i = 0; i < active.length; i++) {\n        cm.addLineClass(active[i], \"wrap\", WRAP_CLASS);\n        cm.addLineClass(active[i], \"background\", BACK_CLASS);\n        cm.addLineClass(active[i], \"gutter\", GUTT_CLASS);\n      }\n      cm.state.activeLines = active;\n    });\n  }\n\n  function selectionChange(cm, sel) {\n    updateActiveLines(cm, sel.ranges);\n  }\n});\n", "// CodeMirror, copyright (c) by <PERSON><PERSON> and others\n// Distributed under an MIT license: https://codemirror.net/5/LICENSE\n\n// declare global: diff_match_patch, DIFF_INSERT, DIFF_DELETE, DIFF_EQUAL\n\n(function(mod) {\n  if (typeof exports == \"object\" && typeof module == \"object\") // CommonJS\n    mod(require(\"../../lib/codemirror\")); // Note non-packaged dependency diff_match_patch\n  else if (typeof define == \"function\" && define.amd) // AMD\n    define([\"../../lib/codemirror\", \"diff_match_patch\"], mod);\n  else // Plain browser env\n    mod(CodeMirror);\n})(function(CodeMirror) {\n  \"use strict\";\n  var Pos = CodeMirror.Pos;\n  var svgNS = \"http://www.w3.org/2000/svg\";\n\n  function DiffView(mv, type) {\n    this.mv = mv;\n    this.type = type;\n    this.classes = type == \"left\"\n      ? {chunk: \"CodeMirror-merge-l-chunk\",\n         start: \"CodeMirror-merge-l-chunk-start\",\n         end: \"CodeMirror-merge-l-chunk-end\",\n         insert: \"CodeMirror-merge-l-inserted\",\n         del: \"CodeMirror-merge-l-deleted\",\n         connect: \"CodeMirror-merge-l-connect\"}\n      : {chunk: \"CodeMirror-merge-r-chunk\",\n         start: \"CodeMirror-merge-r-chunk-start\",\n         end: \"CodeMirror-merge-r-chunk-end\",\n         insert: \"CodeMirror-merge-r-inserted\",\n         del: \"CodeMirror-merge-r-deleted\",\n         connect: \"CodeMirror-merge-r-connect\"};\n  }\n\n  DiffView.prototype = {\n    constructor: DiffView,\n    init: function(pane, orig, options) {\n      this.edit = this.mv.edit;\n      ;(this.edit.state.diffViews || (this.edit.state.diffViews = [])).push(this);\n      this.orig = CodeMirror(pane, copyObj({value: orig, readOnly: !this.mv.options.allowEditingOriginals}, copyObj(options)));\n      if (this.mv.options.connect == \"align\") {\n        if (!this.edit.state.trackAlignable) this.edit.state.trackAlignable = new TrackAlignable(this.edit)\n        this.orig.state.trackAlignable = new TrackAlignable(this.orig)\n      }\n      this.lockButton.title = this.edit.phrase(\"Toggle locked scrolling\");\n      this.lockButton.setAttribute(\"aria-label\", this.lockButton.title);\n\n      this.orig.state.diffViews = [this];\n      var classLocation = options.chunkClassLocation || \"background\";\n      if (Object.prototype.toString.call(classLocation) != \"[object Array]\") classLocation = [classLocation]\n      this.classes.classLocation = classLocation\n\n      this.diff = getDiff(asString(orig), asString(options.value), this.mv.options.ignoreWhitespace);\n      this.chunks = getChunks(this.diff);\n      this.diffOutOfDate = this.dealigned = false;\n      this.needsScrollSync = null\n\n      this.showDifferences = options.showDifferences !== false;\n    },\n    registerEvents: function(otherDv) {\n      this.forceUpdate = registerUpdate(this);\n      setScrollLock(this, true, false);\n      registerScroll(this, otherDv);\n    },\n    setShowDifferences: function(val) {\n      val = val !== false;\n      if (val != this.showDifferences) {\n        this.showDifferences = val;\n        this.forceUpdate(\"full\");\n      }\n    }\n  };\n\n  function ensureDiff(dv) {\n    if (dv.diffOutOfDate) {\n      dv.diff = getDiff(dv.orig.getValue(), dv.edit.getValue(), dv.mv.options.ignoreWhitespace);\n      dv.chunks = getChunks(dv.diff);\n      dv.diffOutOfDate = false;\n      CodeMirror.signal(dv.edit, \"updateDiff\", dv.diff);\n    }\n  }\n\n  var updating = false;\n  function registerUpdate(dv) {\n    var edit = {from: 0, to: 0, marked: []};\n    var orig = {from: 0, to: 0, marked: []};\n    var debounceChange, updatingFast = false;\n    function update(mode) {\n      updating = true;\n      updatingFast = false;\n      if (mode == \"full\") {\n        if (dv.svg) clear(dv.svg);\n        if (dv.copyButtons) clear(dv.copyButtons);\n        clearMarks(dv.edit, edit.marked, dv.classes);\n        clearMarks(dv.orig, orig.marked, dv.classes);\n        edit.from = edit.to = orig.from = orig.to = 0;\n      }\n      ensureDiff(dv);\n      if (dv.showDifferences) {\n        updateMarks(dv.edit, dv.diff, edit, DIFF_INSERT, dv.classes);\n        updateMarks(dv.orig, dv.diff, orig, DIFF_DELETE, dv.classes);\n      }\n\n      if (dv.mv.options.connect == \"align\")\n        alignChunks(dv);\n      makeConnections(dv);\n      if (dv.needsScrollSync != null) syncScroll(dv, dv.needsScrollSync)\n\n      updating = false;\n    }\n    function setDealign(fast) {\n      if (updating) return;\n      dv.dealigned = true;\n      set(fast);\n    }\n    function set(fast) {\n      if (updating || updatingFast) return;\n      clearTimeout(debounceChange);\n      if (fast === true) updatingFast = true;\n      debounceChange = setTimeout(update, fast === true ? 20 : 250);\n    }\n    function change(_cm, change) {\n      if (!dv.diffOutOfDate) {\n        dv.diffOutOfDate = true;\n        edit.from = edit.to = orig.from = orig.to = 0;\n      }\n      // Update faster when a line was added/removed\n      setDealign(change.text.length - 1 != change.to.line - change.from.line);\n    }\n    function swapDoc() {\n      dv.diffOutOfDate = true;\n      dv.dealigned = true;\n      update(\"full\");\n    }\n    dv.edit.on(\"change\", change);\n    dv.orig.on(\"change\", change);\n    dv.edit.on(\"swapDoc\", swapDoc);\n    dv.orig.on(\"swapDoc\", swapDoc);\n    if (dv.mv.options.connect == \"align\") {\n      CodeMirror.on(dv.edit.state.trackAlignable, \"realign\", setDealign)\n      CodeMirror.on(dv.orig.state.trackAlignable, \"realign\", setDealign)\n    }\n    dv.edit.on(\"viewportChange\", function() { set(false); });\n    dv.orig.on(\"viewportChange\", function() { set(false); });\n    update();\n    return update;\n  }\n\n  function registerScroll(dv, otherDv) {\n    dv.edit.on(\"scroll\", function() {\n      syncScroll(dv, true) && makeConnections(dv);\n    });\n    dv.orig.on(\"scroll\", function() {\n      syncScroll(dv, false) && makeConnections(dv);\n      if (otherDv) syncScroll(otherDv, true) && makeConnections(otherDv);\n    });\n  }\n\n  function syncScroll(dv, toOrig) {\n    // Change handler will do a refresh after a timeout when diff is out of date\n    if (dv.diffOutOfDate) {\n      if (dv.lockScroll && dv.needsScrollSync == null) dv.needsScrollSync = toOrig\n      return false\n    }\n    dv.needsScrollSync = null\n    if (!dv.lockScroll) return true;\n    var editor, other, now = +new Date;\n    if (toOrig) { editor = dv.edit; other = dv.orig; }\n    else { editor = dv.orig; other = dv.edit; }\n    // Don't take action if the position of this editor was recently set\n    // (to prevent feedback loops)\n    if (editor.state.scrollSetBy == dv && (editor.state.scrollSetAt || 0) + 250 > now) return false;\n\n    var sInfo = editor.getScrollInfo();\n    if (dv.mv.options.connect == \"align\") {\n      targetPos = sInfo.top;\n    } else {\n      var halfScreen = .5 * sInfo.clientHeight, midY = sInfo.top + halfScreen;\n      var mid = editor.lineAtHeight(midY, \"local\");\n      var around = chunkBoundariesAround(dv.chunks, mid, toOrig);\n      var off = getOffsets(editor, toOrig ? around.edit : around.orig);\n      var offOther = getOffsets(other, toOrig ? around.orig : around.edit);\n      var ratio = (midY - off.top) / (off.bot - off.top);\n      var targetPos = (offOther.top - halfScreen) + ratio * (offOther.bot - offOther.top);\n\n      var botDist, mix;\n      // Some careful tweaking to make sure no space is left out of view\n      // when scrolling to top or bottom.\n      if (targetPos > sInfo.top && (mix = sInfo.top / halfScreen) < 1) {\n        targetPos = targetPos * mix + sInfo.top * (1 - mix);\n      } else if ((botDist = sInfo.height - sInfo.clientHeight - sInfo.top) < halfScreen) {\n        var otherInfo = other.getScrollInfo();\n        var botDistOther = otherInfo.height - otherInfo.clientHeight - targetPos;\n        if (botDistOther > botDist && (mix = botDist / halfScreen) < 1)\n          targetPos = targetPos * mix + (otherInfo.height - otherInfo.clientHeight - botDist) * (1 - mix);\n      }\n    }\n\n    other.scrollTo(sInfo.left, targetPos);\n    other.state.scrollSetAt = now;\n    other.state.scrollSetBy = dv;\n    return true;\n  }\n\n  function getOffsets(editor, around) {\n    var bot = around.after;\n    if (bot == null) bot = editor.lastLine() + 1;\n    return {top: editor.heightAtLine(around.before || 0, \"local\"),\n            bot: editor.heightAtLine(bot, \"local\")};\n  }\n\n  function setScrollLock(dv, val, action) {\n    dv.lockScroll = val;\n    if (val && action != false) syncScroll(dv, DIFF_INSERT) && makeConnections(dv);\n    (val ? CodeMirror.addClass : CodeMirror.rmClass)(dv.lockButton, \"CodeMirror-merge-scrolllock-enabled\");\n  }\n\n  // Updating the marks for editor content\n\n  function removeClass(editor, line, classes) {\n    var locs = classes.classLocation\n    for (var i = 0; i < locs.length; i++) {\n      editor.removeLineClass(line, locs[i], classes.chunk);\n      editor.removeLineClass(line, locs[i], classes.start);\n      editor.removeLineClass(line, locs[i], classes.end);\n    }\n  }\n\n  function clearMarks(editor, arr, classes) {\n    for (var i = 0; i < arr.length; ++i) {\n      var mark = arr[i];\n      if (mark instanceof CodeMirror.TextMarker)\n        mark.clear();\n      else if (mark.parent)\n        removeClass(editor, mark, classes);\n    }\n    arr.length = 0;\n  }\n\n  // FIXME maybe add a margin around viewport to prevent too many updates\n  function updateMarks(editor, diff, state, type, classes) {\n    var vp = editor.getViewport();\n    editor.operation(function() {\n      if (state.from == state.to || vp.from - state.to > 20 || state.from - vp.to > 20) {\n        clearMarks(editor, state.marked, classes);\n        markChanges(editor, diff, type, state.marked, vp.from, vp.to, classes);\n        state.from = vp.from; state.to = vp.to;\n      } else {\n        if (vp.from < state.from) {\n          markChanges(editor, diff, type, state.marked, vp.from, state.from, classes);\n          state.from = vp.from;\n        }\n        if (vp.to > state.to) {\n          markChanges(editor, diff, type, state.marked, state.to, vp.to, classes);\n          state.to = vp.to;\n        }\n      }\n    });\n  }\n\n  function addClass(editor, lineNr, classes, main, start, end) {\n    var locs = classes.classLocation, line = editor.getLineHandle(lineNr);\n    for (var i = 0; i < locs.length; i++) {\n      if (main) editor.addLineClass(line, locs[i], classes.chunk);\n      if (start) editor.addLineClass(line, locs[i], classes.start);\n      if (end) editor.addLineClass(line, locs[i], classes.end);\n    }\n    return line;\n  }\n\n  function markChanges(editor, diff, type, marks, from, to, classes) {\n    var pos = Pos(0, 0);\n    var top = Pos(from, 0), bot = editor.clipPos(Pos(to - 1));\n    var cls = type == DIFF_DELETE ? classes.del : classes.insert;\n    function markChunk(start, end) {\n      var bfrom = Math.max(from, start), bto = Math.min(to, end);\n      for (var i = bfrom; i < bto; ++i)\n        marks.push(addClass(editor, i, classes, true, i == start, i == end - 1));\n      // When the chunk is empty, make sure a horizontal line shows up\n      if (start == end && bfrom == end && bto == end) {\n        if (bfrom)\n          marks.push(addClass(editor, bfrom - 1, classes, false, false, true));\n        else\n          marks.push(addClass(editor, bfrom, classes, false, true, false));\n      }\n    }\n\n    var chunkStart = 0, pending = false;\n    for (var i = 0; i < diff.length; ++i) {\n      var part = diff[i], tp = part[0], str = part[1];\n      if (tp == DIFF_EQUAL) {\n        var cleanFrom = pos.line + (startOfLineClean(diff, i) ? 0 : 1);\n        moveOver(pos, str);\n        var cleanTo = pos.line + (endOfLineClean(diff, i) ? 1 : 0);\n        if (cleanTo > cleanFrom) {\n          if (pending) { markChunk(chunkStart, cleanFrom); pending = false }\n          chunkStart = cleanTo;\n        }\n      } else {\n        pending = true\n        if (tp == type) {\n          var end = moveOver(pos, str, true);\n          var a = posMax(top, pos), b = posMin(bot, end);\n          if (!posEq(a, b))\n            marks.push(editor.markText(a, b, {className: cls}));\n          pos = end;\n        }\n      }\n    }\n    if (pending) markChunk(chunkStart, pos.line + 1);\n  }\n\n  // Updating the gap between editor and original\n\n  function makeConnections(dv) {\n    if (!dv.showDifferences) return;\n\n    if (dv.svg) {\n      clear(dv.svg);\n      var w = dv.gap.offsetWidth;\n      attrs(dv.svg, \"width\", w, \"height\", dv.gap.offsetHeight);\n    }\n    if (dv.copyButtons) clear(dv.copyButtons);\n\n    var vpEdit = dv.edit.getViewport(), vpOrig = dv.orig.getViewport();\n    var outerTop = dv.mv.wrap.getBoundingClientRect().top\n    var sTopEdit = outerTop - dv.edit.getScrollerElement().getBoundingClientRect().top + dv.edit.getScrollInfo().top\n    var sTopOrig = outerTop - dv.orig.getScrollerElement().getBoundingClientRect().top + dv.orig.getScrollInfo().top;\n    for (var i = 0; i < dv.chunks.length; i++) {\n      var ch = dv.chunks[i];\n      if (ch.editFrom <= vpEdit.to && ch.editTo >= vpEdit.from &&\n          ch.origFrom <= vpOrig.to && ch.origTo >= vpOrig.from)\n        drawConnectorsForChunk(dv, ch, sTopOrig, sTopEdit, w);\n    }\n  }\n\n  function getMatchingOrigLine(editLine, chunks) {\n    var editStart = 0, origStart = 0;\n    for (var i = 0; i < chunks.length; i++) {\n      var chunk = chunks[i];\n      if (chunk.editTo > editLine && chunk.editFrom <= editLine) return null;\n      if (chunk.editFrom > editLine) break;\n      editStart = chunk.editTo;\n      origStart = chunk.origTo;\n    }\n    return origStart + (editLine - editStart);\n  }\n\n  // Combines information about chunks and widgets/markers to return\n  // an array of lines, in a single editor, that probably need to be\n  // aligned with their counterparts in the editor next to it.\n  function alignableFor(cm, chunks, isOrig) {\n    var tracker = cm.state.trackAlignable\n    var start = cm.firstLine(), trackI = 0\n    var result = []\n    for (var i = 0;; i++) {\n      var chunk = chunks[i]\n      var chunkStart = !chunk ? 1e9 : isOrig ? chunk.origFrom : chunk.editFrom\n      for (; trackI < tracker.alignable.length; trackI += 2) {\n        var n = tracker.alignable[trackI] + 1\n        if (n <= start) continue\n        if (n <= chunkStart) result.push(n)\n        else break\n      }\n      if (!chunk) break\n      result.push(start = isOrig ? chunk.origTo : chunk.editTo)\n    }\n    return result\n  }\n\n  // Given information about alignable lines in two editors, fill in\n  // the result (an array of three-element arrays) to reflect the\n  // lines that need to be aligned with each other.\n  function mergeAlignable(result, origAlignable, chunks, setIndex) {\n    var rI = 0, origI = 0, chunkI = 0, diff = 0\n    outer: for (;; rI++) {\n      var nextR = result[rI], nextO = origAlignable[origI]\n      if (!nextR && nextO == null) break\n\n      var rLine = nextR ? nextR[0] : 1e9, oLine = nextO == null ? 1e9 : nextO\n      while (chunkI < chunks.length) {\n        var chunk = chunks[chunkI]\n        if (chunk.origFrom <= oLine && chunk.origTo > oLine) {\n          origI++\n          rI--\n          continue outer;\n        }\n        if (chunk.editTo > rLine) {\n          if (chunk.editFrom <= rLine) continue outer;\n          break\n        }\n        diff += (chunk.origTo - chunk.origFrom) - (chunk.editTo - chunk.editFrom)\n        chunkI++\n      }\n      if (rLine == oLine - diff) {\n        nextR[setIndex] = oLine\n        origI++\n      } else if (rLine < oLine - diff) {\n        nextR[setIndex] = rLine + diff\n      } else {\n        var record = [oLine - diff, null, null]\n        record[setIndex] = oLine\n        result.splice(rI, 0, record)\n        origI++\n      }\n    }\n  }\n\n  function findAlignedLines(dv, other) {\n    var alignable = alignableFor(dv.edit, dv.chunks, false), result = []\n    if (other) for (var i = 0, j = 0; i < other.chunks.length; i++) {\n      var n = other.chunks[i].editTo\n      while (j < alignable.length && alignable[j] < n) j++\n      if (j == alignable.length || alignable[j] != n) alignable.splice(j++, 0, n)\n    }\n    for (var i = 0; i < alignable.length; i++)\n      result.push([alignable[i], null, null])\n\n    mergeAlignable(result, alignableFor(dv.orig, dv.chunks, true), dv.chunks, 1)\n    if (other)\n      mergeAlignable(result, alignableFor(other.orig, other.chunks, true), other.chunks, 2)\n\n    return result\n  }\n\n  function alignChunks(dv, force) {\n    if (!dv.dealigned && !force) return;\n    if (!dv.orig.curOp) return dv.orig.operation(function() {\n      alignChunks(dv, force);\n    });\n\n    dv.dealigned = false;\n    var other = dv.mv.left == dv ? dv.mv.right : dv.mv.left;\n    if (other) {\n      ensureDiff(other);\n      other.dealigned = false;\n    }\n    var linesToAlign = findAlignedLines(dv, other);\n\n    // Clear old aligners\n    var aligners = dv.mv.aligners;\n    for (var i = 0; i < aligners.length; i++)\n      aligners[i].clear();\n    aligners.length = 0;\n\n    var cm = [dv.edit, dv.orig], scroll = [], offset = []\n    if (other) cm.push(other.orig);\n    for (var i = 0; i < cm.length; i++) {\n      scroll.push(cm[i].getScrollInfo().top);\n      offset.push(-cm[i].getScrollerElement().getBoundingClientRect().top)\n    }\n\n    if (offset[0] != offset[1] || cm.length == 3 && offset[1] != offset[2])\n      alignLines(cm, offset, [0, 0, 0], aligners)\n    for (var ln = 0; ln < linesToAlign.length; ln++)\n      alignLines(cm, offset, linesToAlign[ln], aligners);\n\n    for (var i = 0; i < cm.length; i++)\n      cm[i].scrollTo(null, scroll[i]);\n  }\n\n  function alignLines(cm, cmOffset, lines, aligners) {\n    var maxOffset = -1e8, offset = [];\n    for (var i = 0; i < cm.length; i++) if (lines[i] != null) {\n      var off = cm[i].heightAtLine(lines[i], \"local\") - cmOffset[i];\n      offset[i] = off;\n      maxOffset = Math.max(maxOffset, off);\n    }\n    for (var i = 0; i < cm.length; i++) if (lines[i] != null) {\n      var diff = maxOffset - offset[i];\n      if (diff > 1)\n        aligners.push(padAbove(cm[i], lines[i], diff));\n    }\n  }\n\n  function padAbove(cm, line, size) {\n    var above = true;\n    if (line > cm.lastLine()) {\n      line--;\n      above = false;\n    }\n    var elt = document.createElement(\"div\");\n    elt.className = \"CodeMirror-merge-spacer\";\n    elt.style.height = size + \"px\"; elt.style.minWidth = \"1px\";\n    return cm.addLineWidget(line, elt, {height: size, above: above, mergeSpacer: true, handleMouseEvents: true});\n  }\n\n  function drawConnectorsForChunk(dv, chunk, sTopOrig, sTopEdit, w) {\n    var flip = dv.type == \"left\";\n    var top = dv.orig.heightAtLine(chunk.origFrom, \"local\", true) - sTopOrig;\n    if (dv.svg) {\n      var topLpx = top;\n      var topRpx = dv.edit.heightAtLine(chunk.editFrom, \"local\", true) - sTopEdit;\n      if (flip) { var tmp = topLpx; topLpx = topRpx; topRpx = tmp; }\n      var botLpx = dv.orig.heightAtLine(chunk.origTo, \"local\", true) - sTopOrig;\n      var botRpx = dv.edit.heightAtLine(chunk.editTo, \"local\", true) - sTopEdit;\n      if (flip) { var tmp = botLpx; botLpx = botRpx; botRpx = tmp; }\n      var curveTop = \" C \" + w/2 + \" \" + topRpx + \" \" + w/2 + \" \" + topLpx + \" \" + (w + 2) + \" \" + topLpx;\n      var curveBot = \" C \" + w/2 + \" \" + botLpx + \" \" + w/2 + \" \" + botRpx + \" -1 \" + botRpx;\n      attrs(dv.svg.appendChild(document.createElementNS(svgNS, \"path\")),\n            \"d\", \"M -1 \" + topRpx + curveTop + \" L \" + (w + 2) + \" \" + botLpx + curveBot + \" z\",\n            \"class\", dv.classes.connect);\n    }\n    if (dv.copyButtons) {\n      var copy = dv.copyButtons.appendChild(elt(\"div\", dv.type == \"left\" ? \"\\u21dd\" : \"\\u21dc\",\n                                                \"CodeMirror-merge-copy\"));\n      var editOriginals = dv.mv.options.allowEditingOriginals;\n      copy.title = dv.edit.phrase(editOriginals ? \"Push to left\" : \"Revert chunk\");\n      copy.chunk = chunk;\n      copy.style.top = (chunk.origTo > chunk.origFrom ? top : dv.edit.heightAtLine(chunk.editFrom, \"local\") - sTopEdit) + \"px\";\n      copy.setAttribute(\"role\", \"button\");\n      copy.setAttribute(\"tabindex\", \"0\");\n      copy.setAttribute(\"aria-label\", copy.title);\n\n      if (editOriginals) {\n        var topReverse = dv.edit.heightAtLine(chunk.editFrom, \"local\") - sTopEdit;\n        var copyReverse = dv.copyButtons.appendChild(elt(\"div\", dv.type == \"right\" ? \"\\u21dd\" : \"\\u21dc\",\n                                                         \"CodeMirror-merge-copy-reverse\"));\n        copyReverse.title = \"Push to right\";\n        copyReverse.chunk = {editFrom: chunk.origFrom, editTo: chunk.origTo,\n                             origFrom: chunk.editFrom, origTo: chunk.editTo};\n        copyReverse.style.top = topReverse + \"px\";\n        dv.type == \"right\" ? copyReverse.style.left = \"2px\" : copyReverse.style.right = \"2px\";\n        copyReverse.setAttribute(\"role\", \"button\");\n        copyReverse.setAttribute(\"tabindex\", \"0\");\n        copyReverse.setAttribute(\"aria-label\", copyReverse.title);\n      }\n    }\n  }\n\n  function copyChunk(dv, to, from, chunk) {\n    if (dv.diffOutOfDate) return;\n    var origStart = chunk.origTo > from.lastLine() ? Pos(chunk.origFrom - 1) : Pos(chunk.origFrom, 0)\n    var origEnd = Pos(chunk.origTo, 0)\n    var editStart = chunk.editTo > to.lastLine() ? Pos(chunk.editFrom - 1) : Pos(chunk.editFrom, 0)\n    var editEnd = Pos(chunk.editTo, 0)\n    var handler = dv.mv.options.revertChunk\n    if (handler)\n      handler(dv.mv, from, origStart, origEnd, to, editStart, editEnd)\n    else\n      to.replaceRange(from.getRange(origStart, origEnd), editStart, editEnd)\n  }\n\n  // Merge view, containing 0, 1, or 2 diff views.\n\n  var MergeView = CodeMirror.MergeView = function(node, options) {\n    if (!(this instanceof MergeView)) return new MergeView(node, options);\n\n    this.options = options;\n    var origLeft = options.origLeft, origRight = options.origRight == null ? options.orig : options.origRight;\n\n    var hasLeft = origLeft != null, hasRight = origRight != null;\n    var panes = 1 + (hasLeft ? 1 : 0) + (hasRight ? 1 : 0);\n    var wrap = [], left = this.left = null, right = this.right = null;\n    var self = this;\n\n    if (hasLeft) {\n      left = this.left = new DiffView(this, \"left\");\n      var leftPane = elt(\"div\", null, \"CodeMirror-merge-pane CodeMirror-merge-left\");\n      wrap.push(leftPane);\n      wrap.push(buildGap(left));\n    }\n\n    var editPane = elt(\"div\", null, \"CodeMirror-merge-pane CodeMirror-merge-editor\");\n    wrap.push(editPane);\n\n    if (hasRight) {\n      right = this.right = new DiffView(this, \"right\");\n      wrap.push(buildGap(right));\n      var rightPane = elt(\"div\", null, \"CodeMirror-merge-pane CodeMirror-merge-right\");\n      wrap.push(rightPane);\n    }\n\n    (hasRight ? rightPane : editPane).className += \" CodeMirror-merge-pane-rightmost\";\n\n    wrap.push(elt(\"div\", null, null, \"height: 0; clear: both;\"));\n\n    var wrapElt = this.wrap = node.appendChild(elt(\"div\", wrap, \"CodeMirror-merge CodeMirror-merge-\" + panes + \"pane\"));\n    this.edit = CodeMirror(editPane, copyObj(options));\n\n    if (left) left.init(leftPane, origLeft, options);\n    if (right) right.init(rightPane, origRight, options);\n    if (options.collapseIdentical)\n      this.editor().operation(function() {\n        collapseIdenticalStretches(self, options.collapseIdentical);\n      });\n    if (options.connect == \"align\") {\n      this.aligners = [];\n      alignChunks(this.left || this.right, true);\n    }\n    if (left) left.registerEvents(right)\n    if (right) right.registerEvents(left)\n\n\n    var onResize = function() {\n      if (left) makeConnections(left);\n      if (right) makeConnections(right);\n    };\n    CodeMirror.on(window, \"resize\", onResize);\n    var resizeInterval = setInterval(function() {\n      for (var p = wrapElt.parentNode; p && p != document.body; p = p.parentNode) {}\n      if (!p) { clearInterval(resizeInterval); CodeMirror.off(window, \"resize\", onResize); }\n    }, 5000);\n  };\n\n  function buildGap(dv) {\n    var lock = dv.lockButton = elt(\"div\", null, \"CodeMirror-merge-scrolllock\");\n    lock.setAttribute(\"role\", \"button\");\n    lock.setAttribute(\"tabindex\", \"0\");\n    var lockWrap = elt(\"div\", [lock], \"CodeMirror-merge-scrolllock-wrap\");\n    CodeMirror.on(lock, \"click\", function() { setScrollLock(dv, !dv.lockScroll); });\n    CodeMirror.on(lock, \"keyup\", function(e) { (e.key === \"Enter\" || e.code === \"Space\") && setScrollLock(dv, !dv.lockScroll); });\n    var gapElts = [lockWrap];\n    if (dv.mv.options.revertButtons !== false) {\n      dv.copyButtons = elt(\"div\", null, \"CodeMirror-merge-copybuttons-\" + dv.type);\n      var copyButtons = function(e) {\n        var node = e.target || e.srcElement;\n        if (!node.chunk) return;\n        if (node.className == \"CodeMirror-merge-copy-reverse\") {\n          copyChunk(dv, dv.orig, dv.edit, node.chunk);\n          return;\n        }\n        copyChunk(dv, dv.edit, dv.orig, node.chunk);\n      }\n      CodeMirror.on(dv.copyButtons, \"click\", copyButtons);\n      CodeMirror.on(dv.copyButtons, \"keyup\", function(e) { (e.key === \"Enter\" || e.code === \"Space\") && copyButtons(e); });\n      gapElts.unshift(dv.copyButtons);\n    }\n    if (dv.mv.options.connect != \"align\") {\n      var svg = document.createElementNS && document.createElementNS(svgNS, \"svg\");\n      if (svg && !svg.createSVGRect) svg = null;\n      dv.svg = svg;\n      if (svg) gapElts.push(svg);\n    }\n\n    return dv.gap = elt(\"div\", gapElts, \"CodeMirror-merge-gap\");\n  }\n\n  MergeView.prototype = {\n    constructor: MergeView,\n    editor: function() { return this.edit; },\n    rightOriginal: function() { return this.right && this.right.orig; },\n    leftOriginal: function() { return this.left && this.left.orig; },\n    setShowDifferences: function(val) {\n      if (this.right) this.right.setShowDifferences(val);\n      if (this.left) this.left.setShowDifferences(val);\n    },\n    rightChunks: function() {\n      if (this.right) { ensureDiff(this.right); return this.right.chunks; }\n    },\n    leftChunks: function() {\n      if (this.left) { ensureDiff(this.left); return this.left.chunks; }\n    }\n  };\n\n  function asString(obj) {\n    if (typeof obj == \"string\") return obj;\n    else return obj.getValue();\n  }\n\n  // Operations on diffs\n  var dmp;\n  function getDiff(a, b, ignoreWhitespace) {\n    if (!dmp) dmp = new diff_match_patch();\n\n    var diff = dmp.diff_main(a, b);\n    // The library sometimes leaves in empty parts, which confuse the algorithm\n    for (var i = 0; i < diff.length; ++i) {\n      var part = diff[i];\n      if (ignoreWhitespace ? !/[^ \\t]/.test(part[1]) : !part[1]) {\n        diff.splice(i--, 1);\n      } else if (i && diff[i - 1][0] == part[0]) {\n        diff.splice(i--, 1);\n        diff[i][1] += part[1];\n      }\n    }\n    return diff;\n  }\n\n  function getChunks(diff) {\n    var chunks = [];\n    if (!diff.length) return chunks;\n    var startEdit = 0, startOrig = 0;\n    var edit = Pos(0, 0), orig = Pos(0, 0);\n    for (var i = 0; i < diff.length; ++i) {\n      var part = diff[i], tp = part[0];\n      if (tp == DIFF_EQUAL) {\n        var startOff = !startOfLineClean(diff, i) || edit.line < startEdit || orig.line < startOrig ? 1 : 0;\n        var cleanFromEdit = edit.line + startOff, cleanFromOrig = orig.line + startOff;\n        moveOver(edit, part[1], null, orig);\n        var endOff = endOfLineClean(diff, i) ? 1 : 0;\n        var cleanToEdit = edit.line + endOff, cleanToOrig = orig.line + endOff;\n        if (cleanToEdit > cleanFromEdit) {\n          if (i) chunks.push({origFrom: startOrig, origTo: cleanFromOrig,\n                              editFrom: startEdit, editTo: cleanFromEdit});\n          startEdit = cleanToEdit; startOrig = cleanToOrig;\n        }\n      } else {\n        moveOver(tp == DIFF_INSERT ? edit : orig, part[1]);\n      }\n    }\n    if (startEdit <= edit.line || startOrig <= orig.line)\n      chunks.push({origFrom: startOrig, origTo: orig.line + 1,\n                   editFrom: startEdit, editTo: edit.line + 1});\n    return chunks;\n  }\n\n  function endOfLineClean(diff, i) {\n    if (i == diff.length - 1) return true;\n    var next = diff[i + 1][1];\n    if ((next.length == 1 && i < diff.length - 2) || next.charCodeAt(0) != 10) return false;\n    if (i == diff.length - 2) return true;\n    next = diff[i + 2][1];\n    return (next.length > 1 || i == diff.length - 3) && next.charCodeAt(0) == 10;\n  }\n\n  function startOfLineClean(diff, i) {\n    if (i == 0) return true;\n    var last = diff[i - 1][1];\n    if (last.charCodeAt(last.length - 1) != 10) return false;\n    if (i == 1) return true;\n    last = diff[i - 2][1];\n    return last.charCodeAt(last.length - 1) == 10;\n  }\n\n  function chunkBoundariesAround(chunks, n, nInEdit) {\n    var beforeE, afterE, beforeO, afterO;\n    for (var i = 0; i < chunks.length; i++) {\n      var chunk = chunks[i];\n      var fromLocal = nInEdit ? chunk.editFrom : chunk.origFrom;\n      var toLocal = nInEdit ? chunk.editTo : chunk.origTo;\n      if (afterE == null) {\n        if (fromLocal > n) { afterE = chunk.editFrom; afterO = chunk.origFrom; }\n        else if (toLocal > n) { afterE = chunk.editTo; afterO = chunk.origTo; }\n      }\n      if (toLocal <= n) { beforeE = chunk.editTo; beforeO = chunk.origTo; }\n      else if (fromLocal <= n) { beforeE = chunk.editFrom; beforeO = chunk.origFrom; }\n    }\n    return {edit: {before: beforeE, after: afterE}, orig: {before: beforeO, after: afterO}};\n  }\n\n  function collapseSingle(cm, from, to) {\n    cm.addLineClass(from, \"wrap\", \"CodeMirror-merge-collapsed-line\");\n    var widget = document.createElement(\"span\");\n    widget.className = \"CodeMirror-merge-collapsed-widget\";\n    widget.title = cm.phrase(\"Identical text collapsed. Click to expand.\");\n    var mark = cm.markText(Pos(from, 0), Pos(to - 1), {\n      inclusiveLeft: true,\n      inclusiveRight: true,\n      replacedWith: widget,\n      clearOnEnter: true\n    });\n    function clear() {\n      mark.clear();\n      cm.removeLineClass(from, \"wrap\", \"CodeMirror-merge-collapsed-line\");\n    }\n    if (mark.explicitlyCleared) clear();\n    CodeMirror.on(widget, \"click\", clear);\n    mark.on(\"clear\", clear);\n    CodeMirror.on(widget, \"click\", clear);\n    return {mark: mark, clear: clear};\n  }\n\n  function collapseStretch(size, editors) {\n    var marks = [];\n    function clear() {\n      for (var i = 0; i < marks.length; i++) marks[i].clear();\n    }\n    for (var i = 0; i < editors.length; i++) {\n      var editor = editors[i];\n      var mark = collapseSingle(editor.cm, editor.line, editor.line + size);\n      marks.push(mark);\n      mark.mark.on(\"clear\", clear);\n    }\n    return marks[0].mark;\n  }\n\n  function unclearNearChunks(dv, margin, off, clear) {\n    for (var i = 0; i < dv.chunks.length; i++) {\n      var chunk = dv.chunks[i];\n      for (var l = chunk.editFrom - margin; l < chunk.editTo + margin; l++) {\n        var pos = l + off;\n        if (pos >= 0 && pos < clear.length) clear[pos] = false;\n      }\n    }\n  }\n\n  function collapseIdenticalStretches(mv, margin) {\n    if (typeof margin != \"number\") margin = 2;\n    var clear = [], edit = mv.editor(), off = edit.firstLine();\n    for (var l = off, e = edit.lastLine(); l <= e; l++) clear.push(true);\n    if (mv.left) unclearNearChunks(mv.left, margin, off, clear);\n    if (mv.right) unclearNearChunks(mv.right, margin, off, clear);\n\n    for (var i = 0; i < clear.length; i++) {\n      if (clear[i]) {\n        var line = i + off;\n        for (var size = 1; i < clear.length - 1 && clear[i + 1]; i++, size++) {}\n        if (size > margin) {\n          var editors = [{line: line, cm: edit}];\n          if (mv.left) editors.push({line: getMatchingOrigLine(line, mv.left.chunks), cm: mv.left.orig});\n          if (mv.right) editors.push({line: getMatchingOrigLine(line, mv.right.chunks), cm: mv.right.orig});\n          var mark = collapseStretch(size, editors);\n          if (mv.options.onCollapse) mv.options.onCollapse(mv, line, size, mark);\n        }\n      }\n    }\n  }\n\n  // General utilities\n\n  function elt(tag, content, className, style) {\n    var e = document.createElement(tag);\n    if (className) e.className = className;\n    if (style) e.style.cssText = style;\n    if (typeof content == \"string\") e.appendChild(document.createTextNode(content));\n    else if (content) for (var i = 0; i < content.length; ++i) e.appendChild(content[i]);\n    return e;\n  }\n\n  function clear(node) {\n    for (var count = node.childNodes.length; count > 0; --count)\n      node.removeChild(node.firstChild);\n  }\n\n  function attrs(elt) {\n    for (var i = 1; i < arguments.length; i += 2)\n      elt.setAttribute(arguments[i], arguments[i+1]);\n  }\n\n  function copyObj(obj, target) {\n    if (!target) target = {};\n    for (var prop in obj) if (obj.hasOwnProperty(prop)) target[prop] = obj[prop];\n    return target;\n  }\n\n  function moveOver(pos, str, copy, other) {\n    var out = copy ? Pos(pos.line, pos.ch) : pos, at = 0;\n    for (;;) {\n      var nl = str.indexOf(\"\\n\", at);\n      if (nl == -1) break;\n      ++out.line;\n      if (other) ++other.line;\n      at = nl + 1;\n    }\n    out.ch = (at ? 0 : out.ch) + (str.length - at);\n    if (other) other.ch = (at ? 0 : other.ch) + (str.length - at);\n    return out;\n  }\n\n  // Tracks collapsed markers and line widgets, in order to be able to\n  // accurately align the content of two editors.\n\n  var F_WIDGET = 1, F_WIDGET_BELOW = 2, F_MARKER = 4\n\n  function TrackAlignable(cm) {\n    this.cm = cm\n    this.alignable = []\n    this.height = cm.doc.height\n    var self = this\n    cm.on(\"markerAdded\", function(_, marker) {\n      if (!marker.collapsed) return\n      var found = marker.find(1)\n      if (found != null) self.set(found.line, F_MARKER)\n    })\n    cm.on(\"markerCleared\", function(_, marker, _min, max) {\n      if (max != null && marker.collapsed)\n        self.check(max, F_MARKER, self.hasMarker)\n    })\n    cm.on(\"markerChanged\", this.signal.bind(this))\n    cm.on(\"lineWidgetAdded\", function(_, widget, lineNo) {\n      if (widget.mergeSpacer) return\n      if (widget.above) self.set(lineNo - 1, F_WIDGET_BELOW)\n      else self.set(lineNo, F_WIDGET)\n    })\n    cm.on(\"lineWidgetCleared\", function(_, widget, lineNo) {\n      if (widget.mergeSpacer) return\n      if (widget.above) self.check(lineNo - 1, F_WIDGET_BELOW, self.hasWidgetBelow)\n      else self.check(lineNo, F_WIDGET, self.hasWidget)\n    })\n    cm.on(\"lineWidgetChanged\", this.signal.bind(this))\n    cm.on(\"change\", function(_, change) {\n      var start = change.from.line, nBefore = change.to.line - change.from.line\n      var nAfter = change.text.length - 1, end = start + nAfter\n      if (nBefore || nAfter) self.map(start, nBefore, nAfter)\n      self.check(end, F_MARKER, self.hasMarker)\n      if (nBefore || nAfter) self.check(change.from.line, F_MARKER, self.hasMarker)\n    })\n    cm.on(\"viewportChange\", function() {\n      if (self.cm.doc.height != self.height) self.signal()\n    })\n  }\n\n  TrackAlignable.prototype = {\n    signal: function() {\n      CodeMirror.signal(this, \"realign\")\n      this.height = this.cm.doc.height\n    },\n\n    set: function(n, flags) {\n      var pos = -1\n      for (; pos < this.alignable.length; pos += 2) {\n        var diff = this.alignable[pos] - n\n        if (diff == 0) {\n          if ((this.alignable[pos + 1] & flags) == flags) return\n          this.alignable[pos + 1] |= flags\n          this.signal()\n          return\n        }\n        if (diff > 0) break\n      }\n      this.signal()\n      this.alignable.splice(pos, 0, n, flags)\n    },\n\n    find: function(n) {\n      for (var i = 0; i < this.alignable.length; i += 2)\n        if (this.alignable[i] == n) return i\n      return -1\n    },\n\n    check: function(n, flag, pred) {\n      var found = this.find(n)\n      if (found == -1 || !(this.alignable[found + 1] & flag)) return\n      if (!pred.call(this, n)) {\n        this.signal()\n        var flags = this.alignable[found + 1] & ~flag\n        if (flags) this.alignable[found + 1] = flags\n        else this.alignable.splice(found, 2)\n      }\n    },\n\n    hasMarker: function(n) {\n      var handle = this.cm.getLineHandle(n)\n      if (handle.markedSpans) for (var i = 0; i < handle.markedSpans.length; i++)\n        if (handle.markedSpans[i].marker.collapsed && handle.markedSpans[i].to != null)\n          return true\n      return false\n    },\n\n    hasWidget: function(n) {\n      var handle = this.cm.getLineHandle(n)\n      if (handle.widgets) for (var i = 0; i < handle.widgets.length; i++)\n        if (!handle.widgets[i].above && !handle.widgets[i].mergeSpacer) return true\n      return false\n    },\n\n    hasWidgetBelow: function(n) {\n      if (n == this.cm.lastLine()) return false\n      var handle = this.cm.getLineHandle(n + 1)\n      if (handle.widgets) for (var i = 0; i < handle.widgets.length; i++)\n        if (handle.widgets[i].above && !handle.widgets[i].mergeSpacer) return true\n      return false\n    },\n\n    map: function(from, nBefore, nAfter) {\n      var diff = nAfter - nBefore, to = from + nBefore, widgetFrom = -1, widgetTo = -1\n      for (var i = 0; i < this.alignable.length; i += 2) {\n        var n = this.alignable[i]\n        if (n == from && (this.alignable[i + 1] & F_WIDGET_BELOW)) widgetFrom = i\n        if (n == to && (this.alignable[i + 1] & F_WIDGET_BELOW)) widgetTo = i\n        if (n <= from) continue\n        else if (n < to) this.alignable.splice(i--, 2)\n        else this.alignable[i] += diff\n      }\n      if (widgetFrom > -1) {\n        var flags = this.alignable[widgetFrom + 1]\n        if (flags == F_WIDGET_BELOW) this.alignable.splice(widgetFrom, 2)\n        else this.alignable[widgetFrom + 1] = flags & ~F_WIDGET_BELOW\n      }\n      if (widgetTo > -1 && nAfter)\n        this.set(from + nAfter, F_WIDGET_BELOW)\n    }\n  }\n\n  function posMin(a, b) { return (a.line - b.line || a.ch - b.ch) < 0 ? a : b; }\n  function posMax(a, b) { return (a.line - b.line || a.ch - b.ch) > 0 ? a : b; }\n  function posEq(a, b) { return a.line == b.line && a.ch == b.ch; }\n\n  function findPrevDiff(chunks, start, isOrig) {\n    for (var i = chunks.length - 1; i >= 0; i--) {\n      var chunk = chunks[i];\n      var to = (isOrig ? chunk.origTo : chunk.editTo) - 1;\n      if (to < start) return to;\n    }\n  }\n\n  function findNextDiff(chunks, start, isOrig) {\n    for (var i = 0; i < chunks.length; i++) {\n      var chunk = chunks[i];\n      var from = (isOrig ? chunk.origFrom : chunk.editFrom);\n      if (from > start) return from;\n    }\n  }\n\n  function goNearbyDiff(cm, dir) {\n    var found = null, views = cm.state.diffViews, line = cm.getCursor().line;\n    if (views) for (var i = 0; i < views.length; i++) {\n      var dv = views[i], isOrig = cm == dv.orig;\n      ensureDiff(dv);\n      var pos = dir < 0 ? findPrevDiff(dv.chunks, line, isOrig) : findNextDiff(dv.chunks, line, isOrig);\n      if (pos != null && (found == null || (dir < 0 ? pos > found : pos < found)))\n        found = pos;\n    }\n    if (found != null)\n      cm.setCursor(found, 0);\n    else\n      return CodeMirror.Pass;\n  }\n\n  CodeMirror.commands.goNextDiff = function(cm) {\n    return goNearbyDiff(cm, 1);\n  };\n  CodeMirror.commands.goPrevDiff = function(cm) {\n    return goNearbyDiff(cm, -1);\n  };\n});\n", "/**\n * Diff Match and Patch\n * Copyright 2018 The diff-match-patch Authors.\n * https://github.com/google/diff-match-patch\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * @fileoverview Computes the difference between two texts to create a patch.\n * Applies the patch onto another text, allowing for errors.\n * <AUTHOR> (<PERSON>)\n */\n\n/**\n * Class containing the diff, match and patch methods.\n * @constructor\n */\nvar diff_match_patch = function() {\n\n  // Defaults.\n  // Redefine these in your program to override the defaults.\n\n  // Number of seconds to map a diff before giving up (0 for infinity).\n  this.Diff_Timeout = 1.0;\n  // Cost of an empty edit operation in terms of edit characters.\n  this.Diff_EditCost = 4;\n  // At what point is no match declared (0.0 = perfection, 1.0 = very loose).\n  this.Match_Threshold = 0.5;\n  // How far to search for a match (0 = exact location, 1000+ = broad match).\n  // A match this many characters away from the expected location will add\n  // 1.0 to the score (0.0 is a perfect match).\n  this.Match_Distance = 1000;\n  // When deleting a large block of text (over ~64 characters), how close do\n  // the contents have to be to match the expected contents. (0.0 = perfection,\n  // 1.0 = very loose).  Note that Match_Threshold controls how closely the\n  // end points of a delete need to match.\n  this.Patch_DeleteThreshold = 0.5;\n  // Chunk size for context length.\n  this.Patch_Margin = 4;\n\n  // The number of bits in an int.\n  this.Match_MaxBits = 32;\n};\n\n\n//  DIFF FUNCTIONS\n\n\n/**\n * The data structure representing a diff is an array of tuples:\n * [[DIFF_DELETE, 'Hello'], [DIFF_INSERT, 'Goodbye'], [DIFF_EQUAL, ' world.']]\n * which means: delete 'Hello', add 'Goodbye' and keep ' world.'\n */\nvar DIFF_DELETE = -1;\nvar DIFF_INSERT = 1;\nvar DIFF_EQUAL = 0;\n\n/**\n * Class representing one diff tuple.\n * ~Attempts to look like a two-element array (which is what this used to be).~\n * Constructor returns an actual two-element array, to allow destructing @JackuB\n * See https://github.com/JackuB/diff-match-patch/issues/14 for details\n * @param {number} op Operation, one of: DIFF_DELETE, DIFF_INSERT, DIFF_EQUAL.\n * @param {string} text Text to be deleted, inserted, or retained.\n * @constructor\n */\ndiff_match_patch.Diff = function(op, text) {\n  return [op, text];\n};\n\n/**\n * Find the differences between two texts.  Simplifies the problem by stripping\n * any common prefix or suffix off the texts before diffing.\n * @param {string} text1 Old string to be diffed.\n * @param {string} text2 New string to be diffed.\n * @param {boolean=} opt_checklines Optional speedup flag. If present and false,\n *     then don't run a line-level diff first to identify the changed areas.\n *     Defaults to true, which does a faster, slightly less optimal diff.\n * @param {number=} opt_deadline Optional time when the diff should be complete\n *     by.  Used internally for recursive calls.  Users should set DiffTimeout\n *     instead.\n * @return {!Array.<!diff_match_patch.Diff>} Array of diff tuples.\n */\ndiff_match_patch.prototype.diff_main = function(text1, text2, opt_checklines,\n    opt_deadline) {\n  // Set a deadline by which time the diff must be complete.\n  if (typeof opt_deadline == 'undefined') {\n    if (this.Diff_Timeout <= 0) {\n      opt_deadline = Number.MAX_VALUE;\n    } else {\n      opt_deadline = (new Date).getTime() + this.Diff_Timeout * 1000;\n    }\n  }\n  var deadline = opt_deadline;\n\n  // Check for null inputs.\n  if (text1 == null || text2 == null) {\n    throw new Error('Null input. (diff_main)');\n  }\n\n  // Check for equality (speedup).\n  if (text1 == text2) {\n    if (text1) {\n      return [new diff_match_patch.Diff(DIFF_EQUAL, text1)];\n    }\n    return [];\n  }\n\n  if (typeof opt_checklines == 'undefined') {\n    opt_checklines = true;\n  }\n  var checklines = opt_checklines;\n\n  // Trim off common prefix (speedup).\n  var commonlength = this.diff_commonPrefix(text1, text2);\n  var commonprefix = text1.substring(0, commonlength);\n  text1 = text1.substring(commonlength);\n  text2 = text2.substring(commonlength);\n\n  // Trim off common suffix (speedup).\n  commonlength = this.diff_commonSuffix(text1, text2);\n  var commonsuffix = text1.substring(text1.length - commonlength);\n  text1 = text1.substring(0, text1.length - commonlength);\n  text2 = text2.substring(0, text2.length - commonlength);\n\n  // Compute the diff on the middle block.\n  var diffs = this.diff_compute_(text1, text2, checklines, deadline);\n\n  // Restore the prefix and suffix.\n  if (commonprefix) {\n    diffs.unshift(new diff_match_patch.Diff(DIFF_EQUAL, commonprefix));\n  }\n  if (commonsuffix) {\n    diffs.push(new diff_match_patch.Diff(DIFF_EQUAL, commonsuffix));\n  }\n  this.diff_cleanupMerge(diffs);\n  return diffs;\n};\n\n\n/**\n * Find the differences between two texts.  Assumes that the texts do not\n * have any common prefix or suffix.\n * @param {string} text1 Old string to be diffed.\n * @param {string} text2 New string to be diffed.\n * @param {boolean} checklines Speedup flag.  If false, then don't run a\n *     line-level diff first to identify the changed areas.\n *     If true, then run a faster, slightly less optimal diff.\n * @param {number} deadline Time when the diff should be complete by.\n * @return {!Array.<!diff_match_patch.Diff>} Array of diff tuples.\n * @private\n */\ndiff_match_patch.prototype.diff_compute_ = function(text1, text2, checklines,\n    deadline) {\n  var diffs;\n\n  if (!text1) {\n    // Just add some text (speedup).\n    return [new diff_match_patch.Diff(DIFF_INSERT, text2)];\n  }\n\n  if (!text2) {\n    // Just delete some text (speedup).\n    return [new diff_match_patch.Diff(DIFF_DELETE, text1)];\n  }\n\n  var longtext = text1.length > text2.length ? text1 : text2;\n  var shorttext = text1.length > text2.length ? text2 : text1;\n  var i = longtext.indexOf(shorttext);\n  if (i != -1) {\n    // Shorter text is inside the longer text (speedup).\n    diffs = [new diff_match_patch.Diff(DIFF_INSERT, longtext.substring(0, i)),\n             new diff_match_patch.Diff(DIFF_EQUAL, shorttext),\n             new diff_match_patch.Diff(DIFF_INSERT,\n                 longtext.substring(i + shorttext.length))];\n    // Swap insertions for deletions if diff is reversed.\n    if (text1.length > text2.length) {\n      diffs[0][0] = diffs[2][0] = DIFF_DELETE;\n    }\n    return diffs;\n  }\n\n  if (shorttext.length == 1) {\n    // Single character string.\n    // After the previous speedup, the character can't be an equality.\n    return [new diff_match_patch.Diff(DIFF_DELETE, text1),\n            new diff_match_patch.Diff(DIFF_INSERT, text2)];\n  }\n\n  // Check to see if the problem can be split in two.\n  var hm = this.diff_halfMatch_(text1, text2);\n  if (hm) {\n    // A half-match was found, sort out the return data.\n    var text1_a = hm[0];\n    var text1_b = hm[1];\n    var text2_a = hm[2];\n    var text2_b = hm[3];\n    var mid_common = hm[4];\n    // Send both pairs off for separate processing.\n    var diffs_a = this.diff_main(text1_a, text2_a, checklines, deadline);\n    var diffs_b = this.diff_main(text1_b, text2_b, checklines, deadline);\n    // Merge the results.\n    return diffs_a.concat([new diff_match_patch.Diff(DIFF_EQUAL, mid_common)],\n                          diffs_b);\n  }\n\n  if (checklines && text1.length > 100 && text2.length > 100) {\n    return this.diff_lineMode_(text1, text2, deadline);\n  }\n\n  return this.diff_bisect_(text1, text2, deadline);\n};\n\n\n/**\n * Do a quick line-level diff on both strings, then rediff the parts for\n * greater accuracy.\n * This speedup can produce non-minimal diffs.\n * @param {string} text1 Old string to be diffed.\n * @param {string} text2 New string to be diffed.\n * @param {number} deadline Time when the diff should be complete by.\n * @return {!Array.<!diff_match_patch.Diff>} Array of diff tuples.\n * @private\n */\ndiff_match_patch.prototype.diff_lineMode_ = function(text1, text2, deadline) {\n  // Scan the text on a line-by-line basis first.\n  var a = this.diff_linesToChars_(text1, text2);\n  text1 = a.chars1;\n  text2 = a.chars2;\n  var linearray = a.lineArray;\n\n  var diffs = this.diff_main(text1, text2, false, deadline);\n\n  // Convert the diff back to original text.\n  this.diff_charsToLines_(diffs, linearray);\n  // Eliminate freak matches (e.g. blank lines)\n  this.diff_cleanupSemantic(diffs);\n\n  // Rediff any replacement blocks, this time character-by-character.\n  // Add a dummy entry at the end.\n  diffs.push(new diff_match_patch.Diff(DIFF_EQUAL, ''));\n  var pointer = 0;\n  var count_delete = 0;\n  var count_insert = 0;\n  var text_delete = '';\n  var text_insert = '';\n  while (pointer < diffs.length) {\n    switch (diffs[pointer][0]) {\n      case DIFF_INSERT:\n        count_insert++;\n        text_insert += diffs[pointer][1];\n        break;\n      case DIFF_DELETE:\n        count_delete++;\n        text_delete += diffs[pointer][1];\n        break;\n      case DIFF_EQUAL:\n        // Upon reaching an equality, check for prior redundancies.\n        if (count_delete >= 1 && count_insert >= 1) {\n          // Delete the offending records and add the merged ones.\n          diffs.splice(pointer - count_delete - count_insert,\n                       count_delete + count_insert);\n          pointer = pointer - count_delete - count_insert;\n          var subDiff =\n              this.diff_main(text_delete, text_insert, false, deadline);\n          for (var j = subDiff.length - 1; j >= 0; j--) {\n            diffs.splice(pointer, 0, subDiff[j]);\n          }\n          pointer = pointer + subDiff.length;\n        }\n        count_insert = 0;\n        count_delete = 0;\n        text_delete = '';\n        text_insert = '';\n        break;\n    }\n    pointer++;\n  }\n  diffs.pop();  // Remove the dummy entry at the end.\n\n  return diffs;\n};\n\n\n/**\n * Find the 'middle snake' of a diff, split the problem in two\n * and return the recursively constructed diff.\n * See Myers 1986 paper: An O(ND) Difference Algorithm and Its Variations.\n * @param {string} text1 Old string to be diffed.\n * @param {string} text2 New string to be diffed.\n * @param {number} deadline Time at which to bail if not yet complete.\n * @return {!Array.<!diff_match_patch.Diff>} Array of diff tuples.\n * @private\n */\ndiff_match_patch.prototype.diff_bisect_ = function(text1, text2, deadline) {\n  // Cache the text lengths to prevent multiple calls.\n  var text1_length = text1.length;\n  var text2_length = text2.length;\n  var max_d = Math.ceil((text1_length + text2_length) / 2);\n  var v_offset = max_d;\n  var v_length = 2 * max_d;\n  var v1 = new Array(v_length);\n  var v2 = new Array(v_length);\n  // Setting all elements to -1 is faster in Chrome & Firefox than mixing\n  // integers and undefined.\n  for (var x = 0; x < v_length; x++) {\n    v1[x] = -1;\n    v2[x] = -1;\n  }\n  v1[v_offset + 1] = 0;\n  v2[v_offset + 1] = 0;\n  var delta = text1_length - text2_length;\n  // If the total number of characters is odd, then the front path will collide\n  // with the reverse path.\n  var front = (delta % 2 != 0);\n  // Offsets for start and end of k loop.\n  // Prevents mapping of space beyond the grid.\n  var k1start = 0;\n  var k1end = 0;\n  var k2start = 0;\n  var k2end = 0;\n  for (var d = 0; d < max_d; d++) {\n    // Bail out if deadline is reached.\n    if ((new Date()).getTime() > deadline) {\n      break;\n    }\n\n    // Walk the front path one step.\n    for (var k1 = -d + k1start; k1 <= d - k1end; k1 += 2) {\n      var k1_offset = v_offset + k1;\n      var x1;\n      if (k1 == -d || (k1 != d && v1[k1_offset - 1] < v1[k1_offset + 1])) {\n        x1 = v1[k1_offset + 1];\n      } else {\n        x1 = v1[k1_offset - 1] + 1;\n      }\n      var y1 = x1 - k1;\n      while (x1 < text1_length && y1 < text2_length &&\n             text1.charAt(x1) == text2.charAt(y1)) {\n        x1++;\n        y1++;\n      }\n      v1[k1_offset] = x1;\n      if (x1 > text1_length) {\n        // Ran off the right of the graph.\n        k1end += 2;\n      } else if (y1 > text2_length) {\n        // Ran off the bottom of the graph.\n        k1start += 2;\n      } else if (front) {\n        var k2_offset = v_offset + delta - k1;\n        if (k2_offset >= 0 && k2_offset < v_length && v2[k2_offset] != -1) {\n          // Mirror x2 onto top-left coordinate system.\n          var x2 = text1_length - v2[k2_offset];\n          if (x1 >= x2) {\n            // Overlap detected.\n            return this.diff_bisectSplit_(text1, text2, x1, y1, deadline);\n          }\n        }\n      }\n    }\n\n    // Walk the reverse path one step.\n    for (var k2 = -d + k2start; k2 <= d - k2end; k2 += 2) {\n      var k2_offset = v_offset + k2;\n      var x2;\n      if (k2 == -d || (k2 != d && v2[k2_offset - 1] < v2[k2_offset + 1])) {\n        x2 = v2[k2_offset + 1];\n      } else {\n        x2 = v2[k2_offset - 1] + 1;\n      }\n      var y2 = x2 - k2;\n      while (x2 < text1_length && y2 < text2_length &&\n             text1.charAt(text1_length - x2 - 1) ==\n             text2.charAt(text2_length - y2 - 1)) {\n        x2++;\n        y2++;\n      }\n      v2[k2_offset] = x2;\n      if (x2 > text1_length) {\n        // Ran off the left of the graph.\n        k2end += 2;\n      } else if (y2 > text2_length) {\n        // Ran off the top of the graph.\n        k2start += 2;\n      } else if (!front) {\n        var k1_offset = v_offset + delta - k2;\n        if (k1_offset >= 0 && k1_offset < v_length && v1[k1_offset] != -1) {\n          var x1 = v1[k1_offset];\n          var y1 = v_offset + x1 - k1_offset;\n          // Mirror x2 onto top-left coordinate system.\n          x2 = text1_length - x2;\n          if (x1 >= x2) {\n            // Overlap detected.\n            return this.diff_bisectSplit_(text1, text2, x1, y1, deadline);\n          }\n        }\n      }\n    }\n  }\n  // Diff took too long and hit the deadline or\n  // number of diffs equals number of characters, no commonality at all.\n  return [new diff_match_patch.Diff(DIFF_DELETE, text1),\n          new diff_match_patch.Diff(DIFF_INSERT, text2)];\n};\n\n\n/**\n * Given the location of the 'middle snake', split the diff in two parts\n * and recurse.\n * @param {string} text1 Old string to be diffed.\n * @param {string} text2 New string to be diffed.\n * @param {number} x Index of split point in text1.\n * @param {number} y Index of split point in text2.\n * @param {number} deadline Time at which to bail if not yet complete.\n * @return {!Array.<!diff_match_patch.Diff>} Array of diff tuples.\n * @private\n */\ndiff_match_patch.prototype.diff_bisectSplit_ = function(text1, text2, x, y,\n    deadline) {\n  var text1a = text1.substring(0, x);\n  var text2a = text2.substring(0, y);\n  var text1b = text1.substring(x);\n  var text2b = text2.substring(y);\n\n  // Compute both diffs serially.\n  var diffs = this.diff_main(text1a, text2a, false, deadline);\n  var diffsb = this.diff_main(text1b, text2b, false, deadline);\n\n  return diffs.concat(diffsb);\n};\n\n\n/**\n * Split two texts into an array of strings.  Reduce the texts to a string of\n * hashes where each Unicode character represents one line.\n * @param {string} text1 First string.\n * @param {string} text2 Second string.\n * @return {{chars1: string, chars2: string, lineArray: !Array.<string>}}\n *     An object containing the encoded text1, the encoded text2 and\n *     the array of unique strings.\n *     The zeroth element of the array of unique strings is intentionally blank.\n * @private\n */\ndiff_match_patch.prototype.diff_linesToChars_ = function(text1, text2) {\n  var lineArray = [];  // e.g. lineArray[4] == 'Hello\\n'\n  var lineHash = {};   // e.g. lineHash['Hello\\n'] == 4\n\n  // '\\x00' is a valid character, but various debuggers don't like it.\n  // So we'll insert a junk entry to avoid generating a null character.\n  lineArray[0] = '';\n\n  /**\n   * Split a text into an array of strings.  Reduce the texts to a string of\n   * hashes where each Unicode character represents one line.\n   * Modifies linearray and linehash through being a closure.\n   * @param {string} text String to encode.\n   * @return {string} Encoded string.\n   * @private\n   */\n  function diff_linesToCharsMunge_(text) {\n    var chars = '';\n    // Walk the text, pulling out a substring for each line.\n    // text.split('\\n') would would temporarily double our memory footprint.\n    // Modifying text would create many large strings to garbage collect.\n    var lineStart = 0;\n    var lineEnd = -1;\n    // Keeping our own length variable is faster than looking it up.\n    var lineArrayLength = lineArray.length;\n    while (lineEnd < text.length - 1) {\n      lineEnd = text.indexOf('\\n', lineStart);\n      if (lineEnd == -1) {\n        lineEnd = text.length - 1;\n      }\n      var line = text.substring(lineStart, lineEnd + 1);\n\n      if (lineHash.hasOwnProperty ? lineHash.hasOwnProperty(line) :\n          (lineHash[line] !== undefined)) {\n        chars += String.fromCharCode(lineHash[line]);\n      } else {\n        if (lineArrayLength == maxLines) {\n          // Bail out at 65535 because\n          // String.fromCharCode(65536) == String.fromCharCode(0)\n          line = text.substring(lineStart);\n          lineEnd = text.length;\n        }\n        chars += String.fromCharCode(lineArrayLength);\n        lineHash[line] = lineArrayLength;\n        lineArray[lineArrayLength++] = line;\n      }\n      lineStart = lineEnd + 1;\n    }\n    return chars;\n  }\n  // Allocate 2/3rds of the space for text1, the rest for text2.\n  var maxLines = 40000;\n  var chars1 = diff_linesToCharsMunge_(text1);\n  maxLines = 65535;\n  var chars2 = diff_linesToCharsMunge_(text2);\n  return {chars1: chars1, chars2: chars2, lineArray: lineArray};\n};\n\n\n/**\n * Rehydrate the text in a diff from a string of line hashes to real lines of\n * text.\n * @param {!Array.<!diff_match_patch.Diff>} diffs Array of diff tuples.\n * @param {!Array.<string>} lineArray Array of unique strings.\n * @private\n */\ndiff_match_patch.prototype.diff_charsToLines_ = function(diffs, lineArray) {\n  for (var i = 0; i < diffs.length; i++) {\n    var chars = diffs[i][1];\n    var text = [];\n    for (var j = 0; j < chars.length; j++) {\n      text[j] = lineArray[chars.charCodeAt(j)];\n    }\n    diffs[i][1] = text.join('');\n  }\n};\n\n\n/**\n * Determine the common prefix of two strings.\n * @param {string} text1 First string.\n * @param {string} text2 Second string.\n * @return {number} The number of characters common to the start of each\n *     string.\n */\ndiff_match_patch.prototype.diff_commonPrefix = function(text1, text2) {\n  // Quick check for common null cases.\n  if (!text1 || !text2 || text1.charAt(0) != text2.charAt(0)) {\n    return 0;\n  }\n  // Binary search.\n  // Performance analysis: https://neil.fraser.name/news/2007/10/09/\n  var pointermin = 0;\n  var pointermax = Math.min(text1.length, text2.length);\n  var pointermid = pointermax;\n  var pointerstart = 0;\n  while (pointermin < pointermid) {\n    if (text1.substring(pointerstart, pointermid) ==\n        text2.substring(pointerstart, pointermid)) {\n      pointermin = pointermid;\n      pointerstart = pointermin;\n    } else {\n      pointermax = pointermid;\n    }\n    pointermid = Math.floor((pointermax - pointermin) / 2 + pointermin);\n  }\n  return pointermid;\n};\n\n\n/**\n * Determine the common suffix of two strings.\n * @param {string} text1 First string.\n * @param {string} text2 Second string.\n * @return {number} The number of characters common to the end of each string.\n */\ndiff_match_patch.prototype.diff_commonSuffix = function(text1, text2) {\n  // Quick check for common null cases.\n  if (!text1 || !text2 ||\n      text1.charAt(text1.length - 1) != text2.charAt(text2.length - 1)) {\n    return 0;\n  }\n  // Binary search.\n  // Performance analysis: https://neil.fraser.name/news/2007/10/09/\n  var pointermin = 0;\n  var pointermax = Math.min(text1.length, text2.length);\n  var pointermid = pointermax;\n  var pointerend = 0;\n  while (pointermin < pointermid) {\n    if (text1.substring(text1.length - pointermid, text1.length - pointerend) ==\n        text2.substring(text2.length - pointermid, text2.length - pointerend)) {\n      pointermin = pointermid;\n      pointerend = pointermin;\n    } else {\n      pointermax = pointermid;\n    }\n    pointermid = Math.floor((pointermax - pointermin) / 2 + pointermin);\n  }\n  return pointermid;\n};\n\n\n/**\n * Determine if the suffix of one string is the prefix of another.\n * @param {string} text1 First string.\n * @param {string} text2 Second string.\n * @return {number} The number of characters common to the end of the first\n *     string and the start of the second string.\n * @private\n */\ndiff_match_patch.prototype.diff_commonOverlap_ = function(text1, text2) {\n  // Cache the text lengths to prevent multiple calls.\n  var text1_length = text1.length;\n  var text2_length = text2.length;\n  // Eliminate the null case.\n  if (text1_length == 0 || text2_length == 0) {\n    return 0;\n  }\n  // Truncate the longer string.\n  if (text1_length > text2_length) {\n    text1 = text1.substring(text1_length - text2_length);\n  } else if (text1_length < text2_length) {\n    text2 = text2.substring(0, text1_length);\n  }\n  var text_length = Math.min(text1_length, text2_length);\n  // Quick check for the worst case.\n  if (text1 == text2) {\n    return text_length;\n  }\n\n  // Start by looking for a single character match\n  // and increase length until no match is found.\n  // Performance analysis: https://neil.fraser.name/news/2010/11/04/\n  var best = 0;\n  var length = 1;\n  while (true) {\n    var pattern = text1.substring(text_length - length);\n    var found = text2.indexOf(pattern);\n    if (found == -1) {\n      return best;\n    }\n    length += found;\n    if (found == 0 || text1.substring(text_length - length) ==\n        text2.substring(0, length)) {\n      best = length;\n      length++;\n    }\n  }\n};\n\n\n/**\n * Do the two texts share a substring which is at least half the length of the\n * longer text?\n * This speedup can produce non-minimal diffs.\n * @param {string} text1 First string.\n * @param {string} text2 Second string.\n * @return {Array.<string>} Five element Array, containing the prefix of\n *     text1, the suffix of text1, the prefix of text2, the suffix of\n *     text2 and the common middle.  Or null if there was no match.\n * @private\n */\ndiff_match_patch.prototype.diff_halfMatch_ = function(text1, text2) {\n  if (this.Diff_Timeout <= 0) {\n    // Don't risk returning a non-optimal diff if we have unlimited time.\n    return null;\n  }\n  var longtext = text1.length > text2.length ? text1 : text2;\n  var shorttext = text1.length > text2.length ? text2 : text1;\n  if (longtext.length < 4 || shorttext.length * 2 < longtext.length) {\n    return null;  // Pointless.\n  }\n  var dmp = this;  // 'this' becomes 'window' in a closure.\n\n  /**\n   * Does a substring of shorttext exist within longtext such that the substring\n   * is at least half the length of longtext?\n   * Closure, but does not reference any external variables.\n   * @param {string} longtext Longer string.\n   * @param {string} shorttext Shorter string.\n   * @param {number} i Start index of quarter length substring within longtext.\n   * @return {Array.<string>} Five element Array, containing the prefix of\n   *     longtext, the suffix of longtext, the prefix of shorttext, the suffix\n   *     of shorttext and the common middle.  Or null if there was no match.\n   * @private\n   */\n  function diff_halfMatchI_(longtext, shorttext, i) {\n    // Start with a 1/4 length substring at position i as a seed.\n    var seed = longtext.substring(i, i + Math.floor(longtext.length / 4));\n    var j = -1;\n    var best_common = '';\n    var best_longtext_a, best_longtext_b, best_shorttext_a, best_shorttext_b;\n    while ((j = shorttext.indexOf(seed, j + 1)) != -1) {\n      var prefixLength = dmp.diff_commonPrefix(longtext.substring(i),\n                                               shorttext.substring(j));\n      var suffixLength = dmp.diff_commonSuffix(longtext.substring(0, i),\n                                               shorttext.substring(0, j));\n      if (best_common.length < suffixLength + prefixLength) {\n        best_common = shorttext.substring(j - suffixLength, j) +\n            shorttext.substring(j, j + prefixLength);\n        best_longtext_a = longtext.substring(0, i - suffixLength);\n        best_longtext_b = longtext.substring(i + prefixLength);\n        best_shorttext_a = shorttext.substring(0, j - suffixLength);\n        best_shorttext_b = shorttext.substring(j + prefixLength);\n      }\n    }\n    if (best_common.length * 2 >= longtext.length) {\n      return [best_longtext_a, best_longtext_b,\n              best_shorttext_a, best_shorttext_b, best_common];\n    } else {\n      return null;\n    }\n  }\n\n  // First check if the second quarter is the seed for a half-match.\n  var hm1 = diff_halfMatchI_(longtext, shorttext,\n                             Math.ceil(longtext.length / 4));\n  // Check again based on the third quarter.\n  var hm2 = diff_halfMatchI_(longtext, shorttext,\n                             Math.ceil(longtext.length / 2));\n  var hm;\n  if (!hm1 && !hm2) {\n    return null;\n  } else if (!hm2) {\n    hm = hm1;\n  } else if (!hm1) {\n    hm = hm2;\n  } else {\n    // Both matched.  Select the longest.\n    hm = hm1[4].length > hm2[4].length ? hm1 : hm2;\n  }\n\n  // A half-match was found, sort out the return data.\n  var text1_a, text1_b, text2_a, text2_b;\n  if (text1.length > text2.length) {\n    text1_a = hm[0];\n    text1_b = hm[1];\n    text2_a = hm[2];\n    text2_b = hm[3];\n  } else {\n    text2_a = hm[0];\n    text2_b = hm[1];\n    text1_a = hm[2];\n    text1_b = hm[3];\n  }\n  var mid_common = hm[4];\n  return [text1_a, text1_b, text2_a, text2_b, mid_common];\n};\n\n\n/**\n * Reduce the number of edits by eliminating semantically trivial equalities.\n * @param {!Array.<!diff_match_patch.Diff>} diffs Array of diff tuples.\n */\ndiff_match_patch.prototype.diff_cleanupSemantic = function(diffs) {\n  var changes = false;\n  var equalities = [];  // Stack of indices where equalities are found.\n  var equalitiesLength = 0;  // Keeping our own length var is faster in JS.\n  /** @type {?string} */\n  var lastEquality = null;\n  // Always equal to diffs[equalities[equalitiesLength - 1]][1]\n  var pointer = 0;  // Index of current position.\n  // Number of characters that changed prior to the equality.\n  var length_insertions1 = 0;\n  var length_deletions1 = 0;\n  // Number of characters that changed after the equality.\n  var length_insertions2 = 0;\n  var length_deletions2 = 0;\n  while (pointer < diffs.length) {\n    if (diffs[pointer][0] == DIFF_EQUAL) {  // Equality found.\n      equalities[equalitiesLength++] = pointer;\n      length_insertions1 = length_insertions2;\n      length_deletions1 = length_deletions2;\n      length_insertions2 = 0;\n      length_deletions2 = 0;\n      lastEquality = diffs[pointer][1];\n    } else {  // An insertion or deletion.\n      if (diffs[pointer][0] == DIFF_INSERT) {\n        length_insertions2 += diffs[pointer][1].length;\n      } else {\n        length_deletions2 += diffs[pointer][1].length;\n      }\n      // Eliminate an equality that is smaller or equal to the edits on both\n      // sides of it.\n      if (lastEquality && (lastEquality.length <=\n          Math.max(length_insertions1, length_deletions1)) &&\n          (lastEquality.length <= Math.max(length_insertions2,\n                                           length_deletions2))) {\n        // Duplicate record.\n        diffs.splice(equalities[equalitiesLength - 1], 0,\n                     new diff_match_patch.Diff(DIFF_DELETE, lastEquality));\n        // Change second copy to insert.\n        diffs[equalities[equalitiesLength - 1] + 1][0] = DIFF_INSERT;\n        // Throw away the equality we just deleted.\n        equalitiesLength--;\n        // Throw away the previous equality (it needs to be reevaluated).\n        equalitiesLength--;\n        pointer = equalitiesLength > 0 ? equalities[equalitiesLength - 1] : -1;\n        length_insertions1 = 0;  // Reset the counters.\n        length_deletions1 = 0;\n        length_insertions2 = 0;\n        length_deletions2 = 0;\n        lastEquality = null;\n        changes = true;\n      }\n    }\n    pointer++;\n  }\n\n  // Normalize the diff.\n  if (changes) {\n    this.diff_cleanupMerge(diffs);\n  }\n  this.diff_cleanupSemanticLossless(diffs);\n\n  // Find any overlaps between deletions and insertions.\n  // e.g: <del>abcxxx</del><ins>xxxdef</ins>\n  //   -> <del>abc</del>xxx<ins>def</ins>\n  // e.g: <del>xxxabc</del><ins>defxxx</ins>\n  //   -> <ins>def</ins>xxx<del>abc</del>\n  // Only extract an overlap if it is as big as the edit ahead or behind it.\n  pointer = 1;\n  while (pointer < diffs.length) {\n    if (diffs[pointer - 1][0] == DIFF_DELETE &&\n        diffs[pointer][0] == DIFF_INSERT) {\n      var deletion = diffs[pointer - 1][1];\n      var insertion = diffs[pointer][1];\n      var overlap_length1 = this.diff_commonOverlap_(deletion, insertion);\n      var overlap_length2 = this.diff_commonOverlap_(insertion, deletion);\n      if (overlap_length1 >= overlap_length2) {\n        if (overlap_length1 >= deletion.length / 2 ||\n            overlap_length1 >= insertion.length / 2) {\n          // Overlap found.  Insert an equality and trim the surrounding edits.\n          diffs.splice(pointer, 0, new diff_match_patch.Diff(DIFF_EQUAL,\n              insertion.substring(0, overlap_length1)));\n          diffs[pointer - 1][1] =\n              deletion.substring(0, deletion.length - overlap_length1);\n          diffs[pointer + 1][1] = insertion.substring(overlap_length1);\n          pointer++;\n        }\n      } else {\n        if (overlap_length2 >= deletion.length / 2 ||\n            overlap_length2 >= insertion.length / 2) {\n          // Reverse overlap found.\n          // Insert an equality and swap and trim the surrounding edits.\n          diffs.splice(pointer, 0, new diff_match_patch.Diff(DIFF_EQUAL,\n              deletion.substring(0, overlap_length2)));\n          diffs[pointer - 1][0] = DIFF_INSERT;\n          diffs[pointer - 1][1] =\n              insertion.substring(0, insertion.length - overlap_length2);\n          diffs[pointer + 1][0] = DIFF_DELETE;\n          diffs[pointer + 1][1] =\n              deletion.substring(overlap_length2);\n          pointer++;\n        }\n      }\n      pointer++;\n    }\n    pointer++;\n  }\n};\n\n\n/**\n * Look for single edits surrounded on both sides by equalities\n * which can be shifted sideways to align the edit to a word boundary.\n * e.g: The c<ins>at c</ins>ame. -> The <ins>cat </ins>came.\n * @param {!Array.<!diff_match_patch.Diff>} diffs Array of diff tuples.\n */\ndiff_match_patch.prototype.diff_cleanupSemanticLossless = function(diffs) {\n  /**\n   * Given two strings, compute a score representing whether the internal\n   * boundary falls on logical boundaries.\n   * Scores range from 6 (best) to 0 (worst).\n   * Closure, but does not reference any external variables.\n   * @param {string} one First string.\n   * @param {string} two Second string.\n   * @return {number} The score.\n   * @private\n   */\n  function diff_cleanupSemanticScore_(one, two) {\n    if (!one || !two) {\n      // Edges are the best.\n      return 6;\n    }\n\n    // Each port of this function behaves slightly differently due to\n    // subtle differences in each language's definition of things like\n    // 'whitespace'.  Since this function's purpose is largely cosmetic,\n    // the choice has been made to use each language's native features\n    // rather than force total conformity.\n    var char1 = one.charAt(one.length - 1);\n    var char2 = two.charAt(0);\n    var nonAlphaNumeric1 = char1.match(diff_match_patch.nonAlphaNumericRegex_);\n    var nonAlphaNumeric2 = char2.match(diff_match_patch.nonAlphaNumericRegex_);\n    var whitespace1 = nonAlphaNumeric1 &&\n        char1.match(diff_match_patch.whitespaceRegex_);\n    var whitespace2 = nonAlphaNumeric2 &&\n        char2.match(diff_match_patch.whitespaceRegex_);\n    var lineBreak1 = whitespace1 &&\n        char1.match(diff_match_patch.linebreakRegex_);\n    var lineBreak2 = whitespace2 &&\n        char2.match(diff_match_patch.linebreakRegex_);\n    var blankLine1 = lineBreak1 &&\n        one.match(diff_match_patch.blanklineEndRegex_);\n    var blankLine2 = lineBreak2 &&\n        two.match(diff_match_patch.blanklineStartRegex_);\n\n    if (blankLine1 || blankLine2) {\n      // Five points for blank lines.\n      return 5;\n    } else if (lineBreak1 || lineBreak2) {\n      // Four points for line breaks.\n      return 4;\n    } else if (nonAlphaNumeric1 && !whitespace1 && whitespace2) {\n      // Three points for end of sentences.\n      return 3;\n    } else if (whitespace1 || whitespace2) {\n      // Two points for whitespace.\n      return 2;\n    } else if (nonAlphaNumeric1 || nonAlphaNumeric2) {\n      // One point for non-alphanumeric.\n      return 1;\n    }\n    return 0;\n  }\n\n  var pointer = 1;\n  // Intentionally ignore the first and last element (don't need checking).\n  while (pointer < diffs.length - 1) {\n    if (diffs[pointer - 1][0] == DIFF_EQUAL &&\n        diffs[pointer + 1][0] == DIFF_EQUAL) {\n      // This is a single edit surrounded by equalities.\n      var equality1 = diffs[pointer - 1][1];\n      var edit = diffs[pointer][1];\n      var equality2 = diffs[pointer + 1][1];\n\n      // First, shift the edit as far left as possible.\n      var commonOffset = this.diff_commonSuffix(equality1, edit);\n      if (commonOffset) {\n        var commonString = edit.substring(edit.length - commonOffset);\n        equality1 = equality1.substring(0, equality1.length - commonOffset);\n        edit = commonString + edit.substring(0, edit.length - commonOffset);\n        equality2 = commonString + equality2;\n      }\n\n      // Second, step character by character right, looking for the best fit.\n      var bestEquality1 = equality1;\n      var bestEdit = edit;\n      var bestEquality2 = equality2;\n      var bestScore = diff_cleanupSemanticScore_(equality1, edit) +\n          diff_cleanupSemanticScore_(edit, equality2);\n      while (edit.charAt(0) === equality2.charAt(0)) {\n        equality1 += edit.charAt(0);\n        edit = edit.substring(1) + equality2.charAt(0);\n        equality2 = equality2.substring(1);\n        var score = diff_cleanupSemanticScore_(equality1, edit) +\n            diff_cleanupSemanticScore_(edit, equality2);\n        // The >= encourages trailing rather than leading whitespace on edits.\n        if (score >= bestScore) {\n          bestScore = score;\n          bestEquality1 = equality1;\n          bestEdit = edit;\n          bestEquality2 = equality2;\n        }\n      }\n\n      if (diffs[pointer - 1][1] != bestEquality1) {\n        // We have an improvement, save it back to the diff.\n        if (bestEquality1) {\n          diffs[pointer - 1][1] = bestEquality1;\n        } else {\n          diffs.splice(pointer - 1, 1);\n          pointer--;\n        }\n        diffs[pointer][1] = bestEdit;\n        if (bestEquality2) {\n          diffs[pointer + 1][1] = bestEquality2;\n        } else {\n          diffs.splice(pointer + 1, 1);\n          pointer--;\n        }\n      }\n    }\n    pointer++;\n  }\n};\n\n// Define some regex patterns for matching boundaries.\ndiff_match_patch.nonAlphaNumericRegex_ = /[^a-zA-Z0-9]/;\ndiff_match_patch.whitespaceRegex_ = /\\s/;\ndiff_match_patch.linebreakRegex_ = /[\\r\\n]/;\ndiff_match_patch.blanklineEndRegex_ = /\\n\\r?\\n$/;\ndiff_match_patch.blanklineStartRegex_ = /^\\r?\\n\\r?\\n/;\n\n/**\n * Reduce the number of edits by eliminating operationally trivial equalities.\n * @param {!Array.<!diff_match_patch.Diff>} diffs Array of diff tuples.\n */\ndiff_match_patch.prototype.diff_cleanupEfficiency = function(diffs) {\n  var changes = false;\n  var equalities = [];  // Stack of indices where equalities are found.\n  var equalitiesLength = 0;  // Keeping our own length var is faster in JS.\n  /** @type {?string} */\n  var lastEquality = null;\n  // Always equal to diffs[equalities[equalitiesLength - 1]][1]\n  var pointer = 0;  // Index of current position.\n  // Is there an insertion operation before the last equality.\n  var pre_ins = false;\n  // Is there a deletion operation before the last equality.\n  var pre_del = false;\n  // Is there an insertion operation after the last equality.\n  var post_ins = false;\n  // Is there a deletion operation after the last equality.\n  var post_del = false;\n  while (pointer < diffs.length) {\n    if (diffs[pointer][0] == DIFF_EQUAL) {  // Equality found.\n      if (diffs[pointer][1].length < this.Diff_EditCost &&\n          (post_ins || post_del)) {\n        // Candidate found.\n        equalities[equalitiesLength++] = pointer;\n        pre_ins = post_ins;\n        pre_del = post_del;\n        lastEquality = diffs[pointer][1];\n      } else {\n        // Not a candidate, and can never become one.\n        equalitiesLength = 0;\n        lastEquality = null;\n      }\n      post_ins = post_del = false;\n    } else {  // An insertion or deletion.\n      if (diffs[pointer][0] == DIFF_DELETE) {\n        post_del = true;\n      } else {\n        post_ins = true;\n      }\n      /*\n       * Five types to be split:\n       * <ins>A</ins><del>B</del>XY<ins>C</ins><del>D</del>\n       * <ins>A</ins>X<ins>C</ins><del>D</del>\n       * <ins>A</ins><del>B</del>X<ins>C</ins>\n       * <ins>A</del>X<ins>C</ins><del>D</del>\n       * <ins>A</ins><del>B</del>X<del>C</del>\n       */\n      if (lastEquality && ((pre_ins && pre_del && post_ins && post_del) ||\n                           ((lastEquality.length < this.Diff_EditCost / 2) &&\n                            (pre_ins + pre_del + post_ins + post_del) == 3))) {\n        // Duplicate record.\n        diffs.splice(equalities[equalitiesLength - 1], 0,\n                     new diff_match_patch.Diff(DIFF_DELETE, lastEquality));\n        // Change second copy to insert.\n        diffs[equalities[equalitiesLength - 1] + 1][0] = DIFF_INSERT;\n        equalitiesLength--;  // Throw away the equality we just deleted;\n        lastEquality = null;\n        if (pre_ins && pre_del) {\n          // No changes made which could affect previous entry, keep going.\n          post_ins = post_del = true;\n          equalitiesLength = 0;\n        } else {\n          equalitiesLength--;  // Throw away the previous equality.\n          pointer = equalitiesLength > 0 ?\n              equalities[equalitiesLength - 1] : -1;\n          post_ins = post_del = false;\n        }\n        changes = true;\n      }\n    }\n    pointer++;\n  }\n\n  if (changes) {\n    this.diff_cleanupMerge(diffs);\n  }\n};\n\n\n/**\n * Reorder and merge like edit sections.  Merge equalities.\n * Any edit section can move as long as it doesn't cross an equality.\n * @param {!Array.<!diff_match_patch.Diff>} diffs Array of diff tuples.\n */\ndiff_match_patch.prototype.diff_cleanupMerge = function(diffs) {\n  // Add a dummy entry at the end.\n  diffs.push(new diff_match_patch.Diff(DIFF_EQUAL, ''));\n  var pointer = 0;\n  var count_delete = 0;\n  var count_insert = 0;\n  var text_delete = '';\n  var text_insert = '';\n  var commonlength;\n  while (pointer < diffs.length) {\n    switch (diffs[pointer][0]) {\n      case DIFF_INSERT:\n        count_insert++;\n        text_insert += diffs[pointer][1];\n        pointer++;\n        break;\n      case DIFF_DELETE:\n        count_delete++;\n        text_delete += diffs[pointer][1];\n        pointer++;\n        break;\n      case DIFF_EQUAL:\n        // Upon reaching an equality, check for prior redundancies.\n        if (count_delete + count_insert > 1) {\n          if (count_delete !== 0 && count_insert !== 0) {\n            // Factor out any common prefixies.\n            commonlength = this.diff_commonPrefix(text_insert, text_delete);\n            if (commonlength !== 0) {\n              if ((pointer - count_delete - count_insert) > 0 &&\n                  diffs[pointer - count_delete - count_insert - 1][0] ==\n                  DIFF_EQUAL) {\n                diffs[pointer - count_delete - count_insert - 1][1] +=\n                    text_insert.substring(0, commonlength);\n              } else {\n                diffs.splice(0, 0, new diff_match_patch.Diff(DIFF_EQUAL,\n                    text_insert.substring(0, commonlength)));\n                pointer++;\n              }\n              text_insert = text_insert.substring(commonlength);\n              text_delete = text_delete.substring(commonlength);\n            }\n            // Factor out any common suffixies.\n            commonlength = this.diff_commonSuffix(text_insert, text_delete);\n            if (commonlength !== 0) {\n              diffs[pointer][1] = text_insert.substring(text_insert.length -\n                  commonlength) + diffs[pointer][1];\n              text_insert = text_insert.substring(0, text_insert.length -\n                  commonlength);\n              text_delete = text_delete.substring(0, text_delete.length -\n                  commonlength);\n            }\n          }\n          // Delete the offending records and add the merged ones.\n          pointer -= count_delete + count_insert;\n          diffs.splice(pointer, count_delete + count_insert);\n          if (text_delete.length) {\n            diffs.splice(pointer, 0,\n                new diff_match_patch.Diff(DIFF_DELETE, text_delete));\n            pointer++;\n          }\n          if (text_insert.length) {\n            diffs.splice(pointer, 0,\n                new diff_match_patch.Diff(DIFF_INSERT, text_insert));\n            pointer++;\n          }\n          pointer++;\n        } else if (pointer !== 0 && diffs[pointer - 1][0] == DIFF_EQUAL) {\n          // Merge this equality with the previous one.\n          diffs[pointer - 1][1] += diffs[pointer][1];\n          diffs.splice(pointer, 1);\n        } else {\n          pointer++;\n        }\n        count_insert = 0;\n        count_delete = 0;\n        text_delete = '';\n        text_insert = '';\n        break;\n    }\n  }\n  if (diffs[diffs.length - 1][1] === '') {\n    diffs.pop();  // Remove the dummy entry at the end.\n  }\n\n  // Second pass: look for single edits surrounded on both sides by equalities\n  // which can be shifted sideways to eliminate an equality.\n  // e.g: A<ins>BA</ins>C -> <ins>AB</ins>AC\n  var changes = false;\n  pointer = 1;\n  // Intentionally ignore the first and last element (don't need checking).\n  while (pointer < diffs.length - 1) {\n    if (diffs[pointer - 1][0] == DIFF_EQUAL &&\n        diffs[pointer + 1][0] == DIFF_EQUAL) {\n      // This is a single edit surrounded by equalities.\n      if (diffs[pointer][1].substring(diffs[pointer][1].length -\n          diffs[pointer - 1][1].length) == diffs[pointer - 1][1]) {\n        // Shift the edit over the previous equality.\n        diffs[pointer][1] = diffs[pointer - 1][1] +\n            diffs[pointer][1].substring(0, diffs[pointer][1].length -\n                                        diffs[pointer - 1][1].length);\n        diffs[pointer + 1][1] = diffs[pointer - 1][1] + diffs[pointer + 1][1];\n        diffs.splice(pointer - 1, 1);\n        changes = true;\n      } else if (diffs[pointer][1].substring(0, diffs[pointer + 1][1].length) ==\n          diffs[pointer + 1][1]) {\n        // Shift the edit over the next equality.\n        diffs[pointer - 1][1] += diffs[pointer + 1][1];\n        diffs[pointer][1] =\n            diffs[pointer][1].substring(diffs[pointer + 1][1].length) +\n            diffs[pointer + 1][1];\n        diffs.splice(pointer + 1, 1);\n        changes = true;\n      }\n    }\n    pointer++;\n  }\n  // If shifts were made, the diff needs reordering and another shift sweep.\n  if (changes) {\n    this.diff_cleanupMerge(diffs);\n  }\n};\n\n\n/**\n * loc is a location in text1, compute and return the equivalent location in\n * text2.\n * e.g. 'The cat' vs 'The big cat', 1->1, 5->8\n * @param {!Array.<!diff_match_patch.Diff>} diffs Array of diff tuples.\n * @param {number} loc Location within text1.\n * @return {number} Location within text2.\n */\ndiff_match_patch.prototype.diff_xIndex = function(diffs, loc) {\n  var chars1 = 0;\n  var chars2 = 0;\n  var last_chars1 = 0;\n  var last_chars2 = 0;\n  var x;\n  for (x = 0; x < diffs.length; x++) {\n    if (diffs[x][0] !== DIFF_INSERT) {  // Equality or deletion.\n      chars1 += diffs[x][1].length;\n    }\n    if (diffs[x][0] !== DIFF_DELETE) {  // Equality or insertion.\n      chars2 += diffs[x][1].length;\n    }\n    if (chars1 > loc) {  // Overshot the location.\n      break;\n    }\n    last_chars1 = chars1;\n    last_chars2 = chars2;\n  }\n  // Was the location was deleted?\n  if (diffs.length != x && diffs[x][0] === DIFF_DELETE) {\n    return last_chars2;\n  }\n  // Add the remaining character length.\n  return last_chars2 + (loc - last_chars1);\n};\n\n\n/**\n * Convert a diff array into a pretty HTML report.\n * @param {!Array.<!diff_match_patch.Diff>} diffs Array of diff tuples.\n * @return {string} HTML representation.\n */\ndiff_match_patch.prototype.diff_prettyHtml = function(diffs) {\n  var html = [];\n  var pattern_amp = /&/g;\n  var pattern_lt = /</g;\n  var pattern_gt = />/g;\n  var pattern_para = /\\n/g;\n  for (var x = 0; x < diffs.length; x++) {\n    var op = diffs[x][0];    // Operation (insert, delete, equal)\n    var data = diffs[x][1];  // Text of change.\n    var text = data.replace(pattern_amp, '&amp;').replace(pattern_lt, '&lt;')\n        .replace(pattern_gt, '&gt;').replace(pattern_para, '&para;<br>');\n    switch (op) {\n      case DIFF_INSERT:\n        html[x] = '<ins style=\"background:#e6ffe6;\">' + text + '</ins>';\n        break;\n      case DIFF_DELETE:\n        html[x] = '<del style=\"background:#ffe6e6;\">' + text + '</del>';\n        break;\n      case DIFF_EQUAL:\n        html[x] = '<span>' + text + '</span>';\n        break;\n    }\n  }\n  return html.join('');\n};\n\n\n/**\n * Compute and return the source text (all equalities and deletions).\n * @param {!Array.<!diff_match_patch.Diff>} diffs Array of diff tuples.\n * @return {string} Source text.\n */\ndiff_match_patch.prototype.diff_text1 = function(diffs) {\n  var text = [];\n  for (var x = 0; x < diffs.length; x++) {\n    if (diffs[x][0] !== DIFF_INSERT) {\n      text[x] = diffs[x][1];\n    }\n  }\n  return text.join('');\n};\n\n\n/**\n * Compute and return the destination text (all equalities and insertions).\n * @param {!Array.<!diff_match_patch.Diff>} diffs Array of diff tuples.\n * @return {string} Destination text.\n */\ndiff_match_patch.prototype.diff_text2 = function(diffs) {\n  var text = [];\n  for (var x = 0; x < diffs.length; x++) {\n    if (diffs[x][0] !== DIFF_DELETE) {\n      text[x] = diffs[x][1];\n    }\n  }\n  return text.join('');\n};\n\n\n/**\n * Compute the Levenshtein distance; the number of inserted, deleted or\n * substituted characters.\n * @param {!Array.<!diff_match_patch.Diff>} diffs Array of diff tuples.\n * @return {number} Number of changes.\n */\ndiff_match_patch.prototype.diff_levenshtein = function(diffs) {\n  var levenshtein = 0;\n  var insertions = 0;\n  var deletions = 0;\n  for (var x = 0; x < diffs.length; x++) {\n    var op = diffs[x][0];\n    var data = diffs[x][1];\n    switch (op) {\n      case DIFF_INSERT:\n        insertions += data.length;\n        break;\n      case DIFF_DELETE:\n        deletions += data.length;\n        break;\n      case DIFF_EQUAL:\n        // A deletion and an insertion is one substitution.\n        levenshtein += Math.max(insertions, deletions);\n        insertions = 0;\n        deletions = 0;\n        break;\n    }\n  }\n  levenshtein += Math.max(insertions, deletions);\n  return levenshtein;\n};\n\n\n/**\n * Crush the diff into an encoded string which describes the operations\n * required to transform text1 into text2.\n * E.g. =3\\t-2\\t+ing  -> Keep 3 chars, delete 2 chars, insert 'ing'.\n * Operations are tab-separated.  Inserted text is escaped using %xx notation.\n * @param {!Array.<!diff_match_patch.Diff>} diffs Array of diff tuples.\n * @return {string} Delta text.\n */\ndiff_match_patch.prototype.diff_toDelta = function(diffs) {\n  var text = [];\n  for (var x = 0; x < diffs.length; x++) {\n    switch (diffs[x][0]) {\n      case DIFF_INSERT:\n        text[x] = '+' + encodeURI(diffs[x][1]);\n        break;\n      case DIFF_DELETE:\n        text[x] = '-' + diffs[x][1].length;\n        break;\n      case DIFF_EQUAL:\n        text[x] = '=' + diffs[x][1].length;\n        break;\n    }\n  }\n  return text.join('\\t').replace(/%20/g, ' ');\n};\n\n\n/**\n * Given the original text1, and an encoded string which describes the\n * operations required to transform text1 into text2, compute the full diff.\n * @param {string} text1 Source string for the diff.\n * @param {string} delta Delta text.\n * @return {!Array.<!diff_match_patch.Diff>} Array of diff tuples.\n * @throws {!Error} If invalid input.\n */\ndiff_match_patch.prototype.diff_fromDelta = function(text1, delta) {\n  var diffs = [];\n  var diffsLength = 0;  // Keeping our own length var is faster in JS.\n  var pointer = 0;  // Cursor in text1\n  var tokens = delta.split(/\\t/g);\n  for (var x = 0; x < tokens.length; x++) {\n    // Each token begins with a one character parameter which specifies the\n    // operation of this token (delete, insert, equality).\n    var param = tokens[x].substring(1);\n    switch (tokens[x].charAt(0)) {\n      case '+':\n        try {\n          diffs[diffsLength++] =\n              new diff_match_patch.Diff(DIFF_INSERT, decodeURI(param));\n        } catch (ex) {\n          // Malformed URI sequence.\n          throw new Error('Illegal escape in diff_fromDelta: ' + param);\n        }\n        break;\n      case '-':\n        // Fall through.\n      case '=':\n        var n = parseInt(param, 10);\n        if (isNaN(n) || n < 0) {\n          throw new Error('Invalid number in diff_fromDelta: ' + param);\n        }\n        var text = text1.substring(pointer, pointer += n);\n        if (tokens[x].charAt(0) == '=') {\n          diffs[diffsLength++] = new diff_match_patch.Diff(DIFF_EQUAL, text);\n        } else {\n          diffs[diffsLength++] = new diff_match_patch.Diff(DIFF_DELETE, text);\n        }\n        break;\n      default:\n        // Blank tokens are ok (from a trailing \\t).\n        // Anything else is an error.\n        if (tokens[x]) {\n          throw new Error('Invalid diff operation in diff_fromDelta: ' +\n                          tokens[x]);\n        }\n    }\n  }\n  if (pointer != text1.length) {\n    throw new Error('Delta length (' + pointer +\n        ') does not equal source text length (' + text1.length + ').');\n  }\n  return diffs;\n};\n\n\n//  MATCH FUNCTIONS\n\n\n/**\n * Locate the best instance of 'pattern' in 'text' near 'loc'.\n * @param {string} text The text to search.\n * @param {string} pattern The pattern to search for.\n * @param {number} loc The location to search around.\n * @return {number} Best match index or -1.\n */\ndiff_match_patch.prototype.match_main = function(text, pattern, loc) {\n  // Check for null inputs.\n  if (text == null || pattern == null || loc == null) {\n    throw new Error('Null input. (match_main)');\n  }\n\n  loc = Math.max(0, Math.min(loc, text.length));\n  if (text == pattern) {\n    // Shortcut (potentially not guaranteed by the algorithm)\n    return 0;\n  } else if (!text.length) {\n    // Nothing to match.\n    return -1;\n  } else if (text.substring(loc, loc + pattern.length) == pattern) {\n    // Perfect match at the perfect spot!  (Includes case of null pattern)\n    return loc;\n  } else {\n    // Do a fuzzy compare.\n    return this.match_bitap_(text, pattern, loc);\n  }\n};\n\n\n/**\n * Locate the best instance of 'pattern' in 'text' near 'loc' using the\n * Bitap algorithm.\n * @param {string} text The text to search.\n * @param {string} pattern The pattern to search for.\n * @param {number} loc The location to search around.\n * @return {number} Best match index or -1.\n * @private\n */\ndiff_match_patch.prototype.match_bitap_ = function(text, pattern, loc) {\n  if (pattern.length > this.Match_MaxBits) {\n    throw new Error('Pattern too long for this browser.');\n  }\n\n  // Initialise the alphabet.\n  var s = this.match_alphabet_(pattern);\n\n  var dmp = this;  // 'this' becomes 'window' in a closure.\n\n  /**\n   * Compute and return the score for a match with e errors and x location.\n   * Accesses loc and pattern through being a closure.\n   * @param {number} e Number of errors in match.\n   * @param {number} x Location of match.\n   * @return {number} Overall score for match (0.0 = good, 1.0 = bad).\n   * @private\n   */\n  function match_bitapScore_(e, x) {\n    var accuracy = e / pattern.length;\n    var proximity = Math.abs(loc - x);\n    if (!dmp.Match_Distance) {\n      // Dodge divide by zero error.\n      return proximity ? 1.0 : accuracy;\n    }\n    return accuracy + (proximity / dmp.Match_Distance);\n  }\n\n  // Highest score beyond which we give up.\n  var score_threshold = this.Match_Threshold;\n  // Is there a nearby exact match? (speedup)\n  var best_loc = text.indexOf(pattern, loc);\n  if (best_loc != -1) {\n    score_threshold = Math.min(match_bitapScore_(0, best_loc), score_threshold);\n    // What about in the other direction? (speedup)\n    best_loc = text.lastIndexOf(pattern, loc + pattern.length);\n    if (best_loc != -1) {\n      score_threshold =\n          Math.min(match_bitapScore_(0, best_loc), score_threshold);\n    }\n  }\n\n  // Initialise the bit arrays.\n  var matchmask = 1 << (pattern.length - 1);\n  best_loc = -1;\n\n  var bin_min, bin_mid;\n  var bin_max = pattern.length + text.length;\n  var last_rd;\n  for (var d = 0; d < pattern.length; d++) {\n    // Scan for the best match; each iteration allows for one more error.\n    // Run a binary search to determine how far from 'loc' we can stray at this\n    // error level.\n    bin_min = 0;\n    bin_mid = bin_max;\n    while (bin_min < bin_mid) {\n      if (match_bitapScore_(d, loc + bin_mid) <= score_threshold) {\n        bin_min = bin_mid;\n      } else {\n        bin_max = bin_mid;\n      }\n      bin_mid = Math.floor((bin_max - bin_min) / 2 + bin_min);\n    }\n    // Use the result from this iteration as the maximum for the next.\n    bin_max = bin_mid;\n    var start = Math.max(1, loc - bin_mid + 1);\n    var finish = Math.min(loc + bin_mid, text.length) + pattern.length;\n\n    var rd = Array(finish + 2);\n    rd[finish + 1] = (1 << d) - 1;\n    for (var j = finish; j >= start; j--) {\n      // The alphabet (s) is a sparse hash, so the following line generates\n      // warnings.\n      var charMatch = s[text.charAt(j - 1)];\n      if (d === 0) {  // First pass: exact match.\n        rd[j] = ((rd[j + 1] << 1) | 1) & charMatch;\n      } else {  // Subsequent passes: fuzzy match.\n        rd[j] = (((rd[j + 1] << 1) | 1) & charMatch) |\n                (((last_rd[j + 1] | last_rd[j]) << 1) | 1) |\n                last_rd[j + 1];\n      }\n      if (rd[j] & matchmask) {\n        var score = match_bitapScore_(d, j - 1);\n        // This match will almost certainly be better than any existing match.\n        // But check anyway.\n        if (score <= score_threshold) {\n          // Told you so.\n          score_threshold = score;\n          best_loc = j - 1;\n          if (best_loc > loc) {\n            // When passing loc, don't exceed our current distance from loc.\n            start = Math.max(1, 2 * loc - best_loc);\n          } else {\n            // Already passed loc, downhill from here on in.\n            break;\n          }\n        }\n      }\n    }\n    // No hope for a (better) match at greater error levels.\n    if (match_bitapScore_(d + 1, loc) > score_threshold) {\n      break;\n    }\n    last_rd = rd;\n  }\n  return best_loc;\n};\n\n\n/**\n * Initialise the alphabet for the Bitap algorithm.\n * @param {string} pattern The text to encode.\n * @return {!Object} Hash of character locations.\n * @private\n */\ndiff_match_patch.prototype.match_alphabet_ = function(pattern) {\n  var s = {};\n  for (var i = 0; i < pattern.length; i++) {\n    s[pattern.charAt(i)] = 0;\n  }\n  for (var i = 0; i < pattern.length; i++) {\n    s[pattern.charAt(i)] |= 1 << (pattern.length - i - 1);\n  }\n  return s;\n};\n\n\n//  PATCH FUNCTIONS\n\n\n/**\n * Increase the context until it is unique,\n * but don't let the pattern expand beyond Match_MaxBits.\n * @param {!diff_match_patch.patch_obj} patch The patch to grow.\n * @param {string} text Source text.\n * @private\n */\ndiff_match_patch.prototype.patch_addContext_ = function(patch, text) {\n  if (text.length == 0) {\n    return;\n  }\n  if (patch.start2 === null) {\n    throw Error('patch not initialized');\n  }\n  var pattern = text.substring(patch.start2, patch.start2 + patch.length1);\n  var padding = 0;\n\n  // Look for the first and last matches of pattern in text.  If two different\n  // matches are found, increase the pattern length.\n  while (text.indexOf(pattern) != text.lastIndexOf(pattern) &&\n         pattern.length < this.Match_MaxBits - this.Patch_Margin -\n         this.Patch_Margin) {\n    padding += this.Patch_Margin;\n    pattern = text.substring(patch.start2 - padding,\n                             patch.start2 + patch.length1 + padding);\n  }\n  // Add one chunk for good luck.\n  padding += this.Patch_Margin;\n\n  // Add the prefix.\n  var prefix = text.substring(patch.start2 - padding, patch.start2);\n  if (prefix) {\n    patch.diffs.unshift(new diff_match_patch.Diff(DIFF_EQUAL, prefix));\n  }\n  // Add the suffix.\n  var suffix = text.substring(patch.start2 + patch.length1,\n                              patch.start2 + patch.length1 + padding);\n  if (suffix) {\n    patch.diffs.push(new diff_match_patch.Diff(DIFF_EQUAL, suffix));\n  }\n\n  // Roll back the start points.\n  patch.start1 -= prefix.length;\n  patch.start2 -= prefix.length;\n  // Extend the lengths.\n  patch.length1 += prefix.length + suffix.length;\n  patch.length2 += prefix.length + suffix.length;\n};\n\n\n/**\n * Compute a list of patches to turn text1 into text2.\n * Use diffs if provided, otherwise compute it ourselves.\n * There are four ways to call this function, depending on what data is\n * available to the caller:\n * Method 1:\n * a = text1, b = text2\n * Method 2:\n * a = diffs\n * Method 3 (optimal):\n * a = text1, b = diffs\n * Method 4 (deprecated, use method 3):\n * a = text1, b = text2, c = diffs\n *\n * @param {string|!Array.<!diff_match_patch.Diff>} a text1 (methods 1,3,4) or\n * Array of diff tuples for text1 to text2 (method 2).\n * @param {string|!Array.<!diff_match_patch.Diff>=} opt_b text2 (methods 1,4) or\n * Array of diff tuples for text1 to text2 (method 3) or undefined (method 2).\n * @param {string|!Array.<!diff_match_patch.Diff>=} opt_c Array of diff tuples\n * for text1 to text2 (method 4) or undefined (methods 1,2,3).\n * @return {!Array.<!diff_match_patch.patch_obj>} Array of Patch objects.\n */\ndiff_match_patch.prototype.patch_make = function(a, opt_b, opt_c) {\n  var text1, diffs;\n  if (typeof a == 'string' && typeof opt_b == 'string' &&\n      typeof opt_c == 'undefined') {\n    // Method 1: text1, text2\n    // Compute diffs from text1 and text2.\n    text1 = /** @type {string} */(a);\n    diffs = this.diff_main(text1, /** @type {string} */(opt_b), true);\n    if (diffs.length > 2) {\n      this.diff_cleanupSemantic(diffs);\n      this.diff_cleanupEfficiency(diffs);\n    }\n  } else if (a && typeof a == 'object' && typeof opt_b == 'undefined' &&\n      typeof opt_c == 'undefined') {\n    // Method 2: diffs\n    // Compute text1 from diffs.\n    diffs = /** @type {!Array.<!diff_match_patch.Diff>} */(a);\n    text1 = this.diff_text1(diffs);\n  } else if (typeof a == 'string' && opt_b && typeof opt_b == 'object' &&\n      typeof opt_c == 'undefined') {\n    // Method 3: text1, diffs\n    text1 = /** @type {string} */(a);\n    diffs = /** @type {!Array.<!diff_match_patch.Diff>} */(opt_b);\n  } else if (typeof a == 'string' && typeof opt_b == 'string' &&\n      opt_c && typeof opt_c == 'object') {\n    // Method 4: text1, text2, diffs\n    // text2 is not used.\n    text1 = /** @type {string} */(a);\n    diffs = /** @type {!Array.<!diff_match_patch.Diff>} */(opt_c);\n  } else {\n    throw new Error('Unknown call format to patch_make.');\n  }\n\n  if (diffs.length === 0) {\n    return [];  // Get rid of the null case.\n  }\n  var patches = [];\n  var patch = new diff_match_patch.patch_obj();\n  var patchDiffLength = 0;  // Keeping our own length var is faster in JS.\n  var char_count1 = 0;  // Number of characters into the text1 string.\n  var char_count2 = 0;  // Number of characters into the text2 string.\n  // Start with text1 (prepatch_text) and apply the diffs until we arrive at\n  // text2 (postpatch_text).  We recreate the patches one by one to determine\n  // context info.\n  var prepatch_text = text1;\n  var postpatch_text = text1;\n  for (var x = 0; x < diffs.length; x++) {\n    var diff_type = diffs[x][0];\n    var diff_text = diffs[x][1];\n\n    if (!patchDiffLength && diff_type !== DIFF_EQUAL) {\n      // A new patch starts here.\n      patch.start1 = char_count1;\n      patch.start2 = char_count2;\n    }\n\n    switch (diff_type) {\n      case DIFF_INSERT:\n        patch.diffs[patchDiffLength++] = diffs[x];\n        patch.length2 += diff_text.length;\n        postpatch_text = postpatch_text.substring(0, char_count2) + diff_text +\n                         postpatch_text.substring(char_count2);\n        break;\n      case DIFF_DELETE:\n        patch.length1 += diff_text.length;\n        patch.diffs[patchDiffLength++] = diffs[x];\n        postpatch_text = postpatch_text.substring(0, char_count2) +\n                         postpatch_text.substring(char_count2 +\n                             diff_text.length);\n        break;\n      case DIFF_EQUAL:\n        if (diff_text.length <= 2 * this.Patch_Margin &&\n            patchDiffLength && diffs.length != x + 1) {\n          // Small equality inside a patch.\n          patch.diffs[patchDiffLength++] = diffs[x];\n          patch.length1 += diff_text.length;\n          patch.length2 += diff_text.length;\n        } else if (diff_text.length >= 2 * this.Patch_Margin) {\n          // Time for a new patch.\n          if (patchDiffLength) {\n            this.patch_addContext_(patch, prepatch_text);\n            patches.push(patch);\n            patch = new diff_match_patch.patch_obj();\n            patchDiffLength = 0;\n            // Unlike Unidiff, our patch lists have a rolling context.\n            // https://github.com/google/diff-match-patch/wiki/Unidiff\n            // Update prepatch text & pos to reflect the application of the\n            // just completed patch.\n            prepatch_text = postpatch_text;\n            char_count1 = char_count2;\n          }\n        }\n        break;\n    }\n\n    // Update the current character count.\n    if (diff_type !== DIFF_INSERT) {\n      char_count1 += diff_text.length;\n    }\n    if (diff_type !== DIFF_DELETE) {\n      char_count2 += diff_text.length;\n    }\n  }\n  // Pick up the leftover patch if not empty.\n  if (patchDiffLength) {\n    this.patch_addContext_(patch, prepatch_text);\n    patches.push(patch);\n  }\n\n  return patches;\n};\n\n\n/**\n * Given an array of patches, return another array that is identical.\n * @param {!Array.<!diff_match_patch.patch_obj>} patches Array of Patch objects.\n * @return {!Array.<!diff_match_patch.patch_obj>} Array of Patch objects.\n */\ndiff_match_patch.prototype.patch_deepCopy = function(patches) {\n  // Making deep copies is hard in JavaScript.\n  var patchesCopy = [];\n  for (var x = 0; x < patches.length; x++) {\n    var patch = patches[x];\n    var patchCopy = new diff_match_patch.patch_obj();\n    patchCopy.diffs = [];\n    for (var y = 0; y < patch.diffs.length; y++) {\n      patchCopy.diffs[y] =\n          new diff_match_patch.Diff(patch.diffs[y][0], patch.diffs[y][1]);\n    }\n    patchCopy.start1 = patch.start1;\n    patchCopy.start2 = patch.start2;\n    patchCopy.length1 = patch.length1;\n    patchCopy.length2 = patch.length2;\n    patchesCopy[x] = patchCopy;\n  }\n  return patchesCopy;\n};\n\n\n/**\n * Merge a set of patches onto the text.  Return a patched text, as well\n * as a list of true/false values indicating which patches were applied.\n * @param {!Array.<!diff_match_patch.patch_obj>} patches Array of Patch objects.\n * @param {string} text Old text.\n * @return {!Array.<string|!Array.<boolean>>} Two element Array, containing the\n *      new text and an array of boolean values.\n */\ndiff_match_patch.prototype.patch_apply = function(patches, text) {\n  if (patches.length == 0) {\n    return [text, []];\n  }\n\n  // Deep copy the patches so that no changes are made to originals.\n  patches = this.patch_deepCopy(patches);\n\n  var nullPadding = this.patch_addPadding(patches);\n  text = nullPadding + text + nullPadding;\n\n  this.patch_splitMax(patches);\n  // delta keeps track of the offset between the expected and actual location\n  // of the previous patch.  If there are patches expected at positions 10 and\n  // 20, but the first patch was found at 12, delta is 2 and the second patch\n  // has an effective expected position of 22.\n  var delta = 0;\n  var results = [];\n  for (var x = 0; x < patches.length; x++) {\n    var expected_loc = patches[x].start2 + delta;\n    var text1 = this.diff_text1(patches[x].diffs);\n    var start_loc;\n    var end_loc = -1;\n    if (text1.length > this.Match_MaxBits) {\n      // patch_splitMax will only provide an oversized pattern in the case of\n      // a monster delete.\n      start_loc = this.match_main(text, text1.substring(0, this.Match_MaxBits),\n                                  expected_loc);\n      if (start_loc != -1) {\n        end_loc = this.match_main(text,\n            text1.substring(text1.length - this.Match_MaxBits),\n            expected_loc + text1.length - this.Match_MaxBits);\n        if (end_loc == -1 || start_loc >= end_loc) {\n          // Can't find valid trailing context.  Drop this patch.\n          start_loc = -1;\n        }\n      }\n    } else {\n      start_loc = this.match_main(text, text1, expected_loc);\n    }\n    if (start_loc == -1) {\n      // No match found.  :(\n      results[x] = false;\n      // Subtract the delta for this failed patch from subsequent patches.\n      delta -= patches[x].length2 - patches[x].length1;\n    } else {\n      // Found a match.  :)\n      results[x] = true;\n      delta = start_loc - expected_loc;\n      var text2;\n      if (end_loc == -1) {\n        text2 = text.substring(start_loc, start_loc + text1.length);\n      } else {\n        text2 = text.substring(start_loc, end_loc + this.Match_MaxBits);\n      }\n      if (text1 == text2) {\n        // Perfect match, just shove the replacement text in.\n        text = text.substring(0, start_loc) +\n               this.diff_text2(patches[x].diffs) +\n               text.substring(start_loc + text1.length);\n      } else {\n        // Imperfect match.  Run a diff to get a framework of equivalent\n        // indices.\n        var diffs = this.diff_main(text1, text2, false);\n        if (text1.length > this.Match_MaxBits &&\n            this.diff_levenshtein(diffs) / text1.length >\n            this.Patch_DeleteThreshold) {\n          // The end points match, but the content is unacceptably bad.\n          results[x] = false;\n        } else {\n          this.diff_cleanupSemanticLossless(diffs);\n          var index1 = 0;\n          var index2;\n          for (var y = 0; y < patches[x].diffs.length; y++) {\n            var mod = patches[x].diffs[y];\n            if (mod[0] !== DIFF_EQUAL) {\n              index2 = this.diff_xIndex(diffs, index1);\n            }\n            if (mod[0] === DIFF_INSERT) {  // Insertion\n              text = text.substring(0, start_loc + index2) + mod[1] +\n                     text.substring(start_loc + index2);\n            } else if (mod[0] === DIFF_DELETE) {  // Deletion\n              text = text.substring(0, start_loc + index2) +\n                     text.substring(start_loc + this.diff_xIndex(diffs,\n                         index1 + mod[1].length));\n            }\n            if (mod[0] !== DIFF_DELETE) {\n              index1 += mod[1].length;\n            }\n          }\n        }\n      }\n    }\n  }\n  // Strip the padding off.\n  text = text.substring(nullPadding.length, text.length - nullPadding.length);\n  return [text, results];\n};\n\n\n/**\n * Add some padding on text start and end so that edges can match something.\n * Intended to be called only from within patch_apply.\n * @param {!Array.<!diff_match_patch.patch_obj>} patches Array of Patch objects.\n * @return {string} The padding string added to each side.\n */\ndiff_match_patch.prototype.patch_addPadding = function(patches) {\n  var paddingLength = this.Patch_Margin;\n  var nullPadding = '';\n  for (var x = 1; x <= paddingLength; x++) {\n    nullPadding += String.fromCharCode(x);\n  }\n\n  // Bump all the patches forward.\n  for (var x = 0; x < patches.length; x++) {\n    patches[x].start1 += paddingLength;\n    patches[x].start2 += paddingLength;\n  }\n\n  // Add some padding on start of first diff.\n  var patch = patches[0];\n  var diffs = patch.diffs;\n  if (diffs.length == 0 || diffs[0][0] != DIFF_EQUAL) {\n    // Add nullPadding equality.\n    diffs.unshift(new diff_match_patch.Diff(DIFF_EQUAL, nullPadding));\n    patch.start1 -= paddingLength;  // Should be 0.\n    patch.start2 -= paddingLength;  // Should be 0.\n    patch.length1 += paddingLength;\n    patch.length2 += paddingLength;\n  } else if (paddingLength > diffs[0][1].length) {\n    // Grow first equality.\n    var extraLength = paddingLength - diffs[0][1].length;\n    diffs[0][1] = nullPadding.substring(diffs[0][1].length) + diffs[0][1];\n    patch.start1 -= extraLength;\n    patch.start2 -= extraLength;\n    patch.length1 += extraLength;\n    patch.length2 += extraLength;\n  }\n\n  // Add some padding on end of last diff.\n  patch = patches[patches.length - 1];\n  diffs = patch.diffs;\n  if (diffs.length == 0 || diffs[diffs.length - 1][0] != DIFF_EQUAL) {\n    // Add nullPadding equality.\n    diffs.push(new diff_match_patch.Diff(DIFF_EQUAL, nullPadding));\n    patch.length1 += paddingLength;\n    patch.length2 += paddingLength;\n  } else if (paddingLength > diffs[diffs.length - 1][1].length) {\n    // Grow last equality.\n    var extraLength = paddingLength - diffs[diffs.length - 1][1].length;\n    diffs[diffs.length - 1][1] += nullPadding.substring(0, extraLength);\n    patch.length1 += extraLength;\n    patch.length2 += extraLength;\n  }\n\n  return nullPadding;\n};\n\n\n/**\n * Look through the patches and break up any which are longer than the maximum\n * limit of the match algorithm.\n * Intended to be called only from within patch_apply.\n * @param {!Array.<!diff_match_patch.patch_obj>} patches Array of Patch objects.\n */\ndiff_match_patch.prototype.patch_splitMax = function(patches) {\n  var patch_size = this.Match_MaxBits;\n  for (var x = 0; x < patches.length; x++) {\n    if (patches[x].length1 <= patch_size) {\n      continue;\n    }\n    var bigpatch = patches[x];\n    // Remove the big old patch.\n    patches.splice(x--, 1);\n    var start1 = bigpatch.start1;\n    var start2 = bigpatch.start2;\n    var precontext = '';\n    while (bigpatch.diffs.length !== 0) {\n      // Create one of several smaller patches.\n      var patch = new diff_match_patch.patch_obj();\n      var empty = true;\n      patch.start1 = start1 - precontext.length;\n      patch.start2 = start2 - precontext.length;\n      if (precontext !== '') {\n        patch.length1 = patch.length2 = precontext.length;\n        patch.diffs.push(new diff_match_patch.Diff(DIFF_EQUAL, precontext));\n      }\n      while (bigpatch.diffs.length !== 0 &&\n             patch.length1 < patch_size - this.Patch_Margin) {\n        var diff_type = bigpatch.diffs[0][0];\n        var diff_text = bigpatch.diffs[0][1];\n        if (diff_type === DIFF_INSERT) {\n          // Insertions are harmless.\n          patch.length2 += diff_text.length;\n          start2 += diff_text.length;\n          patch.diffs.push(bigpatch.diffs.shift());\n          empty = false;\n        } else if (diff_type === DIFF_DELETE && patch.diffs.length == 1 &&\n                   patch.diffs[0][0] == DIFF_EQUAL &&\n                   diff_text.length > 2 * patch_size) {\n          // This is a large deletion.  Let it pass in one chunk.\n          patch.length1 += diff_text.length;\n          start1 += diff_text.length;\n          empty = false;\n          patch.diffs.push(new diff_match_patch.Diff(diff_type, diff_text));\n          bigpatch.diffs.shift();\n        } else {\n          // Deletion or equality.  Only take as much as we can stomach.\n          diff_text = diff_text.substring(0,\n              patch_size - patch.length1 - this.Patch_Margin);\n          patch.length1 += diff_text.length;\n          start1 += diff_text.length;\n          if (diff_type === DIFF_EQUAL) {\n            patch.length2 += diff_text.length;\n            start2 += diff_text.length;\n          } else {\n            empty = false;\n          }\n          patch.diffs.push(new diff_match_patch.Diff(diff_type, diff_text));\n          if (diff_text == bigpatch.diffs[0][1]) {\n            bigpatch.diffs.shift();\n          } else {\n            bigpatch.diffs[0][1] =\n                bigpatch.diffs[0][1].substring(diff_text.length);\n          }\n        }\n      }\n      // Compute the head context for the next patch.\n      precontext = this.diff_text2(patch.diffs);\n      precontext =\n          precontext.substring(precontext.length - this.Patch_Margin);\n      // Append the end context for this patch.\n      var postcontext = this.diff_text1(bigpatch.diffs)\n                            .substring(0, this.Patch_Margin);\n      if (postcontext !== '') {\n        patch.length1 += postcontext.length;\n        patch.length2 += postcontext.length;\n        if (patch.diffs.length !== 0 &&\n            patch.diffs[patch.diffs.length - 1][0] === DIFF_EQUAL) {\n          patch.diffs[patch.diffs.length - 1][1] += postcontext;\n        } else {\n          patch.diffs.push(new diff_match_patch.Diff(DIFF_EQUAL, postcontext));\n        }\n      }\n      if (!empty) {\n        patches.splice(++x, 0, patch);\n      }\n    }\n  }\n};\n\n\n/**\n * Take a list of patches and return a textual representation.\n * @param {!Array.<!diff_match_patch.patch_obj>} patches Array of Patch objects.\n * @return {string} Text representation of patches.\n */\ndiff_match_patch.prototype.patch_toText = function(patches) {\n  var text = [];\n  for (var x = 0; x < patches.length; x++) {\n    text[x] = patches[x];\n  }\n  return text.join('');\n};\n\n\n/**\n * Parse a textual representation of patches and return a list of Patch objects.\n * @param {string} textline Text representation of patches.\n * @return {!Array.<!diff_match_patch.patch_obj>} Array of Patch objects.\n * @throws {!Error} If invalid input.\n */\ndiff_match_patch.prototype.patch_fromText = function(textline) {\n  var patches = [];\n  if (!textline) {\n    return patches;\n  }\n  var text = textline.split('\\n');\n  var textPointer = 0;\n  var patchHeader = /^@@ -(\\d+),?(\\d*) \\+(\\d+),?(\\d*) @@$/;\n  while (textPointer < text.length) {\n    var m = text[textPointer].match(patchHeader);\n    if (!m) {\n      throw new Error('Invalid patch string: ' + text[textPointer]);\n    }\n    var patch = new diff_match_patch.patch_obj();\n    patches.push(patch);\n    patch.start1 = parseInt(m[1], 10);\n    if (m[2] === '') {\n      patch.start1--;\n      patch.length1 = 1;\n    } else if (m[2] == '0') {\n      patch.length1 = 0;\n    } else {\n      patch.start1--;\n      patch.length1 = parseInt(m[2], 10);\n    }\n\n    patch.start2 = parseInt(m[3], 10);\n    if (m[4] === '') {\n      patch.start2--;\n      patch.length2 = 1;\n    } else if (m[4] == '0') {\n      patch.length2 = 0;\n    } else {\n      patch.start2--;\n      patch.length2 = parseInt(m[4], 10);\n    }\n    textPointer++;\n\n    while (textPointer < text.length) {\n      var sign = text[textPointer].charAt(0);\n      try {\n        var line = decodeURI(text[textPointer].substring(1));\n      } catch (ex) {\n        // Malformed URI sequence.\n        throw new Error('Illegal escape in patch_fromText: ' + line);\n      }\n      if (sign == '-') {\n        // Deletion.\n        patch.diffs.push(new diff_match_patch.Diff(DIFF_DELETE, line));\n      } else if (sign == '+') {\n        // Insertion.\n        patch.diffs.push(new diff_match_patch.Diff(DIFF_INSERT, line));\n      } else if (sign == ' ') {\n        // Minor equality.\n        patch.diffs.push(new diff_match_patch.Diff(DIFF_EQUAL, line));\n      } else if (sign == '@') {\n        // Start of next patch.\n        break;\n      } else if (sign === '') {\n        // Blank line?  Whatever.\n      } else {\n        // WTF?\n        throw new Error('Invalid patch mode \"' + sign + '\" in: ' + line);\n      }\n      textPointer++;\n    }\n  }\n  return patches;\n};\n\n\n/**\n * Class representing one patch operation.\n * @constructor\n */\ndiff_match_patch.patch_obj = function() {\n  /** @type {!Array.<!diff_match_patch.Diff>} */\n  this.diffs = [];\n  /** @type {?number} */\n  this.start1 = null;\n  /** @type {?number} */\n  this.start2 = null;\n  /** @type {number} */\n  this.length1 = 0;\n  /** @type {number} */\n  this.length2 = 0;\n};\n\n\n/**\n * Emulate GNU diff's format.\n * Header: @@ -382,8 +481,9 @@\n * Indices are printed as 1-based, not 0-based.\n * @return {string} The GNU diff string.\n */\ndiff_match_patch.patch_obj.prototype.toString = function() {\n  var coords1, coords2;\n  if (this.length1 === 0) {\n    coords1 = this.start1 + ',0';\n  } else if (this.length1 == 1) {\n    coords1 = this.start1 + 1;\n  } else {\n    coords1 = (this.start1 + 1) + ',' + this.length1;\n  }\n  if (this.length2 === 0) {\n    coords2 = this.start2 + ',0';\n  } else if (this.length2 == 1) {\n    coords2 = this.start2 + 1;\n  } else {\n    coords2 = (this.start2 + 1) + ',' + this.length2;\n  }\n  var text = ['@@ -' + coords1 + ' +' + coords2 + ' @@\\n'];\n  var op;\n  // Escape the body of the patch with %xx notation.\n  for (var x = 0; x < this.diffs.length; x++) {\n    switch (this.diffs[x][0]) {\n      case DIFF_INSERT:\n        op = '+';\n        break;\n      case DIFF_DELETE:\n        op = '-';\n        break;\n      case DIFF_EQUAL:\n        op = ' ';\n        break;\n    }\n    text[x + 1] = op + encodeURI(this.diffs[x][1]) + '\\n';\n  }\n  return text.join('').replace(/%20/g, ' ');\n};\n\n\n// The following export code was added by @ForbesLindesay\nmodule.exports = diff_match_patch;\nmodule.exports['diff_match_patch'] = diff_match_patch;\nmodule.exports['DIFF_DELETE'] = DIFF_DELETE;\nmodule.exports['DIFF_INSERT'] = DIFF_INSERT;\nmodule.exports['DIFF_EQUAL'] = DIFF_EQUAL;", "// CodeMirror, copyright (c) by <PERSON><PERSON> and others\n// Distributed under an MIT license: https://codemirror.net/5/LICENSE\n\n(function(mod) {\n  if (typeof exports == \"object\" && typeof module == \"object\") // CommonJS\n    mod(require(\"../../lib/codemirror\"));\n  else if (typeof define == \"function\" && define.amd) // AMD\n    define([\"../../lib/codemirror\"], mod);\n  else // Plain browser env\n    mod(CodeMirror);\n})(function(CodeMirror) {\n  \"use strict\";\n\n  CodeMirror.defineSimpleMode = function(name, states) {\n    CodeMirror.defineMode(name, function(config) {\n      return CodeMirror.simpleMode(config, states);\n    });\n  };\n\n  CodeMirror.simpleMode = function(config, states) {\n    ensureState(states, \"start\");\n    var states_ = {}, meta = states.meta || {}, hasIndentation = false;\n    for (var state in states) if (state != meta && states.hasOwnProperty(state)) {\n      var list = states_[state] = [], orig = states[state];\n      for (var i = 0; i < orig.length; i++) {\n        var data = orig[i];\n        list.push(new Rule(data, states));\n        if (data.indent || data.dedent) hasIndentation = true;\n      }\n    }\n    var mode = {\n      startState: function() {\n        return {state: \"start\", pending: null,\n                local: null, localState: null,\n                indent: hasIndentation ? [] : null};\n      },\n      copyState: function(state) {\n        var s = {state: state.state, pending: state.pending,\n                 local: state.local, localState: null,\n                 indent: state.indent && state.indent.slice(0)};\n        if (state.localState)\n          s.localState = CodeMirror.copyState(state.local.mode, state.localState);\n        if (state.stack)\n          s.stack = state.stack.slice(0);\n        for (var pers = state.persistentStates; pers; pers = pers.next)\n          s.persistentStates = {mode: pers.mode,\n                                spec: pers.spec,\n                                state: pers.state == state.localState ? s.localState : CodeMirror.copyState(pers.mode, pers.state),\n                                next: s.persistentStates};\n        return s;\n      },\n      token: tokenFunction(states_, config),\n      innerMode: function(state) { return state.local && {mode: state.local.mode, state: state.localState}; },\n      indent: indentFunction(states_, meta)\n    };\n    if (meta) for (var prop in meta) if (meta.hasOwnProperty(prop))\n      mode[prop] = meta[prop];\n    return mode;\n  };\n\n  function ensureState(states, name) {\n    if (!states.hasOwnProperty(name))\n      throw new Error(\"Undefined state \" + name + \" in simple mode\");\n  }\n\n  function toRegex(val, caret) {\n    if (!val) return /(?:)/;\n    var flags = \"\";\n    if (val instanceof RegExp) {\n      if (val.ignoreCase) flags = \"i\";\n      if (val.unicode) flags += \"u\"\n      val = val.source;\n    } else {\n      val = String(val);\n    }\n    return new RegExp((caret === false ? \"\" : \"^\") + \"(?:\" + val + \")\", flags);\n  }\n\n  function asToken(val) {\n    if (!val) return null;\n    if (val.apply) return val\n    if (typeof val == \"string\") return val.replace(/\\./g, \" \");\n    var result = [];\n    for (var i = 0; i < val.length; i++)\n      result.push(val[i] && val[i].replace(/\\./g, \" \"));\n    return result;\n  }\n\n  function Rule(data, states) {\n    if (data.next || data.push) ensureState(states, data.next || data.push);\n    this.regex = toRegex(data.regex);\n    this.token = asToken(data.token);\n    this.data = data;\n  }\n\n  function tokenFunction(states, config) {\n    return function(stream, state) {\n      if (state.pending) {\n        var pend = state.pending.shift();\n        if (state.pending.length == 0) state.pending = null;\n        stream.pos += pend.text.length;\n        return pend.token;\n      }\n\n      if (state.local) {\n        if (state.local.end && stream.match(state.local.end)) {\n          var tok = state.local.endToken || null;\n          state.local = state.localState = null;\n          return tok;\n        } else {\n          var tok = state.local.mode.token(stream, state.localState), m;\n          if (state.local.endScan && (m = state.local.endScan.exec(stream.current())))\n            stream.pos = stream.start + m.index;\n          return tok;\n        }\n      }\n\n      var curState = states[state.state];\n      for (var i = 0; i < curState.length; i++) {\n        var rule = curState[i];\n        var matches = (!rule.data.sol || stream.sol()) && stream.match(rule.regex);\n        if (matches) {\n          if (rule.data.next) {\n            state.state = rule.data.next;\n          } else if (rule.data.push) {\n            (state.stack || (state.stack = [])).push(state.state);\n            state.state = rule.data.push;\n          } else if (rule.data.pop && state.stack && state.stack.length) {\n            state.state = state.stack.pop();\n          }\n\n          if (rule.data.mode)\n            enterLocalMode(config, state, rule.data.mode, rule.token);\n          if (rule.data.indent)\n            state.indent.push(stream.indentation() + config.indentUnit);\n          if (rule.data.dedent)\n            state.indent.pop();\n          var token = rule.token\n          if (token && token.apply) token = token(matches)\n          if (matches.length > 2 && rule.token && typeof rule.token != \"string\") {\n            for (var j = 2; j < matches.length; j++)\n              if (matches[j])\n                (state.pending || (state.pending = [])).push({text: matches[j], token: rule.token[j - 1]});\n            stream.backUp(matches[0].length - (matches[1] ? matches[1].length : 0));\n            return token[0];\n          } else if (token && token.join) {\n            return token[0];\n          } else {\n            return token;\n          }\n        }\n      }\n      stream.next();\n      return null;\n    };\n  }\n\n  function cmp(a, b) {\n    if (a === b) return true;\n    if (!a || typeof a != \"object\" || !b || typeof b != \"object\") return false;\n    var props = 0;\n    for (var prop in a) if (a.hasOwnProperty(prop)) {\n      if (!b.hasOwnProperty(prop) || !cmp(a[prop], b[prop])) return false;\n      props++;\n    }\n    for (var prop in b) if (b.hasOwnProperty(prop)) props--;\n    return props == 0;\n  }\n\n  function enterLocalMode(config, state, spec, token) {\n    var pers;\n    if (spec.persistent) for (var p = state.persistentStates; p && !pers; p = p.next)\n      if (spec.spec ? cmp(spec.spec, p.spec) : spec.mode == p.mode) pers = p;\n    var mode = pers ? pers.mode : spec.mode || CodeMirror.getMode(config, spec.spec);\n    var lState = pers ? pers.state : CodeMirror.startState(mode);\n    if (spec.persistent && !pers)\n      state.persistentStates = {mode: mode, spec: spec.spec, state: lState, next: state.persistentStates};\n\n    state.localState = lState;\n    state.local = {mode: mode,\n                   end: spec.end && toRegex(spec.end),\n                   endScan: spec.end && spec.forceEnd !== false && toRegex(spec.end, false),\n                   endToken: token && token.join ? token[token.length - 1] : token};\n  }\n\n  function indexOf(val, arr) {\n    for (var i = 0; i < arr.length; i++) if (arr[i] === val) return true;\n  }\n\n  function indentFunction(states, meta) {\n    return function(state, textAfter, line) {\n      if (state.local && state.local.mode.indent)\n        return state.local.mode.indent(state.localState, textAfter, line);\n      if (state.indent == null || state.local || meta.dontIndentStates && indexOf(state.state, meta.dontIndentStates) > -1)\n        return CodeMirror.Pass;\n\n      var pos = state.indent.length - 1, rules = states[state.state];\n      scan: for (;;) {\n        for (var i = 0; i < rules.length; i++) {\n          var rule = rules[i];\n          if (rule.data.dedent && rule.data.dedentIfLineStart !== false) {\n            var m = rule.regex.exec(textAfter);\n            if (m && m[0]) {\n              pos--;\n              if (rule.next || rule.push) rules = states[rule.next || rule.push];\n              textAfter = textAfter.slice(m[0].length);\n              continue scan;\n            }\n          }\n        }\n        break;\n      }\n      return pos < 0 ? 0 : state.indent[pos];\n    };\n  }\n});\n", "import { defineComponent as E, ref as k, onMounted as N, markRaw as T, watch as _, unref as y, openBlock as C, createElementBlock as O, computed as I, nextTick as K, shallowRef as Q, getCurrentInstance as X, onBeforeUnmount as Y, normalizeClass as ee, normalizeStyle as te, createBlock as ne, resolveDynamicComponent as re, mergeProps as oe } from \"vue\";\nimport \"codemirror/lib/codemirror.css\";\nimport \"codemirror/addon/fold/foldgutter.css\";\nimport \"codemirror/addon/fold/foldcode.js\";\nimport \"codemirror/addon/fold/foldgutter.js\";\nimport \"codemirror/addon/fold/brace-fold.js\";\nimport \"codemirror/addon/selection/active-line.js\";\nimport B from \"codemirror\";\nimport \"codemirror/addon/merge/merge.css\";\nimport \"codemirror/addon/merge/merge.js\";\nimport se from \"diff-match-patch\";\nimport \"codemirror/addon/mode/simple.js\";\n!window.CodeMirror && (window.CodeMirror = B);\nconst b = window.CodeMirror || B, ae = E({\n  name: \"DefaultMode\",\n  props: {\n    name: {\n      type: String,\n      default: `cm-textarea-${+/* @__PURE__ */ new Date()}`\n    },\n    value: {\n      type: String,\n      default: \"\"\n    },\n    content: {\n      type: String,\n      default: \"\"\n    },\n    options: {\n      type: Object,\n      default: () => ({})\n    },\n    cminstance: {\n      type: Object,\n      default: () => null\n    },\n    placeholder: {\n      type: String,\n      default: \"\"\n    }\n  },\n  emits: {\n    ready: (e) => e,\n    \"update:cminstance\": (e) => e\n  },\n  setup(e, { emit: n }) {\n    const o = k(), t = k(null), r = () => {\n      t.value = T(b.fromTextArea(o.value, e.options)), n(\"update:cminstance\", t.value);\n      const s = _(\n        () => e.cminstance,\n        (l) => {\n          var g;\n          l && ((g = e.cminstance) == null || g.setValue(e.value || e.content)), n(\"ready\", y(t)), s == null || s();\n        },\n        { deep: !0 }\n      );\n    };\n    return N(() => {\n      r();\n    }), {\n      textarea: o,\n      initialize: r\n    };\n  }\n}), A = (e, n) => {\n  const o = e.__vccOpts || e;\n  for (const [t, r] of n)\n    o[t] = r;\n  return o;\n}, le = [\"name\", \"placeholder\"];\nfunction ie(e, n, o, t, r, s) {\n  return C(), O(\"textarea\", {\n    ref: \"textarea\",\n    name: e.$props.name,\n    placeholder: e.$props.placeholder\n  }, null, 8, le);\n}\nconst H = /* @__PURE__ */ A(ae, [[\"render\", ie]]);\nwindow.diff_match_patch = se;\nwindow.DIFF_DELETE = -1;\nwindow.DIFF_INSERT = 1;\nwindow.DIFF_EQUAL = 0;\nconst ce = E({\n  name: \"MergeMode\",\n  props: {\n    options: {\n      type: Object,\n      default: () => ({})\n    },\n    cminstance: {\n      type: Object,\n      default: () => ({})\n    }\n  },\n  emits: [\"update:cminstance\", \"ready\"],\n  setup(e, { emit: n }) {\n    const o = k(), t = k(), r = () => {\n      o.value = T(b.MergeView(t.value, e.options)), n(\"update:cminstance\", o.value), n(\"ready\", o);\n    };\n    return N(() => {\n      r();\n    }), {\n      mergeView: t,\n      initialize: r\n    };\n  }\n}), ue = { ref: \"mergeView\" };\nfunction de(e, n, o, t, r, s) {\n  return C(), O(\"div\", ue, null, 512);\n}\nconst pe = /* @__PURE__ */ A(ce, [[\"render\", de]]);\nvar me = /* @__PURE__ */ ((e) => (e.info = \"info\", e.warning = \"warning\", e.error = \"error\", e))(me || {});\nfunction ge() {\n  const e = /* @__PURE__ */ new Date(), n = e.getHours() < 10 ? `0${e.getHours()}` : e.getHours(), o = e.getMinutes() < 10 ? `0${e.getMinutes()}` : e.getMinutes(), t = e.getSeconds() < 10 ? `0${e.getSeconds()}` : e.getSeconds();\n  return `${n}:${o}:${t}`;\n}\nfunction ze(e) {\n  return `#link#${JSON.stringify(e)}#link#`;\n}\nfunction fe(e) {\n  const n = /#link#(.+)#link#/g, o = [];\n  let t;\n  for (t = n.exec(e); t; ) {\n    const r = document.createElement(\"a\"), s = JSON.parse(t[1]), l = Object.entries(s);\n    for (const [g, d] of l)\n      r.setAttribute(g, d);\n    r.className = \"editor_custom_link\", r.innerHTML = \"logDownload\", o.push({\n      start: t.index,\n      end: t.index + t[0].length,\n      node: r\n    }), t = n.exec(e);\n  }\n  return o;\n}\nfunction He(e = \"\", n = \"info\") {\n  return `#log<${n}>log#${e}#log<${n}>log#`;\n}\nfunction he(e) {\n  const n = [];\n  function o() {\n    const t = /#log<(\\w*)>log#((.|\\r\\n|\\n)*?)#log<(\\w*)>log#/g;\n    let r;\n    for (r = t.exec(e); r; ) {\n      const l = r[0].replace(/\\r\\n/g, `\n`).split(`\n`), d = r[2].replace(/\\r\\n/g, `\n`).split(`\n`), p = document.createElement(\"span\"), i = r[1];\n      p.className = `c-editor--log__${i}`;\n      let u = 0;\n      for (let $ = 0; $ < l.length; $++) {\n        const a = l[$], m = d[$], v = p.cloneNode(!1);\n        v.innerText = m, n.push({\n          start: r.index + u,\n          end: r.index + u + a.length,\n          node: v\n        }), u = u + a.length + 1;\n      }\n      r = t.exec(e);\n    }\n  }\n  return o(), n;\n}\nfunction Re(e, n) {\n  return `[${ge()}] <${n}> ${e}`;\n}\nfunction Be(e, n, o) {\n  const r = new Array(Math.max(n || 15, 5)).join(o || \"=\");\n  return `${r}${e}${r}`;\n}\nconst S = [\n  {\n    regex: /(\\[.*?\\])([ \\t]*)(<error>[ \\t])(.+)/,\n    token: [\"tag\", \"\", \"error.strong\", \"error.strong\"],\n    sol: !0\n    // next: \"error\",\n  },\n  {\n    regex: /(\\[.*?\\])([ \\t]*)(<info>)(.+)(.?)/,\n    token: [\"tag\", \"\", \"bracket\", \"bracket\", \"hr\"],\n    sol: !0\n    // next: \"info\",\n  },\n  {\n    regex: /(\\[.*?\\])([ \\t]*)(<warning>)(.+)(.?)/,\n    token: [\"tag\", \"\", \"comment\", \"comment\", \"hr\"],\n    sol: !0\n    // next: \"warning\",\n  }\n];\nb.defineSimpleMode(\"fclog\", {\n  start: [\n    ...S,\n    {\n      regex: /.*/,\n      token: \"hr\"\n    }\n  ],\n  error: [\n    ...S,\n    {\n      regex: /.*/,\n      token: \"error.strong\"\n    }\n  ],\n  info: [\n    ...S,\n    {\n      regex: /.*/,\n      token: \"bracket\"\n    }\n  ],\n  warning: [\n    ...S,\n    {\n      regex: /.*\\[/,\n      token: \"comment\"\n    }\n  ]\n});\nb.defineSimpleMode(\"log\", {\n  start: [\n    {\n      regex: /^[=]+[^=]*[=]+/,\n      token: \"strong\"\n    },\n    {\n      regex: /([^\\w])([A-Z][\\w]*)/,\n      token: [\"\", \"string\"]\n    },\n    {\n      regex: /(^[A-Z][\\w]*)/,\n      token: \"string\"\n    }\n    // {\n    //     regex: /([^\\d])([0-9]+)/,\n    //     token: [null, 'comment']\n    // },\n    // {\n    //     regex: /(^[0-9]+)/,\n    //     token: 'comment'\n    // }\n  ]\n});\nconst ve = E({\n  name: \"CodemirrorFclog\",\n  props: {\n    value: {\n      type: String,\n      default: \"\"\n    },\n    name: {\n      type: String,\n      default: `cm-textarea-${+/* @__PURE__ */ new Date()}`\n    },\n    options: {\n      type: Object,\n      default: () => ({})\n    },\n    cminstance: {\n      type: Object,\n      default: () => ({})\n    },\n    placeholder: {\n      type: String,\n      default: \"\"\n    }\n  },\n  emits: [\"update:cminstance\", \"ready\"],\n  setup(e, { emit: n }) {\n    const o = k(), t = k(null), r = (l = e.cminstance) => {\n      l.getAllMarks().forEach((i) => i.clear());\n      const d = l.getValue(), p = [].concat(fe(d)).concat(he(d));\n      for (let i = 0; i < p.length; i++) {\n        const u = p[i];\n        l.markText(l.posFromIndex(u.start), l.posFromIndex(u.end), {\n          replacedWith: u.node\n        });\n      }\n    }, s = () => {\n      var l;\n      t.value = T(b.fromTextArea(o.value, e.options)), n(\"update:cminstance\", y(t)), (l = t.value) == null || l.on(\"change\", r);\n    };\n    return _(\n      () => e.cminstance,\n      (l) => {\n        var g;\n        l && (r(e.cminstance), (g = e.cminstance) == null || g.setValue(e.value), n(\"ready\", t));\n      },\n      { deep: !0, immediate: !0 }\n    ), N(() => {\n      s();\n    }), {\n      initialize: s,\n      textarea: o\n    };\n  }\n}), ye = [\"name\", \"placeholder\"];\nfunction ke(e, n, o, t, r, s) {\n  return C(), O(\"textarea\", {\n    ref: \"textarea\",\n    name: e.$props.name,\n    placeholder: e.$props.placeholder\n  }, null, 8, ye);\n}\nconst we = /* @__PURE__ */ A(ve, [[\"render\", ke]]), P = {\n  \"update:value\": () => !0,\n  change: (e, n) => ({ value: e, cm: n }),\n  input: () => !0,\n  ready: (e) => e\n}, xe = [\n  \"changes\",\n  \"scroll\",\n  \"beforeChange\",\n  \"cursorActivity\",\n  \"keyHandled\",\n  \"inputRead\",\n  \"electricInput\",\n  \"beforeSelectionChange\",\n  \"viewportChange\",\n  \"swapDoc\",\n  \"gutterClick\",\n  \"gutterContextMenu\",\n  \"focus\",\n  \"blur\",\n  \"refresh\",\n  \"optionChange\",\n  \"scrollCursorIntoView\",\n  \"update\"\n], $e = () => {\n  const e = {};\n  return xe.forEach((n) => {\n    e[n] = (...o) => o;\n  }), e;\n}, _e = { ...P, ...$e() }, j = {\n  mode: \"text\",\n  // Language mode\n  theme: \"default\",\n  // Theme\n  lineNumbers: !0,\n  // Display line number\n  smartIndent: !0,\n  // Intelligent indentation\n  indentUnit: 2,\n  // Indentation unit\n  styleActiveLine: !0\n  // Highlight the current line\n};\nfunction be(e) {\n  Promise.resolve().then(() => {\n    const n = e.getScrollInfo();\n    e.scrollTo(n.left, n.height);\n  });\n}\nconst Ce = ({\n  props: e,\n  cminstance: n,\n  emit: o,\n  internalInstance: t,\n  content: r\n}) => {\n  const s = I(\n    () => {\n      var d;\n      return e.merge ? (d = y(n)) == null ? void 0 : d.editor() : y(n);\n    }\n  ), l = () => {\n    const d = [];\n    return Object.keys(t == null ? void 0 : t.vnode.props).forEach((p) => {\n      if (p.startsWith(\"on\")) {\n        const i = p.replace(p[2], p[2].toLowerCase()).slice(2);\n        !P[i] && d.push(i);\n      }\n    }), d;\n  };\n  return {\n    listenerEvents: () => {\n      s.value.on(\"change\", (i) => {\n        const u = i.getValue();\n        u === r.value && u !== \"\" || (r.value = u, o(\"update:value\", r.value || \"\"), o(\"input\", r.value || \" \"), Promise.resolve().then(() => {\n          o(\"change\", r.value, i);\n        }), e.keepCursorInEnd && be(i));\n      });\n      const d = {};\n      l().filter((i) => !d[i] && (d[i] = !0)).forEach((i) => {\n        s.value.on(i, (...u) => {\n          o(i, ...u);\n        });\n      });\n    }\n  };\n};\nfunction Me({ props: e, cminstance: n, presetRef: o }) {\n  const t = k(\"100%\"), r = k(\"100%\"), s = I(\n    () => {\n      var a;\n      return e.merge ? (a = y(n)) == null ? void 0 : a.editor() : y(n);\n    }\n  ), l = () => {\n    K(() => {\n      var a;\n      (a = s.value) == null || a.refresh();\n    });\n  }, g = (a) => a ? !(a && isNaN(+a)) : !1, d = (a = e.width, m = e.height) => {\n    var M;\n    let v = \"100%\", x = \"100%\";\n    g(a) ? v = `${String(a)}px` : a && (v = a), g(m) ? x = `${String(m)}px` : m && (x = m), t.value = v, r.value = x, console.log(\"resize\", v, x), (M = s.value) == null || M.setSize(\"100%\", \"100%\");\n  }, p = () => {\n    var m;\n    const a = (m = s.value) == null ? void 0 : m.getWrapperElement();\n    a == null || a.remove();\n  }, i = () => {\n    var m, v, x;\n    const a = (m = s.value) == null ? void 0 : m.getDoc().getHistory();\n    (v = o.value) == null || v.initialize(), p(), (x = s.value) == null || x.getDoc().setHistory(a);\n  }, u = () => {\n    const a = document.querySelector(\".CodeMirror-gutters\");\n    return (a == null ? void 0 : a.style.left.replace(\"px\", \"\")) !== \"0\";\n  };\n  return {\n    reload: i,\n    refresh: l,\n    resize: d,\n    destroy: p,\n    containerWidth: t,\n    containerHeight: r,\n    reviseStyle: () => {\n      if (l(), !u()) return;\n      const a = setInterval(() => {\n        u() ? l() : clearInterval(a);\n      }, 60), m = setTimeout(() => {\n        clearInterval(a), clearTimeout(m);\n      }, 400);\n    }\n  };\n}\nconst R = /* @__PURE__ */ E({\n  __name: \"index\",\n  props: {\n    value: {\n      type: String,\n      default: \"\"\n    },\n    options: {\n      type: Object,\n      default: () => j\n    },\n    globalOptions: {\n      type: Object,\n      default: () => j\n    },\n    placeholder: {\n      type: String,\n      default: \"\"\n    },\n    border: {\n      type: Boolean,\n      default: !1\n    },\n    width: {\n      type: [String, Number],\n      default: null\n    },\n    height: {\n      type: [String, Number],\n      default: null\n    },\n    originalStyle: {\n      type: Boolean,\n      default: !1\n    },\n    keepCursorInEnd: {\n      type: Boolean,\n      default: !1\n    },\n    merge: {\n      type: Boolean,\n      default: !1\n    },\n    name: {\n      type: String,\n      default: \"\"\n    },\n    marker: {\n      type: Function,\n      default: () => null\n    },\n    unseenLines: {\n      type: Array,\n      default: () => []\n    }\n  },\n  emits: _e,\n  setup(e, { expose: n, emit: o }) {\n    var V, F, z;\n    typeof Object.assign != \"function\" && Object.defineProperty(Object, \"assign\", {\n      value(c) {\n        if (c == null)\n          throw new TypeError(\"Cannot convert undefined or null to object\");\n        const f = Object(c);\n        for (let h = 1; h < arguments.length; h++) {\n          const w = arguments[h];\n          if (w != null)\n            for (const L in w)\n              Object.prototype.hasOwnProperty.call(w, L) && (f[L] = w[L]);\n        }\n        return f;\n      },\n      writable: !0,\n      configurable: !0\n    });\n    const t = e, r = o, s = k(null), l = k(\"\"), g = Q(H), d = k({\n      foldGutter: !0,\n      ...j,\n      ...t.globalOptions,\n      ...t.options,\n      gutters: [.../* @__PURE__ */ new Set([\"CodeMirror-linenumbers\", \"CodeMirror-foldgutter\", ...((V = t.options) == null ? void 0 : V.gutters) || []])]\n    }), p = X(), i = t.name || ((z = (F = p == null ? void 0 : p.parent) == null ? void 0 : F.type) == null ? void 0 : z.name) || void 0, u = k(null), $ = I(() => {\n      var c;\n      return t.merge ? (c = y(s)) == null ? void 0 : c.editor() : y(s);\n    }), { refresh: a, resize: m, destroy: v, containerHeight: x, containerWidth: M, reviseStyle: W } = Me({\n      props: t,\n      cminstance: s,\n      presetRef: u\n    }), { listenerEvents: G } = Ce({\n      props: t,\n      cminstance: s,\n      emit: r,\n      internalInstance: p,\n      content: l\n    }), D = () => {\n      t.unseenLines !== void 0 && t.marker !== void 0 && t.unseenLines.forEach((c) => {\n        var h, w;\n        const f = (h = s.value) == null ? void 0 : h.lineInfo(c);\n        (w = s.value) == null || w.setGutterMarker(c, \"breakpoints\", f != null && f.gutterMarkers ? null : t.marker());\n      });\n    }, J = (c) => {\n      var h, w;\n      const f = (h = s.value) == null ? void 0 : h.getValue();\n      c !== f && ((w = s.value) == null || w.setValue(c), l.value = c, W()), D();\n    }, Z = () => {\n      G(), D(), m(t.width, t.height), r(\"ready\", s.value), _(\n        [() => t.width, () => t.height],\n        ([c, f]) => {\n          m(c, f);\n        },\n        { deep: !0 }\n      );\n    }, q = () => {\n      if (t.options.mode === \"fclog\" || t.options.mode === \"log\") {\n        g.value = we;\n        return;\n      }\n      if (t.merge) {\n        g.value = pe;\n        return;\n      }\n      g.value = H;\n    };\n    return _(\n      () => t.options,\n      (c) => {\n        var f;\n        for (const h in t.options)\n          (f = $.value) == null || f.setOption(h, y(c[h]));\n      },\n      { deep: !0 }\n    ), _(\n      () => t.value,\n      (c) => {\n        J(c);\n      }\n    ), _(() => t.merge, q, { immediate: !0 }), Y(() => {\n      v();\n    }), n({\n      cminstance: s,\n      resize: m,\n      refresh: a,\n      destroy: v\n    }), (c, f) => (C(), O(\"div\", {\n      class: ee([\"codemirror-container\", {\n        merge: c.$props.merge,\n        bordered: c.$props.border || c.$props.merge && !t.originalStyle,\n        \"original-style\": t.originalStyle\n      }]),\n      style: te({\n        height: y(x),\n        width: y(M)\n      })\n    }, [\n      (C(), ne(re(g.value), oe({\n        ref_key: \"presetRef\",\n        ref: u,\n        cminstance: s.value,\n        \"onUpdate:cminstance\": f[0] || (f[0] = (h) => s.value = h),\n        style: { height: \"100%\" }\n      }, {\n        ...c.$props,\n        ...c.$attrs,\n        options: d.value,\n        name: y(i),\n        content: l.value\n      }, { onReady: Z }), null, 16, [\"cminstance\"]))\n    ], 6));\n  }\n}), U = (e, n) => (n && n.options && (R.props.globalOptions.default = () => n.options), e.component((n == null ? void 0 : n.componentName) || \"Codemirror\", R), e), Pe = window.CodeMirror || b, Ue = U, We = U;\nfunction styleInject(css,ref){if(ref===void 0){ref={}}var insertAt=ref.insertAt;if(!css||typeof document===\"undefined\"){return}var head=document.head||document.getElementsByTagName(\"head\")[0];var style=document.createElement(\"style\");style.type=\"text/css\";if(insertAt===\"top\"){if(head.firstChild){head.insertBefore(style,head.firstChild)}else{head.appendChild(style)}}else{head.appendChild(style)}if(style.styleSheet){style.styleSheet.cssText=css}else{style.appendChild(document.createTextNode(css))}};styleInject(`.codemirror-container {\n  position: relative;\n  display: inline-block;\n  height: 100%;\n  width: fit-content;\n  font-size: 13px;\n  overflow: hidden;\n}\n.codemirror-container.bordered {\n  border: 1px solid #aaaaaa;\n}\n\n.codemirror-container .editor_custom_link {\n  cursor: pointer;\n  color: #1474f1;\n  text-decoration: underline;\n}\n.codemirror-container .editor_custom_link:hover {\n  color: #04b4fa;\n}\n.codemirror-container:not(.original-style) .CodeMirror-lines .CodeMirror-placeholder.CodeMirror-line-like {\n  color: #666;\n}\n.codemirror-container:not(.original-style) .CodeMirror,\n.codemirror-container:not(.original-style) .CodeMirror-merge-pane {\n  height: 100%;\n  font-family: consolas !important;\n}\n.codemirror-container:not(.original-style) .CodeMirror-merge,\n.codemirror-container:not(.original-style) .CodeMirror-merge-right .CodeMirror {\n  height: 100%;\n  border: none !important;\n}\n.codemirror-container:not(.original-style) .c-editor--log__error {\n  color: #bb0606;\n  font-weight: bold;\n}\n.codemirror-container:not(.original-style) .c-editor--log__info {\n  color: #333333;\n  font-weight: bold;\n}\n.codemirror-container:not(.original-style) .c-editor--log__warning {\n  color: #ee9900;\n}\n.codemirror-container:not(.original-style) .c-editor--log__success {\n  color: #669600;\n}\n.codemirror-container:not(.original-style) .cm-header,\n.codemirror-container:not(.original-style) .cm-strong {\n  font-weight: bold;\n}\n`);\nexport {\n  Pe as CodeMirror,\n  Ue as GlobalCmComponent,\n  We as InstallCodeMirror,\n  R as VueCodemirror,\n  ze as createLinkMark,\n  Re as createLog,\n  He as createLogMark,\n  Be as createTitle,\n  R as default,\n  fe as getLinkMarks,\n  ge as getLocalTime,\n  he as getLogMark,\n  me as logErrorType\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAGA,KAAC,SAAS,KAAK;AACb,UAAI,OAAO,WAAW,YAAY,OAAO,UAAU;AACjD,YAAI,oBAA+B;AAAA,eAC5B,OAAO,UAAU,cAAc,OAAO;AAC7C,eAAO,CAAC,sBAAsB,GAAG,GAAG;AAAA;AAEpC,YAAI,UAAU;AAAA,IAClB,GAAG,SAASA,aAAY;AACtB;AAEA,eAAS,OAAO,IAAI,KAAK,SAAS,OAAO;AACvC,YAAI,WAAW,QAAQ,MAAM;AAC3B,cAAI,SAAS;AACb,oBAAU;AAAA,QACZ,OAAO;AACL,cAAI,SAAS,UAAU,IAAI,SAAS,aAAa;AAAA,QACnD;AACA,YAAI,OAAO,OAAO;AAAU,gBAAMA,YAAW,IAAI,KAAK,CAAC;AACvD,YAAI,UAAU,UAAU,IAAI,SAAS,aAAa;AAElD,iBAAS,SAAS,aAAa;AAC7B,cAAIC,SAAQ,OAAO,IAAI,GAAG;AAC1B,cAAI,CAACA,UAASA,OAAM,GAAG,OAAOA,OAAM,KAAK,OAAO;AAAS,mBAAO;AAChE,cAAI,UAAU;AAAQ,mBAAOA;AAE7B,cAAI,QAAQ,GAAG,YAAYA,OAAM,IAAI;AACrC,mBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,EAAE,GAAG;AACrC,gBAAI,MAAM,CAAC,EAAE,UAAU;AACrB,kBAAI,CAAC;AAAa,uBAAO;AACzB,cAAAA,OAAM,UAAU;AAChB,oBAAM,CAAC,EAAE,MAAM;AAAA,YACjB;AAAA,UACF;AACA,iBAAOA;AAAA,QACT;AAEA,YAAI,QAAQ,SAAS,IAAI;AACzB,YAAI,UAAU,IAAI,SAAS,QAAQ;AAAG,iBAAO,CAAC,SAAS,IAAI,OAAO,GAAG,UAAU,GAAG;AAChF,kBAAMD,YAAW,IAAI,IAAI,OAAO,GAAG,CAAC;AACpC,oBAAQ,SAAS,KAAK;AAAA,UACxB;AACA,YAAI,CAAC,SAAS,MAAM,WAAW,UAAU;AAAU;AAEnD,YAAI,WAAW,WAAW,IAAI,SAAS,KAAK;AAC5C,QAAAA,YAAW,GAAG,UAAU,aAAa,SAAS,GAAG;AAC/C,kBAAQ,MAAM;AACd,UAAAA,YAAW,iBAAiB,CAAC;AAAA,QAC/B,CAAC;AACD,YAAI,UAAU,GAAG,SAAS,MAAM,MAAM,MAAM,IAAI;AAAA,UAC9C,cAAc;AAAA,UACd,cAAc,UAAU,IAAI,SAAS,cAAc;AAAA,UACnD,UAAU;AAAA,QACZ,CAAC;AACD,gBAAQ,GAAG,SAAS,SAAS,MAAM,IAAI;AACrC,UAAAA,YAAW,OAAO,IAAI,UAAU,IAAI,MAAM,EAAE;AAAA,QAC9C,CAAC;AACD,QAAAA,YAAW,OAAO,IAAI,QAAQ,IAAI,MAAM,MAAM,MAAM,EAAE;AAAA,MACxD;AAEA,eAAS,WAAW,IAAI,SAAS,OAAO;AACtC,YAAI,SAAS,UAAU,IAAI,SAAS,QAAQ;AAE5C,YAAI,OAAO,UAAU,YAAY;AAC/B,mBAAS,OAAO,MAAM,MAAM,MAAM,EAAE;AAAA,QACtC;AAEA,YAAI,OAAO,UAAU,UAAU;AAC7B,cAAI,OAAO,SAAS,eAAe,MAAM;AACzC,mBAAS,SAAS,cAAc,MAAM;AACtC,iBAAO,YAAY,IAAI;AACvB,iBAAO,YAAY;AAAA,QACrB,WAAW,QAAQ;AACjB,mBAAS,OAAO,UAAU,IAAI;AAAA,QAChC;AACA,eAAO;AAAA,MACT;AAGA,MAAAA,YAAW,kBAAkB,SAAS,aAAa,QAAQ;AACzD,eAAO,SAAS,IAAI,KAAK;AAAE,iBAAO,IAAI,KAAK,EAAC,aAA0B,OAAc,CAAC;AAAA,QAAG;AAAA,MAC1F;AAGA,MAAAA,YAAW,gBAAgB,YAAY,SAAS,KAAK,SAAS,OAAO;AACnE,eAAO,MAAM,KAAK,SAAS,KAAK;AAAA,MAClC,CAAC;AAED,MAAAA,YAAW,gBAAgB,YAAY,SAAS,KAAK;AACnD,YAAI,QAAQ,KAAK,YAAY,GAAG;AAChC,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,EAAE;AAClC,cAAI,MAAM,CAAC,EAAE;AAAU,mBAAO;AAAA,MAClC,CAAC;AAED,MAAAA,YAAW,SAAS,aAAa,SAAS,IAAI;AAC5C,WAAG,SAAS,GAAG,UAAU,CAAC;AAAA,MAC5B;AACA,MAAAA,YAAW,SAAS,OAAO,SAAS,IAAI;AACtC,WAAG,SAAS,GAAG,UAAU,GAAG,MAAM,MAAM;AAAA,MAC1C;AACA,MAAAA,YAAW,SAAS,SAAS,SAAS,IAAI;AACxC,WAAG,SAAS,GAAG,UAAU,GAAG,EAAE,QAAQ,MAAM,GAAG,QAAQ;AAAA,MACzD;AACA,MAAAA,YAAW,SAAS,UAAU,SAAS,IAAI;AACzC,WAAG,UAAU,WAAW;AACtB,mBAAS,IAAI,GAAG,UAAU,GAAG,IAAI,GAAG,SAAS,GAAG,KAAK,GAAG;AACtD,eAAG,SAASA,YAAW,IAAI,GAAG,CAAC,GAAG,EAAE,QAAQ,MAAM,GAAG,MAAM;AAAA,QAC/D,CAAC;AAAA,MACH;AACA,MAAAA,YAAW,SAAS,YAAY,SAAS,IAAI;AAC3C,WAAG,UAAU,WAAW;AACtB,mBAAS,IAAI,GAAG,UAAU,GAAG,IAAI,GAAG,SAAS,GAAG,KAAK,GAAG;AACtD,eAAG,SAASA,YAAW,IAAI,GAAG,CAAC,GAAG,EAAE,QAAQ,MAAM,GAAG,QAAQ;AAAA,QACjE,CAAC;AAAA,MACH;AAEA,MAAAA,YAAW,eAAe,QAAQ,WAAW,WAAW;AACtD,YAAI,QAAQ,MAAM,UAAU,MAAM,KAAK,WAAW,CAAC;AACnD,eAAO,SAAS,IAAI,OAAO;AACzB,mBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,EAAE,GAAG;AACrC,gBAAI,QAAQ,MAAM,CAAC,EAAE,IAAI,KAAK;AAC9B,gBAAI;AAAO,qBAAO;AAAA,UACpB;AAAA,QACF;AAAA,MACF,CAAC;AAED,MAAAA,YAAW,eAAe,QAAQ,QAAQ,SAAS,IAAI,OAAO;AAC5D,YAAI,UAAU,GAAG,WAAW,OAAO,MAAM;AACzC,iBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,cAAI,MAAM,QAAQ,CAAC,EAAE,IAAI,KAAK;AAC9B,cAAI;AAAK,mBAAO;AAAA,QAClB;AAAA,MACF,CAAC;AAED,UAAI,iBAAiB;AAAA,QACnB,aAAaA,YAAW,KAAK;AAAA,QAC7B,QAAQ;AAAA,QACR,aAAa;AAAA,QACb,QAAQ;AAAA,QACR,cAAc;AAAA,MAChB;AAEA,MAAAA,YAAW,aAAa,eAAe,IAAI;AAE3C,eAAS,UAAU,IAAI,SAAS,MAAM;AACpC,YAAI,WAAW,QAAQ,IAAI,MAAM;AAC/B,iBAAO,QAAQ,IAAI;AACrB,YAAI,gBAAgB,GAAG,QAAQ;AAC/B,YAAI,iBAAiB,cAAc,IAAI,MAAM;AAC3C,iBAAO,cAAc,IAAI;AAC3B,eAAO,eAAe,IAAI;AAAA,MAC5B;AAEA,MAAAA,YAAW,gBAAgB,cAAc,SAAS,SAAS,MAAM;AAC/D,eAAO,UAAU,MAAM,SAAS,IAAI;AAAA,MACtC,CAAC;AAAA,IACH,CAAC;AAAA;AAAA;;;AC9JD;AAAA;AAGA,KAAC,SAAS,KAAK;AACb,UAAI,OAAO,WAAW,YAAY,OAAO,UAAU;AACjD,YAAI,sBAAiC,kBAAqB;AAAA,eACnD,OAAO,UAAU,cAAc,OAAO;AAC7C,eAAO,CAAC,wBAAwB,YAAY,GAAG,GAAG;AAAA;AAElD,YAAI,UAAU;AAAA,IAClB,GAAG,SAASE,aAAY;AACtB;AAEA,MAAAA,YAAW,aAAa,cAAc,OAAO,SAAS,IAAI,KAAK,KAAK;AAClE,YAAI,OAAO,OAAOA,YAAW,MAAM;AACjC,aAAG,YAAY,GAAG,MAAM,WAAW,QAAQ,MAAM;AACjD,aAAG,MAAM,aAAa;AACtB,aAAG,IAAI,eAAe,aAAa;AACnC,aAAG,IAAI,WAAW,QAAQ;AAC1B,aAAG,IAAI,kBAAkB,gBAAgB;AACzC,aAAG,IAAI,QAAQ,MAAM;AACrB,aAAG,IAAI,UAAU,MAAM;AACvB,aAAG,IAAI,WAAW,QAAQ;AAC1B,aAAG,IAAI,gBAAgB,YAAY;AAAA,QACrC;AACA,YAAI,KAAK;AACP,aAAG,MAAM,aAAa,IAAI,MAAM,aAAa,GAAG,CAAC;AACjD,2BAAiB,EAAE;AACnB,aAAG,GAAG,eAAe,aAAa;AAClC,aAAG,GAAG,WAAW,QAAQ;AACzB,aAAG,GAAG,kBAAkB,gBAAgB;AACxC,aAAG,GAAG,QAAQ,MAAM;AACpB,aAAG,GAAG,UAAU,MAAM;AACtB,aAAG,GAAG,WAAW,QAAQ;AACzB,aAAG,GAAG,gBAAgB,YAAY;AAAA,QACpC;AAAA,MACF,CAAC;AAED,UAAI,MAAMA,YAAW;AAErB,eAAS,MAAM,SAAS;AACtB,aAAK,UAAU;AACf,aAAK,OAAO,KAAK,KAAK;AAAA,MACxB;AAEA,eAAS,aAAa,MAAM;AAC1B,YAAI,SAAS;AAAM,iBAAO,CAAC;AAC3B,YAAI,KAAK,UAAU;AAAM,eAAK,SAAS;AACvC,YAAI,KAAK,iBAAiB;AAAM,eAAK,gBAAgB;AACrD,YAAI,KAAK,mBAAmB;AAAM,eAAK,kBAAkB;AACzD,eAAO;AAAA,MACT;AAEA,eAAS,SAAS,IAAI,MAAM;AAC1B,YAAI,QAAQ,GAAG,UAAU,IAAI,MAAM,CAAC,GAAG,IAAI,OAAO,GAAG,CAAC,CAAC;AACvD,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,EAAE,GAAG;AACrC,cAAI,MAAM,CAAC,EAAE,UAAU;AACrB,gBAAI,UAAU,MAAM,CAAC,EAAE,KAAK,EAAE;AAC9B,gBAAI,WAAW,QAAQ,SAAS;AAC9B,qBAAO,MAAM,CAAC;AAAA,UAClB;AAAA,QACF;AAAA,MACF;AAEA,eAAS,OAAO,MAAM;AACpB,YAAI,OAAO,QAAQ,UAAU;AAC3B,cAAI,MAAM,SAAS,cAAc,KAAK;AACtC,cAAI,YAAY,OAAO;AACvB,iBAAO;AAAA,QACT,OAAO;AACL,iBAAO,KAAK,UAAU,IAAI;AAAA,QAC5B;AAAA,MACF;AAEA,eAAS,eAAe,IAAI,MAAM,IAAI;AACpC,YAAI,OAAO,GAAG,MAAM,WAAW,SAAS,MAAM,OAAO;AACrD,YAAI,UAAU,GAAG,WAAW,MAAM,aAAa;AAC/C,YAAI,OAAO,GAAG,WAAW,MAAM,aAAa;AAE5C,YAAI,YAAY,OAAO,KAAK,mBAAmB,YAAY,UAAU,KAAK,eAAe;AACzF,YAAI,UAAU,OAAO,KAAK,iBAAiB,YAAY,UAAU,KAAK,aAAa;AACnF,WAAG,SAAS,MAAM,IAAI,SAAS,MAAM;AACnC,YAAE;AACF,cAAI,OAAO;AACX,cAAI,MAAM,KAAK;AACf,cAAI;AAAK,kBAAM,IAAI,KAAK,MAAM;AAC9B,cAAI,SAAS,IAAI,GAAG,GAAG;AACrB,gBAAI,aAAa,OAAO,UAAU,KAAK,IAAI,SAAS;AAAG;AACvD,mBAAO,OAAO,KAAK,eAAe;AAAA,UACpC,OAAO;AACL,gBAAI,MAAM,IAAI,KAAK,CAAC;AACpB,gBAAI,QAAQ,QAAQ,KAAK,IAAI,GAAG;AAChC,gBAAI,SAAS,MAAM,GAAG,OAAO,MAAM,KAAK,QAAQ,SAAS;AACvD,kBAAI,WAAW,OAAO,QAAQ,KAAK,IAAI,SAAS;AAAG;AACnD,qBAAO,OAAO,KAAK,aAAa;AAAA,YAClC;AAAA,UACF;AACA,cAAI,CAAC,QAAQ,CAAC;AAAK;AACnB,aAAG,gBAAgB,MAAM,KAAK,QAAQ,IAAI;AAAA,QAC5C,CAAC;AAAA,MACH;AAGA,eAAS,UAAU,KAAK;AAAE,eAAO,IAAI,OAAO,YAAY,MAAM,eAAe;AAAA,MAAE;AAE/E,eAAS,iBAAiB,IAAI;AAC5B,YAAI,KAAK,GAAG,YAAY,GAAG,QAAQ,GAAG,MAAM;AAC5C,YAAI,CAAC;AAAO;AACZ,WAAG,UAAU,WAAW;AACtB,yBAAe,IAAI,GAAG,MAAM,GAAG,EAAE;AAAA,QACnC,CAAC;AACD,cAAM,OAAO,GAAG;AAAM,cAAM,KAAK,GAAG;AAAA,MACtC;AAEA,eAAS,cAAc,IAAI,MAAM,QAAQ;AACvC,YAAI,QAAQ,GAAG,MAAM;AACrB,YAAI,CAAC;AAAO;AACZ,YAAI,OAAO,MAAM;AACjB,YAAI,UAAU,KAAK;AAAQ;AAC3B,YAAI,SAAS,SAAS,IAAI,IAAI;AAC9B,YAAI;AAAQ,iBAAO,MAAM;AAAA;AACpB,aAAG,SAAS,IAAI,MAAM,CAAC,GAAG,IAAI;AAAA,MACrC;AAEA,eAAS,aAAa,IAAI,QAAQ;AAChC,YAAI,UAAU;AAAQ,mBAAS,EAAE;AAAA,MACnC;AAEA,eAAS,SAAS,IAAI;AACpB,YAAI,QAAQ,GAAG,MAAM;AACrB,YAAI,CAAC;AAAO;AACZ,YAAI,OAAO,MAAM;AACjB,cAAM,OAAO,MAAM,KAAK;AACxB,qBAAa,MAAM,YAAY;AAC/B,cAAM,eAAe,WAAW,WAAW;AAAE,2BAAiB,EAAE;AAAA,QAAG,GAAG,KAAK,wBAAwB,GAAG;AAAA,MACxG;AAEA,eAAS,iBAAiB,IAAI;AAC5B,YAAI,QAAQ,GAAG,MAAM;AACrB,YAAI,CAAC;AAAO;AACZ,YAAI,OAAO,MAAM;AACjB,qBAAa,MAAM,YAAY;AAC/B,cAAM,eAAe,WAAW,WAAW;AACzC,cAAI,KAAK,GAAG,YAAY;AACxB,cAAI,MAAM,QAAQ,MAAM,MAAM,GAAG,OAAO,MAAM,KAAK,MAAM,MAAM,OAAO,GAAG,KAAK,IAAI;AAChF,6BAAiB,EAAE;AAAA,UACrB,OAAO;AACL,eAAG,UAAU,WAAW;AACtB,kBAAI,GAAG,OAAO,MAAM,MAAM;AACxB,+BAAe,IAAI,GAAG,MAAM,MAAM,IAAI;AACtC,sBAAM,OAAO,GAAG;AAAA,cAClB;AACA,kBAAI,GAAG,KAAK,MAAM,IAAI;AACpB,+BAAe,IAAI,MAAM,IAAI,GAAG,EAAE;AAClC,sBAAM,KAAK,GAAG;AAAA,cAChB;AAAA,YACF,CAAC;AAAA,UACH;AAAA,QACF,GAAG,KAAK,0BAA0B,GAAG;AAAA,MACvC;AAEA,eAAS,OAAO,IAAI,MAAM;AACxB,YAAI,QAAQ,GAAG,MAAM;AACrB,YAAI,CAAC;AAAO;AACZ,YAAI,OAAO,KAAK;AAChB,YAAI,QAAQ,MAAM,QAAQ,OAAO,MAAM;AACrC,yBAAe,IAAI,MAAM,OAAO,CAAC;AAAA,MACrC;AAAA,IACF,CAAC;AAAA;AAAA;;;ACxKD;AAAA;AAGA,KAAC,SAAS,KAAK;AACb,UAAI,OAAO,WAAW,YAAY,OAAO,UAAU;AACjD,YAAI,oBAA+B;AAAA,eAC5B,OAAO,UAAU,cAAc,OAAO;AAC7C,eAAO,CAAC,sBAAsB,GAAG,GAAG;AAAA;AAEpC,YAAI,UAAU;AAAA,IAClB,GAAG,SAASC,aAAY;AACxB;AAEA,eAAS,eAAe,OAAO;AAC7B,eAAO,SAAS,IAAI,OAAO;AACzB,cAAI,OAAO,MAAM,MAAM,WAAW,GAAG,QAAQ,IAAI;AAEjD,mBAAS,YAAY,MAAM;AACzB,gBAAI;AACJ,qBAAS,KAAK,MAAM,IAAI,OAAO,OAAK;AAClC,kBAAIC,SAAQ,MAAM,IAAI,KAAK,SAAS,YAAY,KAAK,CAAC,GAAG,KAAK,CAAC;AAC/D,kBAAIA,UAAS,IAAI;AACf,oBAAI,QAAQ;AAAG;AACf,uBAAO;AACP,qBAAK,SAAS;AACd;AAAA,cACF;AACA,kBAAI,QAAQ,KAAKA,SAAQ,MAAM;AAAI;AACnC,0BAAY,GAAG,eAAeD,YAAW,IAAI,MAAMC,SAAQ,CAAC,CAAC;AAC7D,kBAAI,CAAC,oBAAoB,KAAK,SAAS;AAAG,uBAAO,EAAC,IAAIA,SAAQ,GAAG,WAAsB,KAAU;AACjG,mBAAKA,SAAQ;AAAA,YACf;AAAA,UACF;AAEA,mBAAS,UAAUA,QAAO;AACxB,gBAAI,QAAQ,GAAG,WAAW,GAAG,SAAS,GAAG,KAAK,UAAUA,OAAM,IAAI;AAClE;AAAO,uBAASC,KAAI,MAAMA,MAAK,UAAU,EAAEA,IAAG;AAC5C,oBAAI,OAAO,GAAG,QAAQA,EAAC,GAAG,MAAMA,MAAK,OAAO,UAAU;AACtD,2BAAS;AACP,sBAAI,WAAW,KAAK,QAAQD,OAAM,KAAK,CAAC,GAAG,GAAG,GAAG,YAAY,KAAK,QAAQA,OAAM,KAAK,CAAC,GAAG,GAAG;AAC5F,sBAAI,WAAW;AAAG,+BAAW,KAAK;AAClC,sBAAI,YAAY;AAAG,gCAAY,KAAK;AACpC,wBAAM,KAAK,IAAI,UAAU,SAAS;AAClC,sBAAI,OAAO,KAAK;AAAQ;AACxB,sBAAI,GAAG,eAAeD,YAAW,IAAIE,IAAG,MAAM,CAAC,CAAC,KAAKD,OAAM,WAAW;AACpE,wBAAI,OAAO;AAAU,wBAAE;AAAA,6BACd,CAAC,EAAE,OAAO;AAAE,4BAAMC;AAAG,8BAAQ;AAAK,4BAAM;AAAA,oBAAO;AAAA,kBAC1D;AACA,oBAAE;AAAA,gBACJ;AAAA,cACF;AAEA,gBAAI,OAAO,QAAQ,QAAQ;AAAK,qBAAO;AACvC,mBAAO;AAAA,cAAC,MAAMF,YAAW,IAAI,MAAM,OAAO;AAAA,cAClC,IAAIA,YAAW,IAAI,KAAK,KAAK;AAAA,YAAC;AAAA,UACxC;AAEA,cAAI,QAAQ,CAAC;AACb,mBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,gBAAI,OAAO,YAAY,MAAM,CAAC,CAAC;AAC/B,gBAAI;AAAM,oBAAM,KAAK,IAAI;AAAA,UAC3B;AACA,gBAAM,KAAK,SAAS,GAAGG,IAAG;AAAE,mBAAO,EAAE,KAAKA,GAAE;AAAA,UAAG,CAAC;AAChD,mBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,gBAAI,QAAQ,UAAU,MAAM,CAAC,CAAC;AAC9B,gBAAI;AAAO,qBAAO;AAAA,UACpB;AACA,iBAAO;AAAA,QACT;AAAA,MACF;AAEA,MAAAH,YAAW,eAAe,QAAQ,SAAS,eAAe,CAAC,CAAC,KAAK,GAAG,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;AAEnF,MAAAA,YAAW,eAAe,QAAQ,eAAe,eAAe,CAAC,CAAC,KAAK,GAAG,GAAG,CAAC,KAAK,GAAG,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;AAErG,MAAAA,YAAW,eAAe,QAAQ,UAAU,SAAS,IAAI,OAAO;AAC9D,iBAAS,UAAU,MAAM;AACvB,cAAI,OAAO,GAAG,UAAU,KAAK,OAAO,GAAG,SAAS;AAAG,mBAAO;AAC1D,cAAII,SAAQ,GAAG,WAAWJ,YAAW,IAAI,MAAM,CAAC,CAAC;AACjD,cAAI,CAAC,KAAK,KAAKI,OAAM,MAAM;AAAG,YAAAA,SAAQ,GAAG,WAAWJ,YAAW,IAAI,MAAMI,OAAM,MAAM,CAAC,CAAC;AACvF,cAAIA,OAAM,QAAQ,aAAaA,OAAM,UAAU;AAAU,mBAAO;AAEhE,mBAAS,IAAI,MAAM,IAAI,KAAK,IAAI,GAAG,SAAS,GAAG,OAAO,EAAE,GAAG,KAAK,GAAG,EAAE,GAAG;AACtE,gBAAI,OAAO,GAAG,QAAQ,CAAC,GAAG,OAAO,KAAK,QAAQ,GAAG;AACjD,gBAAI,QAAQ;AAAI,qBAAO,EAAC,SAASA,OAAM,KAAK,KAAKJ,YAAW,IAAI,GAAG,IAAI,EAAC;AAAA,UAC1E;AAAA,QACF;AAEA,YAAI,YAAY,MAAM,MAAM,MAAM,UAAU,SAAS,GAAG;AACxD,YAAI,CAAC,OAAO,UAAU,YAAY,CAAC,MAAO,OAAO,UAAU,YAAY,CAAC,MAAM,KAAK,IAAI,QAAQ,YAAY;AACzG,iBAAO;AACT,iBAAS,MAAM,IAAI,SAAO;AACxB,cAAI,OAAO,UAAU,IAAI,OAAO,CAAC;AACjC,cAAI,QAAQ;AAAM;AAClB,gBAAM,KAAK;AAAA,QACb;AACA,eAAO,EAAC,MAAM,GAAG,QAAQA,YAAW,IAAI,WAAW,IAAI,UAAU,CAAC,CAAC,GAAG,IAAI,IAAG;AAAA,MAC/E,CAAC;AAED,MAAAA,YAAW,eAAe,QAAQ,WAAW,SAAS,IAAI,OAAO;AAC/D,iBAAS,WAAW,MAAM;AACxB,cAAI,OAAO,GAAG,UAAU,KAAK,OAAO,GAAG,SAAS;AAAG,mBAAO;AAC1D,cAAII,SAAQ,GAAG,WAAWJ,YAAW,IAAI,MAAM,CAAC,CAAC;AACjD,cAAI,CAAC,KAAK,KAAKI,OAAM,MAAM;AAAG,YAAAA,SAAQ,GAAG,WAAWJ,YAAW,IAAI,MAAMI,OAAM,MAAM,CAAC,CAAC;AACvF,cAAIA,OAAM,QAAQ,UAAUA,OAAM,OAAO,MAAM,GAAG,CAAC,KAAK;AAAY,mBAAOA,OAAM,QAAQ;AAAA,QAC3F;AAEA,YAAI,YAAY,MAAM,MAAM,MAAM,WAAW,SAAS;AACtD,YAAI,OAAO,QAAQ,WAAW,YAAY,CAAC,KAAK;AAAM,iBAAO;AAC7D,iBAAS,MAAM,eAAa;AAC1B,cAAI,OAAO,WAAW,MAAM,CAAC;AAC7B,cAAI,QAAQ;AAAM;AAClB,YAAE;AAAA,QACJ;AACA,eAAO;AAAA,UAAC,MAAMJ,YAAW,IAAI,WAAW,MAAM,CAAC;AAAA,UACvC,IAAI,GAAG,QAAQA,YAAW,IAAI,GAAG,CAAC;AAAA,QAAC;AAAA,MAC7C,CAAC;AAAA,IAED,CAAC;AAAA;AAAA;;;ACtHD;AAAA;AAGA,KAAC,SAAS,KAAK;AACb,UAAI,OAAO,WAAW,YAAY,OAAO,UAAU;AACjD,YAAI,oBAA+B;AAAA,eAC5B,OAAO,UAAU,cAAc,OAAO;AAC7C,eAAO,CAAC,sBAAsB,GAAG,GAAG;AAAA;AAEpC,YAAI,UAAU;AAAA,IAClB,GAAG,SAASK,aAAY;AACtB;AACA,UAAI,aAAa;AACjB,UAAI,aAAa;AACjB,UAAI,aAAa;AAEjB,MAAAA,YAAW,aAAa,mBAAmB,OAAO,SAAS,IAAI,KAAK,KAAK;AACvE,YAAI,OAAO,OAAOA,YAAW,OAAO,QAAQ;AAC5C,YAAI,OAAO;AAAM;AACjB,YAAI,MAAM;AACR,aAAG,IAAI,yBAAyB,eAAe;AAC/C,2BAAiB,EAAE;AACnB,iBAAO,GAAG,MAAM;AAAA,QAClB;AACA,YAAI,KAAK;AACP,aAAG,MAAM,cAAc,CAAC;AACxB,4BAAkB,IAAI,GAAG,eAAe,CAAC;AACzC,aAAG,GAAG,yBAAyB,eAAe;AAAA,QAChD;AAAA,MACF,CAAC;AAED,eAAS,iBAAiB,IAAI;AAC5B,iBAAS,IAAI,GAAG,IAAI,GAAG,MAAM,YAAY,QAAQ,KAAK;AACpD,aAAG,gBAAgB,GAAG,MAAM,YAAY,CAAC,GAAG,QAAQ,UAAU;AAC9D,aAAG,gBAAgB,GAAG,MAAM,YAAY,CAAC,GAAG,cAAc,UAAU;AACpE,aAAG,gBAAgB,GAAG,MAAM,YAAY,CAAC,GAAG,UAAU,UAAU;AAAA,QAClE;AAAA,MACF;AAEA,eAAS,UAAU,GAAGC,IAAG;AACvB,YAAI,EAAE,UAAUA,GAAE;AAAQ,iBAAO;AACjC,iBAAS,IAAI,GAAG,IAAI,EAAE,QAAQ;AAC5B,cAAI,EAAE,CAAC,KAAKA,GAAE,CAAC;AAAG,mBAAO;AAC3B,eAAO;AAAA,MACT;AAEA,eAAS,kBAAkB,IAAI,QAAQ;AACrC,YAAI,SAAS,CAAC;AACd,iBAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,cAAI,QAAQ,OAAO,CAAC;AACpB,cAAI,SAAS,GAAG,UAAU,iBAAiB;AAC3C,cAAI,OAAO,UAAU,YAAY,OAAO,WAAW,MAAM,OAAO,QAAQ,MAAM,KAAK,OAAO,CAAC,MAAM,MAAM;AACrG;AACF,cAAI,OAAO,GAAG,yBAAyB,MAAM,KAAK,IAAI;AACtD,cAAI,OAAO,OAAO,SAAS,CAAC,KAAK;AAAM,mBAAO,KAAK,IAAI;AAAA,QACzD;AACA,YAAI,UAAU,GAAG,MAAM,aAAa,MAAM;AAAG;AAC7C,WAAG,UAAU,WAAW;AACtB,2BAAiB,EAAE;AACnB,mBAASC,KAAI,GAAGA,KAAI,OAAO,QAAQA,MAAK;AACtC,eAAG,aAAa,OAAOA,EAAC,GAAG,QAAQ,UAAU;AAC7C,eAAG,aAAa,OAAOA,EAAC,GAAG,cAAc,UAAU;AACnD,eAAG,aAAa,OAAOA,EAAC,GAAG,UAAU,UAAU;AAAA,UACjD;AACA,aAAG,MAAM,cAAc;AAAA,QACzB,CAAC;AAAA,MACH;AAEA,eAAS,gBAAgB,IAAI,KAAK;AAChC,0BAAkB,IAAI,IAAI,MAAM;AAAA,MAClC;AAAA,IACF,CAAC;AAAA;AAAA;;;ACvED;AAAA;AAKA,KAAC,SAAS,KAAK;AACb,UAAI,OAAO,WAAW,YAAY,OAAO,UAAU;AACjD,YAAI,oBAA+B;AAAA,eAC5B,OAAO,UAAU,cAAc,OAAO;AAC7C,eAAO,CAAC,wBAAwB,kBAAkB,GAAG,GAAG;AAAA;AAExD,YAAI,UAAU;AAAA,IAClB,GAAG,SAASC,aAAY;AACtB;AACA,UAAI,MAAMA,YAAW;AACrB,UAAI,QAAQ;AAEZ,eAAS,SAAS,IAAI,MAAM;AAC1B,aAAK,KAAK;AACV,aAAK,OAAO;AACZ,aAAK,UAAU,QAAQ,SACnB;AAAA,UAAC,OAAO;AAAA,UACP,OAAO;AAAA,UACP,KAAK;AAAA,UACL,QAAQ;AAAA,UACR,KAAK;AAAA,UACL,SAAS;AAAA,QAA4B,IACtC;AAAA,UAAC,OAAO;AAAA,UACP,OAAO;AAAA,UACP,KAAK;AAAA,UACL,QAAQ;AAAA,UACR,KAAK;AAAA,UACL,SAAS;AAAA,QAA4B;AAAA,MAC5C;AAEA,eAAS,YAAY;AAAA,QACnB,aAAa;AAAA,QACb,MAAM,SAAS,MAAM,MAAM,SAAS;AAClC,eAAK,OAAO,KAAK,GAAG;AACpB;AAAC,WAAC,KAAK,KAAK,MAAM,cAAc,KAAK,KAAK,MAAM,YAAY,CAAC,IAAI,KAAK,IAAI;AAC1E,eAAK,OAAOA,YAAW,MAAM,QAAQ,EAAC,OAAO,MAAM,UAAU,CAAC,KAAK,GAAG,QAAQ,sBAAqB,GAAG,QAAQ,OAAO,CAAC,CAAC;AACvH,cAAI,KAAK,GAAG,QAAQ,WAAW,SAAS;AACtC,gBAAI,CAAC,KAAK,KAAK,MAAM;AAAgB,mBAAK,KAAK,MAAM,iBAAiB,IAAI,eAAe,KAAK,IAAI;AAClG,iBAAK,KAAK,MAAM,iBAAiB,IAAI,eAAe,KAAK,IAAI;AAAA,UAC/D;AACA,eAAK,WAAW,QAAQ,KAAK,KAAK,OAAO,yBAAyB;AAClE,eAAK,WAAW,aAAa,cAAc,KAAK,WAAW,KAAK;AAEhE,eAAK,KAAK,MAAM,YAAY,CAAC,IAAI;AACjC,cAAI,gBAAgB,QAAQ,sBAAsB;AAClD,cAAI,OAAO,UAAU,SAAS,KAAK,aAAa,KAAK;AAAkB,4BAAgB,CAAC,aAAa;AACrG,eAAK,QAAQ,gBAAgB;AAE7B,eAAK,OAAO,QAAQ,SAAS,IAAI,GAAG,SAAS,QAAQ,KAAK,GAAG,KAAK,GAAG,QAAQ,gBAAgB;AAC7F,eAAK,SAAS,UAAU,KAAK,IAAI;AACjC,eAAK,gBAAgB,KAAK,YAAY;AACtC,eAAK,kBAAkB;AAEvB,eAAK,kBAAkB,QAAQ,oBAAoB;AAAA,QACrD;AAAA,QACA,gBAAgB,SAAS,SAAS;AAChC,eAAK,cAAc,eAAe,IAAI;AACtC,wBAAc,MAAM,MAAM,KAAK;AAC/B,yBAAe,MAAM,OAAO;AAAA,QAC9B;AAAA,QACA,oBAAoB,SAAS,KAAK;AAChC,gBAAM,QAAQ;AACd,cAAI,OAAO,KAAK,iBAAiB;AAC/B,iBAAK,kBAAkB;AACvB,iBAAK,YAAY,MAAM;AAAA,UACzB;AAAA,QACF;AAAA,MACF;AAEA,eAAS,WAAW,IAAI;AACtB,YAAI,GAAG,eAAe;AACpB,aAAG,OAAO,QAAQ,GAAG,KAAK,SAAS,GAAG,GAAG,KAAK,SAAS,GAAG,GAAG,GAAG,QAAQ,gBAAgB;AACxF,aAAG,SAAS,UAAU,GAAG,IAAI;AAC7B,aAAG,gBAAgB;AACnB,UAAAA,YAAW,OAAO,GAAG,MAAM,cAAc,GAAG,IAAI;AAAA,QAClD;AAAA,MACF;AAEA,UAAI,WAAW;AACf,eAAS,eAAe,IAAI;AAC1B,YAAI,OAAO,EAAC,MAAM,GAAG,IAAI,GAAG,QAAQ,CAAC,EAAC;AACtC,YAAI,OAAO,EAAC,MAAM,GAAG,IAAI,GAAG,QAAQ,CAAC,EAAC;AACtC,YAAI,gBAAgB,eAAe;AACnC,iBAAS,OAAO,MAAM;AACpB,qBAAW;AACX,yBAAe;AACf,cAAI,QAAQ,QAAQ;AAClB,gBAAI,GAAG;AAAK,oBAAM,GAAG,GAAG;AACxB,gBAAI,GAAG;AAAa,oBAAM,GAAG,WAAW;AACxC,uBAAW,GAAG,MAAM,KAAK,QAAQ,GAAG,OAAO;AAC3C,uBAAW,GAAG,MAAM,KAAK,QAAQ,GAAG,OAAO;AAC3C,iBAAK,OAAO,KAAK,KAAK,KAAK,OAAO,KAAK,KAAK;AAAA,UAC9C;AACA,qBAAW,EAAE;AACb,cAAI,GAAG,iBAAiB;AACtB,wBAAY,GAAG,MAAM,GAAG,MAAM,MAAM,aAAa,GAAG,OAAO;AAC3D,wBAAY,GAAG,MAAM,GAAG,MAAM,MAAM,aAAa,GAAG,OAAO;AAAA,UAC7D;AAEA,cAAI,GAAG,GAAG,QAAQ,WAAW;AAC3B,wBAAY,EAAE;AAChB,0BAAgB,EAAE;AAClB,cAAI,GAAG,mBAAmB;AAAM,uBAAW,IAAI,GAAG,eAAe;AAEjE,qBAAW;AAAA,QACb;AACA,iBAAS,WAAW,MAAM;AACxB,cAAI;AAAU;AACd,aAAG,YAAY;AACf,cAAI,IAAI;AAAA,QACV;AACA,iBAAS,IAAI,MAAM;AACjB,cAAI,YAAY;AAAc;AAC9B,uBAAa,cAAc;AAC3B,cAAI,SAAS;AAAM,2BAAe;AAClC,2BAAiB,WAAW,QAAQ,SAAS,OAAO,KAAK,GAAG;AAAA,QAC9D;AACA,iBAAS,OAAO,KAAKC,SAAQ;AAC3B,cAAI,CAAC,GAAG,eAAe;AACrB,eAAG,gBAAgB;AACnB,iBAAK,OAAO,KAAK,KAAK,KAAK,OAAO,KAAK,KAAK;AAAA,UAC9C;AAEA,qBAAWA,QAAO,KAAK,SAAS,KAAKA,QAAO,GAAG,OAAOA,QAAO,KAAK,IAAI;AAAA,QACxE;AACA,iBAAS,UAAU;AACjB,aAAG,gBAAgB;AACnB,aAAG,YAAY;AACf,iBAAO,MAAM;AAAA,QACf;AACA,WAAG,KAAK,GAAG,UAAU,MAAM;AAC3B,WAAG,KAAK,GAAG,UAAU,MAAM;AAC3B,WAAG,KAAK,GAAG,WAAW,OAAO;AAC7B,WAAG,KAAK,GAAG,WAAW,OAAO;AAC7B,YAAI,GAAG,GAAG,QAAQ,WAAW,SAAS;AACpC,UAAAD,YAAW,GAAG,GAAG,KAAK,MAAM,gBAAgB,WAAW,UAAU;AACjE,UAAAA,YAAW,GAAG,GAAG,KAAK,MAAM,gBAAgB,WAAW,UAAU;AAAA,QACnE;AACA,WAAG,KAAK,GAAG,kBAAkB,WAAW;AAAE,cAAI,KAAK;AAAA,QAAG,CAAC;AACvD,WAAG,KAAK,GAAG,kBAAkB,WAAW;AAAE,cAAI,KAAK;AAAA,QAAG,CAAC;AACvD,eAAO;AACP,eAAO;AAAA,MACT;AAEA,eAAS,eAAe,IAAI,SAAS;AACnC,WAAG,KAAK,GAAG,UAAU,WAAW;AAC9B,qBAAW,IAAI,IAAI,KAAK,gBAAgB,EAAE;AAAA,QAC5C,CAAC;AACD,WAAG,KAAK,GAAG,UAAU,WAAW;AAC9B,qBAAW,IAAI,KAAK,KAAK,gBAAgB,EAAE;AAC3C,cAAI;AAAS,uBAAW,SAAS,IAAI,KAAK,gBAAgB,OAAO;AAAA,QACnE,CAAC;AAAA,MACH;AAEA,eAAS,WAAW,IAAI,QAAQ;AAE9B,YAAI,GAAG,eAAe;AACpB,cAAI,GAAG,cAAc,GAAG,mBAAmB;AAAM,eAAG,kBAAkB;AACtE,iBAAO;AAAA,QACT;AACA,WAAG,kBAAkB;AACrB,YAAI,CAAC,GAAG;AAAY,iBAAO;AAC3B,YAAI,QAAQ,OAAO,MAAM,CAAC,oBAAI;AAC9B,YAAI,QAAQ;AAAE,mBAAS,GAAG;AAAM,kBAAQ,GAAG;AAAA,QAAM,OAC5C;AAAE,mBAAS,GAAG;AAAM,kBAAQ,GAAG;AAAA,QAAM;AAG1C,YAAI,OAAO,MAAM,eAAe,OAAO,OAAO,MAAM,eAAe,KAAK,MAAM;AAAK,iBAAO;AAE1F,YAAI,QAAQ,OAAO,cAAc;AACjC,YAAI,GAAG,GAAG,QAAQ,WAAW,SAAS;AACpC,sBAAY,MAAM;AAAA,QACpB,OAAO;AACL,cAAI,aAAa,MAAK,MAAM,cAAc,OAAO,MAAM,MAAM;AAC7D,cAAI,MAAM,OAAO,aAAa,MAAM,OAAO;AAC3C,cAAI,SAAS,sBAAsB,GAAG,QAAQ,KAAK,MAAM;AACzD,cAAI,MAAM,WAAW,QAAQ,SAAS,OAAO,OAAO,OAAO,IAAI;AAC/D,cAAI,WAAW,WAAW,OAAO,SAAS,OAAO,OAAO,OAAO,IAAI;AACnE,cAAI,SAAS,OAAO,IAAI,QAAQ,IAAI,MAAM,IAAI;AAC9C,cAAI,YAAa,SAAS,MAAM,aAAc,SAAS,SAAS,MAAM,SAAS;AAE/E,cAAI,SAAS;AAGb,cAAI,YAAY,MAAM,QAAQ,MAAM,MAAM,MAAM,cAAc,GAAG;AAC/D,wBAAY,YAAY,MAAM,MAAM,OAAO,IAAI;AAAA,UACjD,YAAY,UAAU,MAAM,SAAS,MAAM,eAAe,MAAM,OAAO,YAAY;AACjF,gBAAI,YAAY,MAAM,cAAc;AACpC,gBAAI,eAAe,UAAU,SAAS,UAAU,eAAe;AAC/D,gBAAI,eAAe,YAAY,MAAM,UAAU,cAAc;AAC3D,0BAAY,YAAY,OAAO,UAAU,SAAS,UAAU,eAAe,YAAY,IAAI;AAAA,UAC/F;AAAA,QACF;AAEA,cAAM,SAAS,MAAM,MAAM,SAAS;AACpC,cAAM,MAAM,cAAc;AAC1B,cAAM,MAAM,cAAc;AAC1B,eAAO;AAAA,MACT;AAEA,eAAS,WAAW,QAAQ,QAAQ;AAClC,YAAI,MAAM,OAAO;AACjB,YAAI,OAAO;AAAM,gBAAM,OAAO,SAAS,IAAI;AAC3C,eAAO;AAAA,UAAC,KAAK,OAAO,aAAa,OAAO,UAAU,GAAG,OAAO;AAAA,UACpD,KAAK,OAAO,aAAa,KAAK,OAAO;AAAA,QAAC;AAAA,MAChD;AAEA,eAAS,cAAc,IAAI,KAAK,QAAQ;AACtC,WAAG,aAAa;AAChB,YAAI,OAAO,UAAU;AAAO,qBAAW,IAAI,WAAW,KAAK,gBAAgB,EAAE;AAC7E,SAAC,MAAMA,YAAW,WAAWA,YAAW,SAAS,GAAG,YAAY,qCAAqC;AAAA,MACvG;AAIA,eAAS,YAAY,QAAQ,MAAM,SAAS;AAC1C,YAAI,OAAO,QAAQ;AACnB,iBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,iBAAO,gBAAgB,MAAM,KAAK,CAAC,GAAG,QAAQ,KAAK;AACnD,iBAAO,gBAAgB,MAAM,KAAK,CAAC,GAAG,QAAQ,KAAK;AACnD,iBAAO,gBAAgB,MAAM,KAAK,CAAC,GAAG,QAAQ,GAAG;AAAA,QACnD;AAAA,MACF;AAEA,eAAS,WAAW,QAAQ,KAAK,SAAS;AACxC,iBAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,EAAE,GAAG;AACnC,cAAI,OAAO,IAAI,CAAC;AAChB,cAAI,gBAAgBA,YAAW;AAC7B,iBAAK,MAAM;AAAA,mBACJ,KAAK;AACZ,wBAAY,QAAQ,MAAM,OAAO;AAAA,QACrC;AACA,YAAI,SAAS;AAAA,MACf;AAGA,eAAS,YAAY,QAAQ,MAAM,OAAO,MAAM,SAAS;AACvD,YAAI,KAAK,OAAO,YAAY;AAC5B,eAAO,UAAU,WAAW;AAC1B,cAAI,MAAM,QAAQ,MAAM,MAAM,GAAG,OAAO,MAAM,KAAK,MAAM,MAAM,OAAO,GAAG,KAAK,IAAI;AAChF,uBAAW,QAAQ,MAAM,QAAQ,OAAO;AACxC,wBAAY,QAAQ,MAAM,MAAM,MAAM,QAAQ,GAAG,MAAM,GAAG,IAAI,OAAO;AACrE,kBAAM,OAAO,GAAG;AAAM,kBAAM,KAAK,GAAG;AAAA,UACtC,OAAO;AACL,gBAAI,GAAG,OAAO,MAAM,MAAM;AACxB,0BAAY,QAAQ,MAAM,MAAM,MAAM,QAAQ,GAAG,MAAM,MAAM,MAAM,OAAO;AAC1E,oBAAM,OAAO,GAAG;AAAA,YAClB;AACA,gBAAI,GAAG,KAAK,MAAM,IAAI;AACpB,0BAAY,QAAQ,MAAM,MAAM,MAAM,QAAQ,MAAM,IAAI,GAAG,IAAI,OAAO;AACtE,oBAAM,KAAK,GAAG;AAAA,YAChB;AAAA,UACF;AAAA,QACF,CAAC;AAAA,MACH;AAEA,eAAS,SAAS,QAAQ,QAAQ,SAAS,MAAM,OAAO,KAAK;AAC3D,YAAI,OAAO,QAAQ,eAAe,OAAO,OAAO,cAAc,MAAM;AACpE,iBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,cAAI;AAAM,mBAAO,aAAa,MAAM,KAAK,CAAC,GAAG,QAAQ,KAAK;AAC1D,cAAI;AAAO,mBAAO,aAAa,MAAM,KAAK,CAAC,GAAG,QAAQ,KAAK;AAC3D,cAAI;AAAK,mBAAO,aAAa,MAAM,KAAK,CAAC,GAAG,QAAQ,GAAG;AAAA,QACzD;AACA,eAAO;AAAA,MACT;AAEA,eAAS,YAAY,QAAQ,MAAM,MAAM,OAAO,MAAM,IAAI,SAAS;AACjE,YAAI,MAAM,IAAI,GAAG,CAAC;AAClB,YAAI,MAAM,IAAI,MAAM,CAAC,GAAG,MAAM,OAAO,QAAQ,IAAI,KAAK,CAAC,CAAC;AACxD,YAAI,MAAM,QAAQ,cAAc,QAAQ,MAAM,QAAQ;AACtD,iBAAS,UAAU,OAAOE,MAAK;AAC7B,cAAI,QAAQ,KAAK,IAAI,MAAM,KAAK,GAAG,MAAM,KAAK,IAAI,IAAIA,IAAG;AACzD,mBAASC,KAAI,OAAOA,KAAI,KAAK,EAAEA;AAC7B,kBAAM,KAAK,SAAS,QAAQA,IAAG,SAAS,MAAMA,MAAK,OAAOA,MAAKD,OAAM,CAAC,CAAC;AAEzE,cAAI,SAASA,QAAO,SAASA,QAAO,OAAOA,MAAK;AAC9C,gBAAI;AACF,oBAAM,KAAK,SAAS,QAAQ,QAAQ,GAAG,SAAS,OAAO,OAAO,IAAI,CAAC;AAAA;AAEnE,oBAAM,KAAK,SAAS,QAAQ,OAAO,SAAS,OAAO,MAAM,KAAK,CAAC;AAAA,UACnE;AAAA,QACF;AAEA,YAAI,aAAa,GAAG,UAAU;AAC9B,iBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,EAAE,GAAG;AACpC,cAAI,OAAO,KAAK,CAAC,GAAG,KAAK,KAAK,CAAC,GAAG,MAAM,KAAK,CAAC;AAC9C,cAAI,MAAM,YAAY;AACpB,gBAAI,YAAY,IAAI,QAAQ,iBAAiB,MAAM,CAAC,IAAI,IAAI;AAC5D,qBAAS,KAAK,GAAG;AACjB,gBAAI,UAAU,IAAI,QAAQ,eAAe,MAAM,CAAC,IAAI,IAAI;AACxD,gBAAI,UAAU,WAAW;AACvB,kBAAI,SAAS;AAAE,0BAAU,YAAY,SAAS;AAAG,0BAAU;AAAA,cAAM;AACjE,2BAAa;AAAA,YACf;AAAA,UACF,OAAO;AACL,sBAAU;AACV,gBAAI,MAAM,MAAM;AACd,kBAAI,MAAM,SAAS,KAAK,KAAK,IAAI;AACjC,kBAAI,IAAI,OAAO,KAAK,GAAG,GAAGE,KAAI,OAAO,KAAK,GAAG;AAC7C,kBAAI,CAAC,MAAM,GAAGA,EAAC;AACb,sBAAM,KAAK,OAAO,SAAS,GAAGA,IAAG,EAAC,WAAW,IAAG,CAAC,CAAC;AACpD,oBAAM;AAAA,YACR;AAAA,UACF;AAAA,QACF;AACA,YAAI;AAAS,oBAAU,YAAY,IAAI,OAAO,CAAC;AAAA,MACjD;AAIA,eAAS,gBAAgB,IAAI;AAC3B,YAAI,CAAC,GAAG;AAAiB;AAEzB,YAAI,GAAG,KAAK;AACV,gBAAM,GAAG,GAAG;AACZ,cAAI,IAAI,GAAG,IAAI;AACf,gBAAM,GAAG,KAAK,SAAS,GAAG,UAAU,GAAG,IAAI,YAAY;AAAA,QACzD;AACA,YAAI,GAAG;AAAa,gBAAM,GAAG,WAAW;AAExC,YAAI,SAAS,GAAG,KAAK,YAAY,GAAG,SAAS,GAAG,KAAK,YAAY;AACjE,YAAI,WAAW,GAAG,GAAG,KAAK,sBAAsB,EAAE;AAClD,YAAI,WAAW,WAAW,GAAG,KAAK,mBAAmB,EAAE,sBAAsB,EAAE,MAAM,GAAG,KAAK,cAAc,EAAE;AAC7G,YAAI,WAAW,WAAW,GAAG,KAAK,mBAAmB,EAAE,sBAAsB,EAAE,MAAM,GAAG,KAAK,cAAc,EAAE;AAC7G,iBAAS,IAAI,GAAG,IAAI,GAAG,OAAO,QAAQ,KAAK;AACzC,cAAI,KAAK,GAAG,OAAO,CAAC;AACpB,cAAI,GAAG,YAAY,OAAO,MAAM,GAAG,UAAU,OAAO,QAChD,GAAG,YAAY,OAAO,MAAM,GAAG,UAAU,OAAO;AAClD,mCAAuB,IAAI,IAAI,UAAU,UAAU,CAAC;AAAA,QACxD;AAAA,MACF;AAEA,eAAS,oBAAoB,UAAU,QAAQ;AAC7C,YAAI,YAAY,GAAG,YAAY;AAC/B,iBAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,cAAI,QAAQ,OAAO,CAAC;AACpB,cAAI,MAAM,SAAS,YAAY,MAAM,YAAY;AAAU,mBAAO;AAClE,cAAI,MAAM,WAAW;AAAU;AAC/B,sBAAY,MAAM;AAClB,sBAAY,MAAM;AAAA,QACpB;AACA,eAAO,aAAa,WAAW;AAAA,MACjC;AAKA,eAAS,aAAa,IAAI,QAAQ,QAAQ;AACxC,YAAI,UAAU,GAAG,MAAM;AACvB,YAAI,QAAQ,GAAG,UAAU,GAAG,SAAS;AACrC,YAAI,SAAS,CAAC;AACd,iBAAS,IAAI,KAAI,KAAK;AACpB,cAAI,QAAQ,OAAO,CAAC;AACpB,cAAI,aAAa,CAAC,QAAQ,MAAM,SAAS,MAAM,WAAW,MAAM;AAChE,iBAAO,SAAS,QAAQ,UAAU,QAAQ,UAAU,GAAG;AACrD,gBAAI,IAAI,QAAQ,UAAU,MAAM,IAAI;AACpC,gBAAI,KAAK;AAAO;AAChB,gBAAI,KAAK;AAAY,qBAAO,KAAK,CAAC;AAAA;AAC7B;AAAA,UACP;AACA,cAAI,CAAC;AAAO;AACZ,iBAAO,KAAK,QAAQ,SAAS,MAAM,SAAS,MAAM,MAAM;AAAA,QAC1D;AACA,eAAO;AAAA,MACT;AAKA,eAAS,eAAe,QAAQ,eAAe,QAAQ,UAAU;AAC/D,YAAI,KAAK,GAAG,QAAQ,GAAG,SAAS,GAAG,OAAO;AAC1C;AAAO,mBAAQ,MAAM;AACnB,gBAAI,QAAQ,OAAO,EAAE,GAAG,QAAQ,cAAc,KAAK;AACnD,gBAAI,CAAC,SAAS,SAAS;AAAM;AAE7B,gBAAI,QAAQ,QAAQ,MAAM,CAAC,IAAI,KAAK,QAAQ,SAAS,OAAO,MAAM;AAClE,mBAAO,SAAS,OAAO,QAAQ;AAC7B,kBAAI,QAAQ,OAAO,MAAM;AACzB,kBAAI,MAAM,YAAY,SAAS,MAAM,SAAS,OAAO;AACnD;AACA;AACA,yBAAS;AAAA,cACX;AACA,kBAAI,MAAM,SAAS,OAAO;AACxB,oBAAI,MAAM,YAAY;AAAO,2BAAS;AACtC;AAAA,cACF;AACA,sBAAS,MAAM,SAAS,MAAM,YAAa,MAAM,SAAS,MAAM;AAChE;AAAA,YACF;AACA,gBAAI,SAAS,QAAQ,MAAM;AACzB,oBAAM,QAAQ,IAAI;AAClB;AAAA,YACF,WAAW,QAAQ,QAAQ,MAAM;AAC/B,oBAAM,QAAQ,IAAI,QAAQ;AAAA,YAC5B,OAAO;AACL,kBAAI,SAAS,CAAC,QAAQ,MAAM,MAAM,IAAI;AACtC,qBAAO,QAAQ,IAAI;AACnB,qBAAO,OAAO,IAAI,GAAG,MAAM;AAC3B;AAAA,YACF;AAAA,UACF;AAAA,MACF;AAEA,eAAS,iBAAiB,IAAI,OAAO;AACnC,YAAI,YAAY,aAAa,GAAG,MAAM,GAAG,QAAQ,KAAK,GAAG,SAAS,CAAC;AACnE,YAAI;AAAO,mBAAS,IAAI,GAAGC,KAAI,GAAG,IAAI,MAAM,OAAO,QAAQ,KAAK;AAC9D,gBAAI,IAAI,MAAM,OAAO,CAAC,EAAE;AACxB,mBAAOA,KAAI,UAAU,UAAU,UAAUA,EAAC,IAAI;AAAG,cAAAA;AACjD,gBAAIA,MAAK,UAAU,UAAU,UAAUA,EAAC,KAAK;AAAG,wBAAU,OAAOA,MAAK,GAAG,CAAC;AAAA,UAC5E;AACA,iBAAS,IAAI,GAAG,IAAI,UAAU,QAAQ;AACpC,iBAAO,KAAK,CAAC,UAAU,CAAC,GAAG,MAAM,IAAI,CAAC;AAExC,uBAAe,QAAQ,aAAa,GAAG,MAAM,GAAG,QAAQ,IAAI,GAAG,GAAG,QAAQ,CAAC;AAC3E,YAAI;AACF,yBAAe,QAAQ,aAAa,MAAM,MAAM,MAAM,QAAQ,IAAI,GAAG,MAAM,QAAQ,CAAC;AAEtF,eAAO;AAAA,MACT;AAEA,eAAS,YAAY,IAAI,OAAO;AAC9B,YAAI,CAAC,GAAG,aAAa,CAAC;AAAO;AAC7B,YAAI,CAAC,GAAG,KAAK;AAAO,iBAAO,GAAG,KAAK,UAAU,WAAW;AACtD,wBAAY,IAAI,KAAK;AAAA,UACvB,CAAC;AAED,WAAG,YAAY;AACf,YAAI,QAAQ,GAAG,GAAG,QAAQ,KAAK,GAAG,GAAG,QAAQ,GAAG,GAAG;AACnD,YAAI,OAAO;AACT,qBAAW,KAAK;AAChB,gBAAM,YAAY;AAAA,QACpB;AACA,YAAI,eAAe,iBAAiB,IAAI,KAAK;AAG7C,YAAI,WAAW,GAAG,GAAG;AACrB,iBAAS,IAAI,GAAG,IAAI,SAAS,QAAQ;AACnC,mBAAS,CAAC,EAAE,MAAM;AACpB,iBAAS,SAAS;AAElB,YAAI,KAAK,CAAC,GAAG,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC,GAAG,SAAS,CAAC;AACpD,YAAI;AAAO,aAAG,KAAK,MAAM,IAAI;AAC7B,iBAAS,IAAI,GAAG,IAAI,GAAG,QAAQ,KAAK;AAClC,iBAAO,KAAK,GAAG,CAAC,EAAE,cAAc,EAAE,GAAG;AACrC,iBAAO,KAAK,CAAC,GAAG,CAAC,EAAE,mBAAmB,EAAE,sBAAsB,EAAE,GAAG;AAAA,QACrE;AAEA,YAAI,OAAO,CAAC,KAAK,OAAO,CAAC,KAAK,GAAG,UAAU,KAAK,OAAO,CAAC,KAAK,OAAO,CAAC;AACnE,qBAAW,IAAI,QAAQ,CAAC,GAAG,GAAG,CAAC,GAAG,QAAQ;AAC5C,iBAAS,KAAK,GAAG,KAAK,aAAa,QAAQ;AACzC,qBAAW,IAAI,QAAQ,aAAa,EAAE,GAAG,QAAQ;AAEnD,iBAAS,IAAI,GAAG,IAAI,GAAG,QAAQ;AAC7B,aAAG,CAAC,EAAE,SAAS,MAAM,OAAO,CAAC,CAAC;AAAA,MAClC;AAEA,eAAS,WAAW,IAAI,UAAU,OAAO,UAAU;AACjD,YAAI,YAAY,MAAM,SAAS,CAAC;AAChC,iBAAS,IAAI,GAAG,IAAI,GAAG,QAAQ;AAAK,cAAI,MAAM,CAAC,KAAK,MAAM;AACxD,gBAAI,MAAM,GAAG,CAAC,EAAE,aAAa,MAAM,CAAC,GAAG,OAAO,IAAI,SAAS,CAAC;AAC5D,mBAAO,CAAC,IAAI;AACZ,wBAAY,KAAK,IAAI,WAAW,GAAG;AAAA,UACrC;AACA,iBAAS,IAAI,GAAG,IAAI,GAAG,QAAQ;AAAK,cAAI,MAAM,CAAC,KAAK,MAAM;AACxD,gBAAI,OAAO,YAAY,OAAO,CAAC;AAC/B,gBAAI,OAAO;AACT,uBAAS,KAAK,SAAS,GAAG,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI,CAAC;AAAA,UACjD;AAAA,MACF;AAEA,eAAS,SAAS,IAAI,MAAM,MAAM;AAChC,YAAI,QAAQ;AACZ,YAAI,OAAO,GAAG,SAAS,GAAG;AACxB;AACA,kBAAQ;AAAA,QACV;AACA,YAAIC,OAAM,SAAS,cAAc,KAAK;AACtC,QAAAA,KAAI,YAAY;AAChB,QAAAA,KAAI,MAAM,SAAS,OAAO;AAAM,QAAAA,KAAI,MAAM,WAAW;AACrD,eAAO,GAAG,cAAc,MAAMA,MAAK,EAAC,QAAQ,MAAM,OAAc,aAAa,MAAM,mBAAmB,KAAI,CAAC;AAAA,MAC7G;AAEA,eAAS,uBAAuB,IAAI,OAAO,UAAU,UAAU,GAAG;AAChE,YAAI,OAAO,GAAG,QAAQ;AACtB,YAAI,MAAM,GAAG,KAAK,aAAa,MAAM,UAAU,SAAS,IAAI,IAAI;AAChE,YAAI,GAAG,KAAK;AACV,cAAI,SAAS;AACb,cAAI,SAAS,GAAG,KAAK,aAAa,MAAM,UAAU,SAAS,IAAI,IAAI;AACnE,cAAI,MAAM;AAAE,gBAAI,MAAM;AAAQ,qBAAS;AAAQ,qBAAS;AAAA,UAAK;AAC7D,cAAI,SAAS,GAAG,KAAK,aAAa,MAAM,QAAQ,SAAS,IAAI,IAAI;AACjE,cAAI,SAAS,GAAG,KAAK,aAAa,MAAM,QAAQ,SAAS,IAAI,IAAI;AACjE,cAAI,MAAM;AAAE,gBAAI,MAAM;AAAQ,qBAAS;AAAQ,qBAAS;AAAA,UAAK;AAC7D,cAAI,WAAW,QAAQ,IAAE,IAAI,MAAM,SAAS,MAAM,IAAE,IAAI,MAAM,SAAS,OAAO,IAAI,KAAK,MAAM;AAC7F,cAAI,WAAW,QAAQ,IAAE,IAAI,MAAM,SAAS,MAAM,IAAE,IAAI,MAAM,SAAS,SAAS;AAChF;AAAA,YAAM,GAAG,IAAI,YAAY,SAAS,gBAAgB,OAAO,MAAM,CAAC;AAAA,YAC1D;AAAA,YAAK,UAAU,SAAS,WAAW,SAAS,IAAI,KAAK,MAAM,SAAS,WAAW;AAAA,YAC/E;AAAA,YAAS,GAAG,QAAQ;AAAA,UAAO;AAAA,QACnC;AACA,YAAI,GAAG,aAAa;AAClB,cAAI,OAAO,GAAG,YAAY,YAAY;AAAA,YAAI;AAAA,YAAO,GAAG,QAAQ,SAAS,MAAW;AAAA,YACtC;AAAA,UAAuB,CAAC;AAClE,cAAI,gBAAgB,GAAG,GAAG,QAAQ;AAClC,eAAK,QAAQ,GAAG,KAAK,OAAO,gBAAgB,iBAAiB,cAAc;AAC3E,eAAK,QAAQ;AACb,eAAK,MAAM,OAAO,MAAM,SAAS,MAAM,WAAW,MAAM,GAAG,KAAK,aAAa,MAAM,UAAU,OAAO,IAAI,YAAY;AACpH,eAAK,aAAa,QAAQ,QAAQ;AAClC,eAAK,aAAa,YAAY,GAAG;AACjC,eAAK,aAAa,cAAc,KAAK,KAAK;AAE1C,cAAI,eAAe;AACjB,gBAAI,aAAa,GAAG,KAAK,aAAa,MAAM,UAAU,OAAO,IAAI;AACjE,gBAAI,cAAc,GAAG,YAAY,YAAY;AAAA,cAAI;AAAA,cAAO,GAAG,QAAQ,UAAU,MAAW;AAAA,cACvC;AAAA,YAA+B,CAAC;AACjF,wBAAY,QAAQ;AACpB,wBAAY,QAAQ;AAAA,cAAC,UAAU,MAAM;AAAA,cAAU,QAAQ,MAAM;AAAA,cACxC,UAAU,MAAM;AAAA,cAAU,QAAQ,MAAM;AAAA,YAAM;AACnE,wBAAY,MAAM,MAAM,aAAa;AACrC,eAAG,QAAQ,UAAU,YAAY,MAAM,OAAO,QAAQ,YAAY,MAAM,QAAQ;AAChF,wBAAY,aAAa,QAAQ,QAAQ;AACzC,wBAAY,aAAa,YAAY,GAAG;AACxC,wBAAY,aAAa,cAAc,YAAY,KAAK;AAAA,UAC1D;AAAA,QACF;AAAA,MACF;AAEA,eAAS,UAAU,IAAI,IAAI,MAAM,OAAO;AACtC,YAAI,GAAG;AAAe;AACtB,YAAI,YAAY,MAAM,SAAS,KAAK,SAAS,IAAI,IAAI,MAAM,WAAW,CAAC,IAAI,IAAI,MAAM,UAAU,CAAC;AAChG,YAAI,UAAU,IAAI,MAAM,QAAQ,CAAC;AACjC,YAAI,YAAY,MAAM,SAAS,GAAG,SAAS,IAAI,IAAI,MAAM,WAAW,CAAC,IAAI,IAAI,MAAM,UAAU,CAAC;AAC9F,YAAI,UAAU,IAAI,MAAM,QAAQ,CAAC;AACjC,YAAI,UAAU,GAAG,GAAG,QAAQ;AAC5B,YAAI;AACF,kBAAQ,GAAG,IAAI,MAAM,WAAW,SAAS,IAAI,WAAW,OAAO;AAAA;AAE/D,aAAG,aAAa,KAAK,SAAS,WAAW,OAAO,GAAG,WAAW,OAAO;AAAA,MACzE;AAIA,UAAI,YAAYN,YAAW,YAAY,SAAS,MAAM,SAAS;AAC7D,YAAI,EAAE,gBAAgB;AAAY,iBAAO,IAAI,UAAU,MAAM,OAAO;AAEpE,aAAK,UAAU;AACf,YAAI,WAAW,QAAQ,UAAU,YAAY,QAAQ,aAAa,OAAO,QAAQ,OAAO,QAAQ;AAEhG,YAAI,UAAU,YAAY,MAAM,WAAW,aAAa;AACxD,YAAI,QAAQ,KAAK,UAAU,IAAI,MAAM,WAAW,IAAI;AACpD,YAAI,OAAO,CAAC,GAAG,OAAO,KAAK,OAAO,MAAM,QAAQ,KAAK,QAAQ;AAC7D,YAAI,OAAO;AAEX,YAAI,SAAS;AACX,iBAAO,KAAK,OAAO,IAAI,SAAS,MAAM,MAAM;AAC5C,cAAI,WAAW,IAAI,OAAO,MAAM,6CAA6C;AAC7E,eAAK,KAAK,QAAQ;AAClB,eAAK,KAAK,SAAS,IAAI,CAAC;AAAA,QAC1B;AAEA,YAAI,WAAW,IAAI,OAAO,MAAM,+CAA+C;AAC/E,aAAK,KAAK,QAAQ;AAElB,YAAI,UAAU;AACZ,kBAAQ,KAAK,QAAQ,IAAI,SAAS,MAAM,OAAO;AAC/C,eAAK,KAAK,SAAS,KAAK,CAAC;AACzB,cAAI,YAAY,IAAI,OAAO,MAAM,8CAA8C;AAC/E,eAAK,KAAK,SAAS;AAAA,QACrB;AAEA,SAAC,WAAW,YAAY,UAAU,aAAa;AAE/C,aAAK,KAAK,IAAI,OAAO,MAAM,MAAM,yBAAyB,CAAC;AAE3D,YAAI,UAAU,KAAK,OAAO,KAAK,YAAY,IAAI,OAAO,MAAM,uCAAuC,QAAQ,MAAM,CAAC;AAClH,aAAK,OAAOA,YAAW,UAAU,QAAQ,OAAO,CAAC;AAEjD,YAAI;AAAM,eAAK,KAAK,UAAU,UAAU,OAAO;AAC/C,YAAI;AAAO,gBAAM,KAAK,WAAW,WAAW,OAAO;AACnD,YAAI,QAAQ;AACV,eAAK,OAAO,EAAE,UAAU,WAAW;AACjC,uCAA2B,MAAM,QAAQ,iBAAiB;AAAA,UAC5D,CAAC;AACH,YAAI,QAAQ,WAAW,SAAS;AAC9B,eAAK,WAAW,CAAC;AACjB,sBAAY,KAAK,QAAQ,KAAK,OAAO,IAAI;AAAA,QAC3C;AACA,YAAI;AAAM,eAAK,eAAe,KAAK;AACnC,YAAI;AAAO,gBAAM,eAAe,IAAI;AAGpC,YAAI,WAAW,WAAW;AACxB,cAAI;AAAM,4BAAgB,IAAI;AAC9B,cAAI;AAAO,4BAAgB,KAAK;AAAA,QAClC;AACA,QAAAA,YAAW,GAAG,QAAQ,UAAU,QAAQ;AACxC,YAAI,iBAAiB,YAAY,WAAW;AAC1C,mBAAS,IAAI,QAAQ,YAAY,KAAK,KAAK,SAAS,MAAM,IAAI,EAAE,YAAY;AAAA,UAAC;AAC7E,cAAI,CAAC,GAAG;AAAE,0BAAc,cAAc;AAAG,YAAAA,YAAW,IAAI,QAAQ,UAAU,QAAQ;AAAA,UAAG;AAAA,QACvF,GAAG,GAAI;AAAA,MACT;AAEA,eAAS,SAAS,IAAI;AACpB,YAAI,OAAO,GAAG,aAAa,IAAI,OAAO,MAAM,6BAA6B;AACzE,aAAK,aAAa,QAAQ,QAAQ;AAClC,aAAK,aAAa,YAAY,GAAG;AACjC,YAAI,WAAW,IAAI,OAAO,CAAC,IAAI,GAAG,kCAAkC;AACpE,QAAAA,YAAW,GAAG,MAAM,SAAS,WAAW;AAAE,wBAAc,IAAI,CAAC,GAAG,UAAU;AAAA,QAAG,CAAC;AAC9E,QAAAA,YAAW,GAAG,MAAM,SAAS,SAAS,GAAG;AAAE,WAAC,EAAE,QAAQ,WAAW,EAAE,SAAS,YAAY,cAAc,IAAI,CAAC,GAAG,UAAU;AAAA,QAAG,CAAC;AAC5H,YAAI,UAAU,CAAC,QAAQ;AACvB,YAAI,GAAG,GAAG,QAAQ,kBAAkB,OAAO;AACzC,aAAG,cAAc,IAAI,OAAO,MAAM,kCAAkC,GAAG,IAAI;AAC3E,cAAI,cAAc,SAAS,GAAG;AAC5B,gBAAI,OAAO,EAAE,UAAU,EAAE;AACzB,gBAAI,CAAC,KAAK;AAAO;AACjB,gBAAI,KAAK,aAAa,iCAAiC;AACrD,wBAAU,IAAI,GAAG,MAAM,GAAG,MAAM,KAAK,KAAK;AAC1C;AAAA,YACF;AACA,sBAAU,IAAI,GAAG,MAAM,GAAG,MAAM,KAAK,KAAK;AAAA,UAC5C;AACA,UAAAA,YAAW,GAAG,GAAG,aAAa,SAAS,WAAW;AAClD,UAAAA,YAAW,GAAG,GAAG,aAAa,SAAS,SAAS,GAAG;AAAE,aAAC,EAAE,QAAQ,WAAW,EAAE,SAAS,YAAY,YAAY,CAAC;AAAA,UAAG,CAAC;AACnH,kBAAQ,QAAQ,GAAG,WAAW;AAAA,QAChC;AACA,YAAI,GAAG,GAAG,QAAQ,WAAW,SAAS;AACpC,cAAI,MAAM,SAAS,mBAAmB,SAAS,gBAAgB,OAAO,KAAK;AAC3E,cAAI,OAAO,CAAC,IAAI;AAAe,kBAAM;AACrC,aAAG,MAAM;AACT,cAAI;AAAK,oBAAQ,KAAK,GAAG;AAAA,QAC3B;AAEA,eAAO,GAAG,MAAM,IAAI,OAAO,SAAS,sBAAsB;AAAA,MAC5D;AAEA,gBAAU,YAAY;AAAA,QACpB,aAAa;AAAA,QACb,QAAQ,WAAW;AAAE,iBAAO,KAAK;AAAA,QAAM;AAAA,QACvC,eAAe,WAAW;AAAE,iBAAO,KAAK,SAAS,KAAK,MAAM;AAAA,QAAM;AAAA,QAClE,cAAc,WAAW;AAAE,iBAAO,KAAK,QAAQ,KAAK,KAAK;AAAA,QAAM;AAAA,QAC/D,oBAAoB,SAAS,KAAK;AAChC,cAAI,KAAK;AAAO,iBAAK,MAAM,mBAAmB,GAAG;AACjD,cAAI,KAAK;AAAM,iBAAK,KAAK,mBAAmB,GAAG;AAAA,QACjD;AAAA,QACA,aAAa,WAAW;AACtB,cAAI,KAAK,OAAO;AAAE,uBAAW,KAAK,KAAK;AAAG,mBAAO,KAAK,MAAM;AAAA,UAAQ;AAAA,QACtE;AAAA,QACA,YAAY,WAAW;AACrB,cAAI,KAAK,MAAM;AAAE,uBAAW,KAAK,IAAI;AAAG,mBAAO,KAAK,KAAK;AAAA,UAAQ;AAAA,QACnE;AAAA,MACF;AAEA,eAAS,SAAS,KAAK;AACrB,YAAI,OAAO,OAAO;AAAU,iBAAO;AAAA;AAC9B,iBAAO,IAAI,SAAS;AAAA,MAC3B;AAGA,UAAI;AACJ,eAAS,QAAQ,GAAGI,IAAG,kBAAkB;AACvC,YAAI,CAAC;AAAK,gBAAM,IAAI,iBAAiB;AAErC,YAAI,OAAO,IAAI,UAAU,GAAGA,EAAC;AAE7B,iBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,EAAE,GAAG;AACpC,cAAI,OAAO,KAAK,CAAC;AACjB,cAAI,mBAAmB,CAAC,SAAS,KAAK,KAAK,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG;AACzD,iBAAK,OAAO,KAAK,CAAC;AAAA,UACpB,WAAW,KAAK,KAAK,IAAI,CAAC,EAAE,CAAC,KAAK,KAAK,CAAC,GAAG;AACzC,iBAAK,OAAO,KAAK,CAAC;AAClB,iBAAK,CAAC,EAAE,CAAC,KAAK,KAAK,CAAC;AAAA,UACtB;AAAA,QACF;AACA,eAAO;AAAA,MACT;AAEA,eAAS,UAAU,MAAM;AACvB,YAAI,SAAS,CAAC;AACd,YAAI,CAAC,KAAK;AAAQ,iBAAO;AACzB,YAAI,YAAY,GAAG,YAAY;AAC/B,YAAI,OAAO,IAAI,GAAG,CAAC,GAAG,OAAO,IAAI,GAAG,CAAC;AACrC,iBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,EAAE,GAAG;AACpC,cAAI,OAAO,KAAK,CAAC,GAAG,KAAK,KAAK,CAAC;AAC/B,cAAI,MAAM,YAAY;AACpB,gBAAI,WAAW,CAAC,iBAAiB,MAAM,CAAC,KAAK,KAAK,OAAO,aAAa,KAAK,OAAO,YAAY,IAAI;AAClG,gBAAI,gBAAgB,KAAK,OAAO,UAAU,gBAAgB,KAAK,OAAO;AACtE,qBAAS,MAAM,KAAK,CAAC,GAAG,MAAM,IAAI;AAClC,gBAAI,SAAS,eAAe,MAAM,CAAC,IAAI,IAAI;AAC3C,gBAAI,cAAc,KAAK,OAAO,QAAQ,cAAc,KAAK,OAAO;AAChE,gBAAI,cAAc,eAAe;AAC/B,kBAAI;AAAG,uBAAO,KAAK;AAAA,kBAAC,UAAU;AAAA,kBAAW,QAAQ;AAAA,kBAC7B,UAAU;AAAA,kBAAW,QAAQ;AAAA,gBAAa,CAAC;AAC/D,0BAAY;AAAa,0BAAY;AAAA,YACvC;AAAA,UACF,OAAO;AACL,qBAAS,MAAM,cAAc,OAAO,MAAM,KAAK,CAAC,CAAC;AAAA,UACnD;AAAA,QACF;AACA,YAAI,aAAa,KAAK,QAAQ,aAAa,KAAK;AAC9C,iBAAO,KAAK;AAAA,YAAC,UAAU;AAAA,YAAW,QAAQ,KAAK,OAAO;AAAA,YACzC,UAAU;AAAA,YAAW,QAAQ,KAAK,OAAO;AAAA,UAAC,CAAC;AAC1D,eAAO;AAAA,MACT;AAEA,eAAS,eAAe,MAAM,GAAG;AAC/B,YAAI,KAAK,KAAK,SAAS;AAAG,iBAAO;AACjC,YAAI,OAAO,KAAK,IAAI,CAAC,EAAE,CAAC;AACxB,YAAK,KAAK,UAAU,KAAK,IAAI,KAAK,SAAS,KAAM,KAAK,WAAW,CAAC,KAAK;AAAI,iBAAO;AAClF,YAAI,KAAK,KAAK,SAAS;AAAG,iBAAO;AACjC,eAAO,KAAK,IAAI,CAAC,EAAE,CAAC;AACpB,gBAAQ,KAAK,SAAS,KAAK,KAAK,KAAK,SAAS,MAAM,KAAK,WAAW,CAAC,KAAK;AAAA,MAC5E;AAEA,eAAS,iBAAiB,MAAM,GAAG;AACjC,YAAI,KAAK;AAAG,iBAAO;AACnB,YAAI,OAAO,KAAK,IAAI,CAAC,EAAE,CAAC;AACxB,YAAI,KAAK,WAAW,KAAK,SAAS,CAAC,KAAK;AAAI,iBAAO;AACnD,YAAI,KAAK;AAAG,iBAAO;AACnB,eAAO,KAAK,IAAI,CAAC,EAAE,CAAC;AACpB,eAAO,KAAK,WAAW,KAAK,SAAS,CAAC,KAAK;AAAA,MAC7C;AAEA,eAAS,sBAAsB,QAAQ,GAAG,SAAS;AACjD,YAAI,SAAS,QAAQ,SAAS;AAC9B,iBAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,cAAI,QAAQ,OAAO,CAAC;AACpB,cAAI,YAAY,UAAU,MAAM,WAAW,MAAM;AACjD,cAAI,UAAU,UAAU,MAAM,SAAS,MAAM;AAC7C,cAAI,UAAU,MAAM;AAClB,gBAAI,YAAY,GAAG;AAAE,uBAAS,MAAM;AAAU,uBAAS,MAAM;AAAA,YAAU,WAC9D,UAAU,GAAG;AAAE,uBAAS,MAAM;AAAQ,uBAAS,MAAM;AAAA,YAAQ;AAAA,UACxE;AACA,cAAI,WAAW,GAAG;AAAE,sBAAU,MAAM;AAAQ,sBAAU,MAAM;AAAA,UAAQ,WAC3D,aAAa,GAAG;AAAE,sBAAU,MAAM;AAAU,sBAAU,MAAM;AAAA,UAAU;AAAA,QACjF;AACA,eAAO,EAAC,MAAM,EAAC,QAAQ,SAAS,OAAO,OAAM,GAAG,MAAM,EAAC,QAAQ,SAAS,OAAO,OAAM,EAAC;AAAA,MACxF;AAEA,eAAS,eAAe,IAAI,MAAM,IAAI;AACpC,WAAG,aAAa,MAAM,QAAQ,iCAAiC;AAC/D,YAAI,SAAS,SAAS,cAAc,MAAM;AAC1C,eAAO,YAAY;AACnB,eAAO,QAAQ,GAAG,OAAO,4CAA4C;AACrE,YAAI,OAAO,GAAG,SAAS,IAAI,MAAM,CAAC,GAAG,IAAI,KAAK,CAAC,GAAG;AAAA,UAChD,eAAe;AAAA,UACf,gBAAgB;AAAA,UAChB,cAAc;AAAA,UACd,cAAc;AAAA,QAChB,CAAC;AACD,iBAASG,SAAQ;AACf,eAAK,MAAM;AACX,aAAG,gBAAgB,MAAM,QAAQ,iCAAiC;AAAA,QACpE;AACA,YAAI,KAAK;AAAmB,UAAAA,OAAM;AAClC,QAAAP,YAAW,GAAG,QAAQ,SAASO,MAAK;AACpC,aAAK,GAAG,SAASA,MAAK;AACtB,QAAAP,YAAW,GAAG,QAAQ,SAASO,MAAK;AACpC,eAAO,EAAC,MAAY,OAAOA,OAAK;AAAA,MAClC;AAEA,eAAS,gBAAgB,MAAM,SAAS;AACtC,YAAI,QAAQ,CAAC;AACb,iBAASA,SAAQ;AACf,mBAASJ,KAAI,GAAGA,KAAI,MAAM,QAAQA;AAAK,kBAAMA,EAAC,EAAE,MAAM;AAAA,QACxD;AACA,iBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,cAAI,SAAS,QAAQ,CAAC;AACtB,cAAI,OAAO,eAAe,OAAO,IAAI,OAAO,MAAM,OAAO,OAAO,IAAI;AACpE,gBAAM,KAAK,IAAI;AACf,eAAK,KAAK,GAAG,SAASI,MAAK;AAAA,QAC7B;AACA,eAAO,MAAM,CAAC,EAAE;AAAA,MAClB;AAEA,eAAS,kBAAkB,IAAI,QAAQ,KAAKA,QAAO;AACjD,iBAAS,IAAI,GAAG,IAAI,GAAG,OAAO,QAAQ,KAAK;AACzC,cAAI,QAAQ,GAAG,OAAO,CAAC;AACvB,mBAAS,IAAI,MAAM,WAAW,QAAQ,IAAI,MAAM,SAAS,QAAQ,KAAK;AACpE,gBAAI,MAAM,IAAI;AACd,gBAAI,OAAO,KAAK,MAAMA,OAAM;AAAQ,cAAAA,OAAM,GAAG,IAAI;AAAA,UACnD;AAAA,QACF;AAAA,MACF;AAEA,eAAS,2BAA2B,IAAI,QAAQ;AAC9C,YAAI,OAAO,UAAU;AAAU,mBAAS;AACxC,YAAIA,SAAQ,CAAC,GAAG,OAAO,GAAG,OAAO,GAAG,MAAM,KAAK,UAAU;AACzD,iBAAS,IAAI,KAAK,IAAI,KAAK,SAAS,GAAG,KAAK,GAAG;AAAK,UAAAA,OAAM,KAAK,IAAI;AACnE,YAAI,GAAG;AAAM,4BAAkB,GAAG,MAAM,QAAQ,KAAKA,MAAK;AAC1D,YAAI,GAAG;AAAO,4BAAkB,GAAG,OAAO,QAAQ,KAAKA,MAAK;AAE5D,iBAAS,IAAI,GAAG,IAAIA,OAAM,QAAQ,KAAK;AACrC,cAAIA,OAAM,CAAC,GAAG;AACZ,gBAAI,OAAO,IAAI;AACf,qBAAS,OAAO,GAAG,IAAIA,OAAM,SAAS,KAAKA,OAAM,IAAI,CAAC,GAAG,KAAK,QAAQ;AAAA,YAAC;AACvE,gBAAI,OAAO,QAAQ;AACjB,kBAAI,UAAU,CAAC,EAAC,MAAY,IAAI,KAAI,CAAC;AACrC,kBAAI,GAAG;AAAM,wBAAQ,KAAK,EAAC,MAAM,oBAAoB,MAAM,GAAG,KAAK,MAAM,GAAG,IAAI,GAAG,KAAK,KAAI,CAAC;AAC7F,kBAAI,GAAG;AAAO,wBAAQ,KAAK,EAAC,MAAM,oBAAoB,MAAM,GAAG,MAAM,MAAM,GAAG,IAAI,GAAG,MAAM,KAAI,CAAC;AAChG,kBAAI,OAAO,gBAAgB,MAAM,OAAO;AACxC,kBAAI,GAAG,QAAQ;AAAY,mBAAG,QAAQ,WAAW,IAAI,MAAM,MAAM,IAAI;AAAA,YACvE;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAIA,eAAS,IAAI,KAAK,SAAS,WAAW,OAAO;AAC3C,YAAI,IAAI,SAAS,cAAc,GAAG;AAClC,YAAI;AAAW,YAAE,YAAY;AAC7B,YAAI;AAAO,YAAE,MAAM,UAAU;AAC7B,YAAI,OAAO,WAAW;AAAU,YAAE,YAAY,SAAS,eAAe,OAAO,CAAC;AAAA,iBACrE;AAAS,mBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,EAAE;AAAG,cAAE,YAAY,QAAQ,CAAC,CAAC;AACnF,eAAO;AAAA,MACT;AAEA,eAAS,MAAM,MAAM;AACnB,iBAAS,QAAQ,KAAK,WAAW,QAAQ,QAAQ,GAAG,EAAE;AACpD,eAAK,YAAY,KAAK,UAAU;AAAA,MACpC;AAEA,eAAS,MAAMD,MAAK;AAClB,iBAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,UAAAA,KAAI,aAAa,UAAU,CAAC,GAAG,UAAU,IAAE,CAAC,CAAC;AAAA,MACjD;AAEA,eAAS,QAAQ,KAAK,QAAQ;AAC5B,YAAI,CAAC;AAAQ,mBAAS,CAAC;AACvB,iBAAS,QAAQ;AAAK,cAAI,IAAI,eAAe,IAAI;AAAG,mBAAO,IAAI,IAAI,IAAI,IAAI;AAC3E,eAAO;AAAA,MACT;AAEA,eAAS,SAAS,KAAK,KAAK,MAAM,OAAO;AACvC,YAAI,MAAM,OAAO,IAAI,IAAI,MAAM,IAAI,EAAE,IAAI,KAAK,KAAK;AACnD,mBAAS;AACP,cAAI,KAAK,IAAI,QAAQ,MAAM,EAAE;AAC7B,cAAI,MAAM;AAAI;AACd,YAAE,IAAI;AACN,cAAI;AAAO,cAAE,MAAM;AACnB,eAAK,KAAK;AAAA,QACZ;AACA,YAAI,MAAM,KAAK,IAAI,IAAI,OAAO,IAAI,SAAS;AAC3C,YAAI;AAAO,gBAAM,MAAM,KAAK,IAAI,MAAM,OAAO,IAAI,SAAS;AAC1D,eAAO;AAAA,MACT;AAKA,UAAI,WAAW,GAAG,iBAAiB,GAAG,WAAW;AAEjD,eAAS,eAAe,IAAI;AAC1B,aAAK,KAAK;AACV,aAAK,YAAY,CAAC;AAClB,aAAK,SAAS,GAAG,IAAI;AACrB,YAAI,OAAO;AACX,WAAG,GAAG,eAAe,SAAS,GAAG,QAAQ;AACvC,cAAI,CAAC,OAAO;AAAW;AACvB,cAAI,QAAQ,OAAO,KAAK,CAAC;AACzB,cAAI,SAAS;AAAM,iBAAK,IAAI,MAAM,MAAM,QAAQ;AAAA,QAClD,CAAC;AACD,WAAG,GAAG,iBAAiB,SAAS,GAAG,QAAQ,MAAM,KAAK;AACpD,cAAI,OAAO,QAAQ,OAAO;AACxB,iBAAK,MAAM,KAAK,UAAU,KAAK,SAAS;AAAA,QAC5C,CAAC;AACD,WAAG,GAAG,iBAAiB,KAAK,OAAO,KAAK,IAAI,CAAC;AAC7C,WAAG,GAAG,mBAAmB,SAAS,GAAG,QAAQ,QAAQ;AACnD,cAAI,OAAO;AAAa;AACxB,cAAI,OAAO;AAAO,iBAAK,IAAI,SAAS,GAAG,cAAc;AAAA;AAChD,iBAAK,IAAI,QAAQ,QAAQ;AAAA,QAChC,CAAC;AACD,WAAG,GAAG,qBAAqB,SAAS,GAAG,QAAQ,QAAQ;AACrD,cAAI,OAAO;AAAa;AACxB,cAAI,OAAO;AAAO,iBAAK,MAAM,SAAS,GAAG,gBAAgB,KAAK,cAAc;AAAA;AACvE,iBAAK,MAAM,QAAQ,UAAU,KAAK,SAAS;AAAA,QAClD,CAAC;AACD,WAAG,GAAG,qBAAqB,KAAK,OAAO,KAAK,IAAI,CAAC;AACjD,WAAG,GAAG,UAAU,SAAS,GAAG,QAAQ;AAClC,cAAI,QAAQ,OAAO,KAAK,MAAM,UAAU,OAAO,GAAG,OAAO,OAAO,KAAK;AACrE,cAAI,SAAS,OAAO,KAAK,SAAS,GAAG,MAAM,QAAQ;AACnD,cAAI,WAAW;AAAQ,iBAAK,IAAI,OAAO,SAAS,MAAM;AACtD,eAAK,MAAM,KAAK,UAAU,KAAK,SAAS;AACxC,cAAI,WAAW;AAAQ,iBAAK,MAAM,OAAO,KAAK,MAAM,UAAU,KAAK,SAAS;AAAA,QAC9E,CAAC;AACD,WAAG,GAAG,kBAAkB,WAAW;AACjC,cAAI,KAAK,GAAG,IAAI,UAAU,KAAK;AAAQ,iBAAK,OAAO;AAAA,QACrD,CAAC;AAAA,MACH;AAEA,qBAAe,YAAY;AAAA,QACzB,QAAQ,WAAW;AACjB,UAAAN,YAAW,OAAO,MAAM,SAAS;AACjC,eAAK,SAAS,KAAK,GAAG,IAAI;AAAA,QAC5B;AAAA,QAEA,KAAK,SAAS,GAAG,OAAO;AACtB,cAAI,MAAM;AACV,iBAAO,MAAM,KAAK,UAAU,QAAQ,OAAO,GAAG;AAC5C,gBAAI,OAAO,KAAK,UAAU,GAAG,IAAI;AACjC,gBAAI,QAAQ,GAAG;AACb,mBAAK,KAAK,UAAU,MAAM,CAAC,IAAI,UAAU;AAAO;AAChD,mBAAK,UAAU,MAAM,CAAC,KAAK;AAC3B,mBAAK,OAAO;AACZ;AAAA,YACF;AACA,gBAAI,OAAO;AAAG;AAAA,UAChB;AACA,eAAK,OAAO;AACZ,eAAK,UAAU,OAAO,KAAK,GAAG,GAAG,KAAK;AAAA,QACxC;AAAA,QAEA,MAAM,SAAS,GAAG;AAChB,mBAAS,IAAI,GAAG,IAAI,KAAK,UAAU,QAAQ,KAAK;AAC9C,gBAAI,KAAK,UAAU,CAAC,KAAK;AAAG,qBAAO;AACrC,iBAAO;AAAA,QACT;AAAA,QAEA,OAAO,SAAS,GAAG,MAAM,MAAM;AAC7B,cAAI,QAAQ,KAAK,KAAK,CAAC;AACvB,cAAI,SAAS,MAAM,EAAE,KAAK,UAAU,QAAQ,CAAC,IAAI;AAAO;AACxD,cAAI,CAAC,KAAK,KAAK,MAAM,CAAC,GAAG;AACvB,iBAAK,OAAO;AACZ,gBAAI,QAAQ,KAAK,UAAU,QAAQ,CAAC,IAAI,CAAC;AACzC,gBAAI;AAAO,mBAAK,UAAU,QAAQ,CAAC,IAAI;AAAA;AAClC,mBAAK,UAAU,OAAO,OAAO,CAAC;AAAA,UACrC;AAAA,QACF;AAAA,QAEA,WAAW,SAAS,GAAG;AACrB,cAAI,SAAS,KAAK,GAAG,cAAc,CAAC;AACpC,cAAI,OAAO;AAAa,qBAAS,IAAI,GAAG,IAAI,OAAO,YAAY,QAAQ;AACrE,kBAAI,OAAO,YAAY,CAAC,EAAE,OAAO,aAAa,OAAO,YAAY,CAAC,EAAE,MAAM;AACxE,uBAAO;AAAA;AACX,iBAAO;AAAA,QACT;AAAA,QAEA,WAAW,SAAS,GAAG;AACrB,cAAI,SAAS,KAAK,GAAG,cAAc,CAAC;AACpC,cAAI,OAAO;AAAS,qBAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,QAAQ;AAC7D,kBAAI,CAAC,OAAO,QAAQ,CAAC,EAAE,SAAS,CAAC,OAAO,QAAQ,CAAC,EAAE;AAAa,uBAAO;AAAA;AACzE,iBAAO;AAAA,QACT;AAAA,QAEA,gBAAgB,SAAS,GAAG;AAC1B,cAAI,KAAK,KAAK,GAAG,SAAS;AAAG,mBAAO;AACpC,cAAI,SAAS,KAAK,GAAG,cAAc,IAAI,CAAC;AACxC,cAAI,OAAO;AAAS,qBAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,QAAQ;AAC7D,kBAAI,OAAO,QAAQ,CAAC,EAAE,SAAS,CAAC,OAAO,QAAQ,CAAC,EAAE;AAAa,uBAAO;AAAA;AACxE,iBAAO;AAAA,QACT;AAAA,QAEA,KAAK,SAAS,MAAM,SAAS,QAAQ;AACnC,cAAI,OAAO,SAAS,SAAS,KAAK,OAAO,SAAS,aAAa,IAAI,WAAW;AAC9E,mBAAS,IAAI,GAAG,IAAI,KAAK,UAAU,QAAQ,KAAK,GAAG;AACjD,gBAAI,IAAI,KAAK,UAAU,CAAC;AACxB,gBAAI,KAAK,QAAS,KAAK,UAAU,IAAI,CAAC,IAAI;AAAiB,2BAAa;AACxE,gBAAI,KAAK,MAAO,KAAK,UAAU,IAAI,CAAC,IAAI;AAAiB,yBAAW;AACpE,gBAAI,KAAK;AAAM;AAAA,qBACN,IAAI;AAAI,mBAAK,UAAU,OAAO,KAAK,CAAC;AAAA;AACxC,mBAAK,UAAU,CAAC,KAAK;AAAA,UAC5B;AACA,cAAI,aAAa,IAAI;AACnB,gBAAI,QAAQ,KAAK,UAAU,aAAa,CAAC;AACzC,gBAAI,SAAS;AAAgB,mBAAK,UAAU,OAAO,YAAY,CAAC;AAAA;AAC3D,mBAAK,UAAU,aAAa,CAAC,IAAI,QAAQ,CAAC;AAAA,UACjD;AACA,cAAI,WAAW,MAAM;AACnB,iBAAK,IAAI,OAAO,QAAQ,cAAc;AAAA,QAC1C;AAAA,MACF;AAEA,eAAS,OAAO,GAAGI,IAAG;AAAE,gBAAQ,EAAE,OAAOA,GAAE,QAAQ,EAAE,KAAKA,GAAE,MAAM,IAAI,IAAIA;AAAA,MAAG;AAC7E,eAAS,OAAO,GAAGA,IAAG;AAAE,gBAAQ,EAAE,OAAOA,GAAE,QAAQ,EAAE,KAAKA,GAAE,MAAM,IAAI,IAAIA;AAAA,MAAG;AAC7E,eAAS,MAAM,GAAGA,IAAG;AAAE,eAAO,EAAE,QAAQA,GAAE,QAAQ,EAAE,MAAMA,GAAE;AAAA,MAAI;AAEhE,eAAS,aAAa,QAAQ,OAAO,QAAQ;AAC3C,iBAAS,IAAI,OAAO,SAAS,GAAG,KAAK,GAAG,KAAK;AAC3C,cAAI,QAAQ,OAAO,CAAC;AACpB,cAAI,MAAM,SAAS,MAAM,SAAS,MAAM,UAAU;AAClD,cAAI,KAAK;AAAO,mBAAO;AAAA,QACzB;AAAA,MACF;AAEA,eAAS,aAAa,QAAQ,OAAO,QAAQ;AAC3C,iBAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,cAAI,QAAQ,OAAO,CAAC;AACpB,cAAI,OAAQ,SAAS,MAAM,WAAW,MAAM;AAC5C,cAAI,OAAO;AAAO,mBAAO;AAAA,QAC3B;AAAA,MACF;AAEA,eAAS,aAAa,IAAI,KAAK;AAC7B,YAAI,QAAQ,MAAM,QAAQ,GAAG,MAAM,WAAW,OAAO,GAAG,UAAU,EAAE;AACpE,YAAI;AAAO,mBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AAChD,gBAAI,KAAK,MAAM,CAAC,GAAG,SAAS,MAAM,GAAG;AACrC,uBAAW,EAAE;AACb,gBAAI,MAAM,MAAM,IAAI,aAAa,GAAG,QAAQ,MAAM,MAAM,IAAI,aAAa,GAAG,QAAQ,MAAM,MAAM;AAChG,gBAAI,OAAO,SAAS,SAAS,SAAS,MAAM,IAAI,MAAM,QAAQ,MAAM;AAClE,sBAAQ;AAAA,UACZ;AACA,YAAI,SAAS;AACX,aAAG,UAAU,OAAO,CAAC;AAAA;AAErB,iBAAOJ,YAAW;AAAA,MACtB;AAEA,MAAAA,YAAW,SAAS,aAAa,SAAS,IAAI;AAC5C,eAAO,aAAa,IAAI,CAAC;AAAA,MAC3B;AACA,MAAAA,YAAW,SAAS,aAAa,SAAS,IAAI;AAC5C,eAAO,aAAa,IAAI,EAAE;AAAA,MAC5B;AAAA,IACF,CAAC;AAAA;AAAA;;;ACz/BD;AAAA;AA4BA,QAAIQ,oBAAmB,WAAW;AAMhC,WAAK,eAAe;AAEpB,WAAK,gBAAgB;AAErB,WAAK,kBAAkB;AAIvB,WAAK,iBAAiB;AAKtB,WAAK,wBAAwB;AAE7B,WAAK,eAAe;AAGpB,WAAK,gBAAgB;AAAA,IACvB;AAWA,QAAIC,eAAc;AAClB,QAAIC,eAAc;AAClB,QAAIC,cAAa;AAWjB,IAAAH,kBAAiB,OAAO,SAAS,IAAI,MAAM;AACzC,aAAO,CAAC,IAAI,IAAI;AAAA,IAClB;AAeA,IAAAA,kBAAiB,UAAU,YAAY,SAAS,OAAO,OAAO,gBAC1D,cAAc;AAEhB,UAAI,OAAO,gBAAgB,aAAa;AACtC,YAAI,KAAK,gBAAgB,GAAG;AAC1B,yBAAe,OAAO;AAAA,QACxB,OAAO;AACL,0BAAgB,oBAAI,QAAM,QAAQ,IAAI,KAAK,eAAe;AAAA,QAC5D;AAAA,MACF;AACA,UAAI,WAAW;AAGf,UAAI,SAAS,QAAQ,SAAS,MAAM;AAClC,cAAM,IAAI,MAAM,yBAAyB;AAAA,MAC3C;AAGA,UAAI,SAAS,OAAO;AAClB,YAAI,OAAO;AACT,iBAAO,CAAC,IAAIA,kBAAiB,KAAKG,aAAY,KAAK,CAAC;AAAA,QACtD;AACA,eAAO,CAAC;AAAA,MACV;AAEA,UAAI,OAAO,kBAAkB,aAAa;AACxC,yBAAiB;AAAA,MACnB;AACA,UAAI,aAAa;AAGjB,UAAI,eAAe,KAAK,kBAAkB,OAAO,KAAK;AACtD,UAAI,eAAe,MAAM,UAAU,GAAG,YAAY;AAClD,cAAQ,MAAM,UAAU,YAAY;AACpC,cAAQ,MAAM,UAAU,YAAY;AAGpC,qBAAe,KAAK,kBAAkB,OAAO,KAAK;AAClD,UAAI,eAAe,MAAM,UAAU,MAAM,SAAS,YAAY;AAC9D,cAAQ,MAAM,UAAU,GAAG,MAAM,SAAS,YAAY;AACtD,cAAQ,MAAM,UAAU,GAAG,MAAM,SAAS,YAAY;AAGtD,UAAI,QAAQ,KAAK,cAAc,OAAO,OAAO,YAAY,QAAQ;AAGjE,UAAI,cAAc;AAChB,cAAM,QAAQ,IAAIH,kBAAiB,KAAKG,aAAY,YAAY,CAAC;AAAA,MACnE;AACA,UAAI,cAAc;AAChB,cAAM,KAAK,IAAIH,kBAAiB,KAAKG,aAAY,YAAY,CAAC;AAAA,MAChE;AACA,WAAK,kBAAkB,KAAK;AAC5B,aAAO;AAAA,IACT;AAeA,IAAAH,kBAAiB,UAAU,gBAAgB,SAAS,OAAO,OAAO,YAC9D,UAAU;AACZ,UAAI;AAEJ,UAAI,CAAC,OAAO;AAEV,eAAO,CAAC,IAAIA,kBAAiB,KAAKE,cAAa,KAAK,CAAC;AAAA,MACvD;AAEA,UAAI,CAAC,OAAO;AAEV,eAAO,CAAC,IAAIF,kBAAiB,KAAKC,cAAa,KAAK,CAAC;AAAA,MACvD;AAEA,UAAI,WAAW,MAAM,SAAS,MAAM,SAAS,QAAQ;AACrD,UAAI,YAAY,MAAM,SAAS,MAAM,SAAS,QAAQ;AACtD,UAAI,IAAI,SAAS,QAAQ,SAAS;AAClC,UAAI,KAAK,IAAI;AAEX,gBAAQ;AAAA,UAAC,IAAID,kBAAiB,KAAKE,cAAa,SAAS,UAAU,GAAG,CAAC,CAAC;AAAA,UAC/D,IAAIF,kBAAiB,KAAKG,aAAY,SAAS;AAAA,UAC/C,IAAIH,kBAAiB;AAAA,YAAKE;AAAA,YACtB,SAAS,UAAU,IAAI,UAAU,MAAM;AAAA,UAAC;AAAA,QAAC;AAEtD,YAAI,MAAM,SAAS,MAAM,QAAQ;AAC/B,gBAAM,CAAC,EAAE,CAAC,IAAI,MAAM,CAAC,EAAE,CAAC,IAAID;AAAA,QAC9B;AACA,eAAO;AAAA,MACT;AAEA,UAAI,UAAU,UAAU,GAAG;AAGzB,eAAO;AAAA,UAAC,IAAID,kBAAiB,KAAKC,cAAa,KAAK;AAAA,UAC5C,IAAID,kBAAiB,KAAKE,cAAa,KAAK;AAAA,QAAC;AAAA,MACvD;AAGA,UAAI,KAAK,KAAK,gBAAgB,OAAO,KAAK;AAC1C,UAAI,IAAI;AAEN,YAAI,UAAU,GAAG,CAAC;AAClB,YAAI,UAAU,GAAG,CAAC;AAClB,YAAI,UAAU,GAAG,CAAC;AAClB,YAAI,UAAU,GAAG,CAAC;AAClB,YAAI,aAAa,GAAG,CAAC;AAErB,YAAI,UAAU,KAAK,UAAU,SAAS,SAAS,YAAY,QAAQ;AACnE,YAAI,UAAU,KAAK,UAAU,SAAS,SAAS,YAAY,QAAQ;AAEnE,eAAO,QAAQ;AAAA,UAAO,CAAC,IAAIF,kBAAiB,KAAKG,aAAY,UAAU,CAAC;AAAA,UAClD;AAAA,QAAO;AAAA,MAC/B;AAEA,UAAI,cAAc,MAAM,SAAS,OAAO,MAAM,SAAS,KAAK;AAC1D,eAAO,KAAK,eAAe,OAAO,OAAO,QAAQ;AAAA,MACnD;AAEA,aAAO,KAAK,aAAa,OAAO,OAAO,QAAQ;AAAA,IACjD;AAaA,IAAAH,kBAAiB,UAAU,iBAAiB,SAAS,OAAO,OAAO,UAAU;AAE3E,UAAI,IAAI,KAAK,mBAAmB,OAAO,KAAK;AAC5C,cAAQ,EAAE;AACV,cAAQ,EAAE;AACV,UAAI,YAAY,EAAE;AAElB,UAAI,QAAQ,KAAK,UAAU,OAAO,OAAO,OAAO,QAAQ;AAGxD,WAAK,mBAAmB,OAAO,SAAS;AAExC,WAAK,qBAAqB,KAAK;AAI/B,YAAM,KAAK,IAAIA,kBAAiB,KAAKG,aAAY,EAAE,CAAC;AACpD,UAAI,UAAU;AACd,UAAI,eAAe;AACnB,UAAI,eAAe;AACnB,UAAI,cAAc;AAClB,UAAI,cAAc;AAClB,aAAO,UAAU,MAAM,QAAQ;AAC7B,gBAAQ,MAAM,OAAO,EAAE,CAAC,GAAG;AAAA,UACzB,KAAKD;AACH;AACA,2BAAe,MAAM,OAAO,EAAE,CAAC;AAC/B;AAAA,UACF,KAAKD;AACH;AACA,2BAAe,MAAM,OAAO,EAAE,CAAC;AAC/B;AAAA,UACF,KAAKE;AAEH,gBAAI,gBAAgB,KAAK,gBAAgB,GAAG;AAE1C,oBAAM;AAAA,gBAAO,UAAU,eAAe;AAAA,gBACzB,eAAe;AAAA,cAAY;AACxC,wBAAU,UAAU,eAAe;AACnC,kBAAI,UACA,KAAK,UAAU,aAAa,aAAa,OAAO,QAAQ;AAC5D,uBAASC,KAAI,QAAQ,SAAS,GAAGA,MAAK,GAAGA,MAAK;AAC5C,sBAAM,OAAO,SAAS,GAAG,QAAQA,EAAC,CAAC;AAAA,cACrC;AACA,wBAAU,UAAU,QAAQ;AAAA,YAC9B;AACA,2BAAe;AACf,2BAAe;AACf,0BAAc;AACd,0BAAc;AACd;AAAA,QACJ;AACA;AAAA,MACF;AACA,YAAM,IAAI;AAEV,aAAO;AAAA,IACT;AAaA,IAAAJ,kBAAiB,UAAU,eAAe,SAAS,OAAO,OAAO,UAAU;AAEzE,UAAI,eAAe,MAAM;AACzB,UAAI,eAAe,MAAM;AACzB,UAAI,QAAQ,KAAK,MAAM,eAAe,gBAAgB,CAAC;AACvD,UAAI,WAAW;AACf,UAAI,WAAW,IAAI;AACnB,UAAI,KAAK,IAAI,MAAM,QAAQ;AAC3B,UAAI,KAAK,IAAI,MAAM,QAAQ;AAG3B,eAAS,IAAI,GAAG,IAAI,UAAU,KAAK;AACjC,WAAG,CAAC,IAAI;AACR,WAAG,CAAC,IAAI;AAAA,MACV;AACA,SAAG,WAAW,CAAC,IAAI;AACnB,SAAG,WAAW,CAAC,IAAI;AACnB,UAAI,QAAQ,eAAe;AAG3B,UAAI,QAAS,QAAQ,KAAK;AAG1B,UAAI,UAAU;AACd,UAAI,QAAQ;AACZ,UAAI,UAAU;AACd,UAAI,QAAQ;AACZ,eAAS,IAAI,GAAG,IAAI,OAAO,KAAK;AAE9B,aAAK,oBAAI,KAAK,GAAG,QAAQ,IAAI,UAAU;AACrC;AAAA,QACF;AAGA,iBAAS,KAAK,CAAC,IAAI,SAAS,MAAM,IAAI,OAAO,MAAM,GAAG;AACpD,cAAI,YAAY,WAAW;AAC3B,cAAI;AACJ,cAAI,MAAM,CAAC,KAAM,MAAM,KAAK,GAAG,YAAY,CAAC,IAAI,GAAG,YAAY,CAAC,GAAI;AAClE,iBAAK,GAAG,YAAY,CAAC;AAAA,UACvB,OAAO;AACL,iBAAK,GAAG,YAAY,CAAC,IAAI;AAAA,UAC3B;AACA,cAAI,KAAK,KAAK;AACd,iBAAO,KAAK,gBAAgB,KAAK,gBAC1B,MAAM,OAAO,EAAE,KAAK,MAAM,OAAO,EAAE,GAAG;AAC3C;AACA;AAAA,UACF;AACA,aAAG,SAAS,IAAI;AAChB,cAAI,KAAK,cAAc;AAErB,qBAAS;AAAA,UACX,WAAW,KAAK,cAAc;AAE5B,uBAAW;AAAA,UACb,WAAW,OAAO;AAChB,gBAAI,YAAY,WAAW,QAAQ;AACnC,gBAAI,aAAa,KAAK,YAAY,YAAY,GAAG,SAAS,KAAK,IAAI;AAEjE,kBAAI,KAAK,eAAe,GAAG,SAAS;AACpC,kBAAI,MAAM,IAAI;AAEZ,uBAAO,KAAK,kBAAkB,OAAO,OAAO,IAAI,IAAI,QAAQ;AAAA,cAC9D;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAGA,iBAAS,KAAK,CAAC,IAAI,SAAS,MAAM,IAAI,OAAO,MAAM,GAAG;AACpD,cAAI,YAAY,WAAW;AAC3B,cAAI;AACJ,cAAI,MAAM,CAAC,KAAM,MAAM,KAAK,GAAG,YAAY,CAAC,IAAI,GAAG,YAAY,CAAC,GAAI;AAClE,iBAAK,GAAG,YAAY,CAAC;AAAA,UACvB,OAAO;AACL,iBAAK,GAAG,YAAY,CAAC,IAAI;AAAA,UAC3B;AACA,cAAI,KAAK,KAAK;AACd,iBAAO,KAAK,gBAAgB,KAAK,gBAC1B,MAAM,OAAO,eAAe,KAAK,CAAC,KAClC,MAAM,OAAO,eAAe,KAAK,CAAC,GAAG;AAC1C;AACA;AAAA,UACF;AACA,aAAG,SAAS,IAAI;AAChB,cAAI,KAAK,cAAc;AAErB,qBAAS;AAAA,UACX,WAAW,KAAK,cAAc;AAE5B,uBAAW;AAAA,UACb,WAAW,CAAC,OAAO;AACjB,gBAAI,YAAY,WAAW,QAAQ;AACnC,gBAAI,aAAa,KAAK,YAAY,YAAY,GAAG,SAAS,KAAK,IAAI;AACjE,kBAAI,KAAK,GAAG,SAAS;AACrB,kBAAI,KAAK,WAAW,KAAK;AAEzB,mBAAK,eAAe;AACpB,kBAAI,MAAM,IAAI;AAEZ,uBAAO,KAAK,kBAAkB,OAAO,OAAO,IAAI,IAAI,QAAQ;AAAA,cAC9D;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAGA,aAAO;AAAA,QAAC,IAAIA,kBAAiB,KAAKC,cAAa,KAAK;AAAA,QAC5C,IAAID,kBAAiB,KAAKE,cAAa,KAAK;AAAA,MAAC;AAAA,IACvD;AAcA,IAAAF,kBAAiB,UAAU,oBAAoB,SAAS,OAAO,OAAO,GAAG,GACrE,UAAU;AACZ,UAAI,SAAS,MAAM,UAAU,GAAG,CAAC;AACjC,UAAI,SAAS,MAAM,UAAU,GAAG,CAAC;AACjC,UAAI,SAAS,MAAM,UAAU,CAAC;AAC9B,UAAI,SAAS,MAAM,UAAU,CAAC;AAG9B,UAAI,QAAQ,KAAK,UAAU,QAAQ,QAAQ,OAAO,QAAQ;AAC1D,UAAI,SAAS,KAAK,UAAU,QAAQ,QAAQ,OAAO,QAAQ;AAE3D,aAAO,MAAM,OAAO,MAAM;AAAA,IAC5B;AAcA,IAAAA,kBAAiB,UAAU,qBAAqB,SAAS,OAAO,OAAO;AACrE,UAAI,YAAY,CAAC;AACjB,UAAI,WAAW,CAAC;AAIhB,gBAAU,CAAC,IAAI;AAUf,eAAS,wBAAwB,MAAM;AACrC,YAAI,QAAQ;AAIZ,YAAI,YAAY;AAChB,YAAI,UAAU;AAEd,YAAI,kBAAkB,UAAU;AAChC,eAAO,UAAU,KAAK,SAAS,GAAG;AAChC,oBAAU,KAAK,QAAQ,MAAM,SAAS;AACtC,cAAI,WAAW,IAAI;AACjB,sBAAU,KAAK,SAAS;AAAA,UAC1B;AACA,cAAI,OAAO,KAAK,UAAU,WAAW,UAAU,CAAC;AAEhD,cAAI,SAAS,iBAAiB,SAAS,eAAe,IAAI,IACrD,SAAS,IAAI,MAAM,QAAY;AAClC,qBAAS,OAAO,aAAa,SAAS,IAAI,CAAC;AAAA,UAC7C,OAAO;AACL,gBAAI,mBAAmB,UAAU;AAG/B,qBAAO,KAAK,UAAU,SAAS;AAC/B,wBAAU,KAAK;AAAA,YACjB;AACA,qBAAS,OAAO,aAAa,eAAe;AAC5C,qBAAS,IAAI,IAAI;AACjB,sBAAU,iBAAiB,IAAI;AAAA,UACjC;AACA,sBAAY,UAAU;AAAA,QACxB;AACA,eAAO;AAAA,MACT;AAEA,UAAI,WAAW;AACf,UAAI,SAAS,wBAAwB,KAAK;AAC1C,iBAAW;AACX,UAAI,SAAS,wBAAwB,KAAK;AAC1C,aAAO,EAAC,QAAgB,QAAgB,UAAoB;AAAA,IAC9D;AAUA,IAAAA,kBAAiB,UAAU,qBAAqB,SAAS,OAAO,WAAW;AACzE,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,YAAI,QAAQ,MAAM,CAAC,EAAE,CAAC;AACtB,YAAI,OAAO,CAAC;AACZ,iBAASI,KAAI,GAAGA,KAAI,MAAM,QAAQA,MAAK;AACrC,eAAKA,EAAC,IAAI,UAAU,MAAM,WAAWA,EAAC,CAAC;AAAA,QACzC;AACA,cAAM,CAAC,EAAE,CAAC,IAAI,KAAK,KAAK,EAAE;AAAA,MAC5B;AAAA,IACF;AAUA,IAAAJ,kBAAiB,UAAU,oBAAoB,SAAS,OAAO,OAAO;AAEpE,UAAI,CAAC,SAAS,CAAC,SAAS,MAAM,OAAO,CAAC,KAAK,MAAM,OAAO,CAAC,GAAG;AAC1D,eAAO;AAAA,MACT;AAGA,UAAI,aAAa;AACjB,UAAI,aAAa,KAAK,IAAI,MAAM,QAAQ,MAAM,MAAM;AACpD,UAAI,aAAa;AACjB,UAAI,eAAe;AACnB,aAAO,aAAa,YAAY;AAC9B,YAAI,MAAM,UAAU,cAAc,UAAU,KACxC,MAAM,UAAU,cAAc,UAAU,GAAG;AAC7C,uBAAa;AACb,yBAAe;AAAA,QACjB,OAAO;AACL,uBAAa;AAAA,QACf;AACA,qBAAa,KAAK,OAAO,aAAa,cAAc,IAAI,UAAU;AAAA,MACpE;AACA,aAAO;AAAA,IACT;AASA,IAAAA,kBAAiB,UAAU,oBAAoB,SAAS,OAAO,OAAO;AAEpE,UAAI,CAAC,SAAS,CAAC,SACX,MAAM,OAAO,MAAM,SAAS,CAAC,KAAK,MAAM,OAAO,MAAM,SAAS,CAAC,GAAG;AACpE,eAAO;AAAA,MACT;AAGA,UAAI,aAAa;AACjB,UAAI,aAAa,KAAK,IAAI,MAAM,QAAQ,MAAM,MAAM;AACpD,UAAI,aAAa;AACjB,UAAI,aAAa;AACjB,aAAO,aAAa,YAAY;AAC9B,YAAI,MAAM,UAAU,MAAM,SAAS,YAAY,MAAM,SAAS,UAAU,KACpE,MAAM,UAAU,MAAM,SAAS,YAAY,MAAM,SAAS,UAAU,GAAG;AACzE,uBAAa;AACb,uBAAa;AAAA,QACf,OAAO;AACL,uBAAa;AAAA,QACf;AACA,qBAAa,KAAK,OAAO,aAAa,cAAc,IAAI,UAAU;AAAA,MACpE;AACA,aAAO;AAAA,IACT;AAWA,IAAAA,kBAAiB,UAAU,sBAAsB,SAAS,OAAO,OAAO;AAEtE,UAAI,eAAe,MAAM;AACzB,UAAI,eAAe,MAAM;AAEzB,UAAI,gBAAgB,KAAK,gBAAgB,GAAG;AAC1C,eAAO;AAAA,MACT;AAEA,UAAI,eAAe,cAAc;AAC/B,gBAAQ,MAAM,UAAU,eAAe,YAAY;AAAA,MACrD,WAAW,eAAe,cAAc;AACtC,gBAAQ,MAAM,UAAU,GAAG,YAAY;AAAA,MACzC;AACA,UAAI,cAAc,KAAK,IAAI,cAAc,YAAY;AAErD,UAAI,SAAS,OAAO;AAClB,eAAO;AAAA,MACT;AAKA,UAAI,OAAO;AACX,UAAI,SAAS;AACb,aAAO,MAAM;AACX,YAAI,UAAU,MAAM,UAAU,cAAc,MAAM;AAClD,YAAI,QAAQ,MAAM,QAAQ,OAAO;AACjC,YAAI,SAAS,IAAI;AACf,iBAAO;AAAA,QACT;AACA,kBAAU;AACV,YAAI,SAAS,KAAK,MAAM,UAAU,cAAc,MAAM,KAClD,MAAM,UAAU,GAAG,MAAM,GAAG;AAC9B,iBAAO;AACP;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAcA,IAAAA,kBAAiB,UAAU,kBAAkB,SAAS,OAAO,OAAO;AAClE,UAAI,KAAK,gBAAgB,GAAG;AAE1B,eAAO;AAAA,MACT;AACA,UAAI,WAAW,MAAM,SAAS,MAAM,SAAS,QAAQ;AACrD,UAAI,YAAY,MAAM,SAAS,MAAM,SAAS,QAAQ;AACtD,UAAI,SAAS,SAAS,KAAK,UAAU,SAAS,IAAI,SAAS,QAAQ;AACjE,eAAO;AAAA,MACT;AACA,UAAI,MAAM;AAcV,eAAS,iBAAiBK,WAAUC,YAAW,GAAG;AAEhD,YAAI,OAAOD,UAAS,UAAU,GAAG,IAAI,KAAK,MAAMA,UAAS,SAAS,CAAC,CAAC;AACpE,YAAID,KAAI;AACR,YAAI,cAAc;AAClB,YAAI,iBAAiB,iBAAiB,kBAAkB;AACxD,gBAAQA,KAAIE,WAAU,QAAQ,MAAMF,KAAI,CAAC,MAAM,IAAI;AACjD,cAAI,eAAe,IAAI;AAAA,YAAkBC,UAAS,UAAU,CAAC;AAAA,YACpBC,WAAU,UAAUF,EAAC;AAAA,UAAC;AAC/D,cAAI,eAAe,IAAI;AAAA,YAAkBC,UAAS,UAAU,GAAG,CAAC;AAAA,YACvBC,WAAU,UAAU,GAAGF,EAAC;AAAA,UAAC;AAClE,cAAI,YAAY,SAAS,eAAe,cAAc;AACpD,0BAAcE,WAAU,UAAUF,KAAI,cAAcA,EAAC,IACjDE,WAAU,UAAUF,IAAGA,KAAI,YAAY;AAC3C,8BAAkBC,UAAS,UAAU,GAAG,IAAI,YAAY;AACxD,8BAAkBA,UAAS,UAAU,IAAI,YAAY;AACrD,+BAAmBC,WAAU,UAAU,GAAGF,KAAI,YAAY;AAC1D,+BAAmBE,WAAU,UAAUF,KAAI,YAAY;AAAA,UACzD;AAAA,QACF;AACA,YAAI,YAAY,SAAS,KAAKC,UAAS,QAAQ;AAC7C,iBAAO;AAAA,YAAC;AAAA,YAAiB;AAAA,YACjB;AAAA,YAAkB;AAAA,YAAkB;AAAA,UAAW;AAAA,QACzD,OAAO;AACL,iBAAO;AAAA,QACT;AAAA,MACF;AAGA,UAAI,MAAM;AAAA,QAAiB;AAAA,QAAU;AAAA,QACV,KAAK,KAAK,SAAS,SAAS,CAAC;AAAA,MAAC;AAEzD,UAAI,MAAM;AAAA,QAAiB;AAAA,QAAU;AAAA,QACV,KAAK,KAAK,SAAS,SAAS,CAAC;AAAA,MAAC;AACzD,UAAI;AACJ,UAAI,CAAC,OAAO,CAAC,KAAK;AAChB,eAAO;AAAA,MACT,WAAW,CAAC,KAAK;AACf,aAAK;AAAA,MACP,WAAW,CAAC,KAAK;AACf,aAAK;AAAA,MACP,OAAO;AAEL,aAAK,IAAI,CAAC,EAAE,SAAS,IAAI,CAAC,EAAE,SAAS,MAAM;AAAA,MAC7C;AAGA,UAAI,SAAS,SAAS,SAAS;AAC/B,UAAI,MAAM,SAAS,MAAM,QAAQ;AAC/B,kBAAU,GAAG,CAAC;AACd,kBAAU,GAAG,CAAC;AACd,kBAAU,GAAG,CAAC;AACd,kBAAU,GAAG,CAAC;AAAA,MAChB,OAAO;AACL,kBAAU,GAAG,CAAC;AACd,kBAAU,GAAG,CAAC;AACd,kBAAU,GAAG,CAAC;AACd,kBAAU,GAAG,CAAC;AAAA,MAChB;AACA,UAAI,aAAa,GAAG,CAAC;AACrB,aAAO,CAAC,SAAS,SAAS,SAAS,SAAS,UAAU;AAAA,IACxD;AAOA,IAAAL,kBAAiB,UAAU,uBAAuB,SAAS,OAAO;AAChE,UAAI,UAAU;AACd,UAAI,aAAa,CAAC;AAClB,UAAI,mBAAmB;AAEvB,UAAI,eAAe;AAEnB,UAAI,UAAU;AAEd,UAAI,qBAAqB;AACzB,UAAI,oBAAoB;AAExB,UAAI,qBAAqB;AACzB,UAAI,oBAAoB;AACxB,aAAO,UAAU,MAAM,QAAQ;AAC7B,YAAI,MAAM,OAAO,EAAE,CAAC,KAAKG,aAAY;AACnC,qBAAW,kBAAkB,IAAI;AACjC,+BAAqB;AACrB,8BAAoB;AACpB,+BAAqB;AACrB,8BAAoB;AACpB,yBAAe,MAAM,OAAO,EAAE,CAAC;AAAA,QACjC,OAAO;AACL,cAAI,MAAM,OAAO,EAAE,CAAC,KAAKD,cAAa;AACpC,kCAAsB,MAAM,OAAO,EAAE,CAAC,EAAE;AAAA,UAC1C,OAAO;AACL,iCAAqB,MAAM,OAAO,EAAE,CAAC,EAAE;AAAA,UACzC;AAGA,cAAI,gBAAiB,aAAa,UAC9B,KAAK,IAAI,oBAAoB,iBAAiB,KAC7C,aAAa,UAAU,KAAK;AAAA,YAAI;AAAA,YACA;AAAA,UAAiB,GAAI;AAExD,kBAAM;AAAA,cAAO,WAAW,mBAAmB,CAAC;AAAA,cAAG;AAAA,cAClC,IAAIF,kBAAiB,KAAKC,cAAa,YAAY;AAAA,YAAC;AAEjE,kBAAM,WAAW,mBAAmB,CAAC,IAAI,CAAC,EAAE,CAAC,IAAIC;AAEjD;AAEA;AACA,sBAAU,mBAAmB,IAAI,WAAW,mBAAmB,CAAC,IAAI;AACpE,iCAAqB;AACrB,gCAAoB;AACpB,iCAAqB;AACrB,gCAAoB;AACpB,2BAAe;AACf,sBAAU;AAAA,UACZ;AAAA,QACF;AACA;AAAA,MACF;AAGA,UAAI,SAAS;AACX,aAAK,kBAAkB,KAAK;AAAA,MAC9B;AACA,WAAK,6BAA6B,KAAK;AAQvC,gBAAU;AACV,aAAO,UAAU,MAAM,QAAQ;AAC7B,YAAI,MAAM,UAAU,CAAC,EAAE,CAAC,KAAKD,gBACzB,MAAM,OAAO,EAAE,CAAC,KAAKC,cAAa;AACpC,cAAI,WAAW,MAAM,UAAU,CAAC,EAAE,CAAC;AACnC,cAAI,YAAY,MAAM,OAAO,EAAE,CAAC;AAChC,cAAI,kBAAkB,KAAK,oBAAoB,UAAU,SAAS;AAClE,cAAI,kBAAkB,KAAK,oBAAoB,WAAW,QAAQ;AAClE,cAAI,mBAAmB,iBAAiB;AACtC,gBAAI,mBAAmB,SAAS,SAAS,KACrC,mBAAmB,UAAU,SAAS,GAAG;AAE3C,oBAAM,OAAO,SAAS,GAAG,IAAIF,kBAAiB;AAAA,gBAAKG;AAAA,gBAC/C,UAAU,UAAU,GAAG,eAAe;AAAA,cAAC,CAAC;AAC5C,oBAAM,UAAU,CAAC,EAAE,CAAC,IAChB,SAAS,UAAU,GAAG,SAAS,SAAS,eAAe;AAC3D,oBAAM,UAAU,CAAC,EAAE,CAAC,IAAI,UAAU,UAAU,eAAe;AAC3D;AAAA,YACF;AAAA,UACF,OAAO;AACL,gBAAI,mBAAmB,SAAS,SAAS,KACrC,mBAAmB,UAAU,SAAS,GAAG;AAG3C,oBAAM,OAAO,SAAS,GAAG,IAAIH,kBAAiB;AAAA,gBAAKG;AAAA,gBAC/C,SAAS,UAAU,GAAG,eAAe;AAAA,cAAC,CAAC;AAC3C,oBAAM,UAAU,CAAC,EAAE,CAAC,IAAID;AACxB,oBAAM,UAAU,CAAC,EAAE,CAAC,IAChB,UAAU,UAAU,GAAG,UAAU,SAAS,eAAe;AAC7D,oBAAM,UAAU,CAAC,EAAE,CAAC,IAAID;AACxB,oBAAM,UAAU,CAAC,EAAE,CAAC,IAChB,SAAS,UAAU,eAAe;AACtC;AAAA,YACF;AAAA,UACF;AACA;AAAA,QACF;AACA;AAAA,MACF;AAAA,IACF;AASA,IAAAD,kBAAiB,UAAU,+BAA+B,SAAS,OAAO;AAWxE,eAAS,2BAA2B,KAAK,KAAK;AAC5C,YAAI,CAAC,OAAO,CAAC,KAAK;AAEhB,iBAAO;AAAA,QACT;AAOA,YAAI,QAAQ,IAAI,OAAO,IAAI,SAAS,CAAC;AACrC,YAAI,QAAQ,IAAI,OAAO,CAAC;AACxB,YAAI,mBAAmB,MAAM,MAAMA,kBAAiB,qBAAqB;AACzE,YAAI,mBAAmB,MAAM,MAAMA,kBAAiB,qBAAqB;AACzE,YAAI,cAAc,oBACd,MAAM,MAAMA,kBAAiB,gBAAgB;AACjD,YAAI,cAAc,oBACd,MAAM,MAAMA,kBAAiB,gBAAgB;AACjD,YAAI,aAAa,eACb,MAAM,MAAMA,kBAAiB,eAAe;AAChD,YAAI,aAAa,eACb,MAAM,MAAMA,kBAAiB,eAAe;AAChD,YAAI,aAAa,cACb,IAAI,MAAMA,kBAAiB,kBAAkB;AACjD,YAAI,aAAa,cACb,IAAI,MAAMA,kBAAiB,oBAAoB;AAEnD,YAAI,cAAc,YAAY;AAE5B,iBAAO;AAAA,QACT,WAAW,cAAc,YAAY;AAEnC,iBAAO;AAAA,QACT,WAAW,oBAAoB,CAAC,eAAe,aAAa;AAE1D,iBAAO;AAAA,QACT,WAAW,eAAe,aAAa;AAErC,iBAAO;AAAA,QACT,WAAW,oBAAoB,kBAAkB;AAE/C,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT;AAEA,UAAI,UAAU;AAEd,aAAO,UAAU,MAAM,SAAS,GAAG;AACjC,YAAI,MAAM,UAAU,CAAC,EAAE,CAAC,KAAKG,eACzB,MAAM,UAAU,CAAC,EAAE,CAAC,KAAKA,aAAY;AAEvC,cAAI,YAAY,MAAM,UAAU,CAAC,EAAE,CAAC;AACpC,cAAI,OAAO,MAAM,OAAO,EAAE,CAAC;AAC3B,cAAI,YAAY,MAAM,UAAU,CAAC,EAAE,CAAC;AAGpC,cAAI,eAAe,KAAK,kBAAkB,WAAW,IAAI;AACzD,cAAI,cAAc;AAChB,gBAAI,eAAe,KAAK,UAAU,KAAK,SAAS,YAAY;AAC5D,wBAAY,UAAU,UAAU,GAAG,UAAU,SAAS,YAAY;AAClE,mBAAO,eAAe,KAAK,UAAU,GAAG,KAAK,SAAS,YAAY;AAClE,wBAAY,eAAe;AAAA,UAC7B;AAGA,cAAI,gBAAgB;AACpB,cAAI,WAAW;AACf,cAAI,gBAAgB;AACpB,cAAI,YAAY,2BAA2B,WAAW,IAAI,IACtD,2BAA2B,MAAM,SAAS;AAC9C,iBAAO,KAAK,OAAO,CAAC,MAAM,UAAU,OAAO,CAAC,GAAG;AAC7C,yBAAa,KAAK,OAAO,CAAC;AAC1B,mBAAO,KAAK,UAAU,CAAC,IAAI,UAAU,OAAO,CAAC;AAC7C,wBAAY,UAAU,UAAU,CAAC;AACjC,gBAAI,QAAQ,2BAA2B,WAAW,IAAI,IAClD,2BAA2B,MAAM,SAAS;AAE9C,gBAAI,SAAS,WAAW;AACtB,0BAAY;AACZ,8BAAgB;AAChB,yBAAW;AACX,8BAAgB;AAAA,YAClB;AAAA,UACF;AAEA,cAAI,MAAM,UAAU,CAAC,EAAE,CAAC,KAAK,eAAe;AAE1C,gBAAI,eAAe;AACjB,oBAAM,UAAU,CAAC,EAAE,CAAC,IAAI;AAAA,YAC1B,OAAO;AACL,oBAAM,OAAO,UAAU,GAAG,CAAC;AAC3B;AAAA,YACF;AACA,kBAAM,OAAO,EAAE,CAAC,IAAI;AACpB,gBAAI,eAAe;AACjB,oBAAM,UAAU,CAAC,EAAE,CAAC,IAAI;AAAA,YAC1B,OAAO;AACL,oBAAM,OAAO,UAAU,GAAG,CAAC;AAC3B;AAAA,YACF;AAAA,UACF;AAAA,QACF;AACA;AAAA,MACF;AAAA,IACF;AAGA,IAAAH,kBAAiB,wBAAwB;AACzC,IAAAA,kBAAiB,mBAAmB;AACpC,IAAAA,kBAAiB,kBAAkB;AACnC,IAAAA,kBAAiB,qBAAqB;AACtC,IAAAA,kBAAiB,uBAAuB;AAMxC,IAAAA,kBAAiB,UAAU,yBAAyB,SAAS,OAAO;AAClE,UAAI,UAAU;AACd,UAAI,aAAa,CAAC;AAClB,UAAI,mBAAmB;AAEvB,UAAI,eAAe;AAEnB,UAAI,UAAU;AAEd,UAAI,UAAU;AAEd,UAAI,UAAU;AAEd,UAAI,WAAW;AAEf,UAAI,WAAW;AACf,aAAO,UAAU,MAAM,QAAQ;AAC7B,YAAI,MAAM,OAAO,EAAE,CAAC,KAAKG,aAAY;AACnC,cAAI,MAAM,OAAO,EAAE,CAAC,EAAE,SAAS,KAAK,kBAC/B,YAAY,WAAW;AAE1B,uBAAW,kBAAkB,IAAI;AACjC,sBAAU;AACV,sBAAU;AACV,2BAAe,MAAM,OAAO,EAAE,CAAC;AAAA,UACjC,OAAO;AAEL,+BAAmB;AACnB,2BAAe;AAAA,UACjB;AACA,qBAAW,WAAW;AAAA,QACxB,OAAO;AACL,cAAI,MAAM,OAAO,EAAE,CAAC,KAAKF,cAAa;AACpC,uBAAW;AAAA,UACb,OAAO;AACL,uBAAW;AAAA,UACb;AASA,cAAI,iBAAkB,WAAW,WAAW,YAAY,YACjC,aAAa,SAAS,KAAK,gBAAgB,KAC3C,UAAU,UAAU,WAAW,YAAa,IAAK;AAEtE,kBAAM;AAAA,cAAO,WAAW,mBAAmB,CAAC;AAAA,cAAG;AAAA,cAClC,IAAID,kBAAiB,KAAKC,cAAa,YAAY;AAAA,YAAC;AAEjE,kBAAM,WAAW,mBAAmB,CAAC,IAAI,CAAC,EAAE,CAAC,IAAIC;AACjD;AACA,2BAAe;AACf,gBAAI,WAAW,SAAS;AAEtB,yBAAW,WAAW;AACtB,iCAAmB;AAAA,YACrB,OAAO;AACL;AACA,wBAAU,mBAAmB,IACzB,WAAW,mBAAmB,CAAC,IAAI;AACvC,yBAAW,WAAW;AAAA,YACxB;AACA,sBAAU;AAAA,UACZ;AAAA,QACF;AACA;AAAA,MACF;AAEA,UAAI,SAAS;AACX,aAAK,kBAAkB,KAAK;AAAA,MAC9B;AAAA,IACF;AAQA,IAAAF,kBAAiB,UAAU,oBAAoB,SAAS,OAAO;AAE7D,YAAM,KAAK,IAAIA,kBAAiB,KAAKG,aAAY,EAAE,CAAC;AACpD,UAAI,UAAU;AACd,UAAI,eAAe;AACnB,UAAI,eAAe;AACnB,UAAI,cAAc;AAClB,UAAI,cAAc;AAClB,UAAI;AACJ,aAAO,UAAU,MAAM,QAAQ;AAC7B,gBAAQ,MAAM,OAAO,EAAE,CAAC,GAAG;AAAA,UACzB,KAAKD;AACH;AACA,2BAAe,MAAM,OAAO,EAAE,CAAC;AAC/B;AACA;AAAA,UACF,KAAKD;AACH;AACA,2BAAe,MAAM,OAAO,EAAE,CAAC;AAC/B;AACA;AAAA,UACF,KAAKE;AAEH,gBAAI,eAAe,eAAe,GAAG;AACnC,kBAAI,iBAAiB,KAAK,iBAAiB,GAAG;AAE5C,+BAAe,KAAK,kBAAkB,aAAa,WAAW;AAC9D,oBAAI,iBAAiB,GAAG;AACtB,sBAAK,UAAU,eAAe,eAAgB,KAC1C,MAAM,UAAU,eAAe,eAAe,CAAC,EAAE,CAAC,KAClDA,aAAY;AACd,0BAAM,UAAU,eAAe,eAAe,CAAC,EAAE,CAAC,KAC9C,YAAY,UAAU,GAAG,YAAY;AAAA,kBAC3C,OAAO;AACL,0BAAM,OAAO,GAAG,GAAG,IAAIH,kBAAiB;AAAA,sBAAKG;AAAA,sBACzC,YAAY,UAAU,GAAG,YAAY;AAAA,oBAAC,CAAC;AAC3C;AAAA,kBACF;AACA,gCAAc,YAAY,UAAU,YAAY;AAChD,gCAAc,YAAY,UAAU,YAAY;AAAA,gBAClD;AAEA,+BAAe,KAAK,kBAAkB,aAAa,WAAW;AAC9D,oBAAI,iBAAiB,GAAG;AACtB,wBAAM,OAAO,EAAE,CAAC,IAAI,YAAY,UAAU,YAAY,SAClD,YAAY,IAAI,MAAM,OAAO,EAAE,CAAC;AACpC,gCAAc,YAAY,UAAU,GAAG,YAAY,SAC/C,YAAY;AAChB,gCAAc,YAAY,UAAU,GAAG,YAAY,SAC/C,YAAY;AAAA,gBAClB;AAAA,cACF;AAEA,yBAAW,eAAe;AAC1B,oBAAM,OAAO,SAAS,eAAe,YAAY;AACjD,kBAAI,YAAY,QAAQ;AACtB,sBAAM;AAAA,kBAAO;AAAA,kBAAS;AAAA,kBAClB,IAAIH,kBAAiB,KAAKC,cAAa,WAAW;AAAA,gBAAC;AACvD;AAAA,cACF;AACA,kBAAI,YAAY,QAAQ;AACtB,sBAAM;AAAA,kBAAO;AAAA,kBAAS;AAAA,kBAClB,IAAID,kBAAiB,KAAKE,cAAa,WAAW;AAAA,gBAAC;AACvD;AAAA,cACF;AACA;AAAA,YACF,WAAW,YAAY,KAAK,MAAM,UAAU,CAAC,EAAE,CAAC,KAAKC,aAAY;AAE/D,oBAAM,UAAU,CAAC,EAAE,CAAC,KAAK,MAAM,OAAO,EAAE,CAAC;AACzC,oBAAM,OAAO,SAAS,CAAC;AAAA,YACzB,OAAO;AACL;AAAA,YACF;AACA,2BAAe;AACf,2BAAe;AACf,0BAAc;AACd,0BAAc;AACd;AAAA,QACJ;AAAA,MACF;AACA,UAAI,MAAM,MAAM,SAAS,CAAC,EAAE,CAAC,MAAM,IAAI;AACrC,cAAM,IAAI;AAAA,MACZ;AAKA,UAAI,UAAU;AACd,gBAAU;AAEV,aAAO,UAAU,MAAM,SAAS,GAAG;AACjC,YAAI,MAAM,UAAU,CAAC,EAAE,CAAC,KAAKA,eACzB,MAAM,UAAU,CAAC,EAAE,CAAC,KAAKA,aAAY;AAEvC,cAAI,MAAM,OAAO,EAAE,CAAC,EAAE,UAAU,MAAM,OAAO,EAAE,CAAC,EAAE,SAC9C,MAAM,UAAU,CAAC,EAAE,CAAC,EAAE,MAAM,KAAK,MAAM,UAAU,CAAC,EAAE,CAAC,GAAG;AAE1D,kBAAM,OAAO,EAAE,CAAC,IAAI,MAAM,UAAU,CAAC,EAAE,CAAC,IACpC,MAAM,OAAO,EAAE,CAAC,EAAE,UAAU,GAAG,MAAM,OAAO,EAAE,CAAC,EAAE,SACrB,MAAM,UAAU,CAAC,EAAE,CAAC,EAAE,MAAM;AAC5D,kBAAM,UAAU,CAAC,EAAE,CAAC,IAAI,MAAM,UAAU,CAAC,EAAE,CAAC,IAAI,MAAM,UAAU,CAAC,EAAE,CAAC;AACpE,kBAAM,OAAO,UAAU,GAAG,CAAC;AAC3B,sBAAU;AAAA,UACZ,WAAW,MAAM,OAAO,EAAE,CAAC,EAAE,UAAU,GAAG,MAAM,UAAU,CAAC,EAAE,CAAC,EAAE,MAAM,KAClE,MAAM,UAAU,CAAC,EAAE,CAAC,GAAG;AAEzB,kBAAM,UAAU,CAAC,EAAE,CAAC,KAAK,MAAM,UAAU,CAAC,EAAE,CAAC;AAC7C,kBAAM,OAAO,EAAE,CAAC,IACZ,MAAM,OAAO,EAAE,CAAC,EAAE,UAAU,MAAM,UAAU,CAAC,EAAE,CAAC,EAAE,MAAM,IACxD,MAAM,UAAU,CAAC,EAAE,CAAC;AACxB,kBAAM,OAAO,UAAU,GAAG,CAAC;AAC3B,sBAAU;AAAA,UACZ;AAAA,QACF;AACA;AAAA,MACF;AAEA,UAAI,SAAS;AACX,aAAK,kBAAkB,KAAK;AAAA,MAC9B;AAAA,IACF;AAWA,IAAAH,kBAAiB,UAAU,cAAc,SAAS,OAAO,KAAK;AAC5D,UAAI,SAAS;AACb,UAAI,SAAS;AACb,UAAI,cAAc;AAClB,UAAI,cAAc;AAClB,UAAI;AACJ,WAAK,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACjC,YAAI,MAAM,CAAC,EAAE,CAAC,MAAME,cAAa;AAC/B,oBAAU,MAAM,CAAC,EAAE,CAAC,EAAE;AAAA,QACxB;AACA,YAAI,MAAM,CAAC,EAAE,CAAC,MAAMD,cAAa;AAC/B,oBAAU,MAAM,CAAC,EAAE,CAAC,EAAE;AAAA,QACxB;AACA,YAAI,SAAS,KAAK;AAChB;AAAA,QACF;AACA,sBAAc;AACd,sBAAc;AAAA,MAChB;AAEA,UAAI,MAAM,UAAU,KAAK,MAAM,CAAC,EAAE,CAAC,MAAMA,cAAa;AACpD,eAAO;AAAA,MACT;AAEA,aAAO,eAAe,MAAM;AAAA,IAC9B;AAQA,IAAAD,kBAAiB,UAAU,kBAAkB,SAAS,OAAO;AAC3D,UAAI,OAAO,CAAC;AACZ,UAAI,cAAc;AAClB,UAAI,aAAa;AACjB,UAAI,aAAa;AACjB,UAAI,eAAe;AACnB,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,YAAI,KAAK,MAAM,CAAC,EAAE,CAAC;AACnB,YAAI,OAAO,MAAM,CAAC,EAAE,CAAC;AACrB,YAAI,OAAO,KAAK,QAAQ,aAAa,OAAO,EAAE,QAAQ,YAAY,MAAM,EACnE,QAAQ,YAAY,MAAM,EAAE,QAAQ,cAAc,YAAY;AACnE,gBAAQ,IAAI;AAAA,UACV,KAAKE;AACH,iBAAK,CAAC,IAAI,sCAAsC,OAAO;AACvD;AAAA,UACF,KAAKD;AACH,iBAAK,CAAC,IAAI,sCAAsC,OAAO;AACvD;AAAA,UACF,KAAKE;AACH,iBAAK,CAAC,IAAI,WAAW,OAAO;AAC5B;AAAA,QACJ;AAAA,MACF;AACA,aAAO,KAAK,KAAK,EAAE;AAAA,IACrB;AAQA,IAAAH,kBAAiB,UAAU,aAAa,SAAS,OAAO;AACtD,UAAI,OAAO,CAAC;AACZ,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,YAAI,MAAM,CAAC,EAAE,CAAC,MAAME,cAAa;AAC/B,eAAK,CAAC,IAAI,MAAM,CAAC,EAAE,CAAC;AAAA,QACtB;AAAA,MACF;AACA,aAAO,KAAK,KAAK,EAAE;AAAA,IACrB;AAQA,IAAAF,kBAAiB,UAAU,aAAa,SAAS,OAAO;AACtD,UAAI,OAAO,CAAC;AACZ,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,YAAI,MAAM,CAAC,EAAE,CAAC,MAAMC,cAAa;AAC/B,eAAK,CAAC,IAAI,MAAM,CAAC,EAAE,CAAC;AAAA,QACtB;AAAA,MACF;AACA,aAAO,KAAK,KAAK,EAAE;AAAA,IACrB;AASA,IAAAD,kBAAiB,UAAU,mBAAmB,SAAS,OAAO;AAC5D,UAAI,cAAc;AAClB,UAAI,aAAa;AACjB,UAAI,YAAY;AAChB,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,YAAI,KAAK,MAAM,CAAC,EAAE,CAAC;AACnB,YAAI,OAAO,MAAM,CAAC,EAAE,CAAC;AACrB,gBAAQ,IAAI;AAAA,UACV,KAAKE;AACH,0BAAc,KAAK;AACnB;AAAA,UACF,KAAKD;AACH,yBAAa,KAAK;AAClB;AAAA,UACF,KAAKE;AAEH,2BAAe,KAAK,IAAI,YAAY,SAAS;AAC7C,yBAAa;AACb,wBAAY;AACZ;AAAA,QACJ;AAAA,MACF;AACA,qBAAe,KAAK,IAAI,YAAY,SAAS;AAC7C,aAAO;AAAA,IACT;AAWA,IAAAH,kBAAiB,UAAU,eAAe,SAAS,OAAO;AACxD,UAAI,OAAO,CAAC;AACZ,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,gBAAQ,MAAM,CAAC,EAAE,CAAC,GAAG;AAAA,UACnB,KAAKE;AACH,iBAAK,CAAC,IAAI,MAAM,UAAU,MAAM,CAAC,EAAE,CAAC,CAAC;AACrC;AAAA,UACF,KAAKD;AACH,iBAAK,CAAC,IAAI,MAAM,MAAM,CAAC,EAAE,CAAC,EAAE;AAC5B;AAAA,UACF,KAAKE;AACH,iBAAK,CAAC,IAAI,MAAM,MAAM,CAAC,EAAE,CAAC,EAAE;AAC5B;AAAA,QACJ;AAAA,MACF;AACA,aAAO,KAAK,KAAK,GAAI,EAAE,QAAQ,QAAQ,GAAG;AAAA,IAC5C;AAWA,IAAAH,kBAAiB,UAAU,iBAAiB,SAAS,OAAO,OAAO;AACjE,UAAI,QAAQ,CAAC;AACb,UAAI,cAAc;AAClB,UAAI,UAAU;AACd,UAAI,SAAS,MAAM,MAAM,KAAK;AAC9B,eAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AAGtC,YAAI,QAAQ,OAAO,CAAC,EAAE,UAAU,CAAC;AACjC,gBAAQ,OAAO,CAAC,EAAE,OAAO,CAAC,GAAG;AAAA,UAC3B,KAAK;AACH,gBAAI;AACF,oBAAM,aAAa,IACf,IAAIA,kBAAiB,KAAKE,cAAa,UAAU,KAAK,CAAC;AAAA,YAC7D,SAAS,IAAI;AAEX,oBAAM,IAAI,MAAM,uCAAuC,KAAK;AAAA,YAC9D;AACA;AAAA,UACF,KAAK;AAAA,UAEL,KAAK;AACH,gBAAI,IAAI,SAAS,OAAO,EAAE;AAC1B,gBAAI,MAAM,CAAC,KAAK,IAAI,GAAG;AACrB,oBAAM,IAAI,MAAM,uCAAuC,KAAK;AAAA,YAC9D;AACA,gBAAI,OAAO,MAAM,UAAU,SAAS,WAAW,CAAC;AAChD,gBAAI,OAAO,CAAC,EAAE,OAAO,CAAC,KAAK,KAAK;AAC9B,oBAAM,aAAa,IAAI,IAAIF,kBAAiB,KAAKG,aAAY,IAAI;AAAA,YACnE,OAAO;AACL,oBAAM,aAAa,IAAI,IAAIH,kBAAiB,KAAKC,cAAa,IAAI;AAAA,YACpE;AACA;AAAA,UACF;AAGE,gBAAI,OAAO,CAAC,GAAG;AACb,oBAAM,IAAI,MAAM,+CACA,OAAO,CAAC,CAAC;AAAA,YAC3B;AAAA,QACJ;AAAA,MACF;AACA,UAAI,WAAW,MAAM,QAAQ;AAC3B,cAAM,IAAI,MAAM,mBAAmB,UAC/B,0CAA0C,MAAM,SAAS,IAAI;AAAA,MACnE;AACA,aAAO;AAAA,IACT;AAaA,IAAAD,kBAAiB,UAAU,aAAa,SAAS,MAAM,SAAS,KAAK;AAEnE,UAAI,QAAQ,QAAQ,WAAW,QAAQ,OAAO,MAAM;AAClD,cAAM,IAAI,MAAM,0BAA0B;AAAA,MAC5C;AAEA,YAAM,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,KAAK,MAAM,CAAC;AAC5C,UAAI,QAAQ,SAAS;AAEnB,eAAO;AAAA,MACT,WAAW,CAAC,KAAK,QAAQ;AAEvB,eAAO;AAAA,MACT,WAAW,KAAK,UAAU,KAAK,MAAM,QAAQ,MAAM,KAAK,SAAS;AAE/D,eAAO;AAAA,MACT,OAAO;AAEL,eAAO,KAAK,aAAa,MAAM,SAAS,GAAG;AAAA,MAC7C;AAAA,IACF;AAYA,IAAAA,kBAAiB,UAAU,eAAe,SAAS,MAAM,SAAS,KAAK;AACrE,UAAI,QAAQ,SAAS,KAAK,eAAe;AACvC,cAAM,IAAI,MAAM,oCAAoC;AAAA,MACtD;AAGA,UAAI,IAAI,KAAK,gBAAgB,OAAO;AAEpC,UAAI,MAAM;AAUV,eAAS,kBAAkB,GAAG,GAAG;AAC/B,YAAI,WAAW,IAAI,QAAQ;AAC3B,YAAI,YAAY,KAAK,IAAI,MAAM,CAAC;AAChC,YAAI,CAAC,IAAI,gBAAgB;AAEvB,iBAAO,YAAY,IAAM;AAAA,QAC3B;AACA,eAAO,WAAY,YAAY,IAAI;AAAA,MACrC;AAGA,UAAI,kBAAkB,KAAK;AAE3B,UAAI,WAAW,KAAK,QAAQ,SAAS,GAAG;AACxC,UAAI,YAAY,IAAI;AAClB,0BAAkB,KAAK,IAAI,kBAAkB,GAAG,QAAQ,GAAG,eAAe;AAE1E,mBAAW,KAAK,YAAY,SAAS,MAAM,QAAQ,MAAM;AACzD,YAAI,YAAY,IAAI;AAClB,4BACI,KAAK,IAAI,kBAAkB,GAAG,QAAQ,GAAG,eAAe;AAAA,QAC9D;AAAA,MACF;AAGA,UAAI,YAAY,KAAM,QAAQ,SAAS;AACvC,iBAAW;AAEX,UAAI,SAAS;AACb,UAAI,UAAU,QAAQ,SAAS,KAAK;AACpC,UAAI;AACJ,eAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AAIvC,kBAAU;AACV,kBAAU;AACV,eAAO,UAAU,SAAS;AACxB,cAAI,kBAAkB,GAAG,MAAM,OAAO,KAAK,iBAAiB;AAC1D,sBAAU;AAAA,UACZ,OAAO;AACL,sBAAU;AAAA,UACZ;AACA,oBAAU,KAAK,OAAO,UAAU,WAAW,IAAI,OAAO;AAAA,QACxD;AAEA,kBAAU;AACV,YAAI,QAAQ,KAAK,IAAI,GAAG,MAAM,UAAU,CAAC;AACzC,YAAI,SAAS,KAAK,IAAI,MAAM,SAAS,KAAK,MAAM,IAAI,QAAQ;AAE5D,YAAI,KAAK,MAAM,SAAS,CAAC;AACzB,WAAG,SAAS,CAAC,KAAK,KAAK,KAAK;AAC5B,iBAASI,KAAI,QAAQA,MAAK,OAAOA,MAAK;AAGpC,cAAI,YAAY,EAAE,KAAK,OAAOA,KAAI,CAAC,CAAC;AACpC,cAAI,MAAM,GAAG;AACX,eAAGA,EAAC,KAAM,GAAGA,KAAI,CAAC,KAAK,IAAK,KAAK;AAAA,UACnC,OAAO;AACL,eAAGA,EAAC,KAAO,GAAGA,KAAI,CAAC,KAAK,IAAK,KAAK,cACvB,QAAQA,KAAI,CAAC,IAAI,QAAQA,EAAC,MAAM,IAAK,KACxC,QAAQA,KAAI,CAAC;AAAA,UACvB;AACA,cAAI,GAAGA,EAAC,IAAI,WAAW;AACrB,gBAAI,QAAQ,kBAAkB,GAAGA,KAAI,CAAC;AAGtC,gBAAI,SAAS,iBAAiB;AAE5B,gCAAkB;AAClB,yBAAWA,KAAI;AACf,kBAAI,WAAW,KAAK;AAElB,wBAAQ,KAAK,IAAI,GAAG,IAAI,MAAM,QAAQ;AAAA,cACxC,OAAO;AAEL;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAEA,YAAI,kBAAkB,IAAI,GAAG,GAAG,IAAI,iBAAiB;AACnD;AAAA,QACF;AACA,kBAAU;AAAA,MACZ;AACA,aAAO;AAAA,IACT;AASA,IAAAJ,kBAAiB,UAAU,kBAAkB,SAAS,SAAS;AAC7D,UAAI,IAAI,CAAC;AACT,eAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,UAAE,QAAQ,OAAO,CAAC,CAAC,IAAI;AAAA,MACzB;AACA,eAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,UAAE,QAAQ,OAAO,CAAC,CAAC,KAAK,KAAM,QAAQ,SAAS,IAAI;AAAA,MACrD;AACA,aAAO;AAAA,IACT;AAaA,IAAAA,kBAAiB,UAAU,oBAAoB,SAAS,OAAO,MAAM;AACnE,UAAI,KAAK,UAAU,GAAG;AACpB;AAAA,MACF;AACA,UAAI,MAAM,WAAW,MAAM;AACzB,cAAM,MAAM,uBAAuB;AAAA,MACrC;AACA,UAAI,UAAU,KAAK,UAAU,MAAM,QAAQ,MAAM,SAAS,MAAM,OAAO;AACvE,UAAI,UAAU;AAId,aAAO,KAAK,QAAQ,OAAO,KAAK,KAAK,YAAY,OAAO,KACjD,QAAQ,SAAS,KAAK,gBAAgB,KAAK,eAC3C,KAAK,cAAc;AACxB,mBAAW,KAAK;AAChB,kBAAU,KAAK;AAAA,UAAU,MAAM,SAAS;AAAA,UACf,MAAM,SAAS,MAAM,UAAU;AAAA,QAAO;AAAA,MACjE;AAEA,iBAAW,KAAK;AAGhB,UAAI,SAAS,KAAK,UAAU,MAAM,SAAS,SAAS,MAAM,MAAM;AAChE,UAAI,QAAQ;AACV,cAAM,MAAM,QAAQ,IAAIA,kBAAiB,KAAKG,aAAY,MAAM,CAAC;AAAA,MACnE;AAEA,UAAI,SAAS,KAAK;AAAA,QAAU,MAAM,SAAS,MAAM;AAAA,QACrB,MAAM,SAAS,MAAM,UAAU;AAAA,MAAO;AAClE,UAAI,QAAQ;AACV,cAAM,MAAM,KAAK,IAAIH,kBAAiB,KAAKG,aAAY,MAAM,CAAC;AAAA,MAChE;AAGA,YAAM,UAAU,OAAO;AACvB,YAAM,UAAU,OAAO;AAEvB,YAAM,WAAW,OAAO,SAAS,OAAO;AACxC,YAAM,WAAW,OAAO,SAAS,OAAO;AAAA,IAC1C;AAyBA,IAAAH,kBAAiB,UAAU,aAAa,SAAS,GAAG,OAAO,OAAO;AAChE,UAAI,OAAO;AACX,UAAI,OAAO,KAAK,YAAY,OAAO,SAAS,YACxC,OAAO,SAAS,aAAa;AAG/B;AAAA,QAA8B;AAC9B,gBAAQ,KAAK;AAAA,UAAU;AAAA;AAAA,UAA6B;AAAA,UAAQ;AAAA,QAAI;AAChE,YAAI,MAAM,SAAS,GAAG;AACpB,eAAK,qBAAqB,KAAK;AAC/B,eAAK,uBAAuB,KAAK;AAAA,QACnC;AAAA,MACF,WAAW,KAAK,OAAO,KAAK,YAAY,OAAO,SAAS,eACpD,OAAO,SAAS,aAAa;AAG/B;AAAA,QAAuD;AACvD,gBAAQ,KAAK,WAAW,KAAK;AAAA,MAC/B,WAAW,OAAO,KAAK,YAAY,SAAS,OAAO,SAAS,YACxD,OAAO,SAAS,aAAa;AAE/B;AAAA,QAA8B;AAC9B;AAAA,QAAuD;AAAA,MACzD,WAAW,OAAO,KAAK,YAAY,OAAO,SAAS,YAC/C,SAAS,OAAO,SAAS,UAAU;AAGrC;AAAA,QAA8B;AAC9B;AAAA,QAAuD;AAAA,MACzD,OAAO;AACL,cAAM,IAAI,MAAM,oCAAoC;AAAA,MACtD;AAEA,UAAI,MAAM,WAAW,GAAG;AACtB,eAAO,CAAC;AAAA,MACV;AACA,UAAI,UAAU,CAAC;AACf,UAAI,QAAQ,IAAIA,kBAAiB,UAAU;AAC3C,UAAI,kBAAkB;AACtB,UAAI,cAAc;AAClB,UAAI,cAAc;AAIlB,UAAI,gBAAgB;AACpB,UAAI,iBAAiB;AACrB,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,YAAI,YAAY,MAAM,CAAC,EAAE,CAAC;AAC1B,YAAI,YAAY,MAAM,CAAC,EAAE,CAAC;AAE1B,YAAI,CAAC,mBAAmB,cAAcG,aAAY;AAEhD,gBAAM,SAAS;AACf,gBAAM,SAAS;AAAA,QACjB;AAEA,gBAAQ,WAAW;AAAA,UACjB,KAAKD;AACH,kBAAM,MAAM,iBAAiB,IAAI,MAAM,CAAC;AACxC,kBAAM,WAAW,UAAU;AAC3B,6BAAiB,eAAe,UAAU,GAAG,WAAW,IAAI,YAC3C,eAAe,UAAU,WAAW;AACrD;AAAA,UACF,KAAKD;AACH,kBAAM,WAAW,UAAU;AAC3B,kBAAM,MAAM,iBAAiB,IAAI,MAAM,CAAC;AACxC,6BAAiB,eAAe,UAAU,GAAG,WAAW,IACvC,eAAe,UAAU,cACrB,UAAU,MAAM;AACrC;AAAA,UACF,KAAKE;AACH,gBAAI,UAAU,UAAU,IAAI,KAAK,gBAC7B,mBAAmB,MAAM,UAAU,IAAI,GAAG;AAE5C,oBAAM,MAAM,iBAAiB,IAAI,MAAM,CAAC;AACxC,oBAAM,WAAW,UAAU;AAC3B,oBAAM,WAAW,UAAU;AAAA,YAC7B,WAAW,UAAU,UAAU,IAAI,KAAK,cAAc;AAEpD,kBAAI,iBAAiB;AACnB,qBAAK,kBAAkB,OAAO,aAAa;AAC3C,wBAAQ,KAAK,KAAK;AAClB,wBAAQ,IAAIH,kBAAiB,UAAU;AACvC,kCAAkB;AAKlB,gCAAgB;AAChB,8BAAc;AAAA,cAChB;AAAA,YACF;AACA;AAAA,QACJ;AAGA,YAAI,cAAcE,cAAa;AAC7B,yBAAe,UAAU;AAAA,QAC3B;AACA,YAAI,cAAcD,cAAa;AAC7B,yBAAe,UAAU;AAAA,QAC3B;AAAA,MACF;AAEA,UAAI,iBAAiB;AACnB,aAAK,kBAAkB,OAAO,aAAa;AAC3C,gBAAQ,KAAK,KAAK;AAAA,MACpB;AAEA,aAAO;AAAA,IACT;AAQA,IAAAD,kBAAiB,UAAU,iBAAiB,SAAS,SAAS;AAE5D,UAAI,cAAc,CAAC;AACnB,eAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,YAAI,QAAQ,QAAQ,CAAC;AACrB,YAAI,YAAY,IAAIA,kBAAiB,UAAU;AAC/C,kBAAU,QAAQ,CAAC;AACnB,iBAAS,IAAI,GAAG,IAAI,MAAM,MAAM,QAAQ,KAAK;AAC3C,oBAAU,MAAM,CAAC,IACb,IAAIA,kBAAiB,KAAK,MAAM,MAAM,CAAC,EAAE,CAAC,GAAG,MAAM,MAAM,CAAC,EAAE,CAAC,CAAC;AAAA,QACpE;AACA,kBAAU,SAAS,MAAM;AACzB,kBAAU,SAAS,MAAM;AACzB,kBAAU,UAAU,MAAM;AAC1B,kBAAU,UAAU,MAAM;AAC1B,oBAAY,CAAC,IAAI;AAAA,MACnB;AACA,aAAO;AAAA,IACT;AAWA,IAAAA,kBAAiB,UAAU,cAAc,SAAS,SAAS,MAAM;AAC/D,UAAI,QAAQ,UAAU,GAAG;AACvB,eAAO,CAAC,MAAM,CAAC,CAAC;AAAA,MAClB;AAGA,gBAAU,KAAK,eAAe,OAAO;AAErC,UAAI,cAAc,KAAK,iBAAiB,OAAO;AAC/C,aAAO,cAAc,OAAO;AAE5B,WAAK,eAAe,OAAO;AAK3B,UAAI,QAAQ;AACZ,UAAI,UAAU,CAAC;AACf,eAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,YAAI,eAAe,QAAQ,CAAC,EAAE,SAAS;AACvC,YAAI,QAAQ,KAAK,WAAW,QAAQ,CAAC,EAAE,KAAK;AAC5C,YAAI;AACJ,YAAI,UAAU;AACd,YAAI,MAAM,SAAS,KAAK,eAAe;AAGrC,sBAAY,KAAK;AAAA,YAAW;AAAA,YAAM,MAAM,UAAU,GAAG,KAAK,aAAa;AAAA,YAC3C;AAAA,UAAY;AACxC,cAAI,aAAa,IAAI;AACnB,sBAAU,KAAK;AAAA,cAAW;AAAA,cACtB,MAAM,UAAU,MAAM,SAAS,KAAK,aAAa;AAAA,cACjD,eAAe,MAAM,SAAS,KAAK;AAAA,YAAa;AACpD,gBAAI,WAAW,MAAM,aAAa,SAAS;AAEzC,0BAAY;AAAA,YACd;AAAA,UACF;AAAA,QACF,OAAO;AACL,sBAAY,KAAK,WAAW,MAAM,OAAO,YAAY;AAAA,QACvD;AACA,YAAI,aAAa,IAAI;AAEnB,kBAAQ,CAAC,IAAI;AAEb,mBAAS,QAAQ,CAAC,EAAE,UAAU,QAAQ,CAAC,EAAE;AAAA,QAC3C,OAAO;AAEL,kBAAQ,CAAC,IAAI;AACb,kBAAQ,YAAY;AACpB,cAAI;AACJ,cAAI,WAAW,IAAI;AACjB,oBAAQ,KAAK,UAAU,WAAW,YAAY,MAAM,MAAM;AAAA,UAC5D,OAAO;AACL,oBAAQ,KAAK,UAAU,WAAW,UAAU,KAAK,aAAa;AAAA,UAChE;AACA,cAAI,SAAS,OAAO;AAElB,mBAAO,KAAK,UAAU,GAAG,SAAS,IAC3B,KAAK,WAAW,QAAQ,CAAC,EAAE,KAAK,IAChC,KAAK,UAAU,YAAY,MAAM,MAAM;AAAA,UAChD,OAAO;AAGL,gBAAI,QAAQ,KAAK,UAAU,OAAO,OAAO,KAAK;AAC9C,gBAAI,MAAM,SAAS,KAAK,iBACpB,KAAK,iBAAiB,KAAK,IAAI,MAAM,SACrC,KAAK,uBAAuB;AAE9B,sBAAQ,CAAC,IAAI;AAAA,YACf,OAAO;AACL,mBAAK,6BAA6B,KAAK;AACvC,kBAAI,SAAS;AACb,kBAAI;AACJ,uBAAS,IAAI,GAAG,IAAI,QAAQ,CAAC,EAAE,MAAM,QAAQ,KAAK;AAChD,oBAAI,MAAM,QAAQ,CAAC,EAAE,MAAM,CAAC;AAC5B,oBAAI,IAAI,CAAC,MAAMG,aAAY;AACzB,2BAAS,KAAK,YAAY,OAAO,MAAM;AAAA,gBACzC;AACA,oBAAI,IAAI,CAAC,MAAMD,cAAa;AAC1B,yBAAO,KAAK,UAAU,GAAG,YAAY,MAAM,IAAI,IAAI,CAAC,IAC7C,KAAK,UAAU,YAAY,MAAM;AAAA,gBAC1C,WAAW,IAAI,CAAC,MAAMD,cAAa;AACjC,yBAAO,KAAK,UAAU,GAAG,YAAY,MAAM,IACpC,KAAK,UAAU,YAAY,KAAK;AAAA,oBAAY;AAAA,oBACxC,SAAS,IAAI,CAAC,EAAE;AAAA,kBAAM,CAAC;AAAA,gBACpC;AACA,oBAAI,IAAI,CAAC,MAAMA,cAAa;AAC1B,4BAAU,IAAI,CAAC,EAAE;AAAA,gBACnB;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAEA,aAAO,KAAK,UAAU,YAAY,QAAQ,KAAK,SAAS,YAAY,MAAM;AAC1E,aAAO,CAAC,MAAM,OAAO;AAAA,IACvB;AASA,IAAAD,kBAAiB,UAAU,mBAAmB,SAAS,SAAS;AAC9D,UAAI,gBAAgB,KAAK;AACzB,UAAI,cAAc;AAClB,eAAS,IAAI,GAAG,KAAK,eAAe,KAAK;AACvC,uBAAe,OAAO,aAAa,CAAC;AAAA,MACtC;AAGA,eAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,gBAAQ,CAAC,EAAE,UAAU;AACrB,gBAAQ,CAAC,EAAE,UAAU;AAAA,MACvB;AAGA,UAAI,QAAQ,QAAQ,CAAC;AACrB,UAAI,QAAQ,MAAM;AAClB,UAAI,MAAM,UAAU,KAAK,MAAM,CAAC,EAAE,CAAC,KAAKG,aAAY;AAElD,cAAM,QAAQ,IAAIH,kBAAiB,KAAKG,aAAY,WAAW,CAAC;AAChE,cAAM,UAAU;AAChB,cAAM,UAAU;AAChB,cAAM,WAAW;AACjB,cAAM,WAAW;AAAA,MACnB,WAAW,gBAAgB,MAAM,CAAC,EAAE,CAAC,EAAE,QAAQ;AAE7C,YAAI,cAAc,gBAAgB,MAAM,CAAC,EAAE,CAAC,EAAE;AAC9C,cAAM,CAAC,EAAE,CAAC,IAAI,YAAY,UAAU,MAAM,CAAC,EAAE,CAAC,EAAE,MAAM,IAAI,MAAM,CAAC,EAAE,CAAC;AACpE,cAAM,UAAU;AAChB,cAAM,UAAU;AAChB,cAAM,WAAW;AACjB,cAAM,WAAW;AAAA,MACnB;AAGA,cAAQ,QAAQ,QAAQ,SAAS,CAAC;AAClC,cAAQ,MAAM;AACd,UAAI,MAAM,UAAU,KAAK,MAAM,MAAM,SAAS,CAAC,EAAE,CAAC,KAAKA,aAAY;AAEjE,cAAM,KAAK,IAAIH,kBAAiB,KAAKG,aAAY,WAAW,CAAC;AAC7D,cAAM,WAAW;AACjB,cAAM,WAAW;AAAA,MACnB,WAAW,gBAAgB,MAAM,MAAM,SAAS,CAAC,EAAE,CAAC,EAAE,QAAQ;AAE5D,YAAI,cAAc,gBAAgB,MAAM,MAAM,SAAS,CAAC,EAAE,CAAC,EAAE;AAC7D,cAAM,MAAM,SAAS,CAAC,EAAE,CAAC,KAAK,YAAY,UAAU,GAAG,WAAW;AAClE,cAAM,WAAW;AACjB,cAAM,WAAW;AAAA,MACnB;AAEA,aAAO;AAAA,IACT;AASA,IAAAH,kBAAiB,UAAU,iBAAiB,SAAS,SAAS;AAC5D,UAAI,aAAa,KAAK;AACtB,eAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,YAAI,QAAQ,CAAC,EAAE,WAAW,YAAY;AACpC;AAAA,QACF;AACA,YAAI,WAAW,QAAQ,CAAC;AAExB,gBAAQ,OAAO,KAAK,CAAC;AACrB,YAAI,SAAS,SAAS;AACtB,YAAI,SAAS,SAAS;AACtB,YAAI,aAAa;AACjB,eAAO,SAAS,MAAM,WAAW,GAAG;AAElC,cAAI,QAAQ,IAAIA,kBAAiB,UAAU;AAC3C,cAAI,QAAQ;AACZ,gBAAM,SAAS,SAAS,WAAW;AACnC,gBAAM,SAAS,SAAS,WAAW;AACnC,cAAI,eAAe,IAAI;AACrB,kBAAM,UAAU,MAAM,UAAU,WAAW;AAC3C,kBAAM,MAAM,KAAK,IAAIA,kBAAiB,KAAKG,aAAY,UAAU,CAAC;AAAA,UACpE;AACA,iBAAO,SAAS,MAAM,WAAW,KAC1B,MAAM,UAAU,aAAa,KAAK,cAAc;AACrD,gBAAI,YAAY,SAAS,MAAM,CAAC,EAAE,CAAC;AACnC,gBAAI,YAAY,SAAS,MAAM,CAAC,EAAE,CAAC;AACnC,gBAAI,cAAcD,cAAa;AAE7B,oBAAM,WAAW,UAAU;AAC3B,wBAAU,UAAU;AACpB,oBAAM,MAAM,KAAK,SAAS,MAAM,MAAM,CAAC;AACvC,sBAAQ;AAAA,YACV,WAAW,cAAcD,gBAAe,MAAM,MAAM,UAAU,KACnD,MAAM,MAAM,CAAC,EAAE,CAAC,KAAKE,eACrB,UAAU,SAAS,IAAI,YAAY;AAE5C,oBAAM,WAAW,UAAU;AAC3B,wBAAU,UAAU;AACpB,sBAAQ;AACR,oBAAM,MAAM,KAAK,IAAIH,kBAAiB,KAAK,WAAW,SAAS,CAAC;AAChE,uBAAS,MAAM,MAAM;AAAA,YACvB,OAAO;AAEL,0BAAY,UAAU;AAAA,gBAAU;AAAA,gBAC5B,aAAa,MAAM,UAAU,KAAK;AAAA,cAAY;AAClD,oBAAM,WAAW,UAAU;AAC3B,wBAAU,UAAU;AACpB,kBAAI,cAAcG,aAAY;AAC5B,sBAAM,WAAW,UAAU;AAC3B,0BAAU,UAAU;AAAA,cACtB,OAAO;AACL,wBAAQ;AAAA,cACV;AACA,oBAAM,MAAM,KAAK,IAAIH,kBAAiB,KAAK,WAAW,SAAS,CAAC;AAChE,kBAAI,aAAa,SAAS,MAAM,CAAC,EAAE,CAAC,GAAG;AACrC,yBAAS,MAAM,MAAM;AAAA,cACvB,OAAO;AACL,yBAAS,MAAM,CAAC,EAAE,CAAC,IACf,SAAS,MAAM,CAAC,EAAE,CAAC,EAAE,UAAU,UAAU,MAAM;AAAA,cACrD;AAAA,YACF;AAAA,UACF;AAEA,uBAAa,KAAK,WAAW,MAAM,KAAK;AACxC,uBACI,WAAW,UAAU,WAAW,SAAS,KAAK,YAAY;AAE9D,cAAI,cAAc,KAAK,WAAW,SAAS,KAAK,EACzB,UAAU,GAAG,KAAK,YAAY;AACrD,cAAI,gBAAgB,IAAI;AACtB,kBAAM,WAAW,YAAY;AAC7B,kBAAM,WAAW,YAAY;AAC7B,gBAAI,MAAM,MAAM,WAAW,KACvB,MAAM,MAAM,MAAM,MAAM,SAAS,CAAC,EAAE,CAAC,MAAMG,aAAY;AACzD,oBAAM,MAAM,MAAM,MAAM,SAAS,CAAC,EAAE,CAAC,KAAK;AAAA,YAC5C,OAAO;AACL,oBAAM,MAAM,KAAK,IAAIH,kBAAiB,KAAKG,aAAY,WAAW,CAAC;AAAA,YACrE;AAAA,UACF;AACA,cAAI,CAAC,OAAO;AACV,oBAAQ,OAAO,EAAE,GAAG,GAAG,KAAK;AAAA,UAC9B;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAQA,IAAAH,kBAAiB,UAAU,eAAe,SAAS,SAAS;AAC1D,UAAI,OAAO,CAAC;AACZ,eAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,aAAK,CAAC,IAAI,QAAQ,CAAC;AAAA,MACrB;AACA,aAAO,KAAK,KAAK,EAAE;AAAA,IACrB;AASA,IAAAA,kBAAiB,UAAU,iBAAiB,SAAS,UAAU;AAC7D,UAAI,UAAU,CAAC;AACf,UAAI,CAAC,UAAU;AACb,eAAO;AAAA,MACT;AACA,UAAI,OAAO,SAAS,MAAM,IAAI;AAC9B,UAAI,cAAc;AAClB,UAAI,cAAc;AAClB,aAAO,cAAc,KAAK,QAAQ;AAChC,YAAI,IAAI,KAAK,WAAW,EAAE,MAAM,WAAW;AAC3C,YAAI,CAAC,GAAG;AACN,gBAAM,IAAI,MAAM,2BAA2B,KAAK,WAAW,CAAC;AAAA,QAC9D;AACA,YAAI,QAAQ,IAAIA,kBAAiB,UAAU;AAC3C,gBAAQ,KAAK,KAAK;AAClB,cAAM,SAAS,SAAS,EAAE,CAAC,GAAG,EAAE;AAChC,YAAI,EAAE,CAAC,MAAM,IAAI;AACf,gBAAM;AACN,gBAAM,UAAU;AAAA,QAClB,WAAW,EAAE,CAAC,KAAK,KAAK;AACtB,gBAAM,UAAU;AAAA,QAClB,OAAO;AACL,gBAAM;AACN,gBAAM,UAAU,SAAS,EAAE,CAAC,GAAG,EAAE;AAAA,QACnC;AAEA,cAAM,SAAS,SAAS,EAAE,CAAC,GAAG,EAAE;AAChC,YAAI,EAAE,CAAC,MAAM,IAAI;AACf,gBAAM;AACN,gBAAM,UAAU;AAAA,QAClB,WAAW,EAAE,CAAC,KAAK,KAAK;AACtB,gBAAM,UAAU;AAAA,QAClB,OAAO;AACL,gBAAM;AACN,gBAAM,UAAU,SAAS,EAAE,CAAC,GAAG,EAAE;AAAA,QACnC;AACA;AAEA,eAAO,cAAc,KAAK,QAAQ;AAChC,cAAI,OAAO,KAAK,WAAW,EAAE,OAAO,CAAC;AACrC,cAAI;AACF,gBAAI,OAAO,UAAU,KAAK,WAAW,EAAE,UAAU,CAAC,CAAC;AAAA,UACrD,SAAS,IAAI;AAEX,kBAAM,IAAI,MAAM,uCAAuC,IAAI;AAAA,UAC7D;AACA,cAAI,QAAQ,KAAK;AAEf,kBAAM,MAAM,KAAK,IAAIA,kBAAiB,KAAKC,cAAa,IAAI,CAAC;AAAA,UAC/D,WAAW,QAAQ,KAAK;AAEtB,kBAAM,MAAM,KAAK,IAAID,kBAAiB,KAAKE,cAAa,IAAI,CAAC;AAAA,UAC/D,WAAW,QAAQ,KAAK;AAEtB,kBAAM,MAAM,KAAK,IAAIF,kBAAiB,KAAKG,aAAY,IAAI,CAAC;AAAA,UAC9D,WAAW,QAAQ,KAAK;AAEtB;AAAA,UACF,WAAW,SAAS,IAAI;AAAA,UAExB,OAAO;AAEL,kBAAM,IAAI,MAAM,yBAAyB,OAAO,WAAW,IAAI;AAAA,UACjE;AACA;AAAA,QACF;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAOA,IAAAH,kBAAiB,YAAY,WAAW;AAEtC,WAAK,QAAQ,CAAC;AAEd,WAAK,SAAS;AAEd,WAAK,SAAS;AAEd,WAAK,UAAU;AAEf,WAAK,UAAU;AAAA,IACjB;AASA,IAAAA,kBAAiB,UAAU,UAAU,WAAW,WAAW;AACzD,UAAI,SAAS;AACb,UAAI,KAAK,YAAY,GAAG;AACtB,kBAAU,KAAK,SAAS;AAAA,MAC1B,WAAW,KAAK,WAAW,GAAG;AAC5B,kBAAU,KAAK,SAAS;AAAA,MAC1B,OAAO;AACL,kBAAW,KAAK,SAAS,IAAK,MAAM,KAAK;AAAA,MAC3C;AACA,UAAI,KAAK,YAAY,GAAG;AACtB,kBAAU,KAAK,SAAS;AAAA,MAC1B,WAAW,KAAK,WAAW,GAAG;AAC5B,kBAAU,KAAK,SAAS;AAAA,MAC1B,OAAO;AACL,kBAAW,KAAK,SAAS,IAAK,MAAM,KAAK;AAAA,MAC3C;AACA,UAAI,OAAO,CAAC,SAAS,UAAU,OAAO,UAAU,OAAO;AACvD,UAAI;AAEJ,eAAS,IAAI,GAAG,IAAI,KAAK,MAAM,QAAQ,KAAK;AAC1C,gBAAQ,KAAK,MAAM,CAAC,EAAE,CAAC,GAAG;AAAA,UACxB,KAAKE;AACH,iBAAK;AACL;AAAA,UACF,KAAKD;AACH,iBAAK;AACL;AAAA,UACF,KAAKE;AACH,iBAAK;AACL;AAAA,QACJ;AACA,aAAK,IAAI,CAAC,IAAI,KAAK,UAAU,KAAK,MAAM,CAAC,EAAE,CAAC,CAAC,IAAI;AAAA,MACnD;AACA,aAAO,KAAK,KAAK,EAAE,EAAE,QAAQ,QAAQ,GAAG;AAAA,IAC1C;AAIA,WAAO,UAAUH;AACjB,WAAO,QAAQ,kBAAkB,IAAIA;AACrC,WAAO,QAAQ,aAAa,IAAIC;AAChC,WAAO,QAAQ,aAAa,IAAIC;AAChC,WAAO,QAAQ,YAAY,IAAIC;AAAA;AAAA;;;ACzqE/B;AAAA;AAGA,KAAC,SAAS,KAAK;AACb,UAAI,OAAO,WAAW,YAAY,OAAO,UAAU;AACjD,YAAI,oBAA+B;AAAA,eAC5B,OAAO,UAAU,cAAc,OAAO;AAC7C,eAAO,CAAC,sBAAsB,GAAG,GAAG;AAAA;AAEpC,YAAI,UAAU;AAAA,IAClB,GAAG,SAASI,aAAY;AACtB;AAEA,MAAAA,YAAW,mBAAmB,SAAS,MAAM,QAAQ;AACnD,QAAAA,YAAW,WAAW,MAAM,SAAS,QAAQ;AAC3C,iBAAOA,YAAW,WAAW,QAAQ,MAAM;AAAA,QAC7C,CAAC;AAAA,MACH;AAEA,MAAAA,YAAW,aAAa,SAAS,QAAQ,QAAQ;AAC/C,oBAAY,QAAQ,OAAO;AAC3B,YAAI,UAAU,CAAC,GAAG,OAAO,OAAO,QAAQ,CAAC,GAAG,iBAAiB;AAC7D,iBAAS,SAAS;AAAQ,cAAI,SAAS,QAAQ,OAAO,eAAe,KAAK,GAAG;AAC3E,gBAAI,OAAO,QAAQ,KAAK,IAAI,CAAC,GAAG,OAAO,OAAO,KAAK;AACnD,qBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,kBAAI,OAAO,KAAK,CAAC;AACjB,mBAAK,KAAK,IAAI,KAAK,MAAM,MAAM,CAAC;AAChC,kBAAI,KAAK,UAAU,KAAK;AAAQ,iCAAiB;AAAA,YACnD;AAAA,UACF;AACA,YAAI,OAAO;AAAA,UACT,YAAY,WAAW;AACrB,mBAAO;AAAA,cAAC,OAAO;AAAA,cAAS,SAAS;AAAA,cACzB,OAAO;AAAA,cAAM,YAAY;AAAA,cACzB,QAAQ,iBAAiB,CAAC,IAAI;AAAA,YAAI;AAAA,UAC5C;AAAA,UACA,WAAW,SAASC,QAAO;AACzB,gBAAI,IAAI;AAAA,cAAC,OAAOA,OAAM;AAAA,cAAO,SAASA,OAAM;AAAA,cACnC,OAAOA,OAAM;AAAA,cAAO,YAAY;AAAA,cAChC,QAAQA,OAAM,UAAUA,OAAM,OAAO,MAAM,CAAC;AAAA,YAAC;AACtD,gBAAIA,OAAM;AACR,gBAAE,aAAaD,YAAW,UAAUC,OAAM,MAAM,MAAMA,OAAM,UAAU;AACxE,gBAAIA,OAAM;AACR,gBAAE,QAAQA,OAAM,MAAM,MAAM,CAAC;AAC/B,qBAAS,OAAOA,OAAM,kBAAkB,MAAM,OAAO,KAAK;AACxD,gBAAE,mBAAmB;AAAA,gBAAC,MAAM,KAAK;AAAA,gBACX,MAAM,KAAK;AAAA,gBACX,OAAO,KAAK,SAASA,OAAM,aAAa,EAAE,aAAaD,YAAW,UAAU,KAAK,MAAM,KAAK,KAAK;AAAA,gBACjG,MAAM,EAAE;AAAA,cAAgB;AAChD,mBAAO;AAAA,UACT;AAAA,UACA,OAAO,cAAc,SAAS,MAAM;AAAA,UACpC,WAAW,SAASC,QAAO;AAAE,mBAAOA,OAAM,SAAS,EAAC,MAAMA,OAAM,MAAM,MAAM,OAAOA,OAAM,WAAU;AAAA,UAAG;AAAA,UACtG,QAAQ,eAAe,SAAS,IAAI;AAAA,QACtC;AACA,YAAI;AAAM,mBAAS,QAAQ;AAAM,gBAAI,KAAK,eAAe,IAAI;AAC3D,mBAAK,IAAI,IAAI,KAAK,IAAI;AAAA;AACxB,eAAO;AAAA,MACT;AAEA,eAAS,YAAY,QAAQ,MAAM;AACjC,YAAI,CAAC,OAAO,eAAe,IAAI;AAC7B,gBAAM,IAAI,MAAM,qBAAqB,OAAO,iBAAiB;AAAA,MACjE;AAEA,eAAS,QAAQ,KAAK,OAAO;AAC3B,YAAI,CAAC;AAAK,iBAAO;AACjB,YAAI,QAAQ;AACZ,YAAI,eAAe,QAAQ;AACzB,cAAI,IAAI;AAAY,oBAAQ;AAC5B,cAAI,IAAI;AAAS,qBAAS;AAC1B,gBAAM,IAAI;AAAA,QACZ,OAAO;AACL,gBAAM,OAAO,GAAG;AAAA,QAClB;AACA,eAAO,IAAI,QAAQ,UAAU,QAAQ,KAAK,OAAO,QAAQ,MAAM,KAAK,KAAK;AAAA,MAC3E;AAEA,eAAS,QAAQ,KAAK;AACpB,YAAI,CAAC;AAAK,iBAAO;AACjB,YAAI,IAAI;AAAO,iBAAO;AACtB,YAAI,OAAO,OAAO;AAAU,iBAAO,IAAI,QAAQ,OAAO,GAAG;AACzD,YAAI,SAAS,CAAC;AACd,iBAAS,IAAI,GAAG,IAAI,IAAI,QAAQ;AAC9B,iBAAO,KAAK,IAAI,CAAC,KAAK,IAAI,CAAC,EAAE,QAAQ,OAAO,GAAG,CAAC;AAClD,eAAO;AAAA,MACT;AAEA,eAAS,KAAK,MAAM,QAAQ;AAC1B,YAAI,KAAK,QAAQ,KAAK;AAAM,sBAAY,QAAQ,KAAK,QAAQ,KAAK,IAAI;AACtE,aAAK,QAAQ,QAAQ,KAAK,KAAK;AAC/B,aAAK,QAAQ,QAAQ,KAAK,KAAK;AAC/B,aAAK,OAAO;AAAA,MACd;AAEA,eAAS,cAAc,QAAQ,QAAQ;AACrC,eAAO,SAAS,QAAQ,OAAO;AAC7B,cAAI,MAAM,SAAS;AACjB,gBAAI,OAAO,MAAM,QAAQ,MAAM;AAC/B,gBAAI,MAAM,QAAQ,UAAU;AAAG,oBAAM,UAAU;AAC/C,mBAAO,OAAO,KAAK,KAAK;AACxB,mBAAO,KAAK;AAAA,UACd;AAEA,cAAI,MAAM,OAAO;AACf,gBAAI,MAAM,MAAM,OAAO,OAAO,MAAM,MAAM,MAAM,GAAG,GAAG;AACpD,kBAAI,MAAM,MAAM,MAAM,YAAY;AAClC,oBAAM,QAAQ,MAAM,aAAa;AACjC,qBAAO;AAAA,YACT,OAAO;AACL,kBAAI,MAAM,MAAM,MAAM,KAAK,MAAM,QAAQ,MAAM,UAAU,GAAG;AAC5D,kBAAI,MAAM,MAAM,YAAY,IAAI,MAAM,MAAM,QAAQ,KAAK,OAAO,QAAQ,CAAC;AACvE,uBAAO,MAAM,OAAO,QAAQ,EAAE;AAChC,qBAAO;AAAA,YACT;AAAA,UACF;AAEA,cAAI,WAAW,OAAO,MAAM,KAAK;AACjC,mBAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,gBAAI,OAAO,SAAS,CAAC;AACrB,gBAAI,WAAW,CAAC,KAAK,KAAK,OAAO,OAAO,IAAI,MAAM,OAAO,MAAM,KAAK,KAAK;AACzE,gBAAI,SAAS;AACX,kBAAI,KAAK,KAAK,MAAM;AAClB,sBAAM,QAAQ,KAAK,KAAK;AAAA,cAC1B,WAAW,KAAK,KAAK,MAAM;AACzB,iBAAC,MAAM,UAAU,MAAM,QAAQ,CAAC,IAAI,KAAK,MAAM,KAAK;AACpD,sBAAM,QAAQ,KAAK,KAAK;AAAA,cAC1B,WAAW,KAAK,KAAK,OAAO,MAAM,SAAS,MAAM,MAAM,QAAQ;AAC7D,sBAAM,QAAQ,MAAM,MAAM,IAAI;AAAA,cAChC;AAEA,kBAAI,KAAK,KAAK;AACZ,+BAAe,QAAQ,OAAO,KAAK,KAAK,MAAM,KAAK,KAAK;AAC1D,kBAAI,KAAK,KAAK;AACZ,sBAAM,OAAO,KAAK,OAAO,YAAY,IAAI,OAAO,UAAU;AAC5D,kBAAI,KAAK,KAAK;AACZ,sBAAM,OAAO,IAAI;AACnB,kBAAI,QAAQ,KAAK;AACjB,kBAAI,SAAS,MAAM;AAAO,wBAAQ,MAAM,OAAO;AAC/C,kBAAI,QAAQ,SAAS,KAAK,KAAK,SAAS,OAAO,KAAK,SAAS,UAAU;AACrE,yBAASC,KAAI,GAAGA,KAAI,QAAQ,QAAQA;AAClC,sBAAI,QAAQA,EAAC;AACX,qBAAC,MAAM,YAAY,MAAM,UAAU,CAAC,IAAI,KAAK,EAAC,MAAM,QAAQA,EAAC,GAAG,OAAO,KAAK,MAAMA,KAAI,CAAC,EAAC,CAAC;AAC7F,uBAAO,OAAO,QAAQ,CAAC,EAAE,UAAU,QAAQ,CAAC,IAAI,QAAQ,CAAC,EAAE,SAAS,EAAE;AACtE,uBAAO,MAAM,CAAC;AAAA,cAChB,WAAW,SAAS,MAAM,MAAM;AAC9B,uBAAO,MAAM,CAAC;AAAA,cAChB,OAAO;AACL,uBAAO;AAAA,cACT;AAAA,YACF;AAAA,UACF;AACA,iBAAO,KAAK;AACZ,iBAAO;AAAA,QACT;AAAA,MACF;AAEA,eAAS,IAAI,GAAGC,IAAG;AACjB,YAAI,MAAMA;AAAG,iBAAO;AACpB,YAAI,CAAC,KAAK,OAAO,KAAK,YAAY,CAACA,MAAK,OAAOA,MAAK;AAAU,iBAAO;AACrE,YAAI,QAAQ;AACZ,iBAAS,QAAQ;AAAG,cAAI,EAAE,eAAe,IAAI,GAAG;AAC9C,gBAAI,CAACA,GAAE,eAAe,IAAI,KAAK,CAAC,IAAI,EAAE,IAAI,GAAGA,GAAE,IAAI,CAAC;AAAG,qBAAO;AAC9D;AAAA,UACF;AACA,iBAAS,QAAQA;AAAG,cAAIA,GAAE,eAAe,IAAI;AAAG;AAChD,eAAO,SAAS;AAAA,MAClB;AAEA,eAAS,eAAe,QAAQ,OAAO,MAAM,OAAO;AAClD,YAAI;AACJ,YAAI,KAAK;AAAY,mBAAS,IAAI,MAAM,kBAAkB,KAAK,CAAC,MAAM,IAAI,EAAE;AAC1E,gBAAI,KAAK,OAAO,IAAI,KAAK,MAAM,EAAE,IAAI,IAAI,KAAK,QAAQ,EAAE;AAAM,qBAAO;AAAA;AACvE,YAAI,OAAO,OAAO,KAAK,OAAO,KAAK,QAAQH,YAAW,QAAQ,QAAQ,KAAK,IAAI;AAC/E,YAAI,SAAS,OAAO,KAAK,QAAQA,YAAW,WAAW,IAAI;AAC3D,YAAI,KAAK,cAAc,CAAC;AACtB,gBAAM,mBAAmB,EAAC,MAAY,MAAM,KAAK,MAAM,OAAO,QAAQ,MAAM,MAAM,iBAAgB;AAEpG,cAAM,aAAa;AACnB,cAAM,QAAQ;AAAA,UAAC;AAAA,UACA,KAAK,KAAK,OAAO,QAAQ,KAAK,GAAG;AAAA,UACjC,SAAS,KAAK,OAAO,KAAK,aAAa,SAAS,QAAQ,KAAK,KAAK,KAAK;AAAA,UACvE,UAAU,SAAS,MAAM,OAAO,MAAM,MAAM,SAAS,CAAC,IAAI;AAAA,QAAK;AAAA,MAChF;AAEA,eAAS,QAAQ,KAAK,KAAK;AACzB,iBAAS,IAAI,GAAG,IAAI,IAAI,QAAQ;AAAK,cAAI,IAAI,CAAC,MAAM;AAAK,mBAAO;AAAA,MAClE;AAEA,eAAS,eAAe,QAAQ,MAAM;AACpC,eAAO,SAAS,OAAO,WAAW,MAAM;AACtC,cAAI,MAAM,SAAS,MAAM,MAAM,KAAK;AAClC,mBAAO,MAAM,MAAM,KAAK,OAAO,MAAM,YAAY,WAAW,IAAI;AAClE,cAAI,MAAM,UAAU,QAAQ,MAAM,SAAS,KAAK,oBAAoB,QAAQ,MAAM,OAAO,KAAK,gBAAgB,IAAI;AAChH,mBAAOA,YAAW;AAEpB,cAAI,MAAM,MAAM,OAAO,SAAS,GAAG,QAAQ,OAAO,MAAM,KAAK;AAC7D;AAAM,uBAAS;AACb,uBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,oBAAI,OAAO,MAAM,CAAC;AAClB,oBAAI,KAAK,KAAK,UAAU,KAAK,KAAK,sBAAsB,OAAO;AAC7D,sBAAI,IAAI,KAAK,MAAM,KAAK,SAAS;AACjC,sBAAI,KAAK,EAAE,CAAC,GAAG;AACb;AACA,wBAAI,KAAK,QAAQ,KAAK;AAAM,8BAAQ,OAAO,KAAK,QAAQ,KAAK,IAAI;AACjE,gCAAY,UAAU,MAAM,EAAE,CAAC,EAAE,MAAM;AACvC,6BAAS;AAAA,kBACX;AAAA,gBACF;AAAA,cACF;AACA;AAAA,YACF;AACA,iBAAO,MAAM,IAAI,IAAI,MAAM,OAAO,GAAG;AAAA,QACvC;AAAA,MACF;AAAA,IACF,CAAC;AAAA;AAAA;;;ACpND,sBAAO;AACP,IAAAI,qBAAO;AACP,wBAAO;AACP,yBAAO;AACP,IAAAC,qBAAc;AAEd,IAAAC,gBAAO;AACP,8BAAe;AACf,oBAAO;AAVP,OAAO;AACP,OAAO;AAMP,OAAO;AAIP,CAAC,OAAO,eAAe,OAAO,aAAa,mBAAAC;AAC3C,IAAM,IAAI,OAAO,cAAc,mBAAAA;AAA/B,IAAkC,KAAK,gBAAE;AAAA,EACvC,MAAM;AAAA,EACN,OAAO;AAAA,IACL,MAAM;AAAA,MACJ,MAAM;AAAA,MACN,SAAS,eAAe,CAAiB,oBAAI,KAAK,CAAC;AAAA,IACrD;AAAA,IACA,OAAO;AAAA,MACL,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,SAAS;AAAA,MACP,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,SAAS;AAAA,MACP,MAAM;AAAA,MACN,SAAS,OAAO,CAAC;AAAA,IACnB;AAAA,IACA,YAAY;AAAA,MACV,MAAM;AAAA,MACN,SAAS,MAAM;AAAA,IACjB;AAAA,IACA,aAAa;AAAA,MACX,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,EACF;AAAA,EACA,OAAO;AAAA,IACL,OAAO,CAAC,MAAM;AAAA,IACd,qBAAqB,CAAC,MAAM;AAAA,EAC9B;AAAA,EACA,MAAM,GAAG,EAAE,MAAM,EAAE,GAAG;AACpB,UAAM,IAAI,IAAE,GAAG,IAAI,IAAE,IAAI,GAAG,IAAI,MAAM;AACpC,QAAE,QAAQ,QAAE,EAAE,aAAa,EAAE,OAAO,EAAE,OAAO,CAAC,GAAG,EAAE,qBAAqB,EAAE,KAAK;AAC/E,YAAM,IAAI;AAAA,QACR,MAAM,EAAE;AAAA,QACR,CAAC,MAAM;AACL,cAAI;AACJ,iBAAO,IAAI,EAAE,eAAe,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,OAAO,IAAI,EAAE,SAAS,MAAE,CAAC,CAAC,GAAG,KAAK,QAAQ,EAAE;AAAA,QAC1G;AAAA,QACA,EAAE,MAAM,KAAG;AAAA,MACb;AAAA,IACF;AACA,WAAO,UAAE,MAAM;AACb,QAAE;AAAA,IACJ,CAAC,GAAG;AAAA,MACF,UAAU;AAAA,MACV,YAAY;AAAA,IACd;AAAA,EACF;AACF,CAAC;AAnDD,IAmDI,IAAI,CAAC,GAAG,MAAM;AAChB,QAAM,IAAI,EAAE,aAAa;AACzB,aAAW,CAAC,GAAG,CAAC,KAAK;AACnB,MAAE,CAAC,IAAI;AACT,SAAO;AACT;AAxDA,IAwDG,KAAK,CAAC,QAAQ,aAAa;AAC9B,SAAS,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG;AAC5B,SAAO,UAAE,GAAG,mBAAE,YAAY;AAAA,IACxB,KAAK;AAAA,IACL,MAAM,EAAE,OAAO;AAAA,IACf,aAAa,EAAE,OAAO;AAAA,EACxB,GAAG,MAAM,GAAG,EAAE;AAChB;AACA,IAAM,IAAoB,EAAE,IAAI,CAAC,CAAC,UAAU,EAAE,CAAC,CAAC;AAChD,OAAO,mBAAmB,wBAAAC;AAC1B,OAAO,cAAc;AACrB,OAAO,cAAc;AACrB,OAAO,aAAa;AACpB,IAAM,KAAK,gBAAE;AAAA,EACX,MAAM;AAAA,EACN,OAAO;AAAA,IACL,SAAS;AAAA,MACP,MAAM;AAAA,MACN,SAAS,OAAO,CAAC;AAAA,IACnB;AAAA,IACA,YAAY;AAAA,MACV,MAAM;AAAA,MACN,SAAS,OAAO,CAAC;AAAA,IACnB;AAAA,EACF;AAAA,EACA,OAAO,CAAC,qBAAqB,OAAO;AAAA,EACpC,MAAM,GAAG,EAAE,MAAM,EAAE,GAAG;AACpB,UAAM,IAAI,IAAE,GAAG,IAAI,IAAE,GAAG,IAAI,MAAM;AAChC,QAAE,QAAQ,QAAE,EAAE,UAAU,EAAE,OAAO,EAAE,OAAO,CAAC,GAAG,EAAE,qBAAqB,EAAE,KAAK,GAAG,EAAE,SAAS,CAAC;AAAA,IAC7F;AACA,WAAO,UAAE,MAAM;AACb,QAAE;AAAA,IACJ,CAAC,GAAG;AAAA,MACF,WAAW;AAAA,MACX,YAAY;AAAA,IACd;AAAA,EACF;AACF,CAAC;AAxBD,IAwBI,KAAK,EAAE,KAAK,YAAY;AAC5B,SAAS,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG;AAC5B,SAAO,UAAE,GAAG,mBAAE,OAAO,IAAI,MAAM,GAAG;AACpC;AACA,IAAM,KAAqB,EAAE,IAAI,CAAC,CAAC,UAAU,EAAE,CAAC,CAAC;AACjD,IAAI,MAAsB,CAAC,OAAO,EAAE,OAAO,QAAQ,EAAE,UAAU,WAAW,EAAE,QAAQ,SAAS,IAAI,MAAM,CAAC,CAAC;AACzG,SAAS,KAAK;AACZ,QAAM,IAAoB,oBAAI,KAAK,GAAG,IAAI,EAAE,SAAS,IAAI,KAAK,IAAI,EAAE,SAAS,CAAC,KAAK,EAAE,SAAS,GAAG,IAAI,EAAE,WAAW,IAAI,KAAK,IAAI,EAAE,WAAW,CAAC,KAAK,EAAE,WAAW,GAAG,IAAI,EAAE,WAAW,IAAI,KAAK,IAAI,EAAE,WAAW,CAAC,KAAK,EAAE,WAAW;AAChO,SAAO,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC;AACvB;AACA,SAAS,GAAG,GAAG;AACb,SAAO,SAAS,KAAK,UAAU,CAAC,CAAC;AACnC;AACA,SAAS,GAAG,GAAG;AACb,QAAM,IAAI,qBAAqB,IAAI,CAAC;AACpC,MAAI;AACJ,OAAK,IAAI,EAAE,KAAK,CAAC,GAAG,KAAK;AACvB,UAAM,IAAI,SAAS,cAAc,GAAG,GAAG,IAAI,KAAK,MAAM,EAAE,CAAC,CAAC,GAAG,IAAI,OAAO,QAAQ,CAAC;AACjF,eAAW,CAAC,GAAG,CAAC,KAAK;AACnB,QAAE,aAAa,GAAG,CAAC;AACrB,MAAE,YAAY,sBAAsB,EAAE,YAAY,eAAe,EAAE,KAAK;AAAA,MACtE,OAAO,EAAE;AAAA,MACT,KAAK,EAAE,QAAQ,EAAE,CAAC,EAAE;AAAA,MACpB,MAAM;AAAA,IACR,CAAC,GAAG,IAAI,EAAE,KAAK,CAAC;AAAA,EAClB;AACA,SAAO;AACT;AACA,SAAS,GAAG,IAAI,IAAI,IAAI,QAAQ;AAC9B,SAAO,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC;AACpC;AACA,SAAS,GAAG,GAAG;AACb,QAAM,IAAI,CAAC;AACX,WAAS,IAAI;AACX,UAAM,IAAI;AACV,QAAI;AACJ,SAAK,IAAI,EAAE,KAAK,CAAC,GAAG,KAAK;AACvB,YAAM,IAAI,EAAE,CAAC,EAAE,QAAQ,SAAS;AAAA,CACrC,EAAE,MAAM;AAAA,CACR,GAAG,IAAI,EAAE,CAAC,EAAE,QAAQ,SAAS;AAAA,CAC7B,EAAE,MAAM;AAAA,CACR,GAAG,IAAI,SAAS,cAAc,MAAM,GAAG,IAAI,EAAE,CAAC;AACzC,QAAE,YAAY,kBAAkB,CAAC;AACjC,UAAI,IAAI;AACR,eAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,KAAK;AACjC,cAAM,IAAI,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,GAAG,IAAI,EAAE,UAAU,KAAE;AAC5C,UAAE,YAAY,GAAG,EAAE,KAAK;AAAA,UACtB,OAAO,EAAE,QAAQ;AAAA,UACjB,KAAK,EAAE,QAAQ,IAAI,EAAE;AAAA,UACrB,MAAM;AAAA,QACR,CAAC,GAAG,IAAI,IAAI,EAAE,SAAS;AAAA,MACzB;AACA,UAAI,EAAE,KAAK,CAAC;AAAA,IACd;AAAA,EACF;AACA,SAAO,EAAE,GAAG;AACd;AACA,SAAS,GAAG,GAAG,GAAG;AAChB,SAAO,IAAI,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC;AAC9B;AACA,SAAS,GAAG,GAAG,GAAG,GAAG;AACnB,QAAM,IAAI,IAAI,MAAM,KAAK,IAAI,KAAK,IAAI,CAAC,CAAC,EAAE,KAAK,KAAK,GAAG;AACvD,SAAO,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC;AACrB;AACA,IAAM,IAAI;AAAA,EACR;AAAA,IACE,OAAO;AAAA,IACP,OAAO,CAAC,OAAO,IAAI,gBAAgB,cAAc;AAAA,IACjD,KAAK;AAAA;AAAA,EAEP;AAAA,EACA;AAAA,IACE,OAAO;AAAA,IACP,OAAO,CAAC,OAAO,IAAI,WAAW,WAAW,IAAI;AAAA,IAC7C,KAAK;AAAA;AAAA,EAEP;AAAA,EACA;AAAA,IACE,OAAO;AAAA,IACP,OAAO,CAAC,OAAO,IAAI,WAAW,WAAW,IAAI;AAAA,IAC7C,KAAK;AAAA;AAAA,EAEP;AACF;AACA,EAAE,iBAAiB,SAAS;AAAA,EAC1B,OAAO;AAAA,IACL,GAAG;AAAA,IACH;AAAA,MACE,OAAO;AAAA,MACP,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,OAAO;AAAA,IACL,GAAG;AAAA,IACH;AAAA,MACE,OAAO;AAAA,MACP,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,MAAM;AAAA,IACJ,GAAG;AAAA,IACH;AAAA,MACE,OAAO;AAAA,MACP,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,SAAS;AAAA,IACP,GAAG;AAAA,IACH;AAAA,MACE,OAAO;AAAA,MACP,OAAO;AAAA,IACT;AAAA,EACF;AACF,CAAC;AACD,EAAE,iBAAiB,OAAO;AAAA,EACxB,OAAO;AAAA,IACL;AAAA,MACE,OAAO;AAAA,MACP,OAAO;AAAA,IACT;AAAA,IACA;AAAA,MACE,OAAO;AAAA,MACP,OAAO,CAAC,IAAI,QAAQ;AAAA,IACtB;AAAA,IACA;AAAA,MACE,OAAO;AAAA,MACP,OAAO;AAAA,IACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASF;AACF,CAAC;AACD,IAAM,KAAK,gBAAE;AAAA,EACX,MAAM;AAAA,EACN,OAAO;AAAA,IACL,OAAO;AAAA,MACL,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,MAAM;AAAA,MACJ,MAAM;AAAA,MACN,SAAS,eAAe,CAAiB,oBAAI,KAAK,CAAC;AAAA,IACrD;AAAA,IACA,SAAS;AAAA,MACP,MAAM;AAAA,MACN,SAAS,OAAO,CAAC;AAAA,IACnB;AAAA,IACA,YAAY;AAAA,MACV,MAAM;AAAA,MACN,SAAS,OAAO,CAAC;AAAA,IACnB;AAAA,IACA,aAAa;AAAA,MACX,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,EACF;AAAA,EACA,OAAO,CAAC,qBAAqB,OAAO;AAAA,EACpC,MAAM,GAAG,EAAE,MAAM,EAAE,GAAG;AACpB,UAAM,IAAI,IAAE,GAAG,IAAI,IAAE,IAAI,GAAG,IAAI,CAAC,IAAI,EAAE,eAAe;AACpD,QAAE,YAAY,EAAE,QAAQ,CAAC,MAAM,EAAE,MAAM,CAAC;AACxC,YAAM,IAAI,EAAE,SAAS,GAAG,IAAI,CAAC,EAAE,OAAO,GAAG,CAAC,CAAC,EAAE,OAAO,GAAG,CAAC,CAAC;AACzD,eAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,KAAK;AACjC,cAAM,IAAI,EAAE,CAAC;AACb,UAAE,SAAS,EAAE,aAAa,EAAE,KAAK,GAAG,EAAE,aAAa,EAAE,GAAG,GAAG;AAAA,UACzD,cAAc,EAAE;AAAA,QAClB,CAAC;AAAA,MACH;AAAA,IACF,GAAG,IAAI,MAAM;AACX,UAAI;AACJ,QAAE,QAAQ,QAAE,EAAE,aAAa,EAAE,OAAO,EAAE,OAAO,CAAC,GAAG,EAAE,qBAAqB,MAAE,CAAC,CAAC,IAAI,IAAI,EAAE,UAAU,QAAQ,EAAE,GAAG,UAAU,CAAC;AAAA,IAC1H;AACA,WAAO;AAAA,MACL,MAAM,EAAE;AAAA,MACR,CAAC,MAAM;AACL,YAAI;AACJ,cAAM,EAAE,EAAE,UAAU,IAAI,IAAI,EAAE,eAAe,QAAQ,EAAE,SAAS,EAAE,KAAK,GAAG,EAAE,SAAS,CAAC;AAAA,MACxF;AAAA,MACA,EAAE,MAAM,MAAI,WAAW,KAAG;AAAA,IAC5B,GAAG,UAAE,MAAM;AACT,QAAE;AAAA,IACJ,CAAC,GAAG;AAAA,MACF,YAAY;AAAA,MACZ,UAAU;AAAA,IACZ;AAAA,EACF;AACF,CAAC;AArDD,IAqDI,KAAK,CAAC,QAAQ,aAAa;AAC/B,SAAS,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG;AAC5B,SAAO,UAAE,GAAG,mBAAE,YAAY;AAAA,IACxB,KAAK;AAAA,IACL,MAAM,EAAE,OAAO;AAAA,IACf,aAAa,EAAE,OAAO;AAAA,EACxB,GAAG,MAAM,GAAG,EAAE;AAChB;AACA,IAAM,KAAqB,EAAE,IAAI,CAAC,CAAC,UAAU,EAAE,CAAC,CAAC;AAAjD,IAAoD,IAAI;AAAA,EACtD,gBAAgB,MAAM;AAAA,EACtB,QAAQ,CAAC,GAAG,OAAO,EAAE,OAAO,GAAG,IAAI,EAAE;AAAA,EACrC,OAAO,MAAM;AAAA,EACb,OAAO,CAAC,MAAM;AAChB;AALA,IAKG,KAAK;AAAA,EACN;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAxBA,IAwBG,KAAK,MAAM;AACZ,QAAM,IAAI,CAAC;AACX,SAAO,GAAG,QAAQ,CAAC,MAAM;AACvB,MAAE,CAAC,IAAI,IAAI,MAAM;AAAA,EACnB,CAAC,GAAG;AACN;AA7BA,IA6BG,KAAK,EAAE,GAAG,GAAG,GAAG,GAAG,EAAE;AA7BxB,IA6B2B,IAAI;AAAA,EAC7B,MAAM;AAAA;AAAA,EAEN,OAAO;AAAA;AAAA,EAEP,aAAa;AAAA;AAAA,EAEb,aAAa;AAAA;AAAA,EAEb,YAAY;AAAA;AAAA,EAEZ,iBAAiB;AAAA;AAEnB;AACA,SAAS,GAAG,GAAG;AACb,UAAQ,QAAQ,EAAE,KAAK,MAAM;AAC3B,UAAM,IAAI,EAAE,cAAc;AAC1B,MAAE,SAAS,EAAE,MAAM,EAAE,MAAM;AAAA,EAC7B,CAAC;AACH;AACA,IAAM,KAAK,CAAC;AAAA,EACV,OAAO;AAAA,EACP,YAAY;AAAA,EACZ,MAAM;AAAA,EACN,kBAAkB;AAAA,EAClB,SAAS;AACX,MAAM;AACJ,QAAM,IAAI;AAAA,IACR,MAAM;AACJ,UAAI;AACJ,aAAO,EAAE,SAAS,IAAI,MAAE,CAAC,MAAM,OAAO,SAAS,EAAE,OAAO,IAAI,MAAE,CAAC;AAAA,IACjE;AAAA,EACF,GAAG,IAAI,MAAM;AACX,UAAM,IAAI,CAAC;AACX,WAAO,OAAO,KAAK,KAAK,OAAO,SAAS,EAAE,MAAM,KAAK,EAAE,QAAQ,CAAC,MAAM;AACpE,UAAI,EAAE,WAAW,IAAI,GAAG;AACtB,cAAM,IAAI,EAAE,QAAQ,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,YAAY,CAAC,EAAE,MAAM,CAAC;AACrD,SAAC,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;AAAA,MACnB;AAAA,IACF,CAAC,GAAG;AAAA,EACN;AACA,SAAO;AAAA,IACL,gBAAgB,MAAM;AACpB,QAAE,MAAM,GAAG,UAAU,CAAC,MAAM;AAC1B,cAAM,IAAI,EAAE,SAAS;AACrB,cAAM,EAAE,SAAS,MAAM,OAAO,EAAE,QAAQ,GAAG,EAAE,gBAAgB,EAAE,SAAS,EAAE,GAAG,EAAE,SAAS,EAAE,SAAS,GAAG,GAAG,QAAQ,QAAQ,EAAE,KAAK,MAAM;AACpI,YAAE,UAAU,EAAE,OAAO,CAAC;AAAA,QACxB,CAAC,GAAG,EAAE,mBAAmB,GAAG,CAAC;AAAA,MAC/B,CAAC;AACD,YAAM,IAAI,CAAC;AACX,QAAE,EAAE,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,KAAG,EAAE,QAAQ,CAAC,MAAM;AACrD,UAAE,MAAM,GAAG,GAAG,IAAI,MAAM;AACtB,YAAE,GAAG,GAAG,CAAC;AAAA,QACX,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AAAA,EACF;AACF;AACA,SAAS,GAAG,EAAE,OAAO,GAAG,YAAY,GAAG,WAAW,EAAE,GAAG;AACrD,QAAM,IAAI,IAAE,MAAM,GAAG,IAAI,IAAE,MAAM,GAAG,IAAI;AAAA,IACtC,MAAM;AACJ,UAAI;AACJ,aAAO,EAAE,SAAS,IAAI,MAAE,CAAC,MAAM,OAAO,SAAS,EAAE,OAAO,IAAI,MAAE,CAAC;AAAA,IACjE;AAAA,EACF,GAAG,IAAI,MAAM;AACX,aAAE,MAAM;AACN,UAAI;AACJ,OAAC,IAAI,EAAE,UAAU,QAAQ,EAAE,QAAQ;AAAA,IACrC,CAAC;AAAA,EACH,GAAG,IAAI,CAAC,MAAM,IAAI,EAAE,KAAK,MAAM,CAAC,CAAC,KAAK,OAAI,IAAI,CAAC,IAAI,EAAE,OAAO,IAAI,EAAE,WAAW;AAC3E,QAAI;AACJ,QAAI,IAAI,QAAQ,IAAI;AACpB,MAAE,CAAC,IAAI,IAAI,GAAG,OAAO,CAAC,CAAC,OAAO,MAAM,IAAI,IAAI,EAAE,CAAC,IAAI,IAAI,GAAG,OAAO,CAAC,CAAC,OAAO,MAAM,IAAI,IAAI,EAAE,QAAQ,GAAG,EAAE,QAAQ,GAAG,QAAQ,IAAI,UAAU,GAAG,CAAC,IAAI,IAAI,EAAE,UAAU,QAAQ,EAAE,QAAQ,QAAQ,MAAM;AAAA,EAClM,GAAG,IAAI,MAAM;AACX,QAAI;AACJ,UAAM,KAAK,IAAI,EAAE,UAAU,OAAO,SAAS,EAAE,kBAAkB;AAC/D,SAAK,QAAQ,EAAE,OAAO;AAAA,EACxB,GAAG,IAAI,MAAM;AACX,QAAI,GAAG,GAAG;AACV,UAAM,KAAK,IAAI,EAAE,UAAU,OAAO,SAAS,EAAE,OAAO,EAAE,WAAW;AACjE,KAAC,IAAI,EAAE,UAAU,QAAQ,EAAE,WAAW,GAAG,EAAE,IAAI,IAAI,EAAE,UAAU,QAAQ,EAAE,OAAO,EAAE,WAAW,CAAC;AAAA,EAChG,GAAG,IAAI,MAAM;AACX,UAAM,IAAI,SAAS,cAAc,qBAAqB;AACtD,YAAQ,KAAK,OAAO,SAAS,EAAE,MAAM,KAAK,QAAQ,MAAM,EAAE,OAAO;AAAA,EACnE;AACA,SAAO;AAAA,IACL,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,IACjB,aAAa,MAAM;AACjB,UAAI,EAAE,GAAG,CAAC,EAAE;AAAG;AACf,YAAM,IAAI,YAAY,MAAM;AAC1B,UAAE,IAAI,EAAE,IAAI,cAAc,CAAC;AAAA,MAC7B,GAAG,EAAE,GAAG,IAAI,WAAW,MAAM;AAC3B,sBAAc,CAAC,GAAG,aAAa,CAAC;AAAA,MAClC,GAAG,GAAG;AAAA,IACR;AAAA,EACF;AACF;AACA,IAAM,IAAoB,gBAAE;AAAA,EAC1B,QAAQ;AAAA,EACR,OAAO;AAAA,IACL,OAAO;AAAA,MACL,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,SAAS;AAAA,MACP,MAAM;AAAA,MACN,SAAS,MAAM;AAAA,IACjB;AAAA,IACA,eAAe;AAAA,MACb,MAAM;AAAA,MACN,SAAS,MAAM;AAAA,IACjB;AAAA,IACA,aAAa;AAAA,MACX,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,QAAQ;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,OAAO;AAAA,MACL,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS;AAAA,IACX;AAAA,IACA,QAAQ;AAAA,MACN,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS;AAAA,IACX;AAAA,IACA,eAAe;AAAA,MACb,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,iBAAiB;AAAA,MACf,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,OAAO;AAAA,MACL,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,MAAM;AAAA,MACJ,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,QAAQ;AAAA,MACN,MAAM;AAAA,MACN,SAAS,MAAM;AAAA,IACjB;AAAA,IACA,aAAa;AAAA,MACX,MAAM;AAAA,MACN,SAAS,MAAM,CAAC;AAAA,IAClB;AAAA,EACF;AAAA,EACA,OAAO;AAAA,EACP,MAAM,GAAG,EAAE,QAAQ,GAAG,MAAM,EAAE,GAAG;AAC/B,QAAI,GAAG,GAAG;AACV,WAAO,OAAO,UAAU,cAAc,OAAO,eAAe,QAAQ,UAAU;AAAA,MAC5E,MAAM,GAAG;AACP,YAAI,KAAK;AACP,gBAAM,IAAI,UAAU,4CAA4C;AAClE,cAAM,IAAI,OAAO,CAAC;AAClB,iBAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,gBAAM,IAAI,UAAU,CAAC;AACrB,cAAI,KAAK;AACP,uBAAW,KAAK;AACd,qBAAO,UAAU,eAAe,KAAK,GAAG,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC;AAAA,QAC/D;AACA,eAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,cAAc;AAAA,IAChB,CAAC;AACD,UAAM,IAAI,GAAG,IAAI,GAAG,IAAI,IAAE,IAAI,GAAG,IAAI,IAAE,EAAE,GAAG,IAAI,WAAE,CAAC,GAAG,IAAI,IAAE;AAAA,MAC1D,YAAY;AAAA,MACZ,GAAG;AAAA,MACH,GAAG,EAAE;AAAA,MACL,GAAG,EAAE;AAAA,MACL,SAAS,CAAC,GAAmB,oBAAI,IAAI,CAAC,0BAA0B,yBAAyB,KAAK,IAAI,EAAE,YAAY,OAAO,SAAS,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC;AAAA,IACpJ,CAAC,GAAG,IAAI,mBAAE,GAAG,IAAI,EAAE,UAAU,KAAK,IAAI,KAAK,OAAO,SAAS,EAAE,WAAW,OAAO,SAAS,EAAE,SAAS,OAAO,SAAS,EAAE,SAAS,QAAQ,IAAI,IAAE,IAAI,GAAG,IAAI,SAAE,MAAM;AAC7J,UAAI;AACJ,aAAO,EAAE,SAAS,IAAI,MAAE,CAAC,MAAM,OAAO,SAAS,EAAE,OAAO,IAAI,MAAE,CAAC;AAAA,IACjE,CAAC,GAAG,EAAE,SAAS,GAAG,QAAQ,GAAG,SAAS,GAAG,iBAAiB,GAAG,gBAAgB,GAAG,aAAa,EAAE,IAAI,GAAG;AAAA,MACpG,OAAO;AAAA,MACP,YAAY;AAAA,MACZ,WAAW;AAAA,IACb,CAAC,GAAG,EAAE,gBAAgB,EAAE,IAAI,GAAG;AAAA,MAC7B,OAAO;AAAA,MACP,YAAY;AAAA,MACZ,MAAM;AAAA,MACN,kBAAkB;AAAA,MAClB,SAAS;AAAA,IACX,CAAC,GAAG,IAAI,MAAM;AACZ,QAAE,gBAAgB,UAAU,EAAE,WAAW,UAAU,EAAE,YAAY,QAAQ,CAAC,MAAM;AAC9E,YAAI,GAAG;AACP,cAAM,KAAK,IAAI,EAAE,UAAU,OAAO,SAAS,EAAE,SAAS,CAAC;AACvD,SAAC,IAAI,EAAE,UAAU,QAAQ,EAAE,gBAAgB,GAAG,eAAe,KAAK,QAAQ,EAAE,gBAAgB,OAAO,EAAE,OAAO,CAAC;AAAA,MAC/G,CAAC;AAAA,IACH,GAAG,IAAI,CAAC,MAAM;AACZ,UAAI,GAAG;AACP,YAAM,KAAK,IAAI,EAAE,UAAU,OAAO,SAAS,EAAE,SAAS;AACtD,YAAM,OAAO,IAAI,EAAE,UAAU,QAAQ,EAAE,SAAS,CAAC,GAAG,EAAE,QAAQ,GAAG,EAAE,IAAI,EAAE;AAAA,IAC3E,GAAG,IAAI,MAAM;AACX,QAAE,GAAG,EAAE,GAAG,EAAE,EAAE,OAAO,EAAE,MAAM,GAAG,EAAE,SAAS,EAAE,KAAK,GAAG;AAAA,QACnD,CAAC,MAAM,EAAE,OAAO,MAAM,EAAE,MAAM;AAAA,QAC9B,CAAC,CAAC,GAAG,CAAC,MAAM;AACV,YAAE,GAAG,CAAC;AAAA,QACR;AAAA,QACA,EAAE,MAAM,KAAG;AAAA,MACb;AAAA,IACF,GAAG,IAAI,MAAM;AACX,UAAI,EAAE,QAAQ,SAAS,WAAW,EAAE,QAAQ,SAAS,OAAO;AAC1D,UAAE,QAAQ;AACV;AAAA,MACF;AACA,UAAI,EAAE,OAAO;AACX,UAAE,QAAQ;AACV;AAAA,MACF;AACA,QAAE,QAAQ;AAAA,IACZ;AACA,WAAO;AAAA,MACL,MAAM,EAAE;AAAA,MACR,CAAC,MAAM;AACL,YAAI;AACJ,mBAAW,KAAK,EAAE;AAChB,WAAC,IAAI,EAAE,UAAU,QAAQ,EAAE,UAAU,GAAG,MAAE,EAAE,CAAC,CAAC,CAAC;AAAA,MACnD;AAAA,MACA,EAAE,MAAM,KAAG;AAAA,IACb,GAAG;AAAA,MACD,MAAM,EAAE;AAAA,MACR,CAAC,MAAM;AACL,UAAE,CAAC;AAAA,MACL;AAAA,IACF,GAAG,MAAE,MAAM,EAAE,OAAO,GAAG,EAAE,WAAW,KAAG,CAAC,GAAG,gBAAE,MAAM;AACjD,QAAE;AAAA,IACJ,CAAC,GAAG,EAAE;AAAA,MACJ,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,SAAS;AAAA,IACX,CAAC,GAAG,CAAC,GAAG,OAAO,UAAE,GAAG,mBAAE,OAAO;AAAA,MAC3B,OAAO,eAAG,CAAC,wBAAwB;AAAA,QACjC,OAAO,EAAE,OAAO;AAAA,QAChB,UAAU,EAAE,OAAO,UAAU,EAAE,OAAO,SAAS,CAAC,EAAE;AAAA,QAClD,kBAAkB,EAAE;AAAA,MACtB,CAAC,CAAC;AAAA,MACF,OAAO,eAAG;AAAA,QACR,QAAQ,MAAE,CAAC;AAAA,QACX,OAAO,MAAE,CAAC;AAAA,MACZ,CAAC;AAAA,IACH,GAAG;AAAA,OACA,UAAE,GAAG,YAAG,wBAAG,EAAE,KAAK,GAAG,WAAG;AAAA,QACvB,SAAS;AAAA,QACT,KAAK;AAAA,QACL,YAAY,EAAE;AAAA,QACd,uBAAuB,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,MAAM,EAAE,QAAQ;AAAA,QACxD,OAAO,EAAE,QAAQ,OAAO;AAAA,MAC1B,GAAG;AAAA,QACD,GAAG,EAAE;AAAA,QACL,GAAG,EAAE;AAAA,QACL,SAAS,EAAE;AAAA,QACX,MAAM,MAAE,CAAC;AAAA,QACT,SAAS,EAAE;AAAA,MACb,GAAG,EAAE,SAAS,EAAE,CAAC,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC;AAAA,IAC9C,GAAG,CAAC;AAAA,EACN;AACF,CAAC;AAzKD,IAyKI,IAAI,CAAC,GAAG,OAAO,KAAK,EAAE,YAAY,EAAE,MAAM,cAAc,UAAU,MAAM,EAAE,UAAU,EAAE,WAAW,KAAK,OAAO,SAAS,EAAE,kBAAkB,cAAc,CAAC,GAAG;AAzKhK,IAyKoK,KAAK,OAAO,cAAc;AAzK9L,IAyKiM,KAAK;AAzKtM,IAyKyM,KAAK;AAC9M,SAAS,YAAY,KAAIC,MAAI;AAAC,MAAGA,SAAM,QAAO;AAAC,IAAAA,OAAI,CAAC;AAAA,EAAC;AAAC,MAAI,WAASA,KAAI;AAAS,MAAG,CAAC,OAAK,OAAO,aAAW,aAAY;AAAC;AAAA,EAAM;AAAC,MAAI,OAAK,SAAS,QAAM,SAAS,qBAAqB,MAAM,EAAE,CAAC;AAAE,MAAI,QAAM,SAAS,cAAc,OAAO;AAAE,QAAM,OAAK;AAAW,MAAG,aAAW,OAAM;AAAC,QAAG,KAAK,YAAW;AAAC,WAAK,aAAa,OAAM,KAAK,UAAU;AAAA,IAAC,OAAK;AAAC,WAAK,YAAY,KAAK;AAAA,IAAC;AAAA,EAAC,OAAK;AAAC,SAAK,YAAY,KAAK;AAAA,EAAC;AAAC,MAAG,MAAM,YAAW;AAAC,UAAM,WAAW,UAAQ;AAAA,EAAG,OAAK;AAAC,UAAM,YAAY,SAAS,eAAe,GAAG,CAAC;AAAA,EAAC;AAAC;AAAE,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,CAmDjgB;", "names": ["CodeMirror", "range", "CodeMirror", "CodeMirror", "found", "i", "b", "start", "CodeMirror", "b", "i", "CodeMirror", "change", "end", "i", "b", "j", "elt", "clear", "diff_match_patch", "DIFF_DELETE", "DIFF_INSERT", "DIFF_EQUAL", "j", "longtext", "shorttext", "CodeMirror", "state", "j", "b", "import_foldgutter", "import_codemirror", "import_merge", "B", "se", "ref"]}
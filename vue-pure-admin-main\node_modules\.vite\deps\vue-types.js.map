{"version": 3, "sources": ["../../.pnpm/vue-types@6.0.0_vue@3.5.18_typescript@5.8.3_/node_modules/vue-types/src/config.ts", "../../.pnpm/vue-types@6.0.0_vue@3.5.18_typescript@5.8.3_/node_modules/vue-types/src/is-plain-obj.ts", "../../.pnpm/vue-types@6.0.0_vue@3.5.18_typescript@5.8.3_/node_modules/vue-types/src/sensibles.ts", "../../.pnpm/vue-types@6.0.0_vue@3.5.18_typescript@5.8.3_/node_modules/vue-types/src/utils.ts", "../../.pnpm/vue-types@6.0.0_vue@3.5.18_typescript@5.8.3_/node_modules/vue-types/src/validators/native.ts", "../../.pnpm/vue-types@6.0.0_vue@3.5.18_typescript@5.8.3_/node_modules/vue-types/src/validators/custom.ts", "../../.pnpm/vue-types@6.0.0_vue@3.5.18_typescript@5.8.3_/node_modules/vue-types/src/validators/oneof.ts", "../../.pnpm/vue-types@6.0.0_vue@3.5.18_typescript@5.8.3_/node_modules/vue-types/src/validators/oneoftype.ts", "../../.pnpm/vue-types@6.0.0_vue@3.5.18_typescript@5.8.3_/node_modules/vue-types/src/validators/arrayof.ts", "../../.pnpm/vue-types@6.0.0_vue@3.5.18_typescript@5.8.3_/node_modules/vue-types/src/validators/instanceof.ts", "../../.pnpm/vue-types@6.0.0_vue@3.5.18_typescript@5.8.3_/node_modules/vue-types/src/validators/objectof.ts", "../../.pnpm/vue-types@6.0.0_vue@3.5.18_typescript@5.8.3_/node_modules/vue-types/src/validators/shape.ts", "../../.pnpm/vue-types@6.0.0_vue@3.5.18_typescript@5.8.3_/node_modules/vue-types/src/index.ts"], "sourcesContent": ["import { VueTypesConfig } from './types'\n\nexport const config: VueTypesConfig = {\n  silent: false,\n  logLevel: 'warn',\n}\n", "/**\n * Extracted from https://github.com/sindresorhus/is-plain-obj\n */\nexport function isPlainObject(value: any): value is Record<string, any> {\n  if (typeof value !== 'object' || value === null) {\n    return false\n  }\n\n  const prototype = Object.getPrototypeOf(value)\n  return (\n    (prototype === null ||\n      prototype === Object.prototype ||\n      Object.getPrototypeOf(prototype) === null) &&\n    !(Symbol.toStringTag in value) &&\n    !(Symbol.iterator in value)\n  )\n}\n", "import { VueTypesDefaults } from './types'\n\nexport const typeDefaults = (): VueTypesDefaults => ({\n  func: () => undefined,\n  bool: true,\n  string: '',\n  number: 0,\n  array: () => [],\n  object: () => ({}),\n  integer: 0,\n})\n", "import './global-this'\nimport { config } from './config'\nimport {\n  VueTypeDef,\n  VueTypeValidableDef,\n  VueProp,\n  InferType,\n  PropOptions,\n  VueTypesConfig,\n  ValidatorFunction,\n} from './types'\nimport { isPlainObject } from './is-plain-obj'\n\nexport { isPlainObject }\n\nconst ObjProto = Object.prototype\nconst toString = ObjProto.toString\nexport const hasOwn = ObjProto.hasOwnProperty\n\nconst FN_MATCH_REGEXP = /^\\s*function (\\w+)/\n\n// https://github.com/vuejs/vue/blob/dev/src/core/util/props.js#L177\nexport function getType(\n  fn: VueProp<any> | (() => any) | (new (...args: any[]) => any),\n): string {\n  const type = (fn as VueProp<any>)?.type ?? fn\n  if (type) {\n    const match = type.toString().match(FN_MATCH_REGEXP)\n    return match ? match[1] : ''\n  }\n  return ''\n}\n\nexport function getNativeType(value: any): string {\n  if (value === null || value === undefined) return ''\n  const match = value.constructor.toString().match(FN_MATCH_REGEXP)\n  return match ? match[1].replace(/^Async/, '') : ''\n}\n\nexport function deepClone<T>(input: T): T {\n  if ('structuredClone' in globalThis) {\n    return structuredClone(input)\n  }\n  if (Array.isArray(input)) {\n    return [...input] as T\n  }\n  if (isPlainObject(input)) {\n    return Object.assign({}, input)\n  }\n  return input\n}\n\n/**\n * No-op function\n */\n// eslint-disable-next-line @typescript-eslint/no-empty-function\nexport function noop() {}\n\n/**\n * A function that returns its first argument\n *\n * @param arg - Any argument\n */\nexport const identity = (arg: any) => arg\n\nlet warn: (msg: string, level?: VueTypesConfig['logLevel']) => void = noop\n\nif (process.env.NODE_ENV !== 'production') {\n  const hasConsole = typeof console !== 'undefined'\n  warn = hasConsole\n    ? function warn(msg: string, level = config.logLevel) {\n        if (config.silent === false) {\n          console[level](`[VueTypes warn]: ${msg}`)\n        }\n      }\n    : noop\n}\n\nexport { warn }\n\n/**\n * Checks for a own property in an object\n *\n * @param {object} obj - Object\n * @param {string} prop - Property to check\n */\nexport const has = <T, U extends keyof T>(obj: T, prop: U) =>\n  hasOwn.call(obj, prop)\n\n/**\n * Determines whether the passed value is an integer. Uses `Number.isInteger` if available\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number/isInteger\n * @param {*} value - The value to be tested for being an integer.\n * @returns {boolean}\n */\nexport const isInteger =\n  Number.isInteger ||\n  function isInteger(value: unknown): value is number {\n    return (\n      typeof value === 'number' &&\n      isFinite(value) &&\n      Math.floor(value) === value\n    )\n  }\n\n/**\n * Determines whether the passed value is an Array.\n *\n * @param {*} value - The value to be tested for being an array.\n * @returns {boolean}\n */\nexport const isArray =\n  Array.isArray ||\n  function isArray(value): value is any[] {\n    return toString.call(value) === '[object Array]'\n  }\n\n/**\n * Checks if a value is a function\n *\n * @param {any} value - Value to check\n * @returns {boolean}\n */\n// eslint-disable-next-line @typescript-eslint/no-unsafe-function-type\nexport const isFunction = <T extends Function>(value: unknown): value is T =>\n  toString.call(value) === '[object Function]'\n\n/**\n * Checks if the passed-in value is a VueTypes type\n * @param value - The value to check\n * @param name - Optional validator name\n */\nexport const isVueTypeDef = <T>(\n  value: any,\n  name?: string,\n): value is VueTypeDef<T> | VueTypeValidableDef<T> =>\n  isPlainObject(value) &&\n  has(value, '_vueTypes_name') &&\n  (!name || value._vueTypes_name === name)\n\n/**\n * Checks if the passed-in value is a Vue prop definition object or a VueTypes type\n * @param value - The value to check\n */\nexport const isComplexType = <T>(value: any): value is VueProp<T> =>\n  isPlainObject(value) &&\n  (has(value, 'type') ||\n    ['_vueTypes_name', 'validator', 'default', 'required'].some((k) =>\n      has(value, k),\n    ))\n\nexport interface WrappedFn {\n  (...args: any[]): any\n  __original: (...args: any[]) => any\n}\n\n/**\n * Binds a function to a context and saves a reference to the original.\n *\n * @param fn - Target function\n * @param ctx - New function context\n */\nexport function bindTo(fn: (...args: any[]) => any, ctx: any): WrappedFn {\n  return Object.defineProperty(fn.bind(ctx) as WrappedFn, '__original', {\n    value: fn,\n  })\n}\n\n/**\n * Returns the original function bounded with `bindTo`. If the passed-in function\n * has not be bound, the function itself will be returned instead.\n *\n * @param fn - Function to unwrap\n */\n// eslint-disable-next-line @typescript-eslint/no-unsafe-function-type\nexport function unwrap<T extends WrappedFn | Function>(fn: T) {\n  return (fn as WrappedFn).__original ?? fn\n}\n\n/**\n * Validates a given value against a prop type object.\n *\n * If `silent` is `false` (default) will return a boolean. If it is set to `true`\n * it will return `true` on success or a string error message on failure\n *\n * @param {Object|*} type - Type to use for validation. Either a type object or a constructor\n * @param {*} value - Value to check\n * @param {boolean} silent - Silence warnings\n */\nexport function validateType<T, U>(\n  type: T,\n  value: U,\n  silent = false,\n): string | boolean {\n  let typeToCheck: Record<string, any>\n  let valid = true\n  let expectedType = ''\n  if (!isPlainObject(type)) {\n    typeToCheck = { type }\n  } else {\n    typeToCheck = type\n  }\n  const namePrefix = isVueTypeDef(typeToCheck)\n    ? typeToCheck._vueTypes_name + ' - '\n    : ''\n\n  if (isComplexType(typeToCheck) && typeToCheck.type !== null) {\n    if (typeToCheck.type === undefined || typeToCheck.type === true) {\n      return valid\n    }\n    if (!typeToCheck.required && value == null) {\n      return valid\n    }\n    if (isArray(typeToCheck.type)) {\n      valid = typeToCheck.type.some(\n        (type: any) => validateType(type, value, true) === true,\n      )\n      expectedType = typeToCheck.type\n        .map((type: any) => getType(type))\n        .join(' or ')\n    } else {\n      expectedType = getType(typeToCheck)\n\n      if (expectedType === 'Array') {\n        valid = isArray(value)\n      } else if (expectedType === 'Object') {\n        valid = isPlainObject(value)\n      } else if (\n        expectedType === 'String' ||\n        expectedType === 'Number' ||\n        expectedType === 'Boolean' ||\n        expectedType === 'Function'\n      ) {\n        valid = getNativeType(value) === expectedType\n      } else {\n        valid = value instanceof typeToCheck.type\n      }\n    }\n  }\n\n  if (!valid) {\n    const msg = `${namePrefix}value \"${value}\" should be of type \"${expectedType}\"`\n    if (silent === false) {\n      warn(msg)\n      return false\n    }\n    return msg\n  }\n\n  if (has(typeToCheck, 'validator') && isFunction(typeToCheck.validator)) {\n    const oldWarn = warn\n    const warnLog: string[] = []\n    warn = (msg) => {\n      warnLog.push(msg)\n    }\n\n    valid = typeToCheck.validator(value)\n    warn = oldWarn\n\n    if (!valid) {\n      const msg = (warnLog.length > 1 ? '* ' : '') + warnLog.join('\\n* ')\n      warnLog.length = 0\n      if (silent === false) {\n        warn(msg)\n        return valid\n      }\n      return msg\n    }\n  }\n  return valid\n}\n\n/**\n * Adds `isRequired` and `def` modifiers to an object\n *\n * @param {string} name - Type internal name\n * @param {object} obj - Object to enhance\n */\nexport function toType<T = any>(name: string, obj: PropOptions<T>) {\n  const type: VueTypeDef<T> = Object.defineProperties(obj as VueTypeDef<T>, {\n    _vueTypes_name: {\n      value: name,\n      writable: true,\n    },\n    isRequired: {\n      get() {\n        this.required = true\n        return this\n      },\n    },\n    def: {\n      value(def?: any) {\n        if (def === undefined) {\n          if (\n            this.type === Boolean ||\n            (Array.isArray(this.type) && this.type.includes(Boolean))\n          ) {\n            this.default = undefined\n            return\n          }\n          if (has(this, 'default')) {\n            delete this.default\n          }\n          return this\n        }\n        if (!isFunction(def) && validateType(this, def, true) !== true) {\n          warn(`${this._vueTypes_name} - invalid default value: \"${def}\"`)\n          return this\n        }\n        if (isArray(def)) {\n          this.default = () => deepClone(def)\n        } else if (isPlainObject(def)) {\n          this.default = () => deepClone(def)\n        } else {\n          this.default = def\n        }\n        return this\n      },\n    },\n  })\n\n  const { validator } = type\n  if (isFunction(validator)) {\n    type.validator = bindTo(validator, type)\n  }\n\n  return type\n}\n\n/**\n * Like `toType` but also adds the `validate()` method to the type object\n *\n * @param {string} name - Type internal name\n * @param {object} obj - Object to enhance\n */\nexport function toValidableType<T = any>(name: string, obj: PropOptions<T>) {\n  const type = toType<T>(name, obj)\n  return Object.defineProperty(type, 'validate', {\n    value(fn: ValidatorFunction<T>) {\n      if (isFunction(this.validator)) {\n        warn(\n          `${\n            this._vueTypes_name\n          } - calling .validate() will overwrite the current custom validator function. Validator info:\\n${JSON.stringify(\n            this,\n          )}`,\n        )\n      }\n      this.validator = bindTo(fn, this)\n      return this\n    },\n  }) as VueTypeValidableDef<T>\n}\n\n/**\n *  Clones an object preserving all of it's own keys.\n *\n * @param obj - Object to clone\n */\n\nexport function clone<T extends object>(obj: T): T {\n  const descriptors = {} as { [P in keyof T]: any }\n  Object.getOwnPropertyNames(obj).forEach((key) => {\n    descriptors[key as keyof T] = Object.getOwnPropertyDescriptor(obj, key)\n  })\n  return Object.defineProperties({}, descriptors) as T\n}\n\n/**\n * Return a new VueTypes type using another type as base.\n *\n * Properties in the `props` object will overwrite those defined in the source one\n * expect for the `validator` function. In that case both functions will be executed in series.\n *\n * @param name - Name of the new type\n * @param source - Source type\n * @param props - Custom type properties\n */\nexport function fromType<T extends VueTypeDef<any>>(name: string, source: T): T\nexport function fromType<\n  T extends VueTypeDef<any>,\n  V extends PropOptions<InferType<T>>,\n>(name: string, source: T, props: V): Omit<T, keyof V> & V\nexport function fromType<\n  T extends VueTypeDef<any>,\n  V extends PropOptions<InferType<T>>,\n>(name: string, source: T, props?: V) {\n  // 1. create an exact copy of the source type\n  const copy = clone(source)\n\n  // 2. give it a new name\n  copy._vueTypes_name = name\n\n  if (!isPlainObject(props)) {\n    return copy\n  }\n  const { validator, ...rest } = props\n\n  // 3. compose the validator function\n  // with the one on the source (if present)\n  // and ensure it is bound to the copy\n  if (isFunction(validator)) {\n    let { validator: prevValidator } = copy\n\n    if (prevValidator) {\n      prevValidator = unwrap(prevValidator) as (_v: any) => boolean\n    }\n\n    copy.validator = bindTo(\n      prevValidator\n        ? function (this: T, value: any, props: any) {\n            return (\n              // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n              prevValidator!.call(this, value, props) &&\n              validator.call(this, value, props)\n            )\n          }\n        : validator,\n      copy,\n    )\n  }\n  // 4. overwrite the rest, if present\n  return Object.assign(copy, rest as V)\n}\n\nexport function indent(string: string) {\n  return string.replace(/^(?!\\s*$)/gm, '  ')\n}\n", "import { toType, toValidableType, isInteger, warn } from '../utils'\nimport { PropOptions, PropType } from '../types'\n\nexport const any = <T = any>() => toValidableType<T>('any', {})\n\nexport const func = <T extends (...args: any[]) => any>() =>\n  toValidableType<T>('function', {\n    type: Function as PropType<T>,\n  })\n\nexport const bool = () =>\n  toValidableType('boolean', {\n    type: Boolean,\n  })\n\nexport const string = <T extends string = string>() =>\n  toValidableType<T>('string', {\n    type: String as unknown as PropType<T>,\n  })\n\nexport const number = <T extends number = number>() =>\n  toValidableType<T>('number', {\n    type: Number as unknown as PropType<T>,\n  })\n\nexport const array = <T>() =>\n  toValidableType<T[]>('array', {\n    type: Array,\n  })\n\nexport const object = <T extends Record<string, any>>() =>\n  toValidableType<T>('object', {\n    type: Object,\n  })\n\nexport const integer = <T extends number = number>() =>\n  toType<T>('integer', {\n    type: Number as unknown as PropType<T>,\n    validator(value) {\n      const res = isInteger(value)\n      if (res === false) {\n        warn(`integer - \"${value}\" is not an integer`)\n      }\n      return res\n    },\n  })\n\nexport const symbol = () =>\n  toType<symbol>('symbol', {\n    validator(value: unknown) {\n      const res = typeof value === 'symbol'\n      if (res === false) {\n        warn(`symbol - invalid value \"${value}\"`)\n      }\n      return res\n    },\n  })\n\nexport const nullable = () =>\n  Object.defineProperty(\n    {\n      type: null as unknown as PropType<null>,\n      validator(value: unknown) {\n        const res = value === null\n        if (res === false) {\n          warn(`nullable - value should be null`)\n        }\n        return res\n      },\n    },\n    '_vueTypes_name',\n    { value: 'nullable' },\n  ) as PropOptions<null>\n", "import { toType, warn } from '../utils'\nimport { ValidatorFunction, VueTypeDef, PropType } from '../types'\n\nexport default function custom<T>(\n  validatorFn: ValidatorFunction<T>,\n  warnMsg = 'custom validation failed',\n) {\n  if (typeof validatorFn !== 'function') {\n    throw new TypeError(\n      '[VueTypes error]: You must provide a function as argument',\n    )\n  }\n\n  return toType<T>(validatorFn.name || '<<anonymous function>>', {\n    type: null as unknown as PropType<T>,\n    validator(this: VueTypeDef<T>, value: T) {\n      const valid = validatorFn(value)\n      if (!valid) warn(`${this._vueTypes_name} - ${warnMsg}`)\n      return valid\n    },\n  })\n}\n", "import { Prop, PropOptions } from '../types'\nimport { toType, warn, isArray } from '../utils'\n\nexport default function oneOf<D, T extends readonly D[] = readonly D[]>(\n  arr: T,\n) {\n  if (!isArray(arr)) {\n    throw new TypeError(\n      '[VueTypes error]: You must provide an array as argument.',\n    )\n  }\n  const msg = `oneOf - value should be one of \"${arr\n    .map((v: any) => (typeof v === 'symbol' ? v.toString() : v))\n    .join('\", \"')}\".`\n  const base: PropOptions<T[number]> = {\n    validator(value) {\n      const valid = arr.indexOf(value) !== -1\n      if (!valid) warn(msg)\n      return valid\n    },\n  }\n  if (arr.indexOf(null) === -1) {\n    const type = arr.reduce(\n      (ret, v) => {\n        if (v !== null && v !== undefined) {\n          const constr = (v as any).constructor\n          // eslint-disable-next-line @typescript-eslint/no-unused-expressions\n          ret.indexOf(constr) === -1 && ret.push(constr)\n        }\n        return ret\n      },\n      [] as Prop<T[number]>[],\n    )\n\n    if (type.length > 0) {\n      base.type = type\n    }\n  }\n\n  return toType<T[number]>('oneOf', base)\n}\n", "import { Prop, VueProp, InferType, PropType } from '../types'\nimport {\n  isArray,\n  isComplexType,\n  isVueTypeDef,\n  isFunction,\n  toType,\n  validateType,\n  warn,\n  indent,\n} from '../utils'\n\nexport default function oneOfType<\n  D extends V,\n  U extends VueProp<any> | Prop<any> = any,\n  V = InferType<U>,\n>(arr: U[]) {\n  if (!isArray(arr)) {\n    throw new TypeError(\n      '[VueTypes error]: You must provide an array as argument',\n    )\n  }\n\n  let hasCustomValidators = false\n  let hasNullable = false\n\n  let nativeChecks: (Prop<V> | null)[] = []\n\n  // eslint-disable-next-line @typescript-eslint/prefer-for-of\n  for (let i = 0; i < arr.length; i += 1) {\n    const type = arr[i]\n    if (isComplexType<V>(type)) {\n      if (isFunction(type.validator)) {\n        hasCustomValidators = true\n      }\n      if (isVueTypeDef<V>(type, 'oneOf') && type.type) {\n        nativeChecks = nativeChecks.concat(type.type as PropType<V>)\n        continue\n      }\n      if (isVueTypeDef<V>(type, 'nullable')) {\n        hasNullable = true\n        continue\n      }\n      if (type.type === true || !type.type) {\n        warn('oneOfType - invalid usage of \"true\" and \"null\" as types.')\n        continue\n      }\n      nativeChecks = nativeChecks.concat(type.type)\n    } else {\n      nativeChecks.push(type as Prop<V>)\n    }\n  }\n\n  // filter duplicates\n  nativeChecks = nativeChecks.filter((t, i) => nativeChecks.indexOf(t) === i)\n\n  const typeProp =\n    hasNullable === false && nativeChecks.length > 0 ? nativeChecks : null\n\n  if (!hasCustomValidators) {\n    // we got just native objects (ie: Array, Object)\n    // delegate to Vue native prop check\n    return toType<D>('oneOfType', {\n      type: typeProp as unknown as PropType<D>,\n    })\n  }\n\n  return toType<D>('oneOfType', {\n    type: typeProp as unknown as PropType<D>,\n    validator(value) {\n      const err: string[] = []\n      const valid = arr.some((type) => {\n        const res = validateType(type, value, true)\n        if (typeof res === 'string') {\n          err.push(res)\n        }\n        return res === true\n      })\n\n      if (!valid) {\n        warn(\n          `oneOfType - provided value does not match any of the ${\n            err.length\n          } passed-in validators:\\n${indent(err.join('\\n'))}`,\n        )\n      }\n\n      return valid\n    },\n  })\n}\n", "import { Prop, VueProp, InferType } from '../types'\nimport { toType, validateType, warn, indent } from '../utils'\n\nexport default function arrayOf<T extends VueProp<any> | Prop<any>>(type: T) {\n  return toType<InferType<T>[]>('arrayOf', {\n    type: Array,\n    validator(values: any[]) {\n      let vResult: string | boolean = ''\n      const valid = values.every((value) => {\n        vResult = validateType(type, value, true)\n        return vResult === true\n      })\n      if (!valid) {\n        warn(`arrayOf - value validation error:\\n${indent(vResult as string)}`)\n      }\n      return valid\n    },\n  })\n}\n", "import { toType } from '../utils'\nimport { Constructor } from '../types'\n\nexport default function instanceOf<C extends Constructor>(\n  instanceConstructor: C,\n) {\n  return toType<InstanceType<C>>('instanceOf', {\n    type: instanceConstructor,\n  })\n}\n", "import { Prop, VueProp, InferType } from '../types'\nimport { toType, validateType, warn, indent, isPlainObject } from '../utils'\n\nexport default function objectOf<T extends VueProp<any> | Prop<any>>(type: T) {\n  return toType<Record<string, InferType<T>>>('objectOf', {\n    type: Object,\n    validator(obj) {\n      let vResult: string | boolean = ''\n      if (!isPlainObject(obj)) {\n        return false\n      }\n      const valid = Object.keys(obj).every((key) => {\n        vResult = validateType(type, obj[key], true)\n        return vResult === true\n      })\n\n      if (!valid) {\n        warn(`objectOf - value validation error:\\n${indent(vResult as string)}`)\n      }\n      return valid\n    },\n  })\n}\n", "import { <PERSON>p, VueProp, VueType<PERSON>ha<PERSON>, VueTypeLooseShape } from '../types'\nimport { toType, validateType, warn, isPlainObject, indent } from '../utils'\n\nexport default function shape<T extends object>(obj: {\n  [K in keyof T]: Prop<T[K]> | VueProp<T[K]>\n}): VueTypeShape<T> {\n  const keys = Object.keys(obj)\n  const requiredKeys = keys.filter((key) => !!(obj as any)[key]?.required)\n\n  const type = toType('shape', {\n    type: Object,\n    validator(this: VueTypeShape<T> | VueTypeLooseShape<T>, value) {\n      if (!isPlainObject(value)) {\n        return false\n      }\n      const valueKeys = Object.keys(value)\n\n      // check for required keys (if any)\n      if (\n        requiredKeys.length > 0 &&\n        requiredKeys.some((req) => valueKeys.indexOf(req) === -1)\n      ) {\n        const missing = requiredKeys.filter(\n          (req) => valueKeys.indexOf(req) === -1,\n        )\n        if (missing.length === 1) {\n          warn(`shape - required property \"${missing[0]}\" is not defined.`)\n        } else {\n          warn(\n            `shape - required properties \"${missing.join(\n              '\", \"',\n            )}\" are not defined.`,\n          )\n        }\n\n        return false\n      }\n\n      return valueKeys.every((key) => {\n        if (keys.indexOf(key) === -1) {\n          if ((this as VueTypeLooseShape<T>)._vueTypes_isLoose === true)\n            return true\n          warn(\n            `shape - shape definition does not include a \"${key}\" property. Allowed keys: \"${keys.join(\n              '\", \"',\n            )}\".`,\n          )\n          return false\n        }\n        const type = (obj as any)[key]\n        const valid = validateType(type, value[key], true)\n        if (typeof valid === 'string') {\n          warn(`shape - \"${key}\" property validation error:\\n ${indent(valid)}`)\n        }\n        return valid === true\n      })\n    },\n  }) as VueTypeShape<T>\n\n  Object.defineProperty(type, '_vueTypes_isLoose', {\n    writable: true,\n    value: false,\n  })\n\n  Object.defineProperty(type, 'loose', {\n    get() {\n      this._vueTypes_isLoose = true\n      return this\n    },\n  })\n\n  return type\n}\n", "import { toType, toValidableType, validateType, fromType, warn } from './utils'\n\nimport {\n  VueTypesDefaults,\n  VueTypeDef,\n  VueTypeValidableDef,\n  VueTypeShape,\n  VueTypeLooseShape,\n} from './types'\nimport { typeDefaults } from './sensibles'\nimport { PropOptions } from './types'\n\nimport {\n  any,\n  func,\n  bool,\n  string,\n  number,\n  array,\n  integer,\n  symbol,\n  object,\n  nullable,\n} from './validators/native'\nimport custom from './validators/custom'\nimport oneOf from './validators/oneof'\nimport oneOfType from './validators/oneoftype'\nimport arrayOf from './validators/arrayof'\nimport instanceOf from './validators/instanceof'\nimport objectOf from './validators/objectof'\nimport shape from './validators/shape'\nimport { config } from './config'\n\nconst BaseVueTypes = /*#__PURE__*/ (() =>\n  // eslint-disable-next-line @typescript-eslint/no-extraneous-class\n  class BaseVueTypes {\n    static defaults: Partial<VueTypesDefaults> = {}\n\n    static sensibleDefaults: Partial<VueTypesDefaults> | boolean\n\n    static config = config\n\n    static get any() {\n      return any()\n    }\n    static get func() {\n      return func().def(this.defaults.func)\n    }\n    static get bool() {\n      // prevent undefined to be explicitly set\n      if (this.defaults.bool === undefined) {\n        return bool()\n      }\n      return bool().def(this.defaults.bool)\n    }\n    static get string() {\n      return string().def(this.defaults.string)\n    }\n    static get number() {\n      return number().def(this.defaults.number)\n    }\n    static get array() {\n      return array().def(this.defaults.array)\n    }\n    static get object() {\n      return object().def(this.defaults.object)\n    }\n    static get integer() {\n      return integer().def(this.defaults.integer)\n    }\n    static get symbol() {\n      return symbol()\n    }\n\n    static get nullable() {\n      return nullable()\n    }\n\n    static readonly custom = custom\n    static readonly oneOf = oneOf\n    static readonly instanceOf = instanceOf\n    static readonly oneOfType = oneOfType\n    static readonly arrayOf = arrayOf\n    static readonly objectOf = objectOf\n    static readonly shape = shape\n\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    static extend(...args: any[]) {\n      warn(\n        `VueTypes.extend has been removed. Use the ES6+ method instead. See https://dwightjack.github.io/vue-types/advanced/extending-vue-types.html#extending-namespaced-validators-in-es6 for details.`,\n      )\n    }\n\n    static utils = {\n      validate<T, U>(value: T, type: U) {\n        return validateType<U, T>(type, value, true) === true\n      },\n      toType<T = unknown, Validable extends boolean = false>(\n        name: string,\n        obj: PropOptions<T>,\n        validable: Validable = false as Validable,\n      ): Validable extends true ? VueTypeValidableDef<T> : VueTypeDef<T> {\n        return (\n          validable ? toValidableType<T>(name, obj) : toType<T>(name, obj)\n        ) as any\n      },\n    }\n  })()\n\nfunction createTypes(defs: Partial<VueTypesDefaults> = typeDefaults()) {\n  return class extends BaseVueTypes {\n    static defaults: Partial<VueTypesDefaults> = { ...defs }\n\n    static get sensibleDefaults() {\n      return { ...this.defaults }\n    }\n\n    static set sensibleDefaults(v: boolean | Partial<VueTypesDefaults>) {\n      if (v === false) {\n        this.defaults = {}\n        return\n      }\n      if (v === true) {\n        this.defaults = { ...defs }\n        return\n      }\n      this.defaults = { ...v }\n    }\n  }\n}\n\nexport default class VueTypes /*#__PURE__*/ extends createTypes() {}\n\nexport {\n  any,\n  func,\n  bool,\n  string,\n  number,\n  array,\n  integer,\n  symbol,\n  object,\n  custom,\n  oneOf,\n  oneOfType,\n  arrayOf,\n  instanceOf,\n  objectOf,\n  shape,\n  nullable,\n  createTypes,\n  toType,\n  toValidableType,\n  validateType,\n  fromType,\n  config,\n}\n\nexport type VueTypesInterface = ReturnType<typeof createTypes>\nexport type { VueTypeDef, VueTypeValidableDef, VueTypeShape, VueTypeLooseShape }\n"], "mappings": ";;;AAEO,IAAM,SAAyB;EACpC,QAAQ;EACR,UAAU;AACZ;ACFO,SAAS,cAAc,OAA0C;AACtE,MAAI,OAAO,UAAU,YAAY,UAAU,MAAM;AACxC,WAAA;EAAA;AAGH,QAAA,YAAY,OAAO,eAAe,KAAK;AAC7C,UACG,cAAc,QACb,cAAc,OAAO,aACrB,OAAO,eAAe,SAAS,MAAM,SACvC,EAAE,OAAO,eAAe,UACxB,EAAE,OAAO,YAAY;AAEzB;ACdO,IAAM,eAAe,OAAyB;EACnD,MAAM,MAAM;EACZ,MAAM;EACN,QAAQ;EACR,QAAQ;EACR,OAAO,MAAM,CAAA;EACb,QAAQ,OAAO,CAAA;EACf,SAAS;AACX;;;ACKA,IAAM,WAAW,OAAO;AACxB,IAAM,WAAW,SAAS;AACnB,IAAM,SAAS,SAAS;AAE/B,IAAM,kBAAkB;AAGjB,SAAS,QACd,IACQ;AACF,QAAA,QAAQ,yBAAqB,SAAQ;AAC3C,MAAI,MAAM;AACR,UAAM,QAAQ,KAAK,SAAS,EAAE,MAAM,eAAe;AAC5C,WAAA,QAAQ,MAAM,CAAC,IAAI;EAAA;AAErB,SAAA;AACT;AAEO,SAAS,cAAc,OAAoB;AAC5C,MAAA,UAAU,QAAQ,UAAU;AAAkB,WAAA;AAClD,QAAM,QAAQ,MAAM,YAAY,SAAS,EAAE,MAAM,eAAe;AAChE,SAAO,QAAQ,MAAM,CAAC,EAAE,QAAQ,UAAU,EAAE,IAAI;AAClD;AAEO,SAAS,UAAa,OAAa;AACxC,MAAI,qBAAqB,YAAY;AACnC,WAAO,gBAAgB,KAAK;EAAA;AAE1B,MAAA,MAAM,QAAQ,KAAK,GAAG;AACjB,WAAA,CAAC,GAAG,KAAK;EAAA;AAEd,MAAA,cAAc,KAAK,GAAG;AACxB,WAAO,OAAO,OAAO,CAAA,GAAI,KAAK;EAAA;AAEzB,SAAA;AACT;AAMO,SAAS,OAAO;AAAC;AASxB,IAAI,OAAkE;AAEtE,IAAI,MAAuC;AACnC,QAAA,aAAa,OAAO,YAAY;AACtC,SAAO,aACH,SAASA,MAAK,KAAa,QAAQ,OAAO,UAAU;AAC9C,QAAA,OAAO,WAAW,OAAO;AAC3B,cAAQ,KAAK,EAAE,oBAAoB,GAAG,EAAE;IAAA;EAC1C,IAEF;AACN;AAUO,IAAM,MAAM,CAAuB,KAAQ,SAChD,OAAO,KAAK,KAAK,IAAI;AAShB,IAAM,YACX,OAAO,aACP,SAASC,WAAU,OAAiC;AAEhD,SAAA,OAAO,UAAU,YACjB,SAAS,KAAK,KACd,KAAK,MAAM,KAAK,MAAM;AAE1B;AAQK,IAAM,UACX,MAAM,WACN,SAASC,SAAQ,OAAuB;AAC/B,SAAA,SAAS,KAAK,KAAK,MAAM;AAClC;AASK,IAAM,aAAa,CAAqB,UAC7C,SAAS,KAAK,KAAK,MAAM;AAOpB,IAAM,eAAe,CAC1B,OACA,SAEA,cAAc,KAAK,KACnB,IAAI,OAAO,gBAAgB,MAC1B,CAAC,QAAQ,MAAM,mBAAmB;AAM9B,IAAM,gBAAgB,CAAI,UAC/B,cAAc,KAAK,MAClB,IAAI,OAAO,MAAM,KAChB,CAAC,kBAAkB,aAAa,WAAW,UAAU,EAAE;EAAK,CAAC,MAC3D,IAAI,OAAO,CAAC;AACd;AAaY,SAAA,OAAO,IAA6B,KAAqB;AACvE,SAAO,OAAO,eAAe,GAAG,KAAK,GAAG,GAAgB,cAAc;IACpE,OAAO;EAAA,CACR;AACH;AASO,SAAS,OAAuC,IAAO;AAC5D,SAAQ,GAAiB,cAAc;AACzC;AAYO,SAAS,aACd,MACA,OACA,SAAS,OACS;AACd,MAAA;AACJ,MAAI,QAAQ;AACZ,MAAI,eAAe;AACf,MAAA,CAAC,cAAc,IAAI,GAAG;AACxB,kBAAc,EAAE,KAAK;EAAA,OAChB;AACS,kBAAA;EAAA;AAEhB,QAAM,aAAa,aAAa,WAAW,IACvC,YAAY,iBAAiB,QAC7B;AAEJ,MAAI,cAAc,WAAW,KAAK,YAAY,SAAS,MAAM;AAC3D,QAAI,YAAY,SAAS,UAAa,YAAY,SAAS,MAAM;AACxD,aAAA;IAAA;AAET,QAAI,CAAC,YAAY,YAAY,SAAS,MAAM;AACnC,aAAA;IAAA;AAEL,QAAA,QAAQ,YAAY,IAAI,GAAG;AAC7B,cAAQ,YAAY,KAAK;QACvB,CAACC,UAAc,aAAaA,OAAM,OAAO,IAAI,MAAM;MAAA;AAEtC,qBAAA,YAAY,KACxB,IAAI,CAACA,UAAc,QAAQA,KAAI,CAAC,EAChC,KAAK,MAAM;IAAA,OACT;AACL,qBAAe,QAAQ,WAAW;AAElC,UAAI,iBAAiB,SAAS;AAC5B,gBAAQ,QAAQ,KAAK;MAAA,WACZ,iBAAiB,UAAU;AACpC,gBAAQ,cAAc,KAAK;MAAA,WAE3B,iBAAiB,YACjB,iBAAiB,YACjB,iBAAiB,aACjB,iBAAiB,YACjB;AACQ,gBAAA,cAAc,KAAK,MAAM;MAAA,OAC5B;AACL,gBAAQ,iBAAiB,YAAY;MAAA;IACvC;EACF;AAGF,MAAI,CAAC,OAAO;AACV,UAAM,MAAM,GAAG,UAAU,UAAU,KAAK,wBAAwB,YAAY;AAC5E,QAAI,WAAW,OAAO;AACpB,WAAK,GAAG;AACD,aAAA;IAAA;AAEF,WAAA;EAAA;AAGT,MAAI,IAAI,aAAa,WAAW,KAAK,WAAW,YAAY,SAAS,GAAG;AACtE,UAAM,UAAU;AAChB,UAAM,UAAoB,CAAA;AAC1B,WAAO,CAAC,QAAQ;AACd,cAAQ,KAAK,GAAG;IAAA;AAGV,YAAA,YAAY,UAAU,KAAK;AAC5B,WAAA;AAEP,QAAI,CAAC,OAAO;AACJ,YAAA,OAAO,QAAQ,SAAS,IAAI,OAAO,MAAM,QAAQ,KAAK,MAAM;AAClE,cAAQ,SAAS;AACjB,UAAI,WAAW,OAAO;AACpB,aAAK,GAAG;AACD,eAAA;MAAA;AAEF,aAAA;IAAA;EACT;AAEK,SAAA;AACT;AAQgB,SAAA,OAAgB,MAAc,KAAqB;AAC3D,QAAA,OAAsB,OAAO,iBAAiB,KAAsB;IACxE,gBAAgB;MACd,OAAO;MACP,UAAU;IAAA;IAEZ,YAAY;MACV,MAAM;AACJ,aAAK,WAAW;AACT,eAAA;MAAA;IACT;IAEF,KAAK;MACH,MAAM,KAAW;AACf,YAAI,QAAQ,QAAW;AACrB,cACE,KAAK,SAAS,WACb,MAAM,QAAQ,KAAK,IAAI,KAAK,KAAK,KAAK,SAAS,OAAO,GACvD;AACA,iBAAK,UAAU;AACf;UAAA;AAEE,cAAA,IAAI,MAAM,SAAS,GAAG;AACxB,mBAAO,KAAK;UAAA;AAEP,iBAAA;QAAA;AAEL,YAAA,CAAC,WAAW,GAAG,KAAK,aAAa,MAAM,KAAK,IAAI,MAAM,MAAM;AAC9D,eAAK,GAAG,KAAK,cAAc,8BAA8B,GAAG,GAAG;AACxD,iBAAA;QAAA;AAEL,YAAA,QAAQ,GAAG,GAAG;AACX,eAAA,UAAU,MAAM,UAAU,GAAG;QAAA,WACzB,cAAc,GAAG,GAAG;AACxB,eAAA,UAAU,MAAM,UAAU,GAAG;QAAA,OAC7B;AACL,eAAK,UAAU;QAAA;AAEV,eAAA;MAAA;IACT;EACF,CACD;AAEK,QAAA,EAAE,UAAA,IAAc;AAClB,MAAA,WAAW,SAAS,GAAG;AACpB,SAAA,YAAY,OAAO,WAAW,IAAI;EAAA;AAGlC,SAAA;AACT;AAQgB,SAAA,gBAAyB,MAAc,KAAqB;AACpE,QAAA,OAAO,OAAU,MAAM,GAAG;AACzB,SAAA,OAAO,eAAe,MAAM,YAAY;IAC7C,MAAM,IAA0B;AAC1B,UAAA,WAAW,KAAK,SAAS,GAAG;AAC9B;UACE,GACE,KAAK,cACP;EAAiG,KAAK;YACpG;UAAA,CACD;QAAA;MACH;AAEG,WAAA,YAAY,OAAO,IAAI,IAAI;AACzB,aAAA;IAAA;EACT,CACD;AACH;AAQO,SAAS,MAAwB,KAAW;AACjD,QAAM,cAAc,CAAA;AACpB,SAAO,oBAAoB,GAAG,EAAE,QAAQ,CAAC,QAAQ;AAC/C,gBAAY,GAAc,IAAI,OAAO,yBAAyB,KAAK,GAAG;EAAA,CACvE;AACD,SAAO,OAAO,iBAAiB,CAAA,GAAI,WAAW;AAChD;AAiBgB,SAAA,SAGd,MAAc,QAAW,OAAW;AAE9B,QAAA,OAAO,MAAM,MAAM;AAGzB,OAAK,iBAAiB;AAElB,MAAA,CAAC,cAAc,KAAK,GAAG;AAClB,WAAA;EAAA;AAET,QAAM,EAAE,WAAW,GAAG,KAAA,IAAS;AAK3B,MAAA,WAAW,SAAS,GAAG;AACrB,QAAA,EAAE,WAAW,cAAA,IAAkB;AAEnC,QAAI,eAAe;AACjB,sBAAgB,OAAO,aAAa;IAAA;AAGtC,SAAK,YAAY;MACf,gBACI,SAAmB,OAAYC,QAAY;AACzC;;UAEE,cAAe,KAAK,MAAM,OAAOA,MAAK,KACtC,UAAU,KAAK,MAAM,OAAOA,MAAK;;MAAA,IAGrC;MACJ;IAAA;EACF;AAGK,SAAA,OAAO,OAAO,MAAM,IAAS;AACtC;AAEO,SAAS,OAAOC,SAAgB;AAC9B,SAAAA,QAAO,QAAQ,eAAe,IAAI;AAC3C;ACzaO,IAAM,MAAM,MAAe,gBAAmB,OAAO,CAAA,CAAE;AAEjD,IAAA,OAAO,MAClB,gBAAmB,YAAY;EAC7B,MAAM;AACR,CAAC;AAEU,IAAA,OAAO,MAClB,gBAAgB,WAAW;EACzB,MAAM;AACR,CAAC;AAEU,IAAA,SAAS,MACpB,gBAAmB,UAAU;EAC3B,MAAM;AACR,CAAC;AAEU,IAAA,SAAS,MACpB,gBAAmB,UAAU;EAC3B,MAAM;AACR,CAAC;AAEU,IAAA,QAAQ,MACnB,gBAAqB,SAAS;EAC5B,MAAM;AACR,CAAC;AAEU,IAAA,SAAS,MACpB,gBAAmB,UAAU;EAC3B,MAAM;AACR,CAAC;AAEU,IAAA,UAAU,MACrB,OAAU,WAAW;EACnB,MAAM;EACN,UAAU,OAAO;AACT,UAAA,MAAM,UAAU,KAAK;AAC3B,QAAI,QAAQ,OAAO;AACZ,WAAA,cAAc,KAAK,qBAAqB;IAAA;AAExC,WAAA;EAAA;AAEX,CAAC;AAEU,IAAA,SAAS,MACpB,OAAe,UAAU;EACvB,UAAU,OAAgB;AAClB,UAAA,MAAM,OAAO,UAAU;AAC7B,QAAI,QAAQ,OAAO;AACZ,WAAA,2BAA2B,KAAK,GAAG;IAAA;AAEnC,WAAA;EAAA;AAEX,CAAC;AAEU,IAAA,WAAW,MACtB,OAAO;EACL;IACE,MAAM;IACN,UAAU,OAAgB;AACxB,YAAM,MAAM,UAAU;AACtB,UAAI,QAAQ,OAAO;AACjB,aAAK,iCAAiC;MAAA;AAEjC,aAAA;IAAA;EACT;EAEF;EACA,EAAE,OAAO,WAAW;AACtB;ACrEsB,SAAA,OACtB,aACA,UAAU,4BACV;AACI,MAAA,OAAO,gBAAgB,YAAY;AACrC,UAAM,IAAI;MACR;IAAA;EACF;AAGK,SAAA,OAAU,YAAY,QAAQ,0BAA0B;IAC7D,MAAM;IACN,UAA+B,OAAU;AACjC,YAAA,QAAQ,YAAY,KAAK;AAC/B,UAAI,CAAC;AAAO,aAAK,GAAG,KAAK,cAAc,MAAM,OAAO,EAAE;AAC/C,aAAA;IAAA;EACT,CACD;AACH;AClBA,SAAwB,MACtB,KACA;AACI,MAAA,CAAC,QAAQ,GAAG,GAAG;AACjB,UAAM,IAAI;MACR;IAAA;EACF;AAEF,QAAM,MAAM,mCAAmC,IAC5C,IAAI,CAAC,MAAY,OAAO,MAAM,WAAW,EAAE,SAAA,IAAa,CAAE,EAC1D,KAAK,MAAM,CAAC;AACf,QAAM,OAA+B;IACnC,UAAU,OAAO;AACf,YAAM,QAAQ,IAAI,QAAQ,KAAK,MAAM;AACrC,UAAI,CAAC;AAAO,aAAK,GAAG;AACb,aAAA;IAAA;EACT;AAEF,MAAI,IAAI,QAAQ,IAAI,MAAM,IAAI;AAC5B,UAAM,OAAO,IAAI;MACf,CAAC,KAAK,MAAM;AACN,YAAA,MAAM,QAAQ,MAAM,QAAW;AACjC,gBAAM,SAAU,EAAU;AAE1B,cAAI,QAAQ,MAAM,MAAM,MAAM,IAAI,KAAK,MAAM;QAAA;AAExC,eAAA;MAAA;MAET,CAAA;IAAC;AAGC,QAAA,KAAK,SAAS,GAAG;AACnB,WAAK,OAAO;IAAA;EACd;AAGK,SAAA,OAAkB,SAAS,IAAI;AACxC;AC5BA,SAAwB,UAItB,KAAU;AACN,MAAA,CAAC,QAAQ,GAAG,GAAG;AACjB,UAAM,IAAI;MACR;IAAA;EACF;AAGF,MAAI,sBAAsB;AAC1B,MAAI,cAAc;AAElB,MAAI,eAAmC,CAAA;AAGvC,WAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK,GAAG;AAChC,UAAA,OAAO,IAAI,CAAC;AACd,QAAA,cAAiB,IAAI,GAAG;AACtB,UAAA,WAAW,KAAK,SAAS,GAAG;AACR,8BAAA;MAAA;AAExB,UAAI,aAAgB,MAAM,OAAO,KAAK,KAAK,MAAM;AAChC,uBAAA,aAAa,OAAO,KAAK,IAAmB;AAC3D;MAAA;AAEE,UAAA,aAAgB,MAAM,UAAU,GAAG;AACvB,sBAAA;AACd;MAAA;AAEF,UAAI,KAAK,SAAS,QAAQ,CAAC,KAAK,MAAM;AACpC,aAAK,0DAA0D;AAC/D;MAAA;AAEa,qBAAA,aAAa,OAAO,KAAK,IAAI;IAAA,OACvC;AACL,mBAAa,KAAK,IAAe;IAAA;EACnC;AAIa,iBAAA,aAAa,OAAO,CAAC,GAAG,MAAM,aAAa,QAAQ,CAAC,MAAM,CAAC;AAE1E,QAAM,WACJ,gBAAgB,SAAS,aAAa,SAAS,IAAI,eAAe;AAEpE,MAAI,CAAC,qBAAqB;AAGxB,WAAO,OAAU,aAAa;MAC5B,MAAM;IAAA,CACP;EAAA;AAGH,SAAO,OAAU,aAAa;IAC5B,MAAM;IACN,UAAU,OAAO;AACf,YAAM,MAAgB,CAAA;AACtB,YAAM,QAAQ,IAAI,KAAK,CAAC,SAAS;AAC/B,cAAM,MAAM,aAAa,MAAM,OAAO,IAAI;AACtC,YAAA,OAAO,QAAQ,UAAU;AAC3B,cAAI,KAAK,GAAG;QAAA;AAEd,eAAO,QAAQ;MAAA,CAChB;AAED,UAAI,CAAC,OAAO;AACV;UACE,wDACE,IAAI,MACN;EAA2B,OAAO,IAAI,KAAK,IAAI,CAAC,CAAC;QAAA;MACnD;AAGK,aAAA;IAAA;EACT,CACD;AACH;ACvFA,SAAwB,QAA4C,MAAS;AAC3E,SAAO,OAAuB,WAAW;IACvC,MAAM;IACN,UAAU,QAAe;AACvB,UAAI,UAA4B;AAChC,YAAM,QAAQ,OAAO,MAAM,CAAC,UAAU;AAC1B,kBAAA,aAAa,MAAM,OAAO,IAAI;AACxC,eAAO,YAAY;MAAA,CACpB;AACD,UAAI,CAAC,OAAO;AACL,aAAA;EAAsC,OAAO,OAAiB,CAAC,EAAE;MAAA;AAEjE,aAAA;IAAA;EACT,CACD;AACH;ACfA,SAAwB,WACtB,qBACA;AACA,SAAO,OAAwB,cAAc;IAC3C,MAAM;EAAA,CACP;AACH;ACNA,SAAwB,SAA6C,MAAS;AAC5E,SAAO,OAAqC,YAAY;IACtD,MAAM;IACN,UAAU,KAAK;AACb,UAAI,UAA4B;AAC5B,UAAA,CAAC,cAAc,GAAG,GAAG;AAChB,eAAA;MAAA;AAET,YAAM,QAAQ,OAAO,KAAK,GAAG,EAAE,MAAM,CAAC,QAAQ;AAC5C,kBAAU,aAAa,MAAM,IAAI,GAAG,GAAG,IAAI;AAC3C,eAAO,YAAY;MAAA,CACpB;AAED,UAAI,CAAC,OAAO;AACL,aAAA;EAAuC,OAAO,OAAiB,CAAC,EAAE;MAAA;AAElE,aAAA;IAAA;EACT,CACD;AACH;ACnBA,SAAwB,MAAwB,KAE5B;AACZ,QAAA,OAAO,OAAO,KAAK,GAAG;AACtB,QAAA,eAAe,KAAK,OAAO,CAAC,QAAA;;AAAQ,YAAC,GAAE,SAAY,GAAG,MAAf,mBAAkB;GAAQ;AAEjE,QAAA,OAAO,OAAO,SAAS;IAC3B,MAAM;IACN,UAAwD,OAAO;AACzD,UAAA,CAAC,cAAc,KAAK,GAAG;AAClB,eAAA;MAAA;AAEH,YAAA,YAAY,OAAO,KAAK,KAAK;AAGnC,UACE,aAAa,SAAS,KACtB,aAAa,KAAK,CAAC,QAAQ,UAAU,QAAQ,GAAG,MAAM,EAAE,GACxD;AACA,cAAM,UAAU,aAAa;UAC3B,CAAC,QAAQ,UAAU,QAAQ,GAAG,MAAM;QAAA;AAElC,YAAA,QAAQ,WAAW,GAAG;AACxB,eAAK,8BAA8B,QAAQ,CAAC,CAAC,mBAAmB;QAAA,OAC3D;AACL;YACE,gCAAgC,QAAQ;cACtC;YAAA,CACD;UAAA;QACH;AAGK,eAAA;MAAA;AAGF,aAAA,UAAU,MAAM,CAAC,QAAQ;AAC9B,YAAI,KAAK,QAAQ,GAAG,MAAM,IAAI;AAC5B,cAAK,KAA8B,sBAAsB;AAChD,mBAAA;AACT;YACE,gDAAgD,GAAG,8BAA8B,KAAK;cACpF;YAAA,CACD;UAAA;AAEI,iBAAA;QAAA;AAEHF,cAAAA,QAAQ,IAAY,GAAG;AAC7B,cAAM,QAAQ,aAAaA,OAAM,MAAM,GAAG,GAAG,IAAI;AAC7C,YAAA,OAAO,UAAU,UAAU;AAC7B,eAAK,YAAY,GAAG;GAAkC,OAAO,KAAK,CAAC,EAAE;QAAA;AAEvE,eAAO,UAAU;MAAA,CAClB;IAAA;EACH,CACD;AAEM,SAAA,eAAe,MAAM,qBAAqB;IAC/C,UAAU;IACV,OAAO;EAAA,CACR;AAEM,SAAA,eAAe,MAAM,SAAS;IACnC,MAAM;AACJ,WAAK,oBAAoB;AAClB,aAAA;IAAA;EACT,CACD;AAEM,SAAA;AACT;;;;;;;ACvCA,IAAM,gBAAiC,MAAA;AAjCvC,MAAA;AAmCE;;IAAmB,KAAA,MAAA;MAOjB,WAAW,MAAM;AACf,eAAO,IAAI;MAAA;MAEb,WAAW,OAAO;AAChB,eAAO,KAAK,EAAE,IAAI,KAAK,SAAS,IAAI;MAAA;MAEtC,WAAW,OAAO;AAEZ,YAAA,KAAK,SAAS,SAAS,QAAW;AACpC,iBAAO,KAAK;QAAA;AAEd,eAAO,KAAK,EAAE,IAAI,KAAK,SAAS,IAAI;MAAA;MAEtC,WAAW,SAAS;AAClB,eAAO,OAAO,EAAE,IAAI,KAAK,SAAS,MAAM;MAAA;MAE1C,WAAW,SAAS;AAClB,eAAO,OAAO,EAAE,IAAI,KAAK,SAAS,MAAM;MAAA;MAE1C,WAAW,QAAQ;AACjB,eAAO,MAAM,EAAE,IAAI,KAAK,SAAS,KAAK;MAAA;MAExC,WAAW,SAAS;AAClB,eAAO,OAAO,EAAE,IAAI,KAAK,SAAS,MAAM;MAAA;MAE1C,WAAW,UAAU;AACnB,eAAO,QAAQ,EAAE,IAAI,KAAK,SAAS,OAAO;MAAA;MAE5C,WAAW,SAAS;AAClB,eAAO,OAAO;MAAA;MAGhB,WAAW,WAAW;AACpB,eAAO,SAAS;MAAA;;MAYlB,OAAO,UAAU,MAAa;AAC5B;UACE;QAAA;MACF;IACF,GAvDA,cADF,IACS,YAAsC,CAAA,CAAC,GAE9C,cAHF,IAGS,kBAAA,GAEP,cALF,IAKS,UAAS,MAAA,GAsChB,cA3CF,IA2CkB,UAAS,MACzB,GAAA,cA5CF,IA4CkB,SAAQ,KAAA,GACxB,cA7CF,IA6CkB,cAAa,UAAA,GAC7B,cA9CF,IA8CkB,aAAY,SAC5B,GAAA,cA/CF,IA+CkB,WAAU,OAAA,GAC1B,cAhDF,IAgDkB,YAAW,QAAA,GAC3B,cAjDF,IAiDkB,SAAQ,KASxB,GAAA,cA1DF,IA0DS,SAAQ;MACb,SAAe,OAAU,MAAS;AAChC,eAAO,aAAmB,MAAM,OAAO,IAAI,MAAM;MAAA;MAEnD,OACE,MACA,KACA,YAAuB,OAC0C;AACjE,eACE,YAAY,gBAAmB,MAAM,GAAG,IAAI,OAAU,MAAM,GAAG;MAAA;IAEnE,CAtEJ,GAAA;;AAAA,GAwEG;AAEL,SAAS,YAAY,OAAkC,aAAA,GAAgB;AA7GvE,MAAA;AA8GE,SAAO,KAAA,cAAc,aAAa;IAGhC,WAAW,mBAAmB;AACrB,aAAA,EAAE,GAAG,KAAK,SAAS;IAAA;IAG5B,WAAW,iBAAiB,GAAwC;AAClE,UAAI,MAAM,OAAO;AACf,aAAK,WAAW,CAAA;AAChB;MAAA;AAEF,UAAI,MAAM,MAAM;AACT,aAAA,WAAW,EAAE,GAAG,KAAK;AAC1B;MAAA;AAEG,WAAA,WAAW,EAAE,GAAG,EAAE;IAAA;EACzB,GAhBA,cADK,IACE,YAAsC,EAAE,GAAG,KAAA,CAD7C,GAAA;AAmBT;AAEqB,IAAA,WAAA,cAA+B,YAAA,EAAc;AAAC;", "names": ["warn", "isInteger", "isArray", "type", "props", "string"]}
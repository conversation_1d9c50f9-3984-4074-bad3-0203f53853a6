{"version": 3, "sources": ["../../.pnpm/vue-waterfall-plugin-next@2.6.7/node_modules/vue-waterfall-plugin-next/dist/my-lib.es.js"], "sourcesContent": ["import { getCurrentScope, onScopeDispose, unref, watch, ref, computed, defineComponent, provide, useCssV<PERSON>, openBlock, createElementBlock, normalizeStyle, Fragment, renderList, createElementVNode, renderSlot, inject, onMounted, onUnmounted } from \"vue\";\nfunction tryOnScopeDispose(fn) {\n  if (getCurrentScope()) {\n    onScopeDispose(fn);\n    return true;\n  }\n  return false;\n}\nconst isClient = typeof window !== \"undefined\";\nfunction createFilterWrapper(filter, fn) {\n  function wrapper(...args) {\n    filter(() => fn.apply(this, args), { fn, thisArg: this, args });\n  }\n  return wrapper;\n}\nfunction debounceFilter(ms, options = {}) {\n  let timer;\n  let maxTimer;\n  const filter = (invoke) => {\n    const duration2 = unref(ms);\n    const maxDuration = unref(options.maxWait);\n    if (timer)\n      clearTimeout(timer);\n    if (duration2 <= 0 || maxDuration !== void 0 && maxDuration <= 0) {\n      if (maxTimer) {\n        clearTimeout(maxTimer);\n        maxTimer = null;\n      }\n      return invoke();\n    }\n    if (maxDuration && !maxTimer) {\n      maxTimer = setTimeout(() => {\n        if (timer)\n          clearTimeout(timer);\n        maxTimer = null;\n        invoke();\n      }, maxDuration);\n    }\n    timer = setTimeout(() => {\n      if (maxTimer)\n        clearTimeout(maxTimer);\n      maxTimer = null;\n      invoke();\n    }, duration2);\n  };\n  return filter;\n}\nfunction useDebounceFn(fn, ms = 200, options = {}) {\n  return createFilterWrapper(debounceFilter(ms, options), fn);\n}\nfunction unrefElement(elRef) {\n  var _a2;\n  const plain = unref(elRef);\n  return (_a2 = plain == null ? void 0 : plain.$el) != null ? _a2 : plain;\n}\nconst defaultWindow = isClient ? window : void 0;\nconst _global = typeof globalThis !== \"undefined\" ? globalThis : typeof window !== \"undefined\" ? window : typeof global !== \"undefined\" ? global : typeof self !== \"undefined\" ? self : {};\nconst globalKey = \"__vueuse_ssr_handlers__\";\n_global[globalKey] = _global[globalKey] || {};\n_global[globalKey];\nvar __getOwnPropSymbols$c = Object.getOwnPropertySymbols;\nvar __hasOwnProp$c = Object.prototype.hasOwnProperty;\nvar __propIsEnum$c = Object.prototype.propertyIsEnumerable;\nvar __objRest$2 = (source, exclude) => {\n  var target = {};\n  for (var prop in source)\n    if (__hasOwnProp$c.call(source, prop) && exclude.indexOf(prop) < 0)\n      target[prop] = source[prop];\n  if (source != null && __getOwnPropSymbols$c)\n    for (var prop of __getOwnPropSymbols$c(source)) {\n      if (exclude.indexOf(prop) < 0 && __propIsEnum$c.call(source, prop))\n        target[prop] = source[prop];\n    }\n  return target;\n};\nfunction useResizeObserver(target, callback, options = {}) {\n  const _a2 = options, { window: window2 = defaultWindow } = _a2, observerOptions = __objRest$2(_a2, [\"window\"]);\n  let observer;\n  const isSupported = window2 && \"ResizeObserver\" in window2;\n  const cleanup = () => {\n    if (observer) {\n      observer.disconnect();\n      observer = void 0;\n    }\n  };\n  const stopWatch = watch(() => unrefElement(target), (el) => {\n    cleanup();\n    if (isSupported && window2 && el) {\n      observer = new ResizeObserver(callback);\n      observer.observe(el, observerOptions);\n    }\n  }, { immediate: true, flush: \"post\" });\n  const stop = () => {\n    cleanup();\n    stopWatch();\n  };\n  tryOnScopeDispose(stop);\n  return {\n    isSupported,\n    stop\n  };\n}\nvar _a, _b;\nisClient && (window == null ? void 0 : window.navigator) && ((_a = window == null ? void 0 : window.navigator) == null ? void 0 : _a.platform) && /iP(ad|hone|od)/.test((_b = window == null ? void 0 : window.navigator) == null ? void 0 : _b.platform);\nconst getItemWidth = ({ breakpoints, wrapperWidth, gutter, hasAroundGutter, initWidth }) => {\n  const sizeList = Object.keys(breakpoints).map((key) => {\n    return Number(key);\n  }).sort((a, b) => a - b);\n  let validSize = wrapperWidth;\n  let breakpoint = false;\n  for (const size of sizeList) {\n    if (wrapperWidth <= size) {\n      validSize = size;\n      breakpoint = true;\n      break;\n    }\n  }\n  if (!breakpoint)\n    return initWidth;\n  let breakWidth = 0;\n  const col = breakpoints[validSize].rowPerView;\n  if (hasAroundGutter)\n    breakWidth = (wrapperWidth - gutter) / col - gutter;\n  else\n    breakWidth = (wrapperWidth - (col - 1) * gutter) / col;\n  return Math.floor(breakWidth);\n};\nfunction useCalculateCols(props) {\n  const wrapperWidth = ref(0);\n  const waterfallWrapper = ref(null);\n  useResizeObserver(waterfallWrapper, (entries) => {\n    const entry = entries[0];\n    const { width } = entry.contentRect;\n    wrapperWidth.value = width;\n  });\n  const colWidth = computed(() => {\n    return getItemWidth({\n      wrapperWidth: wrapperWidth.value,\n      breakpoints: props.breakpoints,\n      gutter: props.gutter,\n      hasAroundGutter: props.hasAroundGutter,\n      initWidth: props.width\n    });\n  });\n  const cols = computed(() => {\n    const offset = props.hasAroundGutter ? -props.gutter : props.gutter;\n    const val = (wrapperWidth.value + offset) / (colWidth.value + props.gutter);\n    return Math.floor(val);\n  });\n  const offsetX = computed(() => {\n    if (props.align === \"left\") {\n      return 0;\n    } else if (props.align === \"center\") {\n      const offset = props.hasAroundGutter ? props.gutter : -props.gutter;\n      const contextWidth = cols.value * (colWidth.value + props.gutter) + offset;\n      return (wrapperWidth.value - contextWidth) / 2;\n    } else {\n      const offset = props.hasAroundGutter ? props.gutter : -props.gutter;\n      const contextWidth = cols.value * (colWidth.value + props.gutter) + offset;\n      return wrapperWidth.value - contextWidth;\n    }\n  });\n  return {\n    waterfallWrapper,\n    wrapperWidth,\n    colWidth,\n    cols,\n    offsetX\n  };\n}\nfunction hasClass(el, className) {\n  const reg = new RegExp(`(^|\\\\s)${className}(\\\\s|$)`);\n  return reg.test(el.className);\n}\nfunction addClass(el, className) {\n  if (hasClass(el, className))\n    return;\n  const newClass = el.className.split(/\\s+/);\n  newClass.push(className);\n  el.className = newClass.join(\" \");\n}\nconst elementStyle = document.createElement(\"div\").style;\nconst vendor = (() => {\n  const transformNames = {\n    standard: \"transform\",\n    webkit: \"webkitTransform\",\n    Moz: \"MozTransform\",\n    O: \"OTransform\",\n    ms: \"msTransform\"\n  };\n  for (const key in transformNames) {\n    const val = transformNames[key];\n    if (elementStyle[val] !== void 0)\n      return key;\n  }\n  return false;\n})();\nfunction prefixStyle(style) {\n  if (vendor === false)\n    return false;\n  if (vendor === \"standard\")\n    return style;\n  return vendor + style.charAt(0).toUpperCase() + style.substr(1);\n}\nconst transform = prefixStyle(\"transform\");\nconst duration = prefixStyle(\"animation-duration\");\nconst delay = prefixStyle(\"animation-delay\");\nconst transition = prefixStyle(\"transition\");\nconst fillMode = prefixStyle(\"animation-fill-mode\");\nfunction useLayout(props, colWidth, cols, offsetX, waterfallWrapper, horizontalOrder, heightDifference) {\n  const posY = ref([]);\n  const wrapperHeight = ref(0);\n  const getX = (index) => {\n    const count = props.hasAroundGutter ? index + 1 : index;\n    return props.gutter * count + colWidth.value * index + offsetX.value;\n  };\n  const initY = () => {\n    posY.value = new Array(cols.value).fill(props.hasAroundGutter ? props.gutter : 0);\n  };\n  const animation = addAnimation(props);\n  const layoutHandle = async () => {\n    return new Promise((resolve) => {\n      initY();\n      const items = [];\n      if (waterfallWrapper && waterfallWrapper.value) {\n        waterfallWrapper.value.childNodes.forEach((el) => {\n          if (el.className === \"waterfall-item\")\n            items.push(el);\n        });\n      }\n      if (items.length === 0)\n        return false;\n      for (let i = 0; i < items.length; i++) {\n        const curItem = items[i];\n        let yIndex = findIndexWithinHeightDifference(posY.value, heightDifference);\n        let minY = posY.value[yIndex];\n        if (horizontalOrder) {\n          yIndex = i % cols.value;\n          minY = posY.value[yIndex];\n        }\n        const curX = getX(yIndex);\n        const style = curItem.style;\n        if (transform)\n          style[transform] = `translate3d(${Math.floor(curX)}px,${Math.floor(minY)}px, 0)`;\n        style.width = `${colWidth.value}px`;\n        style.visibility = \"visible\";\n        const { height } = curItem.getBoundingClientRect();\n        posY.value[yIndex] += height + props.gutter;\n        if (!props.animationCancel) {\n          animation(curItem, () => {\n            const time = props.posDuration / 1e3;\n            if (transition)\n              style[transition] = `transform ${time}s`;\n          });\n        }\n      }\n      wrapperHeight.value = Math.max.apply(null, posY.value);\n      setTimeout(() => {\n        resolve(true);\n      }, props.posDuration);\n    });\n  };\n  return {\n    wrapperHeight,\n    layoutHandle\n  };\n}\nfunction findIndexWithinHeightDifference(arr, heightDifference) {\n  if (arr.length === 0)\n    return -1;\n  const minValue = Math.min(...arr);\n  const upperLimit = minValue + heightDifference;\n  let resultIndex = -1;\n  for (let i = 0; i < arr.length; i++) {\n    if (arr[i] >= minValue && arr[i] <= upperLimit) {\n      resultIndex = i;\n      break;\n    }\n  }\n  return resultIndex;\n}\nfunction addAnimation(props) {\n  return (item, callback) => {\n    const content = item.firstChild;\n    if (content && !hasClass(content, props.animationPrefix)) {\n      const durationSec = `${props.animationDuration / 1e3}s`;\n      const delaySec = `${props.animationDelay / 1e3}s`;\n      const style = content.style;\n      addClass(content, props.animationPrefix);\n      addClass(content, props.animationEffect);\n      if (duration)\n        style[duration] = durationSec;\n      if (delay)\n        style[delay] = delaySec;\n      if (fillMode)\n        style[fillMode] = \"both\";\n      if (callback) {\n        setTimeout(() => {\n          callback();\n        }, props.animationDuration + props.animationDelay);\n      }\n    }\n  };\n}\nconst inBrowser = typeof window !== \"undefined\" && window !== null;\nconst hasIntersectionObserver = checkIntersectionObserver();\nconst isEnumerable = Object.prototype.propertyIsEnumerable;\nconst getSymbols = Object.getOwnPropertySymbols;\nfunction getValue(form, ...selectors) {\n  const res = selectors.map((s) => {\n    return s.replace(/\\[(\\w+)\\]/g, \".$1\").split(\".\").reduce((prev, cur) => {\n      return prev && prev[cur];\n    }, form);\n  });\n  return res;\n}\nfunction checkIntersectionObserver() {\n  if (inBrowser && \"IntersectionObserver\" in window && \"IntersectionObserverEntry\" in window && \"intersectionRatio\" in window.IntersectionObserverEntry.prototype) {\n    if (!(\"isIntersecting\" in window.IntersectionObserverEntry.prototype)) {\n      Object.defineProperty(window.IntersectionObserverEntry.prototype, \"isIntersecting\", {\n        get() {\n          return this.intersectionRatio > 0;\n        }\n      });\n    }\n    return true;\n  }\n  return false;\n}\nfunction isObject(val) {\n  return typeof val === \"function\" || toString.call(val) === \"[object Object]\";\n}\nfunction isPrimitive(val) {\n  return typeof val === \"object\" ? val === null : typeof val !== \"function\";\n}\nfunction isValidKey(key) {\n  return key !== \"__proto__\" && key !== \"constructor\" && key !== \"prototype\";\n}\nfunction assignSymbols(target, ...args) {\n  if (!isObject(target))\n    throw new TypeError(\"expected the first argument to be an object\");\n  if (args.length === 0 || typeof Symbol !== \"function\" || typeof getSymbols !== \"function\")\n    return target;\n  for (const arg of args) {\n    const names = getSymbols(arg);\n    for (const key of names) {\n      if (isEnumerable.call(arg, key))\n        target[key] = arg[key];\n    }\n  }\n  return target;\n}\nfunction assign(target, ...args) {\n  let i = 0;\n  if (isPrimitive(target))\n    target = args[i++];\n  if (!target)\n    target = {};\n  for (; i < args.length; i++) {\n    if (isObject(args[i])) {\n      for (const key of Object.keys(args[i])) {\n        if (isValidKey(key)) {\n          if (isObject(target[key]) && isObject(args[i][key]))\n            assign(target[key], args[i][key]);\n          else\n            target[key] = args[i][key];\n        }\n      }\n      assignSymbols(target, args[i]);\n    }\n  }\n  return target;\n}\nfunction loadImage(url, crossOrigin) {\n  return new Promise((resolve, reject) => {\n    const image = new Image();\n    image.onload = () => {\n      resolve(image);\n    };\n    image.onerror = () => {\n      reject(new Error(\"Image load error\"));\n    };\n    if (crossOrigin)\n      image.crossOrigin = \"Anonymous\";\n    image.src = url;\n  });\n}\nvar LifecycleEnum;\n(function(LifecycleEnum2) {\n  LifecycleEnum2[\"LOADING\"] = \"loading\";\n  LifecycleEnum2[\"LOADED\"] = \"loaded\";\n  LifecycleEnum2[\"ERROR\"] = \"error\";\n})(LifecycleEnum || (LifecycleEnum = {}));\nconst DEFAULT_OBSERVER_OPTIONS = {\n  rootMargin: \"0px\",\n  threshold: 0\n};\nconst DEFAULT_LOADING = \"data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7\";\nconst DEFAULT_ERROR = \"\";\nclass Lazy {\n  constructor(flag = true, options, crossOrigin = true) {\n    this.lazyActive = true;\n    this.crossOrigin = true;\n    this.options = {\n      loading: DEFAULT_LOADING,\n      error: DEFAULT_ERROR,\n      observerOptions: DEFAULT_OBSERVER_OPTIONS,\n      log: true,\n      ratioCalculator: (width, height) => height / width\n    };\n    this._images = new WeakMap();\n    this.lazyActive = flag;\n    this.crossOrigin = crossOrigin;\n    this.config(options);\n  }\n  config(options = {}) {\n    assign(this.options, options);\n    options.ratioCalculator && (this.options.ratioCalculator = options.ratioCalculator);\n  }\n  mount(el, binding, callback) {\n    const { src, loading, error } = this._valueFormatter(binding);\n    el.setAttribute(\"lazy\", LifecycleEnum.LOADING);\n    el.setAttribute(\"src\", loading || DEFAULT_LOADING);\n    if (!this.lazyActive) {\n      this._setImageSrc(el, src, callback, error);\n    } else {\n      if (!hasIntersectionObserver) {\n        this._setImageSrc(el, src, callback, error);\n        this._log(() => {\n          throw new Error(\"Not support IntersectionObserver!\");\n        });\n      }\n      this._initIntersectionObserver(el, src, callback, error);\n    }\n  }\n  resize(el, callback) {\n    const lazy = el.getAttribute(\"lazy\");\n    const src = el.getAttribute(\"src\");\n    if (lazy && lazy === LifecycleEnum.LOADED && src) {\n      loadImage(src, this.crossOrigin).then((image) => {\n        const { width, height } = image;\n        const curHeight = el.width / width * height;\n        el.height = curHeight;\n        const style = el.style;\n        style.height = `${curHeight}px`;\n        callback();\n      });\n    }\n  }\n  unmount(el) {\n    const imgItem = this._realObserver(el);\n    imgItem && imgItem.unobserve(el);\n    this._images.delete(el);\n  }\n  _setImageSrc(el, src, callback, error) {\n    if (!src)\n      return;\n    const preSrc = el.getAttribute(\"src\");\n    if (preSrc === src)\n      return;\n    loadImage(src, this.crossOrigin).then((image) => {\n      var _a2, _b2;\n      const { width, height } = image;\n      const ratio = ((_b2 = (_a2 = this.options).ratioCalculator) == null ? void 0 : _b2.call(_a2, width, height)) || height / width;\n      const lazyBox = el.parentNode.parentNode;\n      lazyBox.style.paddingBottom = `${ratio * 100}%`;\n      el.setAttribute(\"lazy\", LifecycleEnum.LOADED);\n      el.removeAttribute(\"src\");\n      el.src = image.src;\n      callback(true);\n    }).catch(() => {\n      const imgItem = this._realObserver(el);\n      imgItem && imgItem.disconnect();\n      if (error) {\n        el.setAttribute(\"lazy\", LifecycleEnum.ERROR);\n        el.setAttribute(\"src\", error);\n        callback(false);\n      }\n      this._log(() => {\n        throw new Error(`Image failed to load!And failed src was: ${src} `);\n      });\n    });\n  }\n  _isOpenLazy() {\n    return hasIntersectionObserver && this.lazyActive;\n  }\n  _initIntersectionObserver(el, src, callback, error) {\n    const observerOptions = this.options.observerOptions;\n    this._images.set(el, new IntersectionObserver((entries) => {\n      Array.prototype.forEach.call(entries, (entry) => {\n        if (entry.isIntersecting) {\n          const imgItem2 = this._realObserver(el);\n          imgItem2 && imgItem2.unobserve(entry.target);\n          this._setImageSrc(el, src, callback, error);\n        }\n      });\n    }, observerOptions));\n    const imgItem = this._realObserver(el);\n    imgItem && imgItem.observe(el);\n  }\n  _valueFormatter(value) {\n    let src = value;\n    let loading = this.options.loading;\n    let error = this.options.error;\n    if (isObject(value)) {\n      src = value.src;\n      loading = value.loading || this.options.loading;\n      error = value.error || this.options.error;\n    }\n    return {\n      src,\n      loading,\n      error\n    };\n  }\n  _log(callback) {\n    if (this.options.log)\n      callback();\n  }\n  _realObserver(el) {\n    return this._images.get(el);\n  }\n}\nvar Waterfall_vue_vue_type_style_index_0_scoped_true_lang = \"\";\nvar _export_sfc = (sfc, props) => {\n  for (const [key, val] of props) {\n    sfc[key] = val;\n  }\n  return sfc;\n};\nconst __default__ = defineComponent({\n  props: {\n    list: {\n      type: Array,\n      default: () => []\n    },\n    rowKey: {\n      type: String,\n      default: \"id\"\n    },\n    imgSelector: {\n      type: String,\n      default: \"src\"\n    },\n    width: {\n      type: Number,\n      default: 200\n    },\n    breakpoints: {\n      type: Object,\n      default: () => ({\n        1200: {\n          rowPerView: 3\n        },\n        800: {\n          rowPerView: 2\n        },\n        500: {\n          rowPerView: 1\n        }\n      })\n    },\n    gutter: {\n      type: Number,\n      default: 10\n    },\n    hasAroundGutter: {\n      type: Boolean,\n      default: true\n    },\n    posDuration: {\n      type: Number,\n      default: 300\n    },\n    animationPrefix: {\n      type: String,\n      default: \"animate__animated\"\n    },\n    animationEffect: {\n      type: String,\n      default: \"fadeIn\"\n    },\n    animationDuration: {\n      type: Number,\n      default: 1e3\n    },\n    animationDelay: {\n      type: Number,\n      default: 300\n    },\n    animationCancel: {\n      type: Boolean,\n      default: false\n    },\n    backgroundColor: {\n      type: String,\n      default: \"#fff\"\n    },\n    lazyload: {\n      type: Boolean,\n      default: true\n    },\n    loadProps: {\n      type: Object,\n      default: () => {\n      }\n    },\n    crossOrigin: {\n      type: Boolean,\n      default: true\n    },\n    delay: {\n      type: Number,\n      default: 300\n    },\n    align: {\n      type: String,\n      default: \"center\"\n    },\n    horizontalOrder: {\n      type: Boolean,\n      default: false\n    },\n    heightDifference: {\n      type: Number,\n      default: 0\n    }\n  },\n  setup(props, ctx) {\n    const lazy = new Lazy(props.lazyload, props.loadProps, props.crossOrigin);\n    provide(\"lazy\", lazy);\n    const {\n      waterfallWrapper,\n      wrapperWidth,\n      colWidth,\n      cols,\n      offsetX\n    } = useCalculateCols(props);\n    const { wrapperHeight, layoutHandle } = useLayout(props, colWidth, cols, offsetX, waterfallWrapper, props.horizontalOrder, props.heightDifference);\n    const renderer = useDebounceFn(() => {\n      layoutHandle().then(() => {\n        ctx.emit(\"afterRender\");\n      });\n    }, props.delay);\n    watch(() => [wrapperWidth, colWidth, props.list], () => {\n      if (wrapperWidth.value > 0)\n        renderer();\n    }, { deep: true });\n    const sizeChangeTime = ref(0);\n    provide(\"sizeChangeTime\", sizeChangeTime);\n    provide(\"imgLoaded\", renderer);\n    const getRenderURL = (item) => {\n      return getValue(item, props.imgSelector)[0];\n    };\n    const getKey = (item, index) => {\n      return item[props.rowKey] || index;\n    };\n    return {\n      colWidth,\n      waterfallWrapper,\n      wrapperHeight,\n      getRenderURL,\n      getKey,\n      renderer\n    };\n  }\n});\nconst __injectCSSVars__ = () => {\n  useCssVars((_ctx) => ({\n    \"feb77110\": _ctx.backgroundColor\n  }));\n};\nconst __setup__ = __default__.setup;\n__default__.setup = __setup__ ? (props, ctx) => {\n  __injectCSSVars__();\n  return __setup__(props, ctx);\n} : __injectCSSVars__;\nconst _sfc_main$1 = __default__;\nconst _hoisted_1$1 = { class: \"waterfall-card\" };\nfunction _sfc_render$1(_ctx, _cache, $props, $setup, $data, $options) {\n  return openBlock(), createElementBlock(\"div\", {\n    ref: \"waterfallWrapper\",\n    class: \"waterfall-list\",\n    style: normalizeStyle({ height: `${_ctx.wrapperHeight}px` })\n  }, [\n    (openBlock(true), createElementBlock(Fragment, null, renderList(_ctx.list, (item, index) => {\n      return openBlock(), createElementBlock(\"div\", {\n        key: _ctx.getKey(item, index),\n        class: \"waterfall-item\"\n      }, [\n        createElementVNode(\"div\", _hoisted_1$1, [\n          renderSlot(_ctx.$slots, \"default\", {\n            item,\n            index,\n            url: _ctx.getRenderURL(item)\n          }, void 0, true),\n          renderSlot(_ctx.$slots, \"item\", {\n            item,\n            index,\n            url: _ctx.getRenderURL(item)\n          }, void 0, true)\n        ])\n      ]);\n    }), 128))\n  ], 4);\n}\nvar Waterfall = /* @__PURE__ */ _export_sfc(_sfc_main$1, [[\"render\", _sfc_render$1], [\"__scopeId\", \"data-v-380efca1\"]]);\nvar LazyImg_vue_vue_type_style_index_0_scoped_true_lang = \"\";\nconst _sfc_main = defineComponent({\n  props: {\n    url: {\n      type: String,\n      default: \"\"\n    },\n    title: {\n      type: String,\n      default: \"\"\n    },\n    alt: {\n      type: String,\n      default: \"\"\n    }\n  },\n  setup(props, ctx) {\n    const imgLoaded = inject(\"imgLoaded\");\n    const lazy = inject(\"lazy\");\n    const lazyRef = ref(null);\n    onMounted(() => {\n      render();\n    });\n    onUnmounted(() => {\n      unRender();\n    });\n    watch(() => props.url, (newValue, oldValue) => {\n      render();\n    });\n    function render() {\n      if (!lazyRef.value)\n        return;\n      lazy.mount(lazyRef.value, props.url, (status) => {\n        imgLoaded();\n        if (status)\n          ctx.emit(\"success\", props.url);\n        else\n          ctx.emit(\"error\", props.url);\n      });\n    }\n    function unRender() {\n      if (!lazyRef.value)\n        return;\n      lazy.unmount(lazyRef.value);\n    }\n    function imageLoad() {\n      ctx.emit(\"load\", props.url);\n    }\n    return {\n      lazyRef,\n      imageLoad\n    };\n  }\n});\nconst _hoisted_1 = { class: \"lazy__box\" };\nconst _hoisted_2 = { class: \"lazy__resource\" };\nconst _hoisted_3 = [\"title\", \"alt\"];\nfunction _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {\n  return openBlock(), createElementBlock(\"div\", _hoisted_1, [\n    createElementVNode(\"div\", _hoisted_2, [\n      createElementVNode(\"img\", {\n        ref: \"lazyRef\",\n        class: \"lazy__img\",\n        title: _ctx.title,\n        alt: _ctx.alt,\n        onLoad: _cache[0] || (_cache[0] = (...args) => _ctx.imageLoad && _ctx.imageLoad(...args))\n      }, null, 40, _hoisted_3)\n    ])\n  ]);\n}\nvar LazyImg = /* @__PURE__ */ _export_sfc(_sfc_main, [[\"render\", _sfc_render], [\"__scopeId\", \"data-v-b53b6b10\"]]);\nexport { LazyImg, Waterfall };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AACA,SAAS,kBAAkB,IAAI;AAC7B,MAAI,gBAAgB,GAAG;AACrB,mBAAe,EAAE;AACjB,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,IAAM,WAAW,OAAO,WAAW;AACnC,SAAS,oBAAoB,QAAQ,IAAI;AACvC,WAAS,WAAW,MAAM;AACxB,WAAO,MAAM,GAAG,MAAM,MAAM,IAAI,GAAG,EAAE,IAAI,SAAS,MAAM,KAAK,CAAC;AAAA,EAChE;AACA,SAAO;AACT;AACA,SAAS,eAAe,IAAI,UAAU,CAAC,GAAG;AACxC,MAAI;AACJ,MAAI;AACJ,QAAM,SAAS,CAAC,WAAW;AACzB,UAAM,YAAY,MAAM,EAAE;AAC1B,UAAM,cAAc,MAAM,QAAQ,OAAO;AACzC,QAAI;AACF,mBAAa,KAAK;AACpB,QAAI,aAAa,KAAK,gBAAgB,UAAU,eAAe,GAAG;AAChE,UAAI,UAAU;AACZ,qBAAa,QAAQ;AACrB,mBAAW;AAAA,MACb;AACA,aAAO,OAAO;AAAA,IAChB;AACA,QAAI,eAAe,CAAC,UAAU;AAC5B,iBAAW,WAAW,MAAM;AAC1B,YAAI;AACF,uBAAa,KAAK;AACpB,mBAAW;AACX,eAAO;AAAA,MACT,GAAG,WAAW;AAAA,IAChB;AACA,YAAQ,WAAW,MAAM;AACvB,UAAI;AACF,qBAAa,QAAQ;AACvB,iBAAW;AACX,aAAO;AAAA,IACT,GAAG,SAAS;AAAA,EACd;AACA,SAAO;AACT;AACA,SAAS,cAAc,IAAI,KAAK,KAAK,UAAU,CAAC,GAAG;AACjD,SAAO,oBAAoB,eAAe,IAAI,OAAO,GAAG,EAAE;AAC5D;AACA,SAAS,aAAa,OAAO;AAC3B,MAAI;AACJ,QAAM,QAAQ,MAAM,KAAK;AACzB,UAAQ,MAAM,SAAS,OAAO,SAAS,MAAM,QAAQ,OAAO,MAAM;AACpE;AACA,IAAM,gBAAgB,WAAW,SAAS;AAC1C,IAAM,UAAU,OAAO,eAAe,cAAc,aAAa,OAAO,WAAW,cAAc,SAAS,OAAO,WAAW,cAAc,SAAS,OAAO,SAAS,cAAc,OAAO,CAAC;AACzL,IAAM,YAAY;AAClB,QAAQ,SAAS,IAAI,QAAQ,SAAS,KAAK,CAAC;AAC5C,QAAQ,SAAS;AACjB,IAAI,wBAAwB,OAAO;AACnC,IAAI,iBAAiB,OAAO,UAAU;AACtC,IAAI,iBAAiB,OAAO,UAAU;AACtC,IAAI,cAAc,CAAC,QAAQ,YAAY;AACrC,MAAI,SAAS,CAAC;AACd,WAAS,QAAQ;AACf,QAAI,eAAe,KAAK,QAAQ,IAAI,KAAK,QAAQ,QAAQ,IAAI,IAAI;AAC/D,aAAO,IAAI,IAAI,OAAO,IAAI;AAC9B,MAAI,UAAU,QAAQ;AACpB,aAAS,QAAQ,sBAAsB,MAAM,GAAG;AAC9C,UAAI,QAAQ,QAAQ,IAAI,IAAI,KAAK,eAAe,KAAK,QAAQ,IAAI;AAC/D,eAAO,IAAI,IAAI,OAAO,IAAI;AAAA,IAC9B;AACF,SAAO;AACT;AACA,SAAS,kBAAkB,QAAQ,UAAU,UAAU,CAAC,GAAG;AACzD,QAAM,MAAM,SAAS,EAAE,QAAQ,UAAU,cAAc,IAAI,KAAK,kBAAkB,YAAY,KAAK,CAAC,QAAQ,CAAC;AAC7G,MAAI;AACJ,QAAM,cAAc,WAAW,oBAAoB;AACnD,QAAM,UAAU,MAAM;AACpB,QAAI,UAAU;AACZ,eAAS,WAAW;AACpB,iBAAW;AAAA,IACb;AAAA,EACF;AACA,QAAM,YAAY,MAAM,MAAM,aAAa,MAAM,GAAG,CAAC,OAAO;AAC1D,YAAQ;AACR,QAAI,eAAe,WAAW,IAAI;AAChC,iBAAW,IAAI,eAAe,QAAQ;AACtC,eAAS,QAAQ,IAAI,eAAe;AAAA,IACtC;AAAA,EACF,GAAG,EAAE,WAAW,MAAM,OAAO,OAAO,CAAC;AACrC,QAAM,OAAO,MAAM;AACjB,YAAQ;AACR,cAAU;AAAA,EACZ;AACA,oBAAkB,IAAI;AACtB,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AACA,IAAI;AAAJ,IAAQ;AACR,aAAa,UAAU,OAAO,SAAS,OAAO,gBAAgB,KAAK,UAAU,OAAO,SAAS,OAAO,cAAc,OAAO,SAAS,GAAG,aAAa,iBAAiB,MAAM,KAAK,UAAU,OAAO,SAAS,OAAO,cAAc,OAAO,SAAS,GAAG,QAAQ;AACxP,IAAM,eAAe,CAAC,EAAE,aAAa,cAAc,QAAQ,iBAAiB,UAAU,MAAM;AAC1F,QAAM,WAAW,OAAO,KAAK,WAAW,EAAE,IAAI,CAAC,QAAQ;AACrD,WAAO,OAAO,GAAG;AAAA,EACnB,CAAC,EAAE,KAAK,CAAC,GAAG,MAAM,IAAI,CAAC;AACvB,MAAI,YAAY;AAChB,MAAI,aAAa;AACjB,aAAW,QAAQ,UAAU;AAC3B,QAAI,gBAAgB,MAAM;AACxB,kBAAY;AACZ,mBAAa;AACb;AAAA,IACF;AAAA,EACF;AACA,MAAI,CAAC;AACH,WAAO;AACT,MAAI,aAAa;AACjB,QAAM,MAAM,YAAY,SAAS,EAAE;AACnC,MAAI;AACF,kBAAc,eAAe,UAAU,MAAM;AAAA;AAE7C,kBAAc,gBAAgB,MAAM,KAAK,UAAU;AACrD,SAAO,KAAK,MAAM,UAAU;AAC9B;AACA,SAAS,iBAAiB,OAAO;AAC/B,QAAM,eAAe,IAAI,CAAC;AAC1B,QAAM,mBAAmB,IAAI,IAAI;AACjC,oBAAkB,kBAAkB,CAAC,YAAY;AAC/C,UAAM,QAAQ,QAAQ,CAAC;AACvB,UAAM,EAAE,MAAM,IAAI,MAAM;AACxB,iBAAa,QAAQ;AAAA,EACvB,CAAC;AACD,QAAM,WAAW,SAAS,MAAM;AAC9B,WAAO,aAAa;AAAA,MAClB,cAAc,aAAa;AAAA,MAC3B,aAAa,MAAM;AAAA,MACnB,QAAQ,MAAM;AAAA,MACd,iBAAiB,MAAM;AAAA,MACvB,WAAW,MAAM;AAAA,IACnB,CAAC;AAAA,EACH,CAAC;AACD,QAAM,OAAO,SAAS,MAAM;AAC1B,UAAM,SAAS,MAAM,kBAAkB,CAAC,MAAM,SAAS,MAAM;AAC7D,UAAM,OAAO,aAAa,QAAQ,WAAW,SAAS,QAAQ,MAAM;AACpE,WAAO,KAAK,MAAM,GAAG;AAAA,EACvB,CAAC;AACD,QAAM,UAAU,SAAS,MAAM;AAC7B,QAAI,MAAM,UAAU,QAAQ;AAC1B,aAAO;AAAA,IACT,WAAW,MAAM,UAAU,UAAU;AACnC,YAAM,SAAS,MAAM,kBAAkB,MAAM,SAAS,CAAC,MAAM;AAC7D,YAAM,eAAe,KAAK,SAAS,SAAS,QAAQ,MAAM,UAAU;AACpE,cAAQ,aAAa,QAAQ,gBAAgB;AAAA,IAC/C,OAAO;AACL,YAAM,SAAS,MAAM,kBAAkB,MAAM,SAAS,CAAC,MAAM;AAC7D,YAAM,eAAe,KAAK,SAAS,SAAS,QAAQ,MAAM,UAAU;AACpE,aAAO,aAAa,QAAQ;AAAA,IAC9B;AAAA,EACF,CAAC;AACD,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AACA,SAAS,SAAS,IAAI,WAAW;AAC/B,QAAM,MAAM,IAAI,OAAO,UAAU,SAAS,SAAS;AACnD,SAAO,IAAI,KAAK,GAAG,SAAS;AAC9B;AACA,SAAS,SAAS,IAAI,WAAW;AAC/B,MAAI,SAAS,IAAI,SAAS;AACxB;AACF,QAAM,WAAW,GAAG,UAAU,MAAM,KAAK;AACzC,WAAS,KAAK,SAAS;AACvB,KAAG,YAAY,SAAS,KAAK,GAAG;AAClC;AACA,IAAM,eAAe,SAAS,cAAc,KAAK,EAAE;AACnD,IAAM,UAAU,MAAM;AACpB,QAAM,iBAAiB;AAAA,IACrB,UAAU;AAAA,IACV,QAAQ;AAAA,IACR,KAAK;AAAA,IACL,GAAG;AAAA,IACH,IAAI;AAAA,EACN;AACA,aAAW,OAAO,gBAAgB;AAChC,UAAM,MAAM,eAAe,GAAG;AAC9B,QAAI,aAAa,GAAG,MAAM;AACxB,aAAO;AAAA,EACX;AACA,SAAO;AACT,GAAG;AACH,SAAS,YAAY,OAAO;AAC1B,MAAI,WAAW;AACb,WAAO;AACT,MAAI,WAAW;AACb,WAAO;AACT,SAAO,SAAS,MAAM,OAAO,CAAC,EAAE,YAAY,IAAI,MAAM,OAAO,CAAC;AAChE;AACA,IAAM,YAAY,YAAY,WAAW;AACzC,IAAM,WAAW,YAAY,oBAAoB;AACjD,IAAM,QAAQ,YAAY,iBAAiB;AAC3C,IAAM,aAAa,YAAY,YAAY;AAC3C,IAAM,WAAW,YAAY,qBAAqB;AAClD,SAAS,UAAU,OAAO,UAAU,MAAM,SAAS,kBAAkB,iBAAiB,kBAAkB;AACtG,QAAM,OAAO,IAAI,CAAC,CAAC;AACnB,QAAM,gBAAgB,IAAI,CAAC;AAC3B,QAAM,OAAO,CAAC,UAAU;AACtB,UAAM,QAAQ,MAAM,kBAAkB,QAAQ,IAAI;AAClD,WAAO,MAAM,SAAS,QAAQ,SAAS,QAAQ,QAAQ,QAAQ;AAAA,EACjE;AACA,QAAM,QAAQ,MAAM;AAClB,SAAK,QAAQ,IAAI,MAAM,KAAK,KAAK,EAAE,KAAK,MAAM,kBAAkB,MAAM,SAAS,CAAC;AAAA,EAClF;AACA,QAAM,YAAY,aAAa,KAAK;AACpC,QAAM,eAAe,YAAY;AAC/B,WAAO,IAAI,QAAQ,CAAC,YAAY;AAC9B,YAAM;AACN,YAAM,QAAQ,CAAC;AACf,UAAI,oBAAoB,iBAAiB,OAAO;AAC9C,yBAAiB,MAAM,WAAW,QAAQ,CAAC,OAAO;AAChD,cAAI,GAAG,cAAc;AACnB,kBAAM,KAAK,EAAE;AAAA,QACjB,CAAC;AAAA,MACH;AACA,UAAI,MAAM,WAAW;AACnB,eAAO;AACT,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,cAAM,UAAU,MAAM,CAAC;AACvB,YAAI,SAAS,gCAAgC,KAAK,OAAO,gBAAgB;AACzE,YAAI,OAAO,KAAK,MAAM,MAAM;AAC5B,YAAI,iBAAiB;AACnB,mBAAS,IAAI,KAAK;AAClB,iBAAO,KAAK,MAAM,MAAM;AAAA,QAC1B;AACA,cAAM,OAAO,KAAK,MAAM;AACxB,cAAM,QAAQ,QAAQ;AACtB,YAAI;AACF,gBAAM,SAAS,IAAI,eAAe,KAAK,MAAM,IAAI,CAAC,MAAM,KAAK,MAAM,IAAI,CAAC;AAC1E,cAAM,QAAQ,GAAG,SAAS,KAAK;AAC/B,cAAM,aAAa;AACnB,cAAM,EAAE,OAAO,IAAI,QAAQ,sBAAsB;AACjD,aAAK,MAAM,MAAM,KAAK,SAAS,MAAM;AACrC,YAAI,CAAC,MAAM,iBAAiB;AAC1B,oBAAU,SAAS,MAAM;AACvB,kBAAM,OAAO,MAAM,cAAc;AACjC,gBAAI;AACF,oBAAM,UAAU,IAAI,aAAa,IAAI;AAAA,UACzC,CAAC;AAAA,QACH;AAAA,MACF;AACA,oBAAc,QAAQ,KAAK,IAAI,MAAM,MAAM,KAAK,KAAK;AACrD,iBAAW,MAAM;AACf,gBAAQ,IAAI;AAAA,MACd,GAAG,MAAM,WAAW;AAAA,IACtB,CAAC;AAAA,EACH;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AACA,SAAS,gCAAgC,KAAK,kBAAkB;AAC9D,MAAI,IAAI,WAAW;AACjB,WAAO;AACT,QAAM,WAAW,KAAK,IAAI,GAAG,GAAG;AAChC,QAAM,aAAa,WAAW;AAC9B,MAAI,cAAc;AAClB,WAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACnC,QAAI,IAAI,CAAC,KAAK,YAAY,IAAI,CAAC,KAAK,YAAY;AAC9C,oBAAc;AACd;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,aAAa,OAAO;AAC3B,SAAO,CAAC,MAAM,aAAa;AACzB,UAAM,UAAU,KAAK;AACrB,QAAI,WAAW,CAAC,SAAS,SAAS,MAAM,eAAe,GAAG;AACxD,YAAM,cAAc,GAAG,MAAM,oBAAoB,GAAG;AACpD,YAAM,WAAW,GAAG,MAAM,iBAAiB,GAAG;AAC9C,YAAM,QAAQ,QAAQ;AACtB,eAAS,SAAS,MAAM,eAAe;AACvC,eAAS,SAAS,MAAM,eAAe;AACvC,UAAI;AACF,cAAM,QAAQ,IAAI;AACpB,UAAI;AACF,cAAM,KAAK,IAAI;AACjB,UAAI;AACF,cAAM,QAAQ,IAAI;AACpB,UAAI,UAAU;AACZ,mBAAW,MAAM;AACf,mBAAS;AAAA,QACX,GAAG,MAAM,oBAAoB,MAAM,cAAc;AAAA,MACnD;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAM,YAAY,OAAO,WAAW,eAAe,WAAW;AAC9D,IAAM,0BAA0B,0BAA0B;AAC1D,IAAM,eAAe,OAAO,UAAU;AACtC,IAAM,aAAa,OAAO;AAC1B,SAAS,SAAS,SAAS,WAAW;AACpC,QAAM,MAAM,UAAU,IAAI,CAAC,MAAM;AAC/B,WAAO,EAAE,QAAQ,cAAc,KAAK,EAAE,MAAM,GAAG,EAAE,OAAO,CAAC,MAAM,QAAQ;AACrE,aAAO,QAAQ,KAAK,GAAG;AAAA,IACzB,GAAG,IAAI;AAAA,EACT,CAAC;AACD,SAAO;AACT;AACA,SAAS,4BAA4B;AACnC,MAAI,aAAa,0BAA0B,UAAU,+BAA+B,UAAU,uBAAuB,OAAO,0BAA0B,WAAW;AAC/J,QAAI,EAAE,oBAAoB,OAAO,0BAA0B,YAAY;AACrE,aAAO,eAAe,OAAO,0BAA0B,WAAW,kBAAkB;AAAA,QAClF,MAAM;AACJ,iBAAO,KAAK,oBAAoB;AAAA,QAClC;AAAA,MACF,CAAC;AAAA,IACH;AACA,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,SAAS,SAAS,KAAK;AACrB,SAAO,OAAO,QAAQ,cAAc,SAAS,KAAK,GAAG,MAAM;AAC7D;AACA,SAAS,YAAY,KAAK;AACxB,SAAO,OAAO,QAAQ,WAAW,QAAQ,OAAO,OAAO,QAAQ;AACjE;AACA,SAAS,WAAW,KAAK;AACvB,SAAO,QAAQ,eAAe,QAAQ,iBAAiB,QAAQ;AACjE;AACA,SAAS,cAAc,WAAW,MAAM;AACtC,MAAI,CAAC,SAAS,MAAM;AAClB,UAAM,IAAI,UAAU,6CAA6C;AACnE,MAAI,KAAK,WAAW,KAAK,OAAO,WAAW,cAAc,OAAO,eAAe;AAC7E,WAAO;AACT,aAAW,OAAO,MAAM;AACtB,UAAM,QAAQ,WAAW,GAAG;AAC5B,eAAW,OAAO,OAAO;AACvB,UAAI,aAAa,KAAK,KAAK,GAAG;AAC5B,eAAO,GAAG,IAAI,IAAI,GAAG;AAAA,IACzB;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,OAAO,WAAW,MAAM;AAC/B,MAAI,IAAI;AACR,MAAI,YAAY,MAAM;AACpB,aAAS,KAAK,GAAG;AACnB,MAAI,CAAC;AACH,aAAS,CAAC;AACZ,SAAO,IAAI,KAAK,QAAQ,KAAK;AAC3B,QAAI,SAAS,KAAK,CAAC,CAAC,GAAG;AACrB,iBAAW,OAAO,OAAO,KAAK,KAAK,CAAC,CAAC,GAAG;AACtC,YAAI,WAAW,GAAG,GAAG;AACnB,cAAI,SAAS,OAAO,GAAG,CAAC,KAAK,SAAS,KAAK,CAAC,EAAE,GAAG,CAAC;AAChD,mBAAO,OAAO,GAAG,GAAG,KAAK,CAAC,EAAE,GAAG,CAAC;AAAA;AAEhC,mBAAO,GAAG,IAAI,KAAK,CAAC,EAAE,GAAG;AAAA,QAC7B;AAAA,MACF;AACA,oBAAc,QAAQ,KAAK,CAAC,CAAC;AAAA,IAC/B;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,UAAU,KAAK,aAAa;AACnC,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,UAAM,QAAQ,IAAI,MAAM;AACxB,UAAM,SAAS,MAAM;AACnB,cAAQ,KAAK;AAAA,IACf;AACA,UAAM,UAAU,MAAM;AACpB,aAAO,IAAI,MAAM,kBAAkB,CAAC;AAAA,IACtC;AACA,QAAI;AACF,YAAM,cAAc;AACtB,UAAM,MAAM;AAAA,EACd,CAAC;AACH;AACA,IAAI;AAAA,CACH,SAAS,gBAAgB;AACxB,iBAAe,SAAS,IAAI;AAC5B,iBAAe,QAAQ,IAAI;AAC3B,iBAAe,OAAO,IAAI;AAC5B,GAAG,kBAAkB,gBAAgB,CAAC,EAAE;AACxC,IAAM,2BAA2B;AAAA,EAC/B,YAAY;AAAA,EACZ,WAAW;AACb;AACA,IAAM,kBAAkB;AACxB,IAAM,gBAAgB;AACtB,IAAM,OAAN,MAAW;AAAA,EACT,YAAY,OAAO,MAAM,SAAS,cAAc,MAAM;AACpD,SAAK,aAAa;AAClB,SAAK,cAAc;AACnB,SAAK,UAAU;AAAA,MACb,SAAS;AAAA,MACT,OAAO;AAAA,MACP,iBAAiB;AAAA,MACjB,KAAK;AAAA,MACL,iBAAiB,CAAC,OAAO,WAAW,SAAS;AAAA,IAC/C;AACA,SAAK,UAAU,oBAAI,QAAQ;AAC3B,SAAK,aAAa;AAClB,SAAK,cAAc;AACnB,SAAK,OAAO,OAAO;AAAA,EACrB;AAAA,EACA,OAAO,UAAU,CAAC,GAAG;AACnB,WAAO,KAAK,SAAS,OAAO;AAC5B,YAAQ,oBAAoB,KAAK,QAAQ,kBAAkB,QAAQ;AAAA,EACrE;AAAA,EACA,MAAM,IAAI,SAAS,UAAU;AAC3B,UAAM,EAAE,KAAK,SAAS,MAAM,IAAI,KAAK,gBAAgB,OAAO;AAC5D,OAAG,aAAa,QAAQ,cAAc,OAAO;AAC7C,OAAG,aAAa,OAAO,WAAW,eAAe;AACjD,QAAI,CAAC,KAAK,YAAY;AACpB,WAAK,aAAa,IAAI,KAAK,UAAU,KAAK;AAAA,IAC5C,OAAO;AACL,UAAI,CAAC,yBAAyB;AAC5B,aAAK,aAAa,IAAI,KAAK,UAAU,KAAK;AAC1C,aAAK,KAAK,MAAM;AACd,gBAAM,IAAI,MAAM,mCAAmC;AAAA,QACrD,CAAC;AAAA,MACH;AACA,WAAK,0BAA0B,IAAI,KAAK,UAAU,KAAK;AAAA,IACzD;AAAA,EACF;AAAA,EACA,OAAO,IAAI,UAAU;AACnB,UAAM,OAAO,GAAG,aAAa,MAAM;AACnC,UAAM,MAAM,GAAG,aAAa,KAAK;AACjC,QAAI,QAAQ,SAAS,cAAc,UAAU,KAAK;AAChD,gBAAU,KAAK,KAAK,WAAW,EAAE,KAAK,CAAC,UAAU;AAC/C,cAAM,EAAE,OAAO,OAAO,IAAI;AAC1B,cAAM,YAAY,GAAG,QAAQ,QAAQ;AACrC,WAAG,SAAS;AACZ,cAAM,QAAQ,GAAG;AACjB,cAAM,SAAS,GAAG,SAAS;AAC3B,iBAAS;AAAA,MACX,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,QAAQ,IAAI;AACV,UAAM,UAAU,KAAK,cAAc,EAAE;AACrC,eAAW,QAAQ,UAAU,EAAE;AAC/B,SAAK,QAAQ,OAAO,EAAE;AAAA,EACxB;AAAA,EACA,aAAa,IAAI,KAAK,UAAU,OAAO;AACrC,QAAI,CAAC;AACH;AACF,UAAM,SAAS,GAAG,aAAa,KAAK;AACpC,QAAI,WAAW;AACb;AACF,cAAU,KAAK,KAAK,WAAW,EAAE,KAAK,CAAC,UAAU;AAC/C,UAAI,KAAK;AACT,YAAM,EAAE,OAAO,OAAO,IAAI;AAC1B,YAAM,UAAU,OAAO,MAAM,KAAK,SAAS,oBAAoB,OAAO,SAAS,IAAI,KAAK,KAAK,OAAO,MAAM,MAAM,SAAS;AACzH,YAAM,UAAU,GAAG,WAAW;AAC9B,cAAQ,MAAM,gBAAgB,GAAG,QAAQ,GAAG;AAC5C,SAAG,aAAa,QAAQ,cAAc,MAAM;AAC5C,SAAG,gBAAgB,KAAK;AACxB,SAAG,MAAM,MAAM;AACf,eAAS,IAAI;AAAA,IACf,CAAC,EAAE,MAAM,MAAM;AACb,YAAM,UAAU,KAAK,cAAc,EAAE;AACrC,iBAAW,QAAQ,WAAW;AAC9B,UAAI,OAAO;AACT,WAAG,aAAa,QAAQ,cAAc,KAAK;AAC3C,WAAG,aAAa,OAAO,KAAK;AAC5B,iBAAS,KAAK;AAAA,MAChB;AACA,WAAK,KAAK,MAAM;AACd,cAAM,IAAI,MAAM,4CAA4C,GAAG,GAAG;AAAA,MACpE,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA,EACA,cAAc;AACZ,WAAO,2BAA2B,KAAK;AAAA,EACzC;AAAA,EACA,0BAA0B,IAAI,KAAK,UAAU,OAAO;AAClD,UAAM,kBAAkB,KAAK,QAAQ;AACrC,SAAK,QAAQ,IAAI,IAAI,IAAI,qBAAqB,CAAC,YAAY;AACzD,YAAM,UAAU,QAAQ,KAAK,SAAS,CAAC,UAAU;AAC/C,YAAI,MAAM,gBAAgB;AACxB,gBAAM,WAAW,KAAK,cAAc,EAAE;AACtC,sBAAY,SAAS,UAAU,MAAM,MAAM;AAC3C,eAAK,aAAa,IAAI,KAAK,UAAU,KAAK;AAAA,QAC5C;AAAA,MACF,CAAC;AAAA,IACH,GAAG,eAAe,CAAC;AACnB,UAAM,UAAU,KAAK,cAAc,EAAE;AACrC,eAAW,QAAQ,QAAQ,EAAE;AAAA,EAC/B;AAAA,EACA,gBAAgB,OAAO;AACrB,QAAI,MAAM;AACV,QAAI,UAAU,KAAK,QAAQ;AAC3B,QAAI,QAAQ,KAAK,QAAQ;AACzB,QAAI,SAAS,KAAK,GAAG;AACnB,YAAM,MAAM;AACZ,gBAAU,MAAM,WAAW,KAAK,QAAQ;AACxC,cAAQ,MAAM,SAAS,KAAK,QAAQ;AAAA,IACtC;AACA,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA,EACA,KAAK,UAAU;AACb,QAAI,KAAK,QAAQ;AACf,eAAS;AAAA,EACb;AAAA,EACA,cAAc,IAAI;AAChB,WAAO,KAAK,QAAQ,IAAI,EAAE;AAAA,EAC5B;AACF;AAEA,IAAI,cAAc,CAAC,KAAK,UAAU;AAChC,aAAW,CAAC,KAAK,GAAG,KAAK,OAAO;AAC9B,QAAI,GAAG,IAAI;AAAA,EACb;AACA,SAAO;AACT;AACA,IAAM,cAAc,gBAAgB;AAAA,EAClC,OAAO;AAAA,IACL,MAAM;AAAA,MACJ,MAAM;AAAA,MACN,SAAS,MAAM,CAAC;AAAA,IAClB;AAAA,IACA,QAAQ;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,aAAa;AAAA,MACX,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,OAAO;AAAA,MACL,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,aAAa;AAAA,MACX,MAAM;AAAA,MACN,SAAS,OAAO;AAAA,QACd,MAAM;AAAA,UACJ,YAAY;AAAA,QACd;AAAA,QACA,KAAK;AAAA,UACH,YAAY;AAAA,QACd;AAAA,QACA,KAAK;AAAA,UACH,YAAY;AAAA,QACd;AAAA,MACF;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,iBAAiB;AAAA,MACf,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,aAAa;AAAA,MACX,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,iBAAiB;AAAA,MACf,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,iBAAiB;AAAA,MACf,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,mBAAmB;AAAA,MACjB,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,gBAAgB;AAAA,MACd,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,iBAAiB;AAAA,MACf,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,iBAAiB;AAAA,MACf,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,UAAU;AAAA,MACR,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,WAAW;AAAA,MACT,MAAM;AAAA,MACN,SAAS,MAAM;AAAA,MACf;AAAA,IACF;AAAA,IACA,aAAa;AAAA,MACX,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,OAAO;AAAA,MACL,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,OAAO;AAAA,MACL,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,iBAAiB;AAAA,MACf,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,kBAAkB;AAAA,MAChB,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,EACF;AAAA,EACA,MAAM,OAAO,KAAK;AAChB,UAAM,OAAO,IAAI,KAAK,MAAM,UAAU,MAAM,WAAW,MAAM,WAAW;AACxE,YAAQ,QAAQ,IAAI;AACpB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI,iBAAiB,KAAK;AAC1B,UAAM,EAAE,eAAe,aAAa,IAAI,UAAU,OAAO,UAAU,MAAM,SAAS,kBAAkB,MAAM,iBAAiB,MAAM,gBAAgB;AACjJ,UAAM,WAAW,cAAc,MAAM;AACnC,mBAAa,EAAE,KAAK,MAAM;AACxB,YAAI,KAAK,aAAa;AAAA,MACxB,CAAC;AAAA,IACH,GAAG,MAAM,KAAK;AACd,UAAM,MAAM,CAAC,cAAc,UAAU,MAAM,IAAI,GAAG,MAAM;AACtD,UAAI,aAAa,QAAQ;AACvB,iBAAS;AAAA,IACb,GAAG,EAAE,MAAM,KAAK,CAAC;AACjB,UAAM,iBAAiB,IAAI,CAAC;AAC5B,YAAQ,kBAAkB,cAAc;AACxC,YAAQ,aAAa,QAAQ;AAC7B,UAAM,eAAe,CAAC,SAAS;AAC7B,aAAO,SAAS,MAAM,MAAM,WAAW,EAAE,CAAC;AAAA,IAC5C;AACA,UAAM,SAAS,CAAC,MAAM,UAAU;AAC9B,aAAO,KAAK,MAAM,MAAM,KAAK;AAAA,IAC/B;AACA,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACF,CAAC;AACD,IAAM,oBAAoB,MAAM;AAC9B,aAAW,CAAC,UAAU;AAAA,IACpB,YAAY,KAAK;AAAA,EACnB,EAAE;AACJ;AACA,IAAM,YAAY,YAAY;AAC9B,YAAY,QAAQ,YAAY,CAAC,OAAO,QAAQ;AAC9C,oBAAkB;AAClB,SAAO,UAAU,OAAO,GAAG;AAC7B,IAAI;AACJ,IAAM,cAAc;AACpB,IAAM,eAAe,EAAE,OAAO,iBAAiB;AAC/C,SAAS,cAAc,MAAM,QAAQ,QAAQ,QAAQ,OAAO,UAAU;AACpE,SAAO,UAAU,GAAG,mBAAmB,OAAO;AAAA,IAC5C,KAAK;AAAA,IACL,OAAO;AAAA,IACP,OAAO,eAAe,EAAE,QAAQ,GAAG,KAAK,aAAa,KAAK,CAAC;AAAA,EAC7D,GAAG;AAAA,KACA,UAAU,IAAI,GAAG,mBAAmB,UAAU,MAAM,WAAW,KAAK,MAAM,CAAC,MAAM,UAAU;AAC1F,aAAO,UAAU,GAAG,mBAAmB,OAAO;AAAA,QAC5C,KAAK,KAAK,OAAO,MAAM,KAAK;AAAA,QAC5B,OAAO;AAAA,MACT,GAAG;AAAA,QACD,gBAAmB,OAAO,cAAc;AAAA,UACtC,WAAW,KAAK,QAAQ,WAAW;AAAA,YACjC;AAAA,YACA;AAAA,YACA,KAAK,KAAK,aAAa,IAAI;AAAA,UAC7B,GAAG,QAAQ,IAAI;AAAA,UACf,WAAW,KAAK,QAAQ,QAAQ;AAAA,YAC9B;AAAA,YACA;AAAA,YACA,KAAK,KAAK,aAAa,IAAI;AAAA,UAC7B,GAAG,QAAQ,IAAI;AAAA,QACjB,CAAC;AAAA,MACH,CAAC;AAAA,IACH,CAAC,GAAG,GAAG;AAAA,EACT,GAAG,CAAC;AACN;AACA,IAAI,YAA4B,YAAY,aAAa,CAAC,CAAC,UAAU,aAAa,GAAG,CAAC,aAAa,iBAAiB,CAAC,CAAC;AAEtH,IAAM,YAAY,gBAAgB;AAAA,EAChC,OAAO;AAAA,IACL,KAAK;AAAA,MACH,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,OAAO;AAAA,MACL,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,KAAK;AAAA,MACH,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,EACF;AAAA,EACA,MAAM,OAAO,KAAK;AAChB,UAAM,YAAY,OAAO,WAAW;AACpC,UAAM,OAAO,OAAO,MAAM;AAC1B,UAAM,UAAU,IAAI,IAAI;AACxB,cAAU,MAAM;AACd,aAAO;AAAA,IACT,CAAC;AACD,gBAAY,MAAM;AAChB,eAAS;AAAA,IACX,CAAC;AACD,UAAM,MAAM,MAAM,KAAK,CAAC,UAAU,aAAa;AAC7C,aAAO;AAAA,IACT,CAAC;AACD,aAAS,SAAS;AAChB,UAAI,CAAC,QAAQ;AACX;AACF,WAAK,MAAM,QAAQ,OAAO,MAAM,KAAK,CAAC,WAAW;AAC/C,kBAAU;AACV,YAAI;AACF,cAAI,KAAK,WAAW,MAAM,GAAG;AAAA;AAE7B,cAAI,KAAK,SAAS,MAAM,GAAG;AAAA,MAC/B,CAAC;AAAA,IACH;AACA,aAAS,WAAW;AAClB,UAAI,CAAC,QAAQ;AACX;AACF,WAAK,QAAQ,QAAQ,KAAK;AAAA,IAC5B;AACA,aAAS,YAAY;AACnB,UAAI,KAAK,QAAQ,MAAM,GAAG;AAAA,IAC5B;AACA,WAAO;AAAA,MACL;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACF,CAAC;AACD,IAAM,aAAa,EAAE,OAAO,YAAY;AACxC,IAAM,aAAa,EAAE,OAAO,iBAAiB;AAC7C,IAAM,aAAa,CAAC,SAAS,KAAK;AAClC,SAAS,YAAY,MAAM,QAAQ,QAAQ,QAAQ,OAAO,UAAU;AAClE,SAAO,UAAU,GAAG,mBAAmB,OAAO,YAAY;AAAA,IACxD,gBAAmB,OAAO,YAAY;AAAA,MACpC,gBAAmB,OAAO;AAAA,QACxB,KAAK;AAAA,QACL,OAAO;AAAA,QACP,OAAO,KAAK;AAAA,QACZ,KAAK,KAAK;AAAA,QACV,QAAQ,OAAO,CAAC,MAAM,OAAO,CAAC,IAAI,IAAI,SAAS,KAAK,aAAa,KAAK,UAAU,GAAG,IAAI;AAAA,MACzF,GAAG,MAAM,IAAI,UAAU;AAAA,IACzB,CAAC;AAAA,EACH,CAAC;AACH;AACA,IAAI,UAA0B,YAAY,WAAW,CAAC,CAAC,UAAU,WAAW,GAAG,CAAC,aAAa,iBAAiB,CAAC,CAAC;", "names": []}
{"version": 3, "sources": ["../../.pnpm/plus-pro-components@0.1.26__58f148a1d363315c5590851fc69d8998/node_modules/plus-pro-components/es/locale/lang/en.mjs"], "sourcesContent": ["var English = {\n  name: \"en\",\n  plus: {\n    dialog: {\n      confirmText: \"Yes\",\n      cancelText: \"No\",\n      title: \"Dialog\"\n    },\n    datepicker: {\n      startPlaceholder: \"Please select start time\",\n      endPlaceholder: \"Please select end time\"\n    },\n    dialogForm: {\n      title: \"Dialog form\"\n    },\n    drawerForm: {\n      title: \"Drawer form\",\n      confirmText: \"Yes\",\n      cancelText: \"No\"\n    },\n    form: {\n      submitText: \"Submit\",\n      resetText: \"Reset\",\n      errorTip: \"Please complete the form and submit again!\"\n    },\n    field: {\n      pleaseEnter: \"Please enter \",\n      pleaseSelect: \"Please select \"\n    },\n    popover: {\n      confirmText: \"Yes\",\n      cancelText: \"No\"\n    },\n    search: {\n      searchText: \"Search\",\n      resetText: \"Reset\",\n      expand: \"Expand\",\n      retract: \"Retract\"\n    },\n    table: {\n      title: \"Table\",\n      density: \"Density\",\n      refresh: \"Refresh\",\n      columnSettings: \"Column settings\",\n      selectAll: \"Select all\",\n      default: \"Default\",\n      loose: \"Loose\",\n      compact: \"Compact\",\n      action: \"Action\",\n      more: \"More\",\n      confirmToPerformThisOperation: \"Confirm to perform this operation?\",\n      prompt: \"Prompt\",\n      sort: \"Sort\",\n      resetText: \"Reset\"\n    },\n    stepsForm: {\n      nextText: \"Next step\",\n      preText: \"Previous step\",\n      submitText: \"Submit\"\n    },\n    inputTag: {\n      placeholder: \"Please enter keywords and press enter or space key\"\n    },\n    header: {\n      logout: \"logout\"\n    }\n  }\n};\n\nexport { English as default };\n"], "mappings": ";AAAA,IAAI,UAAU;AAAA,EACZ,MAAM;AAAA,EACN,MAAM;AAAA,IACJ,QAAQ;AAAA,MACN,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,OAAO;AAAA,IACT;AAAA,IACA,YAAY;AAAA,MACV,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,IACA,YAAY;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,YAAY;AAAA,MACV,OAAO;AAAA,MACP,aAAa;AAAA,MACb,YAAY;AAAA,IACd;AAAA,IACA,MAAM;AAAA,MACJ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,UAAU;AAAA,IACZ;AAAA,IACA,OAAO;AAAA,MACL,aAAa;AAAA,MACb,cAAc;AAAA,IAChB;AAAA,IACA,SAAS;AAAA,MACP,aAAa;AAAA,MACb,YAAY;AAAA,IACd;AAAA,IACA,QAAQ;AAAA,MACN,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,QAAQ;AAAA,MACR,SAAS;AAAA,IACX;AAAA,IACA,OAAO;AAAA,MACL,OAAO;AAAA,MACP,SAAS;AAAA,MACT,SAAS;AAAA,MACT,gBAAgB;AAAA,MAChB,WAAW;AAAA,MACX,SAAS;AAAA,MACT,OAAO;AAAA,MACP,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,+BAA+B;AAAA,MAC/B,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,WAAW;AAAA,IACb;AAAA,IACA,WAAW;AAAA,MACT,UAAU;AAAA,MACV,SAAS;AAAA,MACT,YAAY;AAAA,IACd;AAAA,IACA,UAAU;AAAA,MACR,aAAa;AAAA,IACf;AAAA,IACA,QAAQ;AAAA,MACN,QAAQ;AAAA,IACV;AAAA,EACF;AACF;", "names": []}
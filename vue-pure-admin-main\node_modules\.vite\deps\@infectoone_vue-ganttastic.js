import {
  require_dayjs_min
} from "./chunk-WIMLKF2O.js";
import {
  Fragment,
  Teleport,
  Transition,
  TransitionGroup,
  computed,
  createBaseVNode,
  createBlock,
  createCommentVNode,
  createElementBlock,
  createTextVNode,
  createVNode,
  defineComponent,
  getCurrentInstance,
  getCurrentScope,
  inject,
  mergeProps,
  nextTick,
  normalizeClass,
  normalizeStyle,
  onMounted,
  onScopeDispose,
  openBlock,
  provide,
  ref,
  renderList,
  renderSlot,
  toDisplayString,
  toRefs,
  unref,
  useSlots,
  watch,
  withCtx,
  withModifiers
} from "./chunk-JBQXOB42.js";
import {
  __toESM
} from "./chunk-PRH6DGNM.js";

// node_modules/.pnpm/@infectoone+vue-ganttastic@_b72bffbc86c7cce3f7238f281b613c68/node_modules/@infectoone/vue-ganttastic/lib/vue-ganttastic.js
var import_dayjs = __toESM(require_dayjs_min());
var K = typeof globalThis < "u" ? globalThis : typeof window < "u" ? window : typeof global < "u" ? global : typeof self < "u" ? self : {};
var Ye = { exports: {} };
(function(e, s) {
  (function(r, n) {
    e.exports = n();
  })(K, function() {
    var r = "day";
    return function(n, p, l) {
      var c = function(t) {
        return t.add(4 - t.isoWeekday(), r);
      }, a = p.prototype;
      a.isoWeekYear = function() {
        return c(this).year();
      }, a.isoWeek = function(t) {
        if (!this.$utils().u(t))
          return this.add(7 * (t - this.isoWeek()), r);
        var o, f, d, m, x = c(this), g = (o = this.isoWeekYear(), f = this.$u, d = (f ? l.utc : l)().year(o).startOf("year"), m = 4 - d.isoWeekday(), d.isoWeekday() > 4 && (m += 7), d.add(m, r));
        return x.diff(g, "week") + 1;
      }, a.isoWeekday = function(t) {
        return this.$utils().u(t) ? this.day() || 7 : this.day(this.day() % 7 ? t : t - 7);
      };
      var i = a.startOf;
      a.startOf = function(t, o) {
        var f = this.$utils(), d = !!f.u(o) || o;
        return f.p(t) === "isoweek" ? d ? this.date(this.date() - (this.isoWeekday() - 1)).startOf("day") : this.date(this.date() - 1 - (this.isoWeekday() - 1) + 7).endOf("day") : i.bind(this)(t, o);
      };
    };
  });
})(Ye);
var ut = Ye.exports;
var Ge = { exports: {} };
(function(e, s) {
  (function(r, n) {
    e.exports = n();
  })(K, function() {
    return function(r, n) {
      n.prototype.isSameOrBefore = function(p, l) {
        return this.isSame(p, l) || this.isBefore(p, l);
      };
    };
  });
})(Ge);
var ct = Ge.exports;
var Ie = { exports: {} };
(function(e, s) {
  (function(r, n) {
    e.exports = n();
  })(K, function() {
    return function(r, n) {
      n.prototype.isSameOrAfter = function(p, l) {
        return this.isSame(p, l) || this.isAfter(p, l);
      };
    };
  });
})(Ie);
var dt = Ie.exports;
var Re = { exports: {} };
(function(e, s) {
  (function(r, n) {
    e.exports = n();
  })(K, function() {
    return function(r, n, p) {
      n.prototype.isBetween = function(l, c, a, i) {
        var t = p(l), o = p(c), f = (i = i || "()")[0] === "(", d = i[1] === ")";
        return (f ? this.isAfter(t, a) : !this.isBefore(t, a)) && (d ? this.isBefore(o, a) : !this.isAfter(o, a)) || (f ? this.isBefore(t, a) : !this.isAfter(t, a)) && (d ? this.isAfter(o, a) : !this.isBefore(o, a));
      };
    };
  });
})(Re);
var ft = Re.exports;
var He = { exports: {} };
(function(e, s) {
  (function(r, n) {
    e.exports = n();
  })(K, function() {
    var r = "week", n = "year";
    return function(p, l, c) {
      var a = l.prototype;
      a.week = function(i) {
        if (i === void 0 && (i = null), i !== null)
          return this.add(7 * (i - this.week()), "day");
        var t = this.$locale().yearStart || 1;
        if (this.month() === 11 && this.date() > 25) {
          var o = c(this).startOf(n).add(1, n).date(t), f = c(this).endOf(r);
          if (o.isBefore(f))
            return 1;
        }
        var d = c(this).startOf(n).date(t).startOf(r).subtract(1, "millisecond"), m = this.diff(d, r, true);
        return m < 0 ? c(this).startOf("week").week() : Math.ceil(m);
      }, a.weeks = function(i) {
        return i === void 0 && (i = null), this.week(i);
      };
    };
  });
})(He);
var gt = He.exports;
var Ae = { exports: {} };
(function(e, s) {
  (function(r, n) {
    e.exports = n();
  })(K, function() {
    return function(r, n, p) {
      var l = n.prototype, c = l.format;
      p.en.ordinal = function(a) {
        var i = ["th", "st", "nd", "rd"], t = a % 100;
        return "[" + a + (i[(t - 20) % 10] || i[t] || i[0]) + "]";
      }, l.format = function(a) {
        var i = this, t = this.$locale();
        if (!this.isValid())
          return c.bind(this)(a);
        var o = this.$utils(), f = (a || "YYYY-MM-DDTHH:mm:ssZ").replace(/\[([^\]]+)]|Q|wo|ww|w|WW|W|zzz|z|gggg|GGGG|Do|X|x|k{1,2}|S/g, function(d) {
          switch (d) {
            case "Q":
              return Math.ceil((i.$M + 1) / 3);
            case "Do":
              return t.ordinal(i.$D);
            case "gggg":
              return i.weekYear();
            case "GGGG":
              return i.isoWeekYear();
            case "wo":
              return t.ordinal(i.week(), "W");
            case "w":
            case "ww":
              return o.s(i.week(), d === "w" ? 1 : 2, "0");
            case "W":
            case "WW":
              return o.s(i.isoWeek(), d === "W" ? 1 : 2, "0");
            case "k":
            case "kk":
              return o.s(String(i.$H === 0 ? 24 : i.$H), d === "k" ? 1 : 2, "0");
            case "X":
              return Math.floor(i.$d.getTime() / 1e3);
            case "x":
              return i.$d.getTime();
            case "z":
              return "[" + i.offsetName() + "]";
            case "zzz":
              return "[" + i.offsetName("long") + "]";
            default:
              return d;
          }
        });
        return c.bind(this)(f);
      };
    };
  });
})(Ae);
var ht = Ae.exports;
var We = { exports: {} };
(function(e, s) {
  (function(r, n) {
    e.exports = n();
  })(K, function() {
    var r = { LTS: "h:mm:ss A", LT: "h:mm A", L: "MM/DD/YYYY", LL: "MMMM D, YYYY", LLL: "MMMM D, YYYY h:mm A", LLLL: "dddd, MMMM D, YYYY h:mm A" }, n = /(\[[^[]*\])|([-_:/.,()\s]+)|(A|a|YYYY|YY?|MM?M?M?|Do|DD?|hh?|HH?|mm?|ss?|S{1,3}|z|ZZ?)/g, p = /\d\d/, l = /\d\d?/, c = /\d*[^-_:/,()\s\d]+/, a = {}, i = function(g) {
      return (g = +g) + (g > 68 ? 1900 : 2e3);
    }, t = function(g) {
      return function(h) {
        this[g] = +h;
      };
    }, o = [/[+-]\d\d:?(\d\d)?|Z/, function(g) {
      (this.zone || (this.zone = {})).offset = function(h) {
        if (!h || h === "Z")
          return 0;
        var _ = h.match(/([+-]|\d\d)/g), v = 60 * _[1] + (+_[2] || 0);
        return v === 0 ? 0 : _[0] === "+" ? -v : v;
      }(g);
    }], f = function(g) {
      var h = a[g];
      return h && (h.indexOf ? h : h.s.concat(h.f));
    }, d = function(g, h) {
      var _, v = a.meridiem;
      if (v) {
        for (var b = 1; b <= 24; b += 1)
          if (g.indexOf(v(b, 0, h)) > -1) {
            _ = b > 12;
            break;
          }
      } else
        _ = g === (h ? "pm" : "PM");
      return _;
    }, m = { A: [c, function(g) {
      this.afternoon = d(g, false);
    }], a: [c, function(g) {
      this.afternoon = d(g, true);
    }], S: [/\d/, function(g) {
      this.milliseconds = 100 * +g;
    }], SS: [p, function(g) {
      this.milliseconds = 10 * +g;
    }], SSS: [/\d{3}/, function(g) {
      this.milliseconds = +g;
    }], s: [l, t("seconds")], ss: [l, t("seconds")], m: [l, t("minutes")], mm: [l, t("minutes")], H: [l, t("hours")], h: [l, t("hours")], HH: [l, t("hours")], hh: [l, t("hours")], D: [l, t("day")], DD: [p, t("day")], Do: [c, function(g) {
      var h = a.ordinal, _ = g.match(/\d+/);
      if (this.day = _[0], h)
        for (var v = 1; v <= 31; v += 1)
          h(v).replace(/\[|\]/g, "") === g && (this.day = v);
    }], M: [l, t("month")], MM: [p, t("month")], MMM: [c, function(g) {
      var h = f("months"), _ = (f("monthsShort") || h.map(function(v) {
        return v.slice(0, 3);
      })).indexOf(g) + 1;
      if (_ < 1)
        throw new Error();
      this.month = _ % 12 || _;
    }], MMMM: [c, function(g) {
      var h = f("months").indexOf(g) + 1;
      if (h < 1)
        throw new Error();
      this.month = h % 12 || h;
    }], Y: [/[+-]?\d+/, t("year")], YY: [p, function(g) {
      this.year = i(g);
    }], YYYY: [/\d{4}/, t("year")], Z: o, ZZ: o };
    function x(g) {
      var h, _;
      h = g, _ = a && a.formats;
      for (var v = (g = h.replace(/(\[[^\]]+])|(LTS?|l{1,4}|L{1,4})/g, function(E, D, T) {
        var $ = T && T.toUpperCase();
        return D || _[T] || r[T] || _[$].replace(/(\[[^\]]+])|(MMMM|MM|DD|dddd)/g, function(I, W, j) {
          return W || j.slice(1);
        });
      })).match(n), b = v.length, B = 0; B < b; B += 1) {
        var u = v[B], w = m[u], y = w && w[0], k = w && w[1];
        v[B] = k ? { regex: y, parser: k } : u.replace(/^\[|\]$/g, "");
      }
      return function(E) {
        for (var D = {}, T = 0, $ = 0; T < b; T += 1) {
          var I = v[T];
          if (typeof I == "string")
            $ += I.length;
          else {
            var W = I.regex, j = I.parser, z = E.slice($), U = W.exec(z)[0];
            j.call(D, U), E = E.replace(U, "");
          }
        }
        return function(F) {
          var P = F.afternoon;
          if (P !== void 0) {
            var Z = F.hours;
            P ? Z < 12 && (F.hours += 12) : Z === 12 && (F.hours = 0), delete F.afternoon;
          }
        }(D), D;
      };
    }
    return function(g, h, _) {
      _.p.customParseFormat = true, g && g.parseTwoDigitYear && (i = g.parseTwoDigitYear);
      var v = h.prototype, b = v.parse;
      v.parse = function(B) {
        var u = B.date, w = B.utc, y = B.args;
        this.$u = w;
        var k = y[1];
        if (typeof k == "string") {
          var E = y[2] === true, D = y[3] === true, T = E || D, $ = y[2];
          D && ($ = y[2]), a = this.$locale(), !E && $ && (a = _.Ls[$]), this.$d = function(z, U, F) {
            try {
              if (["x", "X"].indexOf(U) > -1)
                return new Date((U === "X" ? 1e3 : 1) * z);
              var P = x(U)(z), Z = P.year, ie = P.month, Qe = P.day, Xe = P.hours, Ke = P.minutes, Ze = P.seconds, Je = P.milliseconds, ke = P.zone, fe = /* @__PURE__ */ new Date(), ge = Qe || (Z || ie ? 1 : fe.getDate()), he = Z || fe.getFullYear(), se = 0;
              Z && !ie || (se = ie > 0 ? ie - 1 : fe.getMonth());
              var me = Xe || 0, pe = Ke || 0, ve = Ze || 0, ye = Je || 0;
              return ke ? new Date(Date.UTC(he, se, ge, me, pe, ve, ye + 60 * ke.offset * 1e3)) : F ? new Date(Date.UTC(he, se, ge, me, pe, ve, ye)) : new Date(he, se, ge, me, pe, ve, ye);
            } catch {
              return /* @__PURE__ */ new Date("");
            }
          }(u, k, w), this.init(), $ && $ !== true && (this.$L = this.locale($).$L), T && u != this.format(k) && (this.$d = /* @__PURE__ */ new Date("")), a = {};
        } else if (k instanceof Array)
          for (var I = k.length, W = 1; W <= I; W += 1) {
            y[1] = k[W - 1];
            var j = _.apply(this, y);
            if (j.isValid()) {
              this.$d = j.$d, this.$L = j.$L, this.init();
              break;
            }
            W === I && (this.$d = /* @__PURE__ */ new Date(""));
          }
        else
          b.call(this, B);
      };
    };
  });
})(We);
var mt = We.exports;
var Fe = Symbol("CHART_ROWS_KEY");
var Pe = Symbol("CONFIG_KEY");
var je = Symbol("EMIT_BAR_EVENT_KEY");
var ze = Symbol("BAR_CONTAINER_KEY");
function H() {
  const e = inject(Pe);
  if (!e)
    throw Error("Failed to inject config!");
  return e;
}
var Ue = "YYYY-MM-DD HH:mm";
function ae(e = H()) {
  const { chartStart: s, chartEnd: r, barStart: n, barEnd: p, dateFormat: l } = e, c = computed(() => i(s.value)), a = computed(() => i(r.value)), i = (o, f) => {
    let d;
    if (f !== void 0 && typeof o != "string" && !(o instanceof Date) && (d = f === "start" ? o[n.value] : o[p.value]), typeof o == "string")
      d = o;
    else if (o instanceof Date)
      return (0, import_dayjs.default)(o);
    const m = l.value || Ue;
    return (0, import_dayjs.default)(d, m, true);
  };
  return {
    chartStartDayjs: c,
    chartEndDayjs: a,
    toDayjs: i,
    format: (o, f) => f === false ? o instanceof Date ? o : (0, import_dayjs.default)(o).toDate() : (typeof o == "string" || o instanceof Date ? i(o) : o).format(f)
  };
}
function Ne() {
  const { precision: e } = H(), { chartStartDayjs: s, chartEndDayjs: r } = ae(), n = computed(() => {
    switch (e == null ? void 0 : e.value) {
      case "hour":
        return "day";
      case "day":
        return "month";
      case "date":
      case "week":
        return "month";
      case "month":
        return "year";
      default:
        throw new Error(
          "Precision prop incorrect. Must be one of the following: 'hour', 'day', 'date', 'week', 'month'"
        );
    }
  }), p = computed(() => {
    switch (e.value) {
      case "date":
        return "day";
      case "week":
        return "isoWeek";
      default:
        return e.value;
    }
  }), l = {
    hour: "HH",
    date: "DD.MMM",
    day: "DD.MMM",
    week: "WW",
    month: "MMMM YYYY",
    year: "YYYY"
  };
  return {
    timeaxisUnits: computed(() => {
      const a = [], i = [], t = r.value.diff(s.value, "minutes", true), o = n.value, f = p.value;
      let d = s.value, m = s.value;
      for (; m.isSameOrBefore(r.value); ) {
        const x = m.endOf(f), h = x.isAfter(r.value) ? r.value.diff(m, "minutes", true) / t * 100 : x.diff(m, "minutes", true) / t * 100;
        i.push({
          label: m.format(l[e == null ? void 0 : e.value]),
          value: String(m),
          date: m.toDate(),
          width: String(h) + "%"
        }), m = x.add(1, f === "isoWeek" ? "week" : f).startOf(f);
      }
      for (; d.isSameOrBefore(r.value); ) {
        const x = d.endOf(o), h = x.isAfter(r.value) ? r.value.diff(d, "minutes", true) / t * 100 : x.diff(d, "minutes", true) / t * 100;
        a.push({
          label: d.format(l[o]),
          value: String(d),
          date: d.toDate(),
          width: String(h) + "%"
        }), d = x.add(1, o).startOf(o);
      }
      return { upperUnits: a, lowerUnits: i };
    })
  };
}
var pt = { class: "g-grid-container" };
var vt = defineComponent({
  __name: "GGanttGrid",
  props: {
    highlightedUnits: {}
  },
  setup(e) {
    const { colors: s } = H(), { timeaxisUnits: r } = Ne();
    return (n, p) => (openBlock(), createElementBlock("div", pt, [
      (openBlock(true), createElementBlock(Fragment, null, renderList(unref(r).lowerUnits, ({ label: l, value: c, width: a }) => {
        var i;
        return openBlock(), createElementBlock("div", {
          key: l,
          class: "g-grid-line",
          style: normalizeStyle({
            width: a,
            background: (i = n.highlightedUnits) != null && i.includes(Number(c)) ? unref(s).hoverHighlight : void 0
          })
        }, null, 4);
      }), 128))
    ]));
  }
});
function _e() {
  const e = inject(Fe);
  if (!e)
    throw Error("Failed to inject getChartRows!");
  return e;
}
var yt = { class: "g-label-column-rows" };
var bt = defineComponent({
  __name: "GGanttLabelColumn",
  setup(e) {
    const { font: s, colors: r, labelColumnTitle: n, rowHeight: p } = H(), l = _e();
    return (c, a) => (openBlock(), createElementBlock("div", {
      class: "g-label-column",
      style: normalizeStyle({ fontFamily: unref(s), color: unref(r).text })
    }, [
      renderSlot(c.$slots, "label-column-title", {}, () => [
        createBaseVNode("div", {
          class: "g-label-column-header",
          style: normalizeStyle({ background: unref(r).primary })
        }, toDisplayString(unref(n)), 5)
      ]),
      createBaseVNode("div", yt, [
        (openBlock(true), createElementBlock(Fragment, null, renderList(unref(l)(), ({ label: i }, t) => (openBlock(), createElementBlock("div", {
          key: `${i}_${t}`,
          class: "g-label-column-row",
          style: normalizeStyle({
            background: t % 2 === 0 ? unref(r).ternary : unref(r).quartenary,
            height: `${unref(p)}px`
          })
        }, [
          renderSlot(c.$slots, "label-column-row", { label: i }, () => [
            createBaseVNode("span", null, toDisplayString(i), 1)
          ])
        ], 4))), 128))
      ])
    ], 4));
  }
});
var wt = { class: "g-timeaxis" };
var xt = { class: "g-timeunits-container" };
var Bt = { class: "g-timeunits-container" };
var _t = defineComponent({
  __name: "GGanttTimeaxis",
  setup(e) {
    const { precision: s, colors: r } = H(), { timeaxisUnits: n } = Ne();
    return (p, l) => (openBlock(), createElementBlock("div", wt, [
      createBaseVNode("div", xt, [
        (openBlock(true), createElementBlock(Fragment, null, renderList(unref(n).upperUnits, ({ label: c, value: a, date: i, width: t }, o) => (openBlock(), createElementBlock("div", {
          key: c,
          class: "g-upper-timeunit",
          style: normalizeStyle({
            background: o % 2 === 0 ? unref(r).primary : unref(r).secondary,
            color: unref(r).text,
            width: t
          })
        }, [
          renderSlot(p.$slots, "upper-timeunit", {
            label: c,
            value: a,
            date: i
          }, () => [
            createTextVNode(toDisplayString(c), 1)
          ])
        ], 4))), 128))
      ]),
      createBaseVNode("div", Bt, [
        (openBlock(true), createElementBlock(Fragment, null, renderList(unref(n).lowerUnits, ({ label: c, value: a, date: i, width: t }, o) => (openBlock(), createElementBlock("div", {
          key: c,
          class: "g-timeunit",
          style: normalizeStyle({
            background: o % 2 === 0 ? unref(r).ternary : unref(r).quartenary,
            color: unref(r).text,
            flexDirection: unref(s) === "hour" ? "column" : "row",
            alignItems: unref(s) === "hour" ? "" : "center",
            width: t
          })
        }, [
          renderSlot(p.$slots, "timeunit", {
            label: c,
            value: a,
            date: i
          }, () => [
            createTextVNode(toDisplayString(c), 1)
          ]),
          unref(s) === "hour" ? (openBlock(), createElementBlock("div", {
            key: 0,
            class: "g-timeaxis-hour-pin",
            style: normalizeStyle({ background: unref(r).text })
          }, null, 4)) : createCommentVNode("", true)
        ], 4))), 128))
      ])
    ]));
  }
});
var kt = "cadetblue";
var Ct = defineComponent({
  __name: "GGanttBarTooltip",
  props: {
    bar: {},
    modelValue: { type: Boolean }
  },
  setup(e) {
    const s = e, r = {
      hour: "HH:mm",
      day: "DD. MMM HH:mm",
      date: "DD. MMMM YYYY",
      month: "DD. MMMM YYYY",
      week: "DD. MMMM YYYY (WW)"
    }, { bar: n } = toRefs(s), { precision: p, font: l, barStart: c, barEnd: a, rowHeight: i } = H(), t = ref("0px"), o = ref("0px");
    watch(
      () => s.bar,
      async () => {
        var u;
        await nextTick();
        const h = ((u = n == null ? void 0 : n.value) == null ? void 0 : u.ganttBarConfig.id) || "";
        if (!h)
          return;
        const _ = document.getElementById(h), { top: v, left: b } = (_ == null ? void 0 : _.getBoundingClientRect()) || {
          top: 0,
          left: 0
        }, B = Math.max(b, 10);
        t.value = `${v + i.value - 10}px`, o.value = `${B}px`;
      },
      { deep: true, immediate: true }
    );
    const f = computed(() => {
      var h, _;
      return ((_ = (h = n == null ? void 0 : n.value) == null ? void 0 : h.ganttBarConfig.style) == null ? void 0 : _.background) || kt;
    }), { toDayjs: d } = ae(), m = computed(() => {
      var h;
      return (h = n.value) == null ? void 0 : h[c.value];
    }), x = computed(() => {
      var h;
      return (h = n.value) == null ? void 0 : h[a.value];
    }), g = computed(() => {
      if (!(n != null && n.value))
        return "";
      const h = r[p.value], _ = d(m.value).format(h), v = d(x.value).format(h);
      return `${_} – ${v}`;
    });
    return (h, _) => (openBlock(), createBlock(Teleport, { to: "body" }, [
      createVNode(Transition, {
        name: "g-fade",
        mode: "out-in"
      }, {
        default: withCtx(() => [
          h.modelValue ? (openBlock(), createElementBlock("div", {
            key: 0,
            class: "g-gantt-tooltip",
            style: normalizeStyle({
              top: t.value,
              left: o.value,
              fontFamily: unref(l)
            })
          }, [
            createBaseVNode("div", {
              class: "g-gantt-tooltip-color-dot",
              style: normalizeStyle({ background: f.value })
            }, null, 4),
            renderSlot(h.$slots, "default", {
              bar: unref(n),
              barStart: m.value,
              barEnd: x.value
            }, () => [
              createTextVNode(toDisplayString(g.value), 1)
            ])
          ], 4)) : createCommentVNode("", true)
        ]),
        _: 3
      })
    ]));
  }
});
function de(e = H()) {
  const { dateFormat: s, chartSize: r } = e, { chartStartDayjs: n, chartEndDayjs: p, toDayjs: l, format: c } = ae(e), a = computed(() => p.value.diff(n.value, "minutes"));
  return {
    mapTimeToPosition: (o) => {
      const f = r.width.value || 0, d = l(o).diff(n.value, "minutes", true);
      return Math.ceil(d / a.value * f);
    },
    mapPositionToTime: (o) => {
      const f = r.width.value || 0, d = o / f * a.value;
      return c(n.value.add(d, "minutes"), s.value);
    }
  };
}
var Et = defineComponent({
  __name: "GGanttCurrentTime",
  setup(e) {
    const { mapTimeToPosition: s } = de(), r = ref((0, import_dayjs.default)()), { colors: n, dateFormat: p, currentTimeLabel: l } = H(), c = computed(() => {
      const a = p.value || "YYYY-MM-DD HH:mm";
      return s((0, import_dayjs.default)(r.value, a).format(a));
    });
    return (a, i) => (openBlock(), createElementBlock("div", {
      class: "g-grid-current-time",
      style: normalizeStyle({
        left: `${c.value}px`
      })
    }, [
      createBaseVNode("div", {
        class: "g-grid-current-time-marker",
        style: normalizeStyle({
          border: `1px dashed ${unref(n).markerCurrentTime}`
        })
      }, null, 4),
      createBaseVNode("span", {
        class: "g-grid-current-time-text",
        style: normalizeStyle({ color: unref(n).markerCurrentTime })
      }, [
        renderSlot(a.$slots, "current-time-label", {}, () => [
          createTextVNode(toDisplayString(unref(l)), 1)
        ])
      ], 4)
    ], 4));
  }
});
var Ce;
var oe = typeof window < "u";
oe && ((Ce = window == null ? void 0 : window.navigator) == null ? void 0 : Ce.userAgent) && /iP(ad|hone|od)/.test(window.navigator.userAgent);
function Ot(e) {
  return typeof e == "function" ? e() : unref(e);
}
function Dt(e) {
  return e;
}
function Tt(e) {
  return getCurrentScope() ? (onScopeDispose(e), true) : false;
}
function Mt(e, s = true) {
  getCurrentInstance() ? onMounted(e) : s ? e() : nextTick(e);
}
function Ve(e) {
  var s;
  const r = Ot(e);
  return (s = r == null ? void 0 : r.$el) != null ? s : r;
}
var $t = oe ? window : void 0;
oe && window.document;
oe && window.navigator;
oe && window.location;
function Lt(e, s = false) {
  const r = ref(), n = () => r.value = Boolean(e());
  return n(), Mt(n, s), r;
}
var we = typeof globalThis < "u" ? globalThis : typeof window < "u" ? window : typeof global < "u" ? global : typeof self < "u" ? self : {};
var xe = "__vueuse_ssr_handlers__";
we[xe] = we[xe] || {};
we[xe];
var Ee = Object.getOwnPropertySymbols;
var St = Object.prototype.hasOwnProperty;
var Yt = Object.prototype.propertyIsEnumerable;
var Gt = (e, s) => {
  var r = {};
  for (var n in e)
    St.call(e, n) && s.indexOf(n) < 0 && (r[n] = e[n]);
  if (e != null && Ee)
    for (var n of Ee(e))
      s.indexOf(n) < 0 && Yt.call(e, n) && (r[n] = e[n]);
  return r;
};
function It(e, s, r = {}) {
  const n = r, { window: p = $t } = n, l = Gt(n, ["window"]);
  let c;
  const a = Lt(() => p && "ResizeObserver" in p), i = () => {
    c && (c.disconnect(), c = void 0);
  }, t = watch(() => Ve(e), (f) => {
    i(), a.value && p && f && (c = new ResizeObserver(s), c.observe(f, l));
  }, { immediate: true, flush: "post" }), o = () => {
    i(), t();
  };
  return Tt(o), {
    isSupported: a,
    stop: o
  };
}
function Rt(e, s = { width: 0, height: 0 }, r = {}) {
  const n = ref(s.width), p = ref(s.height);
  return It(e, ([l]) => {
    n.value = l.contentRect.width, p.value = l.contentRect.height;
  }, r), watch(() => Ve(e), (l) => {
    n.value = l ? s.width : 0, p.value = l ? s.height : 0;
  }), {
    width: n,
    height: p
  };
}
var Oe;
(function(e) {
  e.UP = "UP", e.RIGHT = "RIGHT", e.DOWN = "DOWN", e.LEFT = "LEFT", e.NONE = "NONE";
})(Oe || (Oe = {}));
var Ht = Object.defineProperty;
var De = Object.getOwnPropertySymbols;
var At = Object.prototype.hasOwnProperty;
var Wt = Object.prototype.propertyIsEnumerable;
var Te = (e, s, r) => s in e ? Ht(e, s, { enumerable: true, configurable: true, writable: true, value: r }) : e[s] = r;
var Ft = (e, s) => {
  for (var r in s || (s = {}))
    At.call(s, r) && Te(e, r, s[r]);
  if (De)
    for (var r of De(s))
      Wt.call(s, r) && Te(e, r, s[r]);
  return e;
};
var Pt = {
  easeInSine: [0.12, 0, 0.39, 0],
  easeOutSine: [0.61, 1, 0.88, 1],
  easeInOutSine: [0.37, 0, 0.63, 1],
  easeInQuad: [0.11, 0, 0.5, 0],
  easeOutQuad: [0.5, 1, 0.89, 1],
  easeInOutQuad: [0.45, 0, 0.55, 1],
  easeInCubic: [0.32, 0, 0.67, 0],
  easeOutCubic: [0.33, 1, 0.68, 1],
  easeInOutCubic: [0.65, 0, 0.35, 1],
  easeInQuart: [0.5, 0, 0.75, 0],
  easeOutQuart: [0.25, 1, 0.5, 1],
  easeInOutQuart: [0.76, 0, 0.24, 1],
  easeInQuint: [0.64, 0, 0.78, 0],
  easeOutQuint: [0.22, 1, 0.36, 1],
  easeInOutQuint: [0.83, 0, 0.17, 1],
  easeInExpo: [0.7, 0, 0.84, 0],
  easeOutExpo: [0.16, 1, 0.3, 1],
  easeInOutExpo: [0.87, 0, 0.13, 1],
  easeInCirc: [0.55, 0, 1, 0.45],
  easeOutCirc: [0, 0.55, 0.45, 1],
  easeInOutCirc: [0.85, 0, 0.15, 1],
  easeInBack: [0.36, 0, 0.66, -0.56],
  easeOutBack: [0.34, 1.56, 0.64, 1],
  easeInOutBack: [0.68, -0.6, 0.32, 1.6]
};
Ft({
  linear: Dt
}, Pt);
var Me = {
  default: {
    primary: "#eeeeee",
    secondary: "#E0E0E0",
    ternary: "#F5F5F5",
    quartenary: "#ededed",
    hoverHighlight: "rgba(204, 216, 219, 0.5)",
    markerCurrentTime: "#000",
    text: "#404040",
    background: "white"
  },
  creamy: {
    primary: "#ffe8d9",
    secondary: "#fcdcc5",
    ternary: "#fff6f0",
    quartenary: "#f7ece6",
    hoverHighlight: "rgba(230, 221, 202, 0.5)",
    markerCurrentTime: "#000",
    text: "#542d05",
    background: "white"
  },
  crimson: {
    primary: "#a82039",
    secondary: "#c41238",
    ternary: "#db4f56",
    quartenary: "#ce5f64",
    hoverHighlight: "rgba(196, 141, 141, 0.5)",
    markerCurrentTime: "#000",
    text: "white",
    background: "white"
  },
  dark: {
    primary: "#404040",
    secondary: "#303030",
    ternary: "#353535",
    quartenary: "#383838",
    hoverHighlight: "rgba(159, 160, 161, 0.5)",
    markerCurrentTime: "#fff",
    text: "white",
    background: "#525252",
    toast: "#1f1f1f"
  },
  flare: {
    primary: "#e08a38",
    secondary: "#e67912",
    ternary: "#5e5145",
    quartenary: "#665648",
    hoverHighlight: "rgba(196, 141, 141, 0.5)",
    markerCurrentTime: "#000",
    text: "white",
    background: "white"
  },
  fuchsia: {
    primary: "#de1d5a",
    secondary: "#b50b41",
    ternary: "#ff7da6",
    quartenary: "#f2799f",
    hoverHighlight: "rgba(196, 141, 141, 0.5)",
    markerCurrentTime: "#000",
    text: "white",
    background: "white"
  },
  grove: {
    primary: "#3d9960",
    secondary: "#288542",
    ternary: "#72b585",
    quartenary: "#65a577",
    hoverHighlight: "rgba(160, 219, 171, 0.5)",
    markerCurrentTime: "#000",
    text: "white",
    background: "white"
  },
  "material-blue": {
    primary: "#0D47A1",
    secondary: "#1565C0",
    ternary: "#42a5f5",
    quartenary: "#409fed",
    hoverHighlight: "rgba(110, 165, 196, 0.5)",
    markerCurrentTime: "#000",
    text: "white",
    background: "white"
  },
  sky: {
    primary: "#b5e3ff",
    secondary: "#a1d6f7",
    ternary: "#d6f7ff",
    quartenary: "#d0edf4",
    hoverHighlight: "rgba(193, 202, 214, 0.5)",
    markerCurrentTime: "#000",
    text: "#022c47",
    background: "white"
  },
  slumber: {
    primary: "#2a2f42",
    secondary: "#2f3447",
    ternary: "#35394d",
    quartenary: "#2c3044",
    hoverHighlight: "rgba(179, 162, 127, 0.5)",
    markerCurrentTime: "#fff",
    text: "#ffe0b3",
    background: "#38383b",
    toast: "#1f1f1f"
  },
  vue: {
    primary: "#258a5d",
    secondary: "#41B883",
    ternary: "#35495E",
    quartenary: "#2a3d51",
    hoverHighlight: "rgba(160, 219, 171, 0.5)",
    markerCurrentTime: "#000",
    text: "white",
    background: "white"
  }
};
var jt = { class: "g-gantt-rows-container" };
var zt = defineComponent({
  __name: "GGanttChart",
  props: {
    chartStart: {},
    chartEnd: {},
    precision: { default: "day" },
    barStart: {},
    barEnd: {},
    currentTime: { type: Boolean },
    currentTimeLabel: { default: "" },
    dateFormat: { type: [String, Boolean], default: Ue },
    width: { default: "100%" },
    hideTimeaxis: { type: Boolean, default: false },
    colorScheme: { default: "default" },
    grid: { type: Boolean, default: false },
    pushOnOverlap: { type: Boolean, default: false },
    noOverlap: { type: Boolean, default: false },
    rowHeight: { default: 40 },
    highlightedUnits: { default: () => [] },
    font: { default: "inherit" },
    labelColumnTitle: { default: "" },
    labelColumnWidth: { default: "150px" }
  },
  emits: ["click-bar", "mousedown-bar", "mouseup-bar", "dblclick-bar", "mouseenter-bar", "mouseleave-bar", "dragstart-bar", "drag-bar", "dragend-bar", "contextmenu-bar"],
  setup(e, { emit: s }) {
    const r = e, { width: n, font: p, colorScheme: l } = toRefs(r), c = useSlots(), a = computed(
      () => typeof l.value != "string" ? l.value : Me[l.value] || Me.default
    ), i = () => {
      var B;
      const v = (B = c.default) == null ? void 0 : B.call(c), b = [];
      return v && v.forEach((u) => {
        var w;
        if ((w = u.props) != null && w.bars) {
          const { label: y, bars: k } = u.props;
          b.push({ label: y, bars: k });
        } else
          Array.isArray(u.children) && u.children.forEach((y) => {
            var E;
            const k = y;
            if ((E = k == null ? void 0 : k.props) != null && E.bars) {
              const { label: D, bars: T } = k.props;
              b.push({ label: D, bars: T });
            }
          });
      }), b;
    }, t = ref(false), o = ref(false), f = ref(void 0);
    let d;
    const m = (v) => {
      d && clearTimeout(d), d = setTimeout(() => {
        t.value = true;
      }, 800), f.value = v;
    }, x = () => {
      clearTimeout(d), t.value = false;
    }, g = (v, b, B, u) => {
      switch (v.type) {
        case "click":
          s("click-bar", { bar: b, e: v, datetime: B });
          break;
        case "mousedown":
          s("mousedown-bar", { bar: b, e: v, datetime: B });
          break;
        case "mouseup":
          s("mouseup-bar", { bar: b, e: v, datetime: B });
          break;
        case "dblclick":
          s("dblclick-bar", { bar: b, e: v, datetime: B });
          break;
        case "mouseenter":
          m(b), s("mouseenter-bar", { bar: b, e: v });
          break;
        case "mouseleave":
          x(), s("mouseleave-bar", { bar: b, e: v });
          break;
        case "dragstart":
          o.value = true, s("dragstart-bar", { bar: b, e: v });
          break;
        case "drag":
          s("drag-bar", { bar: b, e: v });
          break;
        case "dragend":
          o.value = false, s("dragend-bar", { bar: b, e: v, movedBars: u });
          break;
        case "contextmenu":
          s("contextmenu-bar", { bar: b, e: v, datetime: B });
          break;
      }
    }, h = ref(null), _ = Rt(h);
    return provide(Fe, i), provide(Pe, {
      ...toRefs(r),
      colors: a,
      chartSize: _
    }), provide(je, g), (v, b) => (openBlock(), createElementBlock("div", null, [
      createBaseVNode("div", {
        class: normalizeClass([{ "labels-in-column": !!v.labelColumnTitle }])
      }, [
        v.labelColumnTitle ? (openBlock(), createBlock(bt, {
          key: 0,
          style: normalizeStyle({
            width: v.labelColumnWidth
          })
        }, {
          "label-column-title": withCtx(() => [
            renderSlot(v.$slots, "label-column-title")
          ]),
          "label-column-row": withCtx(({ label: B }) => [
            renderSlot(v.$slots, "label-column-row", { label: B })
          ]),
          _: 3
        }, 8, ["style"])) : createCommentVNode("", true),
        createBaseVNode("div", {
          ref_key: "ganttChart",
          ref: h,
          class: normalizeClass(["g-gantt-chart", { "with-column": v.labelColumnTitle }]),
          style: normalizeStyle({ width: unref(n), background: a.value.background, fontFamily: unref(p) })
        }, [
          v.hideTimeaxis ? createCommentVNode("", true) : (openBlock(), createBlock(_t, { key: 0 }, {
            "upper-timeunit": withCtx(({ label: B, value: u, date: w }) => [
              renderSlot(v.$slots, "upper-timeunit", {
                label: B,
                value: u,
                date: w
              })
            ]),
            timeunit: withCtx(({ label: B, value: u, date: w }) => [
              renderSlot(v.$slots, "timeunit", {
                label: B,
                value: u,
                date: w
              })
            ]),
            _: 3
          })),
          v.grid ? (openBlock(), createBlock(vt, {
            key: 1,
            "highlighted-units": v.highlightedUnits
          }, null, 8, ["highlighted-units"])) : createCommentVNode("", true),
          v.currentTime ? (openBlock(), createBlock(Et, { key: 2 }, {
            "current-time-label": withCtx(() => [
              renderSlot(v.$slots, "current-time-label")
            ]),
            _: 3
          })) : createCommentVNode("", true),
          createBaseVNode("div", jt, [
            renderSlot(v.$slots, "default")
          ])
        ], 6)
      ], 2),
      createVNode(Ct, {
        "model-value": t.value || o.value,
        bar: f.value
      }, {
        default: withCtx(() => [
          renderSlot(v.$slots, "bar-tooltip", { bar: f.value })
        ]),
        _: 3
      }, 8, ["model-value", "bar"])
    ]));
  }
});
function $e(e, s = () => null, r = () => null, n = H()) {
  const { barStart: p, barEnd: l, pushOnOverlap: c } = n, a = ref(false);
  let i = 0, t;
  const { mapPositionToTime: o } = de(n), { toDayjs: f } = ae(n), d = (b) => {
    const B = document.getElementById(e.ganttBarConfig.id);
    if (!B)
      return;
    switch (i = b.clientX - (B.getBoundingClientRect().left || 0), b.target.className) {
      case "g-gantt-bar-handle-left":
        document.body.style.cursor = "ew-resize", t = g;
        break;
      case "g-gantt-bar-handle-right":
        document.body.style.cursor = "ew-resize", t = h;
        break;
      default:
        t = x;
    }
    a.value = true, window.addEventListener("mousemove", t), window.addEventListener("mouseup", v);
  }, m = () => {
    var u;
    const b = document.getElementById(e.ganttBarConfig.id), B = (u = b == null ? void 0 : b.closest(".g-gantt-row-bars-container")) == null ? void 0 : u.getBoundingClientRect();
    return { barElement: b, barContainer: B };
  }, x = (b) => {
    const { barElement: B, barContainer: u } = m();
    if (!B || !u)
      return;
    const w = B.getBoundingClientRect().width, y = b.clientX - u.left - i, k = y + w;
    _(y, k) || (e[p.value] = o(y), e[l.value] = o(k), s(b, e));
  }, g = (b) => {
    const { barElement: B, barContainer: u } = m();
    if (!B || !u)
      return;
    const w = b.clientX - u.left, y = o(w);
    f(y).isSameOrAfter(f(e, "end")) || (e[p.value] = y, s(b, e));
  }, h = (b) => {
    const { barElement: B, barContainer: u } = m();
    if (!B || !u)
      return;
    const w = b.clientX - u.left, y = o(w);
    f(y).isSameOrBefore(f(e, "start")) || (e[l.value] = y, s(b, e));
  }, _ = (b, B) => {
    if (!c.value)
      return false;
    const u = e.ganttBarConfig.dragLimitLeft, w = e.ganttBarConfig.dragLimitRight;
    return b && u != null && b < u || B && w != null && B > w;
  }, v = (b) => {
    a.value = false, document.body.style.cursor = "", window.removeEventListener("mousemove", t), window.removeEventListener("mouseup", v), r(b, e);
  };
  return {
    isDragging: a,
    initDrag: d
  };
}
function qe() {
  const e = inject(je);
  if (!e)
    throw Error("Failed to inject emitBarEvent!");
  return e;
}
function Ut() {
  const e = H(), s = _e(), r = qe(), { pushOnOverlap: n, barStart: p, barEnd: l, noOverlap: c, dateFormat: a } = e, i = /* @__PURE__ */ new Map(), { toDayjs: t, format: o } = ae(), f = (u, w) => {
    const { initDrag: y } = $e(u, m, v, e);
    r({ ...w, type: "dragstart" }, u), y(w), b(u);
  }, d = (u, w) => {
    const y = u.ganttBarConfig.bundle;
    y != null && (s().forEach((k) => {
      k.bars.forEach((E) => {
        if (E.ganttBarConfig.bundle === y) {
          const D = E === u ? v : () => null, { initDrag: T } = $e(E, m, D, e);
          T(w), b(E);
        }
      });
    }), r({ ...w, type: "dragstart" }, u));
  }, m = (u, w) => {
    r({ ...u, type: "drag" }, w), x(w);
  }, x = (u) => {
    if (!(n != null && n.value))
      return;
    let w = u, { overlapBar: y, overlapType: k } = g(w);
    for (; y; ) {
      b(y);
      const E = t(w[p.value]), D = t(w[l.value]), T = t(y[p.value]), $ = t(y[l.value]);
      let I;
      switch (k) {
        case "left":
          I = $.diff(E, "minutes", true), y[l.value] = o(w[p.value], a.value), y[p.value] = o(
            T.subtract(I, "minutes"),
            a.value
          );
          break;
        case "right":
          I = D.diff(T, "minutes", true), y[p.value] = o(D, a.value), y[l.value] = o(
            $.add(I, "minutes"),
            a.value
          );
          break;
        default:
          console.warn(
            "Vue-Ganttastic: One bar is inside of the other one! This should never occur while push-on-overlap is active!"
          );
          return;
      }
      y && (k === "left" || k === "right") && h(y, I, k), w = y, { overlapBar: y, overlapType: k } = g(y);
    }
  }, g = (u) => {
    var W, j;
    let w, y, k;
    const E = (j = (W = s().find((z) => z.bars.includes(u))) == null ? void 0 : W.bars) != null ? j : [], D = t(u[p.value]), T = t(u[l.value]);
    return { overlapBar: E.find((z) => {
      if (z === u)
        return false;
      const U = t(z[p.value]), F = t(z[l.value]);
      return w = D.isBetween(U, F), y = T.isBetween(U, F), k = U.isBetween(D, T) || F.isBetween(D, T), w || y || k;
    }), overlapType: w ? "left" : y ? "right" : k ? "between" : null };
  }, h = (u, w, y) => {
    b(u), u.ganttBarConfig.bundle && s().forEach((k) => {
      k.bars.forEach((E) => {
        E.ganttBarConfig.bundle === u.ganttBarConfig.bundle && E !== u && (b(E), _(E, w, y));
      });
    });
  }, _ = (u, w, y) => {
    switch (y) {
      case "left":
        u[p.value] = o(
          t(u, "start").subtract(w, "minutes"),
          a.value
        ), u[l.value] = o(
          t(u, "end").subtract(w, "minutes"),
          a.value
        );
        break;
      case "right":
        u[p.value] = o(
          t(u, "start").add(w, "minutes"),
          a.value
        ), u[l.value] = o(t(u, "end").add(w, "minutes"), a.value);
    }
    x(u);
  }, v = (u, w) => {
    B();
    const y = {
      ...u,
      type: "dragend"
    };
    r(y, w, void 0, new Map(i)), i.clear();
  }, b = (u) => {
    if (!i.has(u)) {
      const w = u[p.value], y = u[l.value];
      i.set(u, { oldStart: w, oldEnd: y });
    }
  }, B = () => {
    if (n.value || !c.value)
      return;
    let u = false;
    i.forEach((w, y) => {
      const { overlapBar: k } = g(y);
      k != null && (u = true);
    }), u && i.forEach(({ oldStart: w, oldEnd: y }, k) => {
      k[p.value] = w, k[l.value] = y;
    });
  };
  return {
    initDragOfBar: f,
    initDragOfBundle: d
  };
}
function Nt() {
  const { pushOnOverlap: e } = H(), s = _e(), r = (c) => {
    const a = [];
    return c != null && s().forEach((i) => {
      i.bars.forEach((t) => {
        t.ganttBarConfig.bundle === c && a.push(t);
      });
    }), a;
  }, n = (c) => {
    if (!e.value || c.ganttBarConfig.pushOnOverlap === false)
      return;
    for (const i of ["left", "right"]) {
      const t = i, { gapDistanceSoFar: o, bundleBarsAndGapDist: f } = p(
        c,
        0,
        t
      );
      let d = o;
      const m = f;
      if (!m)
        continue;
      for (let g = 0; g < m.length; g++) {
        const h = m[g].bar, _ = m[g].gapDistance;
        r(h.ganttBarConfig.bundle).filter(
          (b) => b !== h
        ).forEach((b) => {
          const B = p(b, _, t), u = B.gapDistanceSoFar, w = B.bundleBarsAndGapDist;
          u != null && (!d || u < d) && (d = u), w.forEach((y) => {
            m.find((k) => k.bar === y.bar) || m.push(y);
          });
        });
      }
      const x = document.getElementById(c.ganttBarConfig.id);
      d != null && t === "left" ? c.ganttBarConfig.dragLimitLeft = x.offsetLeft - d : d != null && t === "right" && (c.ganttBarConfig.dragLimitRight = x.offsetLeft + x.offsetWidth + d);
    }
    r(c.ganttBarConfig.bundle).forEach((i) => {
      i.ganttBarConfig.dragLimitLeft = c.ganttBarConfig.dragLimitLeft, i.ganttBarConfig.dragLimitRight = c.ganttBarConfig.dragLimitRight;
    });
  }, p = (c, a = 0, i) => {
    const t = c.ganttBarConfig.bundle ? [{ bar: c, gapDistance: a }] : [];
    let o = c, f = l(o, i);
    if (i === "left")
      for (; f; ) {
        const d = document.getElementById(o.ganttBarConfig.id), m = document.getElementById(f.ganttBarConfig.id), x = m.offsetLeft + m.offsetWidth;
        if (a += d.offsetLeft - x, f.ganttBarConfig.immobile)
          return { gapDistanceSoFar: a, bundleBarsAndGapDist: t };
        f.ganttBarConfig.bundle && t.push({
          bar: f,
          gapDistance: a
        }), o = f, f = l(f, "left");
      }
    if (i === "right")
      for (; f; ) {
        const d = document.getElementById(o.ganttBarConfig.id), m = document.getElementById(f.ganttBarConfig.id), x = d.offsetLeft + d.offsetWidth;
        if (a += m.offsetLeft - x, f.ganttBarConfig.immobile)
          return { gapDistanceSoFar: a, bundleBarsAndGapDist: t };
        f.ganttBarConfig.bundle && t.push({
          bar: f,
          gapDistance: a
        }), o = f, f = l(f, "right");
      }
    return { gapDistanceSoFar: null, bundleBarsAndGapDist: t };
  }, l = (c, a) => {
    var f, d;
    const i = document.getElementById(c.ganttBarConfig.id), t = (d = (f = s().find((m) => m.bars.includes(c))) == null ? void 0 : f.bars) != null ? d : [];
    let o = [];
    return a === "left" ? o = t.filter((m) => {
      const x = document.getElementById(m.ganttBarConfig.id);
      return x && x.offsetLeft < i.offsetLeft && m.ganttBarConfig.pushOnOverlap !== false;
    }) : o = t.filter((m) => {
      const x = document.getElementById(m.ganttBarConfig.id);
      return x && x.offsetLeft > i.offsetLeft && m.ganttBarConfig.pushOnOverlap !== false;
    }), o.length > 0 ? o.reduce((m, x) => {
      const g = document.getElementById(m.ganttBarConfig.id), h = document.getElementById(x.ganttBarConfig.id), _ = Math.abs(g.offsetLeft - i.offsetLeft), v = Math.abs(h.offsetLeft - i.offsetLeft);
      return _ < v ? m : x;
    }, o[0]) : null;
  };
  return {
    setDragLimitsOfGanttBar: n
  };
}
var Vt = ["id"];
var qt = { class: "g-gantt-bar-label" };
var Qt = ["innerHTML"];
var Xt = createBaseVNode("div", { class: "g-gantt-bar-handle-left" }, null, -1);
var Kt = createBaseVNode("div", { class: "g-gantt-bar-handle-right" }, null, -1);
var Zt = defineComponent({
  __name: "GGanttBar",
  props: {
    bar: {}
  },
  setup(e) {
    const s = e, r = qe(), n = H(), { rowHeight: p } = n, { bar: l } = toRefs(s), { mapTimeToPosition: c, mapPositionToTime: a } = de(), { initDragOfBar: i, initDragOfBundle: t } = Ut(), { setDragLimitsOfGanttBar: o } = Nt(), f = ref(false), d = computed(() => l.value.ganttBarConfig);
    function m(E) {
      d.value.bundle != null ? t(l.value, E) : i(l.value, E), f.value = true;
    }
    const x = () => {
      o(l.value), !d.value.immobile && (window.addEventListener("mousemove", m, {
        once: true
      }), window.addEventListener(
        "mouseup",
        () => {
          window.removeEventListener("mousemove", m), f.value = false;
        },
        { once: true }
      ));
    }, g = inject(ze), h = (E) => {
      var $;
      E.preventDefault(), E.type === "mousedown" && x();
      const D = ($ = g == null ? void 0 : g.value) == null ? void 0 : $.getBoundingClientRect();
      if (!D)
        return;
      const T = a(E.clientX - D.left);
      r(E, l.value, T);
    }, { barStart: _, barEnd: v, width: b, chartStart: B, chartEnd: u, chartSize: w } = n, y = ref(0), k = ref(0);
    return onMounted(() => {
      watch(
        [l, b, B, u, w.width],
        () => {
          y.value = c(l.value[_.value]), k.value = c(l.value[v.value]);
        },
        { deep: true, immediate: true }
      );
    }), (E, D) => (openBlock(), createElementBlock("div", {
      id: d.value.id,
      class: normalizeClass(["g-gantt-bar", d.value.class || ""]),
      style: normalizeStyle({
        ...d.value.style,
        position: "absolute",
        top: `${unref(p) * 0.1}px`,
        left: `${y.value}px`,
        width: `${k.value - y.value}px`,
        height: `${unref(p) * 0.8}px`,
        zIndex: f.value ? 3 : 2
      }),
      onMousedown: h,
      onClick: h,
      onDblclick: h,
      onMouseenter: h,
      onMouseleave: h,
      onContextmenu: h
    }, [
      createBaseVNode("div", qt, [
        renderSlot(E.$slots, "default", { bar: unref(l) }, () => [
          createBaseVNode("div", null, toDisplayString(d.value.label || ""), 1),
          d.value.html ? (openBlock(), createElementBlock("div", {
            key: 0,
            innerHTML: d.value.html
          }, null, 8, Qt)) : createCommentVNode("", true)
        ])
      ]),
      d.value.hasHandles ? (openBlock(), createElementBlock(Fragment, { key: 0 }, [
        Xt,
        Kt
      ], 64)) : createCommentVNode("", true)
    ], 46, Vt));
  }
});
var Jt = defineComponent({
  __name: "GGanttRow",
  props: {
    label: {},
    bars: {},
    highlightOnHover: { type: Boolean }
  },
  emits: ["drop"],
  setup(e, { emit: s }) {
    const r = e, { rowHeight: n, colors: p, labelColumnTitle: l } = H(), { highlightOnHover: c } = toRefs(r), a = ref(false), i = computed(() => ({
      height: `${n.value}px`,
      background: (c == null ? void 0 : c.value) && a.value ? p.value.hoverHighlight : null
    })), { mapPositionToTime: t } = de(), o = ref(null);
    provide(ze, o);
    const f = (m) => {
      var _;
      const x = (_ = o.value) == null ? void 0 : _.getBoundingClientRect();
      if (!x) {
        console.error("Vue-Ganttastic: failed to find bar container element for row.");
        return;
      }
      const g = m.clientX - x.left, h = t(g);
      s("drop", { e: m, datetime: h });
    }, d = (m) => !m || /^\s*$/.test(m);
    return (m, x) => (openBlock(), createElementBlock("div", {
      class: "g-gantt-row",
      style: normalizeStyle(i.value),
      onDragover: x[0] || (x[0] = withModifiers((g) => a.value = true, ["prevent"])),
      onDragleave: x[1] || (x[1] = (g) => a.value = false),
      onDrop: x[2] || (x[2] = (g) => f(g)),
      onMouseover: x[3] || (x[3] = (g) => a.value = true),
      onMouseleave: x[4] || (x[4] = (g) => a.value = false)
    }, [
      !d(m.label) && !unref(l) ? (openBlock(), createElementBlock("div", {
        key: 0,
        class: "g-gantt-row-label",
        style: normalizeStyle({ background: unref(p).primary, color: unref(p).text })
      }, [
        renderSlot(m.$slots, "label", {}, () => [
          createTextVNode(toDisplayString(m.label), 1)
        ])
      ], 4)) : createCommentVNode("", true),
      createBaseVNode("div", mergeProps({
        ref_key: "barContainer",
        ref: o,
        class: "g-gantt-row-bars-container"
      }, m.$attrs), [
        createVNode(TransitionGroup, {
          name: "bar-transition",
          tag: "div"
        }, {
          default: withCtx(() => [
            (openBlock(true), createElementBlock(Fragment, null, renderList(m.bars, (g) => (openBlock(), createBlock(Zt, {
              key: g.ganttBarConfig.id,
              bar: g
            }, {
              default: withCtx(() => [
                renderSlot(m.$slots, "bar-label", { bar: g })
              ]),
              _: 2
            }, 1032, ["bar"]))), 128))
          ]),
          _: 3
        })
      ], 16)
    ], 36));
  }
});
function en() {
  import_dayjs.default.extend(ct), import_dayjs.default.extend(dt), import_dayjs.default.extend(ft), import_dayjs.default.extend(mt), import_dayjs.default.extend(gt), import_dayjs.default.extend(ut), import_dayjs.default.extend(ht);
}
var an = {
  install(e, s) {
    en(), e.component("GGanttChart", zt), e.component("GGanttRow", Jt);
  }
};
function X(e, s = "top") {
  if (!e || typeof document > "u")
    return;
  const r = document.head, n = document.createElement("style");
  s === "top" && r.firstChild ? r.insertBefore(n, r.firstChild) : r.appendChild(n), n.appendChild(document.createTextNode(e));
}
X(`
.g-gantt-chart {
  position: relative;
  display: flex;
  flex-direction: column;
  overflow-x: hidden;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  font-feature-settings: "tnum";
  font-variant-numeric: tabular-nums;
  border-radius: 5px;
}
.with-column {
  border-top-left-radius: 0px;
  border-bottom-left-radius: 0px;
  border-top-right-radius: 5px;
  border-bottom-right-radius: 5px;
}
.g-gantt-rows-container {
  position: relative;
}
.labels-in-column {
  display: flex;
  flex-direction: row;
}
`, "top");
X(`
.g-gantt-row {
  width: 100%;
  transition: background 0.4s;
  position: relative;
}
.g-gantt-row > .g-gantt-row-bars-container {
  position: relative;
  border-top: 1px solid #eaeaea;
  width: 100%;
  border-bottom: 1px solid #eaeaea;
}
.g-gantt-row-label {
  position: absolute;
  top: 0;
  left: 0px;
  padding: 0px 8px;
  display: flex;
  align-items: center;
  height: 60%;
  min-height: 20px;
  font-size: 0.8em;
  font-weight: bold;
  border-bottom-right-radius: 6px;
  background: #f2f2f2;
  z-index: 3;
  box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.6);
}
.bar-transition-leave-active,
.bar-transition-enter-active {
  transition: all 0.2s;
}
.bar-transition-enter-from,
.bar-transition-leave-to {
  transform: scale(0.8);
  opacity: 0;
}
`, "top");
X(`
.g-grid-container {
  position: absolute;
  top: 0;
  left: 0%;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: space-between;
}
.g-grid-line {
  width: 1px;
  height: 100%;
  border-left: 1px solid #eaeaea;
}
`, "top");
X(`
.g-label-column {
  display: flex;
  align-items: center;
  flex-direction: column;
  color: rgb(64, 64, 64);
  font-feature-settings: "tnum";
  font-variant-numeric: tabular-nums;
  font-size: 0.9em;
}
.g-label-column-header {
  width: 100%;
  height: 80px;
  min-height: 80px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  border-top-left-radius: 5px;
}
.g-label-column-rows {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  border-bottom-left-radius: 5px;
}
.g-label-column-row {
  width: 100%;
  height: 100%;
  display: flex;
  padding: 0.1rem 0.3rem;
  overflow: hidden;
  white-space: normal;
  box-sizing: border-box;
  text-align: center;
  align-items: center;
  justify-content: center;
}
.g-label-column-row:last-child {
  border-bottom-left-radius: 5px;
}
`, "top");
X(`
.g-timeaxis {
  position: sticky;
  top: 0;
  width: 100%;
  height: 80px;
  background: white;
  z-index: 4;
  display: flex;
  flex-direction: column;
}
.g-timeunits-container {
  display: flex;
  width: 100%;
  height: 50%;
}
.g-timeunit {
  height: 100%;
  font-size: 65%;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.g-upper-timeunit {
  display: flex;
  height: 100%;
  justify-content: center;
  align-items: center;
}
.g-timeaxis-hour-pin {
  width: 1px;
  height: 10px;
}
`, "top");
X(`
.g-gantt-tooltip {
  position: fixed;
  background: black;
  color: white;
  z-index: 4;
  font-size: 0.85em;
  padding: 5px;
  border-radius: 3px;
  transition: opacity 0.2s;
  display: flex;
  align-items: center;
  font-feature-settings: "tnum";
  font-variant-numeric: tabular-nums;
}
.g-gantt-tooltip:before {
  content: "";
  position: absolute;
  top: 0;
  left: 10%;
  width: 0;
  height: 0;
  border: 10px solid transparent;
  border-bottom-color: black;
  border-top: 0;
  margin-left: -5px;
  margin-top: -5px;
}
.g-gantt-tooltip-color-dot {
  width: 8px;
  height: 8px;
  border-radius: 100%;
  margin-right: 4px;
}
.g-fade-enter-active,
.g-fade-leave-active {
  transition: opacity 0.3s ease;
}
.g-fade-enter-from,
.g-fade-leave-to {
  opacity: 0;
}
`, "top");
X(`
.g-grid-current-time {
  position: absolute;
  height: 100%;
  display: flex;
  z-index: 5;
}
.g-grid-current-time-marker {
  width: 0px;
  height: calc(100% - 2px);
  display: flex;
}
.g-grid-current-time-text {
  font-size: x-small;
}
`, "top");
X(`
.g-gantt-bar {
  display: flex;
  justify-content: center;
  align-items: center;
  background: cadetblue;
  overflow: hidden;
}
.g-gantt-bar-label {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  padding: 0 14px 0 14px; /* 14px is the width of the handle */
  display: flex;
  justify-content: center;
  align-items: center;
}
.g-gantt-bar-label > * {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.g-gantt-bar-handle-left,
.g-gantt-bar-handle-right {
  position: absolute;
  width: 10px;
  height: 100%;
  background: white;
  opacity: 0.7;
  border-radius: 0px;
  cursor: ew-resize;
  top: 0;
}
.g-gantt-bar-handle-left {
  left: 0;
}
.g-gantt-bar-handle-right {
  right: 0;
}
.g-gantt-bar-label img {
  pointer-events: none;
}
`, "top");
export {
  zt as GGanttChart,
  Jt as GGanttRow,
  an as default,
  en as extendDayjs,
  an as ganttastic
};
//# sourceMappingURL=@infectoone_vue-ganttastic.js.map

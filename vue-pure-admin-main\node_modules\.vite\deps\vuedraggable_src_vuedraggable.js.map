{"version": 3, "sources": ["../../.pnpm/sortablejs@1.14.0/node_modules/sortablejs/modular/sortable.esm.js", "../../.pnpm/vuedraggable@4.1.0_vue@3.5.18_typescript@5.8.3_/node_modules/vuedraggable/src/util/htmlHelper.js", "../../.pnpm/vuedraggable@4.1.0_vue@3.5.18_typescript@5.8.3_/node_modules/vuedraggable/src/util/console.js", "../../.pnpm/vuedraggable@4.1.0_vue@3.5.18_typescript@5.8.3_/node_modules/vuedraggable/src/util/string.js", "../../.pnpm/vuedraggable@4.1.0_vue@3.5.18_typescript@5.8.3_/node_modules/vuedraggable/src/core/sortableEvents.js", "../../.pnpm/vuedraggable@4.1.0_vue@3.5.18_typescript@5.8.3_/node_modules/vuedraggable/src/util/tags.js", "../../.pnpm/vuedraggable@4.1.0_vue@3.5.18_typescript@5.8.3_/node_modules/vuedraggable/src/core/componentBuilderHelper.js", "../../.pnpm/vuedraggable@4.1.0_vue@3.5.18_typescript@5.8.3_/node_modules/vuedraggable/src/core/componentStructure.js", "../../.pnpm/vuedraggable@4.1.0_vue@3.5.18_typescript@5.8.3_/node_modules/vuedraggable/src/core/renderHelper.js", "../../.pnpm/vuedraggable@4.1.0_vue@3.5.18_typescript@5.8.3_/node_modules/vuedraggable/src/vuedraggable.js"], "sourcesContent": ["/**!\n * Sortable 1.14.0\n * <AUTHOR>   <<EMAIL>>\n * <AUTHOR>    <<EMAIL>>\n * @license MIT\n */\nfunction ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n\n    if (enumerableOnly) {\n      symbols = symbols.filter(function (sym) {\n        return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n      });\n    }\n\n    keys.push.apply(keys, symbols);\n  }\n\n  return keys;\n}\n\nfunction _objectSpread2(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i] != null ? arguments[i] : {};\n\n    if (i % 2) {\n      ownKeys(Object(source), true).forEach(function (key) {\n        _defineProperty(target, key, source[key]);\n      });\n    } else if (Object.getOwnPropertyDescriptors) {\n      Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));\n    } else {\n      ownKeys(Object(source)).forEach(function (key) {\n        Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n      });\n    }\n  }\n\n  return target;\n}\n\nfunction _typeof(obj) {\n  \"@babel/helpers - typeof\";\n\n  if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") {\n    _typeof = function (obj) {\n      return typeof obj;\n    };\n  } else {\n    _typeof = function (obj) {\n      return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n    };\n  }\n\n  return _typeof(obj);\n}\n\nfunction _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n\n  return obj;\n}\n\nfunction _extends() {\n  _extends = Object.assign || function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n\n    return target;\n  };\n\n  return _extends.apply(this, arguments);\n}\n\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  var sourceKeys = Object.keys(source);\n  var key, i;\n\n  for (i = 0; i < sourceKeys.length; i++) {\n    key = sourceKeys[i];\n    if (excluded.indexOf(key) >= 0) continue;\n    target[key] = source[key];\n  }\n\n  return target;\n}\n\nfunction _objectWithoutProperties(source, excluded) {\n  if (source == null) return {};\n\n  var target = _objectWithoutPropertiesLoose(source, excluded);\n\n  var key, i;\n\n  if (Object.getOwnPropertySymbols) {\n    var sourceSymbolKeys = Object.getOwnPropertySymbols(source);\n\n    for (i = 0; i < sourceSymbolKeys.length; i++) {\n      key = sourceSymbolKeys[i];\n      if (excluded.indexOf(key) >= 0) continue;\n      if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;\n      target[key] = source[key];\n    }\n  }\n\n  return target;\n}\n\nfunction _toConsumableArray(arr) {\n  return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread();\n}\n\nfunction _arrayWithoutHoles(arr) {\n  if (Array.isArray(arr)) return _arrayLikeToArray(arr);\n}\n\nfunction _iterableToArray(iter) {\n  if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter);\n}\n\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\n\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n\n  for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];\n\n  return arr2;\n}\n\nfunction _nonIterableSpread() {\n  throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\n\nvar version = \"1.14.0\";\n\nfunction userAgent(pattern) {\n  if (typeof window !== 'undefined' && window.navigator) {\n    return !! /*@__PURE__*/navigator.userAgent.match(pattern);\n  }\n}\n\nvar IE11OrLess = userAgent(/(?:Trident.*rv[ :]?11\\.|msie|iemobile|Windows Phone)/i);\nvar Edge = userAgent(/Edge/i);\nvar FireFox = userAgent(/firefox/i);\nvar Safari = userAgent(/safari/i) && !userAgent(/chrome/i) && !userAgent(/android/i);\nvar IOS = userAgent(/iP(ad|od|hone)/i);\nvar ChromeForAndroid = userAgent(/chrome/i) && userAgent(/android/i);\n\nvar captureMode = {\n  capture: false,\n  passive: false\n};\n\nfunction on(el, event, fn) {\n  el.addEventListener(event, fn, !IE11OrLess && captureMode);\n}\n\nfunction off(el, event, fn) {\n  el.removeEventListener(event, fn, !IE11OrLess && captureMode);\n}\n\nfunction matches(\n/**HTMLElement*/\nel,\n/**String*/\nselector) {\n  if (!selector) return;\n  selector[0] === '>' && (selector = selector.substring(1));\n\n  if (el) {\n    try {\n      if (el.matches) {\n        return el.matches(selector);\n      } else if (el.msMatchesSelector) {\n        return el.msMatchesSelector(selector);\n      } else if (el.webkitMatchesSelector) {\n        return el.webkitMatchesSelector(selector);\n      }\n    } catch (_) {\n      return false;\n    }\n  }\n\n  return false;\n}\n\nfunction getParentOrHost(el) {\n  return el.host && el !== document && el.host.nodeType ? el.host : el.parentNode;\n}\n\nfunction closest(\n/**HTMLElement*/\nel,\n/**String*/\nselector,\n/**HTMLElement*/\nctx, includeCTX) {\n  if (el) {\n    ctx = ctx || document;\n\n    do {\n      if (selector != null && (selector[0] === '>' ? el.parentNode === ctx && matches(el, selector) : matches(el, selector)) || includeCTX && el === ctx) {\n        return el;\n      }\n\n      if (el === ctx) break;\n      /* jshint boss:true */\n    } while (el = getParentOrHost(el));\n  }\n\n  return null;\n}\n\nvar R_SPACE = /\\s+/g;\n\nfunction toggleClass(el, name, state) {\n  if (el && name) {\n    if (el.classList) {\n      el.classList[state ? 'add' : 'remove'](name);\n    } else {\n      var className = (' ' + el.className + ' ').replace(R_SPACE, ' ').replace(' ' + name + ' ', ' ');\n      el.className = (className + (state ? ' ' + name : '')).replace(R_SPACE, ' ');\n    }\n  }\n}\n\nfunction css(el, prop, val) {\n  var style = el && el.style;\n\n  if (style) {\n    if (val === void 0) {\n      if (document.defaultView && document.defaultView.getComputedStyle) {\n        val = document.defaultView.getComputedStyle(el, '');\n      } else if (el.currentStyle) {\n        val = el.currentStyle;\n      }\n\n      return prop === void 0 ? val : val[prop];\n    } else {\n      if (!(prop in style) && prop.indexOf('webkit') === -1) {\n        prop = '-webkit-' + prop;\n      }\n\n      style[prop] = val + (typeof val === 'string' ? '' : 'px');\n    }\n  }\n}\n\nfunction matrix(el, selfOnly) {\n  var appliedTransforms = '';\n\n  if (typeof el === 'string') {\n    appliedTransforms = el;\n  } else {\n    do {\n      var transform = css(el, 'transform');\n\n      if (transform && transform !== 'none') {\n        appliedTransforms = transform + ' ' + appliedTransforms;\n      }\n      /* jshint boss:true */\n\n    } while (!selfOnly && (el = el.parentNode));\n  }\n\n  var matrixFn = window.DOMMatrix || window.WebKitCSSMatrix || window.CSSMatrix || window.MSCSSMatrix;\n  /*jshint -W056 */\n\n  return matrixFn && new matrixFn(appliedTransforms);\n}\n\nfunction find(ctx, tagName, iterator) {\n  if (ctx) {\n    var list = ctx.getElementsByTagName(tagName),\n        i = 0,\n        n = list.length;\n\n    if (iterator) {\n      for (; i < n; i++) {\n        iterator(list[i], i);\n      }\n    }\n\n    return list;\n  }\n\n  return [];\n}\n\nfunction getWindowScrollingElement() {\n  var scrollingElement = document.scrollingElement;\n\n  if (scrollingElement) {\n    return scrollingElement;\n  } else {\n    return document.documentElement;\n  }\n}\n/**\n * Returns the \"bounding client rect\" of given element\n * @param  {HTMLElement} el                       The element whose boundingClientRect is wanted\n * @param  {[Boolean]} relativeToContainingBlock  Whether the rect should be relative to the containing block of (including) the container\n * @param  {[Boolean]} relativeToNonStaticParent  Whether the rect should be relative to the relative parent of (including) the contaienr\n * @param  {[Boolean]} undoScale                  Whether the container's scale() should be undone\n * @param  {[HTMLElement]} container              The parent the element will be placed in\n * @return {Object}                               The boundingClientRect of el, with specified adjustments\n */\n\n\nfunction getRect(el, relativeToContainingBlock, relativeToNonStaticParent, undoScale, container) {\n  if (!el.getBoundingClientRect && el !== window) return;\n  var elRect, top, left, bottom, right, height, width;\n\n  if (el !== window && el.parentNode && el !== getWindowScrollingElement()) {\n    elRect = el.getBoundingClientRect();\n    top = elRect.top;\n    left = elRect.left;\n    bottom = elRect.bottom;\n    right = elRect.right;\n    height = elRect.height;\n    width = elRect.width;\n  } else {\n    top = 0;\n    left = 0;\n    bottom = window.innerHeight;\n    right = window.innerWidth;\n    height = window.innerHeight;\n    width = window.innerWidth;\n  }\n\n  if ((relativeToContainingBlock || relativeToNonStaticParent) && el !== window) {\n    // Adjust for translate()\n    container = container || el.parentNode; // solves #1123 (see: https://stackoverflow.com/a/37953806/6088312)\n    // Not needed on <= IE11\n\n    if (!IE11OrLess) {\n      do {\n        if (container && container.getBoundingClientRect && (css(container, 'transform') !== 'none' || relativeToNonStaticParent && css(container, 'position') !== 'static')) {\n          var containerRect = container.getBoundingClientRect(); // Set relative to edges of padding box of container\n\n          top -= containerRect.top + parseInt(css(container, 'border-top-width'));\n          left -= containerRect.left + parseInt(css(container, 'border-left-width'));\n          bottom = top + elRect.height;\n          right = left + elRect.width;\n          break;\n        }\n        /* jshint boss:true */\n\n      } while (container = container.parentNode);\n    }\n  }\n\n  if (undoScale && el !== window) {\n    // Adjust for scale()\n    var elMatrix = matrix(container || el),\n        scaleX = elMatrix && elMatrix.a,\n        scaleY = elMatrix && elMatrix.d;\n\n    if (elMatrix) {\n      top /= scaleY;\n      left /= scaleX;\n      width /= scaleX;\n      height /= scaleY;\n      bottom = top + height;\n      right = left + width;\n    }\n  }\n\n  return {\n    top: top,\n    left: left,\n    bottom: bottom,\n    right: right,\n    width: width,\n    height: height\n  };\n}\n/**\n * Checks if a side of an element is scrolled past a side of its parents\n * @param  {HTMLElement}  el           The element who's side being scrolled out of view is in question\n * @param  {String}       elSide       Side of the element in question ('top', 'left', 'right', 'bottom')\n * @param  {String}       parentSide   Side of the parent in question ('top', 'left', 'right', 'bottom')\n * @return {HTMLElement}               The parent scroll element that the el's side is scrolled past, or null if there is no such element\n */\n\n\nfunction isScrolledPast(el, elSide, parentSide) {\n  var parent = getParentAutoScrollElement(el, true),\n      elSideVal = getRect(el)[elSide];\n  /* jshint boss:true */\n\n  while (parent) {\n    var parentSideVal = getRect(parent)[parentSide],\n        visible = void 0;\n\n    if (parentSide === 'top' || parentSide === 'left') {\n      visible = elSideVal >= parentSideVal;\n    } else {\n      visible = elSideVal <= parentSideVal;\n    }\n\n    if (!visible) return parent;\n    if (parent === getWindowScrollingElement()) break;\n    parent = getParentAutoScrollElement(parent, false);\n  }\n\n  return false;\n}\n/**\n * Gets nth child of el, ignoring hidden children, sortable's elements (does not ignore clone if it's visible)\n * and non-draggable elements\n * @param  {HTMLElement} el       The parent element\n * @param  {Number} childNum      The index of the child\n * @param  {Object} options       Parent Sortable's options\n * @return {HTMLElement}          The child at index childNum, or null if not found\n */\n\n\nfunction getChild(el, childNum, options, includeDragEl) {\n  var currentChild = 0,\n      i = 0,\n      children = el.children;\n\n  while (i < children.length) {\n    if (children[i].style.display !== 'none' && children[i] !== Sortable.ghost && (includeDragEl || children[i] !== Sortable.dragged) && closest(children[i], options.draggable, el, false)) {\n      if (currentChild === childNum) {\n        return children[i];\n      }\n\n      currentChild++;\n    }\n\n    i++;\n  }\n\n  return null;\n}\n/**\n * Gets the last child in the el, ignoring ghostEl or invisible elements (clones)\n * @param  {HTMLElement} el       Parent element\n * @param  {selector} selector    Any other elements that should be ignored\n * @return {HTMLElement}          The last child, ignoring ghostEl\n */\n\n\nfunction lastChild(el, selector) {\n  var last = el.lastElementChild;\n\n  while (last && (last === Sortable.ghost || css(last, 'display') === 'none' || selector && !matches(last, selector))) {\n    last = last.previousElementSibling;\n  }\n\n  return last || null;\n}\n/**\n * Returns the index of an element within its parent for a selected set of\n * elements\n * @param  {HTMLElement} el\n * @param  {selector} selector\n * @return {number}\n */\n\n\nfunction index(el, selector) {\n  var index = 0;\n\n  if (!el || !el.parentNode) {\n    return -1;\n  }\n  /* jshint boss:true */\n\n\n  while (el = el.previousElementSibling) {\n    if (el.nodeName.toUpperCase() !== 'TEMPLATE' && el !== Sortable.clone && (!selector || matches(el, selector))) {\n      index++;\n    }\n  }\n\n  return index;\n}\n/**\n * Returns the scroll offset of the given element, added with all the scroll offsets of parent elements.\n * The value is returned in real pixels.\n * @param  {HTMLElement} el\n * @return {Array}             Offsets in the format of [left, top]\n */\n\n\nfunction getRelativeScrollOffset(el) {\n  var offsetLeft = 0,\n      offsetTop = 0,\n      winScroller = getWindowScrollingElement();\n\n  if (el) {\n    do {\n      var elMatrix = matrix(el),\n          scaleX = elMatrix.a,\n          scaleY = elMatrix.d;\n      offsetLeft += el.scrollLeft * scaleX;\n      offsetTop += el.scrollTop * scaleY;\n    } while (el !== winScroller && (el = el.parentNode));\n  }\n\n  return [offsetLeft, offsetTop];\n}\n/**\n * Returns the index of the object within the given array\n * @param  {Array} arr   Array that may or may not hold the object\n * @param  {Object} obj  An object that has a key-value pair unique to and identical to a key-value pair in the object you want to find\n * @return {Number}      The index of the object in the array, or -1\n */\n\n\nfunction indexOfObject(arr, obj) {\n  for (var i in arr) {\n    if (!arr.hasOwnProperty(i)) continue;\n\n    for (var key in obj) {\n      if (obj.hasOwnProperty(key) && obj[key] === arr[i][key]) return Number(i);\n    }\n  }\n\n  return -1;\n}\n\nfunction getParentAutoScrollElement(el, includeSelf) {\n  // skip to window\n  if (!el || !el.getBoundingClientRect) return getWindowScrollingElement();\n  var elem = el;\n  var gotSelf = false;\n\n  do {\n    // we don't need to get elem css if it isn't even overflowing in the first place (performance)\n    if (elem.clientWidth < elem.scrollWidth || elem.clientHeight < elem.scrollHeight) {\n      var elemCSS = css(elem);\n\n      if (elem.clientWidth < elem.scrollWidth && (elemCSS.overflowX == 'auto' || elemCSS.overflowX == 'scroll') || elem.clientHeight < elem.scrollHeight && (elemCSS.overflowY == 'auto' || elemCSS.overflowY == 'scroll')) {\n        if (!elem.getBoundingClientRect || elem === document.body) return getWindowScrollingElement();\n        if (gotSelf || includeSelf) return elem;\n        gotSelf = true;\n      }\n    }\n    /* jshint boss:true */\n\n  } while (elem = elem.parentNode);\n\n  return getWindowScrollingElement();\n}\n\nfunction extend(dst, src) {\n  if (dst && src) {\n    for (var key in src) {\n      if (src.hasOwnProperty(key)) {\n        dst[key] = src[key];\n      }\n    }\n  }\n\n  return dst;\n}\n\nfunction isRectEqual(rect1, rect2) {\n  return Math.round(rect1.top) === Math.round(rect2.top) && Math.round(rect1.left) === Math.round(rect2.left) && Math.round(rect1.height) === Math.round(rect2.height) && Math.round(rect1.width) === Math.round(rect2.width);\n}\n\nvar _throttleTimeout;\n\nfunction throttle(callback, ms) {\n  return function () {\n    if (!_throttleTimeout) {\n      var args = arguments,\n          _this = this;\n\n      if (args.length === 1) {\n        callback.call(_this, args[0]);\n      } else {\n        callback.apply(_this, args);\n      }\n\n      _throttleTimeout = setTimeout(function () {\n        _throttleTimeout = void 0;\n      }, ms);\n    }\n  };\n}\n\nfunction cancelThrottle() {\n  clearTimeout(_throttleTimeout);\n  _throttleTimeout = void 0;\n}\n\nfunction scrollBy(el, x, y) {\n  el.scrollLeft += x;\n  el.scrollTop += y;\n}\n\nfunction clone(el) {\n  var Polymer = window.Polymer;\n  var $ = window.jQuery || window.Zepto;\n\n  if (Polymer && Polymer.dom) {\n    return Polymer.dom(el).cloneNode(true);\n  } else if ($) {\n    return $(el).clone(true)[0];\n  } else {\n    return el.cloneNode(true);\n  }\n}\n\nfunction setRect(el, rect) {\n  css(el, 'position', 'absolute');\n  css(el, 'top', rect.top);\n  css(el, 'left', rect.left);\n  css(el, 'width', rect.width);\n  css(el, 'height', rect.height);\n}\n\nfunction unsetRect(el) {\n  css(el, 'position', '');\n  css(el, 'top', '');\n  css(el, 'left', '');\n  css(el, 'width', '');\n  css(el, 'height', '');\n}\n\nvar expando = 'Sortable' + new Date().getTime();\n\nfunction AnimationStateManager() {\n  var animationStates = [],\n      animationCallbackId;\n  return {\n    captureAnimationState: function captureAnimationState() {\n      animationStates = [];\n      if (!this.options.animation) return;\n      var children = [].slice.call(this.el.children);\n      children.forEach(function (child) {\n        if (css(child, 'display') === 'none' || child === Sortable.ghost) return;\n        animationStates.push({\n          target: child,\n          rect: getRect(child)\n        });\n\n        var fromRect = _objectSpread2({}, animationStates[animationStates.length - 1].rect); // If animating: compensate for current animation\n\n\n        if (child.thisAnimationDuration) {\n          var childMatrix = matrix(child, true);\n\n          if (childMatrix) {\n            fromRect.top -= childMatrix.f;\n            fromRect.left -= childMatrix.e;\n          }\n        }\n\n        child.fromRect = fromRect;\n      });\n    },\n    addAnimationState: function addAnimationState(state) {\n      animationStates.push(state);\n    },\n    removeAnimationState: function removeAnimationState(target) {\n      animationStates.splice(indexOfObject(animationStates, {\n        target: target\n      }), 1);\n    },\n    animateAll: function animateAll(callback) {\n      var _this = this;\n\n      if (!this.options.animation) {\n        clearTimeout(animationCallbackId);\n        if (typeof callback === 'function') callback();\n        return;\n      }\n\n      var animating = false,\n          animationTime = 0;\n      animationStates.forEach(function (state) {\n        var time = 0,\n            target = state.target,\n            fromRect = target.fromRect,\n            toRect = getRect(target),\n            prevFromRect = target.prevFromRect,\n            prevToRect = target.prevToRect,\n            animatingRect = state.rect,\n            targetMatrix = matrix(target, true);\n\n        if (targetMatrix) {\n          // Compensate for current animation\n          toRect.top -= targetMatrix.f;\n          toRect.left -= targetMatrix.e;\n        }\n\n        target.toRect = toRect;\n\n        if (target.thisAnimationDuration) {\n          // Could also check if animatingRect is between fromRect and toRect\n          if (isRectEqual(prevFromRect, toRect) && !isRectEqual(fromRect, toRect) && // Make sure animatingRect is on line between toRect & fromRect\n          (animatingRect.top - toRect.top) / (animatingRect.left - toRect.left) === (fromRect.top - toRect.top) / (fromRect.left - toRect.left)) {\n            // If returning to same place as started from animation and on same axis\n            time = calculateRealTime(animatingRect, prevFromRect, prevToRect, _this.options);\n          }\n        } // if fromRect != toRect: animate\n\n\n        if (!isRectEqual(toRect, fromRect)) {\n          target.prevFromRect = fromRect;\n          target.prevToRect = toRect;\n\n          if (!time) {\n            time = _this.options.animation;\n          }\n\n          _this.animate(target, animatingRect, toRect, time);\n        }\n\n        if (time) {\n          animating = true;\n          animationTime = Math.max(animationTime, time);\n          clearTimeout(target.animationResetTimer);\n          target.animationResetTimer = setTimeout(function () {\n            target.animationTime = 0;\n            target.prevFromRect = null;\n            target.fromRect = null;\n            target.prevToRect = null;\n            target.thisAnimationDuration = null;\n          }, time);\n          target.thisAnimationDuration = time;\n        }\n      });\n      clearTimeout(animationCallbackId);\n\n      if (!animating) {\n        if (typeof callback === 'function') callback();\n      } else {\n        animationCallbackId = setTimeout(function () {\n          if (typeof callback === 'function') callback();\n        }, animationTime);\n      }\n\n      animationStates = [];\n    },\n    animate: function animate(target, currentRect, toRect, duration) {\n      if (duration) {\n        css(target, 'transition', '');\n        css(target, 'transform', '');\n        var elMatrix = matrix(this.el),\n            scaleX = elMatrix && elMatrix.a,\n            scaleY = elMatrix && elMatrix.d,\n            translateX = (currentRect.left - toRect.left) / (scaleX || 1),\n            translateY = (currentRect.top - toRect.top) / (scaleY || 1);\n        target.animatingX = !!translateX;\n        target.animatingY = !!translateY;\n        css(target, 'transform', 'translate3d(' + translateX + 'px,' + translateY + 'px,0)');\n        this.forRepaintDummy = repaint(target); // repaint\n\n        css(target, 'transition', 'transform ' + duration + 'ms' + (this.options.easing ? ' ' + this.options.easing : ''));\n        css(target, 'transform', 'translate3d(0,0,0)');\n        typeof target.animated === 'number' && clearTimeout(target.animated);\n        target.animated = setTimeout(function () {\n          css(target, 'transition', '');\n          css(target, 'transform', '');\n          target.animated = false;\n          target.animatingX = false;\n          target.animatingY = false;\n        }, duration);\n      }\n    }\n  };\n}\n\nfunction repaint(target) {\n  return target.offsetWidth;\n}\n\nfunction calculateRealTime(animatingRect, fromRect, toRect, options) {\n  return Math.sqrt(Math.pow(fromRect.top - animatingRect.top, 2) + Math.pow(fromRect.left - animatingRect.left, 2)) / Math.sqrt(Math.pow(fromRect.top - toRect.top, 2) + Math.pow(fromRect.left - toRect.left, 2)) * options.animation;\n}\n\nvar plugins = [];\nvar defaults = {\n  initializeByDefault: true\n};\nvar PluginManager = {\n  mount: function mount(plugin) {\n    // Set default static properties\n    for (var option in defaults) {\n      if (defaults.hasOwnProperty(option) && !(option in plugin)) {\n        plugin[option] = defaults[option];\n      }\n    }\n\n    plugins.forEach(function (p) {\n      if (p.pluginName === plugin.pluginName) {\n        throw \"Sortable: Cannot mount plugin \".concat(plugin.pluginName, \" more than once\");\n      }\n    });\n    plugins.push(plugin);\n  },\n  pluginEvent: function pluginEvent(eventName, sortable, evt) {\n    var _this = this;\n\n    this.eventCanceled = false;\n\n    evt.cancel = function () {\n      _this.eventCanceled = true;\n    };\n\n    var eventNameGlobal = eventName + 'Global';\n    plugins.forEach(function (plugin) {\n      if (!sortable[plugin.pluginName]) return; // Fire global events if it exists in this sortable\n\n      if (sortable[plugin.pluginName][eventNameGlobal]) {\n        sortable[plugin.pluginName][eventNameGlobal](_objectSpread2({\n          sortable: sortable\n        }, evt));\n      } // Only fire plugin event if plugin is enabled in this sortable,\n      // and plugin has event defined\n\n\n      if (sortable.options[plugin.pluginName] && sortable[plugin.pluginName][eventName]) {\n        sortable[plugin.pluginName][eventName](_objectSpread2({\n          sortable: sortable\n        }, evt));\n      }\n    });\n  },\n  initializePlugins: function initializePlugins(sortable, el, defaults, options) {\n    plugins.forEach(function (plugin) {\n      var pluginName = plugin.pluginName;\n      if (!sortable.options[pluginName] && !plugin.initializeByDefault) return;\n      var initialized = new plugin(sortable, el, sortable.options);\n      initialized.sortable = sortable;\n      initialized.options = sortable.options;\n      sortable[pluginName] = initialized; // Add default options from plugin\n\n      _extends(defaults, initialized.defaults);\n    });\n\n    for (var option in sortable.options) {\n      if (!sortable.options.hasOwnProperty(option)) continue;\n      var modified = this.modifyOption(sortable, option, sortable.options[option]);\n\n      if (typeof modified !== 'undefined') {\n        sortable.options[option] = modified;\n      }\n    }\n  },\n  getEventProperties: function getEventProperties(name, sortable) {\n    var eventProperties = {};\n    plugins.forEach(function (plugin) {\n      if (typeof plugin.eventProperties !== 'function') return;\n\n      _extends(eventProperties, plugin.eventProperties.call(sortable[plugin.pluginName], name));\n    });\n    return eventProperties;\n  },\n  modifyOption: function modifyOption(sortable, name, value) {\n    var modifiedValue;\n    plugins.forEach(function (plugin) {\n      // Plugin must exist on the Sortable\n      if (!sortable[plugin.pluginName]) return; // If static option listener exists for this option, call in the context of the Sortable's instance of this plugin\n\n      if (plugin.optionListeners && typeof plugin.optionListeners[name] === 'function') {\n        modifiedValue = plugin.optionListeners[name].call(sortable[plugin.pluginName], value);\n      }\n    });\n    return modifiedValue;\n  }\n};\n\nfunction dispatchEvent(_ref) {\n  var sortable = _ref.sortable,\n      rootEl = _ref.rootEl,\n      name = _ref.name,\n      targetEl = _ref.targetEl,\n      cloneEl = _ref.cloneEl,\n      toEl = _ref.toEl,\n      fromEl = _ref.fromEl,\n      oldIndex = _ref.oldIndex,\n      newIndex = _ref.newIndex,\n      oldDraggableIndex = _ref.oldDraggableIndex,\n      newDraggableIndex = _ref.newDraggableIndex,\n      originalEvent = _ref.originalEvent,\n      putSortable = _ref.putSortable,\n      extraEventProperties = _ref.extraEventProperties;\n  sortable = sortable || rootEl && rootEl[expando];\n  if (!sortable) return;\n  var evt,\n      options = sortable.options,\n      onName = 'on' + name.charAt(0).toUpperCase() + name.substr(1); // Support for new CustomEvent feature\n\n  if (window.CustomEvent && !IE11OrLess && !Edge) {\n    evt = new CustomEvent(name, {\n      bubbles: true,\n      cancelable: true\n    });\n  } else {\n    evt = document.createEvent('Event');\n    evt.initEvent(name, true, true);\n  }\n\n  evt.to = toEl || rootEl;\n  evt.from = fromEl || rootEl;\n  evt.item = targetEl || rootEl;\n  evt.clone = cloneEl;\n  evt.oldIndex = oldIndex;\n  evt.newIndex = newIndex;\n  evt.oldDraggableIndex = oldDraggableIndex;\n  evt.newDraggableIndex = newDraggableIndex;\n  evt.originalEvent = originalEvent;\n  evt.pullMode = putSortable ? putSortable.lastPutMode : undefined;\n\n  var allEventProperties = _objectSpread2(_objectSpread2({}, extraEventProperties), PluginManager.getEventProperties(name, sortable));\n\n  for (var option in allEventProperties) {\n    evt[option] = allEventProperties[option];\n  }\n\n  if (rootEl) {\n    rootEl.dispatchEvent(evt);\n  }\n\n  if (options[onName]) {\n    options[onName].call(sortable, evt);\n  }\n}\n\nvar _excluded = [\"evt\"];\n\nvar pluginEvent = function pluginEvent(eventName, sortable) {\n  var _ref = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {},\n      originalEvent = _ref.evt,\n      data = _objectWithoutProperties(_ref, _excluded);\n\n  PluginManager.pluginEvent.bind(Sortable)(eventName, sortable, _objectSpread2({\n    dragEl: dragEl,\n    parentEl: parentEl,\n    ghostEl: ghostEl,\n    rootEl: rootEl,\n    nextEl: nextEl,\n    lastDownEl: lastDownEl,\n    cloneEl: cloneEl,\n    cloneHidden: cloneHidden,\n    dragStarted: moved,\n    putSortable: putSortable,\n    activeSortable: Sortable.active,\n    originalEvent: originalEvent,\n    oldIndex: oldIndex,\n    oldDraggableIndex: oldDraggableIndex,\n    newIndex: newIndex,\n    newDraggableIndex: newDraggableIndex,\n    hideGhostForTarget: _hideGhostForTarget,\n    unhideGhostForTarget: _unhideGhostForTarget,\n    cloneNowHidden: function cloneNowHidden() {\n      cloneHidden = true;\n    },\n    cloneNowShown: function cloneNowShown() {\n      cloneHidden = false;\n    },\n    dispatchSortableEvent: function dispatchSortableEvent(name) {\n      _dispatchEvent({\n        sortable: sortable,\n        name: name,\n        originalEvent: originalEvent\n      });\n    }\n  }, data));\n};\n\nfunction _dispatchEvent(info) {\n  dispatchEvent(_objectSpread2({\n    putSortable: putSortable,\n    cloneEl: cloneEl,\n    targetEl: dragEl,\n    rootEl: rootEl,\n    oldIndex: oldIndex,\n    oldDraggableIndex: oldDraggableIndex,\n    newIndex: newIndex,\n    newDraggableIndex: newDraggableIndex\n  }, info));\n}\n\nvar dragEl,\n    parentEl,\n    ghostEl,\n    rootEl,\n    nextEl,\n    lastDownEl,\n    cloneEl,\n    cloneHidden,\n    oldIndex,\n    newIndex,\n    oldDraggableIndex,\n    newDraggableIndex,\n    activeGroup,\n    putSortable,\n    awaitingDragStarted = false,\n    ignoreNextClick = false,\n    sortables = [],\n    tapEvt,\n    touchEvt,\n    lastDx,\n    lastDy,\n    tapDistanceLeft,\n    tapDistanceTop,\n    moved,\n    lastTarget,\n    lastDirection,\n    pastFirstInvertThresh = false,\n    isCircumstantialInvert = false,\n    targetMoveDistance,\n    // For positioning ghost absolutely\nghostRelativeParent,\n    ghostRelativeParentInitialScroll = [],\n    // (left, top)\n_silent = false,\n    savedInputChecked = [];\n/** @const */\n\nvar documentExists = typeof document !== 'undefined',\n    PositionGhostAbsolutely = IOS,\n    CSSFloatProperty = Edge || IE11OrLess ? 'cssFloat' : 'float',\n    // This will not pass for IE9, because IE9 DnD only works on anchors\nsupportDraggable = documentExists && !ChromeForAndroid && !IOS && 'draggable' in document.createElement('div'),\n    supportCssPointerEvents = function () {\n  if (!documentExists) return; // false when <= IE11\n\n  if (IE11OrLess) {\n    return false;\n  }\n\n  var el = document.createElement('x');\n  el.style.cssText = 'pointer-events:auto';\n  return el.style.pointerEvents === 'auto';\n}(),\n    _detectDirection = function _detectDirection(el, options) {\n  var elCSS = css(el),\n      elWidth = parseInt(elCSS.width) - parseInt(elCSS.paddingLeft) - parseInt(elCSS.paddingRight) - parseInt(elCSS.borderLeftWidth) - parseInt(elCSS.borderRightWidth),\n      child1 = getChild(el, 0, options),\n      child2 = getChild(el, 1, options),\n      firstChildCSS = child1 && css(child1),\n      secondChildCSS = child2 && css(child2),\n      firstChildWidth = firstChildCSS && parseInt(firstChildCSS.marginLeft) + parseInt(firstChildCSS.marginRight) + getRect(child1).width,\n      secondChildWidth = secondChildCSS && parseInt(secondChildCSS.marginLeft) + parseInt(secondChildCSS.marginRight) + getRect(child2).width;\n\n  if (elCSS.display === 'flex') {\n    return elCSS.flexDirection === 'column' || elCSS.flexDirection === 'column-reverse' ? 'vertical' : 'horizontal';\n  }\n\n  if (elCSS.display === 'grid') {\n    return elCSS.gridTemplateColumns.split(' ').length <= 1 ? 'vertical' : 'horizontal';\n  }\n\n  if (child1 && firstChildCSS[\"float\"] && firstChildCSS[\"float\"] !== 'none') {\n    var touchingSideChild2 = firstChildCSS[\"float\"] === 'left' ? 'left' : 'right';\n    return child2 && (secondChildCSS.clear === 'both' || secondChildCSS.clear === touchingSideChild2) ? 'vertical' : 'horizontal';\n  }\n\n  return child1 && (firstChildCSS.display === 'block' || firstChildCSS.display === 'flex' || firstChildCSS.display === 'table' || firstChildCSS.display === 'grid' || firstChildWidth >= elWidth && elCSS[CSSFloatProperty] === 'none' || child2 && elCSS[CSSFloatProperty] === 'none' && firstChildWidth + secondChildWidth > elWidth) ? 'vertical' : 'horizontal';\n},\n    _dragElInRowColumn = function _dragElInRowColumn(dragRect, targetRect, vertical) {\n  var dragElS1Opp = vertical ? dragRect.left : dragRect.top,\n      dragElS2Opp = vertical ? dragRect.right : dragRect.bottom,\n      dragElOppLength = vertical ? dragRect.width : dragRect.height,\n      targetS1Opp = vertical ? targetRect.left : targetRect.top,\n      targetS2Opp = vertical ? targetRect.right : targetRect.bottom,\n      targetOppLength = vertical ? targetRect.width : targetRect.height;\n  return dragElS1Opp === targetS1Opp || dragElS2Opp === targetS2Opp || dragElS1Opp + dragElOppLength / 2 === targetS1Opp + targetOppLength / 2;\n},\n\n/**\n * Detects first nearest empty sortable to X and Y position using emptyInsertThreshold.\n * @param  {Number} x      X position\n * @param  {Number} y      Y position\n * @return {HTMLElement}   Element of the first found nearest Sortable\n */\n_detectNearestEmptySortable = function _detectNearestEmptySortable(x, y) {\n  var ret;\n  sortables.some(function (sortable) {\n    var threshold = sortable[expando].options.emptyInsertThreshold;\n    if (!threshold || lastChild(sortable)) return;\n    var rect = getRect(sortable),\n        insideHorizontally = x >= rect.left - threshold && x <= rect.right + threshold,\n        insideVertically = y >= rect.top - threshold && y <= rect.bottom + threshold;\n\n    if (insideHorizontally && insideVertically) {\n      return ret = sortable;\n    }\n  });\n  return ret;\n},\n    _prepareGroup = function _prepareGroup(options) {\n  function toFn(value, pull) {\n    return function (to, from, dragEl, evt) {\n      var sameGroup = to.options.group.name && from.options.group.name && to.options.group.name === from.options.group.name;\n\n      if (value == null && (pull || sameGroup)) {\n        // Default pull value\n        // Default pull and put value if same group\n        return true;\n      } else if (value == null || value === false) {\n        return false;\n      } else if (pull && value === 'clone') {\n        return value;\n      } else if (typeof value === 'function') {\n        return toFn(value(to, from, dragEl, evt), pull)(to, from, dragEl, evt);\n      } else {\n        var otherGroup = (pull ? to : from).options.group.name;\n        return value === true || typeof value === 'string' && value === otherGroup || value.join && value.indexOf(otherGroup) > -1;\n      }\n    };\n  }\n\n  var group = {};\n  var originalGroup = options.group;\n\n  if (!originalGroup || _typeof(originalGroup) != 'object') {\n    originalGroup = {\n      name: originalGroup\n    };\n  }\n\n  group.name = originalGroup.name;\n  group.checkPull = toFn(originalGroup.pull, true);\n  group.checkPut = toFn(originalGroup.put);\n  group.revertClone = originalGroup.revertClone;\n  options.group = group;\n},\n    _hideGhostForTarget = function _hideGhostForTarget() {\n  if (!supportCssPointerEvents && ghostEl) {\n    css(ghostEl, 'display', 'none');\n  }\n},\n    _unhideGhostForTarget = function _unhideGhostForTarget() {\n  if (!supportCssPointerEvents && ghostEl) {\n    css(ghostEl, 'display', '');\n  }\n}; // #1184 fix - Prevent click event on fallback if dragged but item not changed position\n\n\nif (documentExists) {\n  document.addEventListener('click', function (evt) {\n    if (ignoreNextClick) {\n      evt.preventDefault();\n      evt.stopPropagation && evt.stopPropagation();\n      evt.stopImmediatePropagation && evt.stopImmediatePropagation();\n      ignoreNextClick = false;\n      return false;\n    }\n  }, true);\n}\n\nvar nearestEmptyInsertDetectEvent = function nearestEmptyInsertDetectEvent(evt) {\n  if (dragEl) {\n    evt = evt.touches ? evt.touches[0] : evt;\n\n    var nearest = _detectNearestEmptySortable(evt.clientX, evt.clientY);\n\n    if (nearest) {\n      // Create imitation event\n      var event = {};\n\n      for (var i in evt) {\n        if (evt.hasOwnProperty(i)) {\n          event[i] = evt[i];\n        }\n      }\n\n      event.target = event.rootEl = nearest;\n      event.preventDefault = void 0;\n      event.stopPropagation = void 0;\n\n      nearest[expando]._onDragOver(event);\n    }\n  }\n};\n\nvar _checkOutsideTargetEl = function _checkOutsideTargetEl(evt) {\n  if (dragEl) {\n    dragEl.parentNode[expando]._isOutsideThisEl(evt.target);\n  }\n};\n/**\n * @class  Sortable\n * @param  {HTMLElement}  el\n * @param  {Object}       [options]\n */\n\n\nfunction Sortable(el, options) {\n  if (!(el && el.nodeType && el.nodeType === 1)) {\n    throw \"Sortable: `el` must be an HTMLElement, not \".concat({}.toString.call(el));\n  }\n\n  this.el = el; // root element\n\n  this.options = options = _extends({}, options); // Export instance\n\n  el[expando] = this;\n  var defaults = {\n    group: null,\n    sort: true,\n    disabled: false,\n    store: null,\n    handle: null,\n    draggable: /^[uo]l$/i.test(el.nodeName) ? '>li' : '>*',\n    swapThreshold: 1,\n    // percentage; 0 <= x <= 1\n    invertSwap: false,\n    // invert always\n    invertedSwapThreshold: null,\n    // will be set to same as swapThreshold if default\n    removeCloneOnHide: true,\n    direction: function direction() {\n      return _detectDirection(el, this.options);\n    },\n    ghostClass: 'sortable-ghost',\n    chosenClass: 'sortable-chosen',\n    dragClass: 'sortable-drag',\n    ignore: 'a, img',\n    filter: null,\n    preventOnFilter: true,\n    animation: 0,\n    easing: null,\n    setData: function setData(dataTransfer, dragEl) {\n      dataTransfer.setData('Text', dragEl.textContent);\n    },\n    dropBubble: false,\n    dragoverBubble: false,\n    dataIdAttr: 'data-id',\n    delay: 0,\n    delayOnTouchOnly: false,\n    touchStartThreshold: (Number.parseInt ? Number : window).parseInt(window.devicePixelRatio, 10) || 1,\n    forceFallback: false,\n    fallbackClass: 'sortable-fallback',\n    fallbackOnBody: false,\n    fallbackTolerance: 0,\n    fallbackOffset: {\n      x: 0,\n      y: 0\n    },\n    supportPointer: Sortable.supportPointer !== false && 'PointerEvent' in window && !Safari,\n    emptyInsertThreshold: 5\n  };\n  PluginManager.initializePlugins(this, el, defaults); // Set default options\n\n  for (var name in defaults) {\n    !(name in options) && (options[name] = defaults[name]);\n  }\n\n  _prepareGroup(options); // Bind all private methods\n\n\n  for (var fn in this) {\n    if (fn.charAt(0) === '_' && typeof this[fn] === 'function') {\n      this[fn] = this[fn].bind(this);\n    }\n  } // Setup drag mode\n\n\n  this.nativeDraggable = options.forceFallback ? false : supportDraggable;\n\n  if (this.nativeDraggable) {\n    // Touch start threshold cannot be greater than the native dragstart threshold\n    this.options.touchStartThreshold = 1;\n  } // Bind events\n\n\n  if (options.supportPointer) {\n    on(el, 'pointerdown', this._onTapStart);\n  } else {\n    on(el, 'mousedown', this._onTapStart);\n    on(el, 'touchstart', this._onTapStart);\n  }\n\n  if (this.nativeDraggable) {\n    on(el, 'dragover', this);\n    on(el, 'dragenter', this);\n  }\n\n  sortables.push(this.el); // Restore sorting\n\n  options.store && options.store.get && this.sort(options.store.get(this) || []); // Add animation state manager\n\n  _extends(this, AnimationStateManager());\n}\n\nSortable.prototype =\n/** @lends Sortable.prototype */\n{\n  constructor: Sortable,\n  _isOutsideThisEl: function _isOutsideThisEl(target) {\n    if (!this.el.contains(target) && target !== this.el) {\n      lastTarget = null;\n    }\n  },\n  _getDirection: function _getDirection(evt, target) {\n    return typeof this.options.direction === 'function' ? this.options.direction.call(this, evt, target, dragEl) : this.options.direction;\n  },\n  _onTapStart: function _onTapStart(\n  /** Event|TouchEvent */\n  evt) {\n    if (!evt.cancelable) return;\n\n    var _this = this,\n        el = this.el,\n        options = this.options,\n        preventOnFilter = options.preventOnFilter,\n        type = evt.type,\n        touch = evt.touches && evt.touches[0] || evt.pointerType && evt.pointerType === 'touch' && evt,\n        target = (touch || evt).target,\n        originalTarget = evt.target.shadowRoot && (evt.path && evt.path[0] || evt.composedPath && evt.composedPath()[0]) || target,\n        filter = options.filter;\n\n    _saveInputCheckedState(el); // Don't trigger start event when an element is been dragged, otherwise the evt.oldindex always wrong when set option.group.\n\n\n    if (dragEl) {\n      return;\n    }\n\n    if (/mousedown|pointerdown/.test(type) && evt.button !== 0 || options.disabled) {\n      return; // only left button and enabled\n    } // cancel dnd if original target is content editable\n\n\n    if (originalTarget.isContentEditable) {\n      return;\n    } // Safari ignores further event handling after mousedown\n\n\n    if (!this.nativeDraggable && Safari && target && target.tagName.toUpperCase() === 'SELECT') {\n      return;\n    }\n\n    target = closest(target, options.draggable, el, false);\n\n    if (target && target.animated) {\n      return;\n    }\n\n    if (lastDownEl === target) {\n      // Ignoring duplicate `down`\n      return;\n    } // Get the index of the dragged element within its parent\n\n\n    oldIndex = index(target);\n    oldDraggableIndex = index(target, options.draggable); // Check filter\n\n    if (typeof filter === 'function') {\n      if (filter.call(this, evt, target, this)) {\n        _dispatchEvent({\n          sortable: _this,\n          rootEl: originalTarget,\n          name: 'filter',\n          targetEl: target,\n          toEl: el,\n          fromEl: el\n        });\n\n        pluginEvent('filter', _this, {\n          evt: evt\n        });\n        preventOnFilter && evt.cancelable && evt.preventDefault();\n        return; // cancel dnd\n      }\n    } else if (filter) {\n      filter = filter.split(',').some(function (criteria) {\n        criteria = closest(originalTarget, criteria.trim(), el, false);\n\n        if (criteria) {\n          _dispatchEvent({\n            sortable: _this,\n            rootEl: criteria,\n            name: 'filter',\n            targetEl: target,\n            fromEl: el,\n            toEl: el\n          });\n\n          pluginEvent('filter', _this, {\n            evt: evt\n          });\n          return true;\n        }\n      });\n\n      if (filter) {\n        preventOnFilter && evt.cancelable && evt.preventDefault();\n        return; // cancel dnd\n      }\n    }\n\n    if (options.handle && !closest(originalTarget, options.handle, el, false)) {\n      return;\n    } // Prepare `dragstart`\n\n\n    this._prepareDragStart(evt, touch, target);\n  },\n  _prepareDragStart: function _prepareDragStart(\n  /** Event */\n  evt,\n  /** Touch */\n  touch,\n  /** HTMLElement */\n  target) {\n    var _this = this,\n        el = _this.el,\n        options = _this.options,\n        ownerDocument = el.ownerDocument,\n        dragStartFn;\n\n    if (target && !dragEl && target.parentNode === el) {\n      var dragRect = getRect(target);\n      rootEl = el;\n      dragEl = target;\n      parentEl = dragEl.parentNode;\n      nextEl = dragEl.nextSibling;\n      lastDownEl = target;\n      activeGroup = options.group;\n      Sortable.dragged = dragEl;\n      tapEvt = {\n        target: dragEl,\n        clientX: (touch || evt).clientX,\n        clientY: (touch || evt).clientY\n      };\n      tapDistanceLeft = tapEvt.clientX - dragRect.left;\n      tapDistanceTop = tapEvt.clientY - dragRect.top;\n      this._lastX = (touch || evt).clientX;\n      this._lastY = (touch || evt).clientY;\n      dragEl.style['will-change'] = 'all';\n\n      dragStartFn = function dragStartFn() {\n        pluginEvent('delayEnded', _this, {\n          evt: evt\n        });\n\n        if (Sortable.eventCanceled) {\n          _this._onDrop();\n\n          return;\n        } // Delayed drag has been triggered\n        // we can re-enable the events: touchmove/mousemove\n\n\n        _this._disableDelayedDragEvents();\n\n        if (!FireFox && _this.nativeDraggable) {\n          dragEl.draggable = true;\n        } // Bind the events: dragstart/dragend\n\n\n        _this._triggerDragStart(evt, touch); // Drag start event\n\n\n        _dispatchEvent({\n          sortable: _this,\n          name: 'choose',\n          originalEvent: evt\n        }); // Chosen item\n\n\n        toggleClass(dragEl, options.chosenClass, true);\n      }; // Disable \"draggable\"\n\n\n      options.ignore.split(',').forEach(function (criteria) {\n        find(dragEl, criteria.trim(), _disableDraggable);\n      });\n      on(ownerDocument, 'dragover', nearestEmptyInsertDetectEvent);\n      on(ownerDocument, 'mousemove', nearestEmptyInsertDetectEvent);\n      on(ownerDocument, 'touchmove', nearestEmptyInsertDetectEvent);\n      on(ownerDocument, 'mouseup', _this._onDrop);\n      on(ownerDocument, 'touchend', _this._onDrop);\n      on(ownerDocument, 'touchcancel', _this._onDrop); // Make dragEl draggable (must be before delay for FireFox)\n\n      if (FireFox && this.nativeDraggable) {\n        this.options.touchStartThreshold = 4;\n        dragEl.draggable = true;\n      }\n\n      pluginEvent('delayStart', this, {\n        evt: evt\n      }); // Delay is impossible for native DnD in Edge or IE\n\n      if (options.delay && (!options.delayOnTouchOnly || touch) && (!this.nativeDraggable || !(Edge || IE11OrLess))) {\n        if (Sortable.eventCanceled) {\n          this._onDrop();\n\n          return;\n        } // If the user moves the pointer or let go the click or touch\n        // before the delay has been reached:\n        // disable the delayed drag\n\n\n        on(ownerDocument, 'mouseup', _this._disableDelayedDrag);\n        on(ownerDocument, 'touchend', _this._disableDelayedDrag);\n        on(ownerDocument, 'touchcancel', _this._disableDelayedDrag);\n        on(ownerDocument, 'mousemove', _this._delayedDragTouchMoveHandler);\n        on(ownerDocument, 'touchmove', _this._delayedDragTouchMoveHandler);\n        options.supportPointer && on(ownerDocument, 'pointermove', _this._delayedDragTouchMoveHandler);\n        _this._dragStartTimer = setTimeout(dragStartFn, options.delay);\n      } else {\n        dragStartFn();\n      }\n    }\n  },\n  _delayedDragTouchMoveHandler: function _delayedDragTouchMoveHandler(\n  /** TouchEvent|PointerEvent **/\n  e) {\n    var touch = e.touches ? e.touches[0] : e;\n\n    if (Math.max(Math.abs(touch.clientX - this._lastX), Math.abs(touch.clientY - this._lastY)) >= Math.floor(this.options.touchStartThreshold / (this.nativeDraggable && window.devicePixelRatio || 1))) {\n      this._disableDelayedDrag();\n    }\n  },\n  _disableDelayedDrag: function _disableDelayedDrag() {\n    dragEl && _disableDraggable(dragEl);\n    clearTimeout(this._dragStartTimer);\n\n    this._disableDelayedDragEvents();\n  },\n  _disableDelayedDragEvents: function _disableDelayedDragEvents() {\n    var ownerDocument = this.el.ownerDocument;\n    off(ownerDocument, 'mouseup', this._disableDelayedDrag);\n    off(ownerDocument, 'touchend', this._disableDelayedDrag);\n    off(ownerDocument, 'touchcancel', this._disableDelayedDrag);\n    off(ownerDocument, 'mousemove', this._delayedDragTouchMoveHandler);\n    off(ownerDocument, 'touchmove', this._delayedDragTouchMoveHandler);\n    off(ownerDocument, 'pointermove', this._delayedDragTouchMoveHandler);\n  },\n  _triggerDragStart: function _triggerDragStart(\n  /** Event */\n  evt,\n  /** Touch */\n  touch) {\n    touch = touch || evt.pointerType == 'touch' && evt;\n\n    if (!this.nativeDraggable || touch) {\n      if (this.options.supportPointer) {\n        on(document, 'pointermove', this._onTouchMove);\n      } else if (touch) {\n        on(document, 'touchmove', this._onTouchMove);\n      } else {\n        on(document, 'mousemove', this._onTouchMove);\n      }\n    } else {\n      on(dragEl, 'dragend', this);\n      on(rootEl, 'dragstart', this._onDragStart);\n    }\n\n    try {\n      if (document.selection) {\n        // Timeout neccessary for IE9\n        _nextTick(function () {\n          document.selection.empty();\n        });\n      } else {\n        window.getSelection().removeAllRanges();\n      }\n    } catch (err) {}\n  },\n  _dragStarted: function _dragStarted(fallback, evt) {\n\n    awaitingDragStarted = false;\n\n    if (rootEl && dragEl) {\n      pluginEvent('dragStarted', this, {\n        evt: evt\n      });\n\n      if (this.nativeDraggable) {\n        on(document, 'dragover', _checkOutsideTargetEl);\n      }\n\n      var options = this.options; // Apply effect\n\n      !fallback && toggleClass(dragEl, options.dragClass, false);\n      toggleClass(dragEl, options.ghostClass, true);\n      Sortable.active = this;\n      fallback && this._appendGhost(); // Drag start event\n\n      _dispatchEvent({\n        sortable: this,\n        name: 'start',\n        originalEvent: evt\n      });\n    } else {\n      this._nulling();\n    }\n  },\n  _emulateDragOver: function _emulateDragOver() {\n    if (touchEvt) {\n      this._lastX = touchEvt.clientX;\n      this._lastY = touchEvt.clientY;\n\n      _hideGhostForTarget();\n\n      var target = document.elementFromPoint(touchEvt.clientX, touchEvt.clientY);\n      var parent = target;\n\n      while (target && target.shadowRoot) {\n        target = target.shadowRoot.elementFromPoint(touchEvt.clientX, touchEvt.clientY);\n        if (target === parent) break;\n        parent = target;\n      }\n\n      dragEl.parentNode[expando]._isOutsideThisEl(target);\n\n      if (parent) {\n        do {\n          if (parent[expando]) {\n            var inserted = void 0;\n            inserted = parent[expando]._onDragOver({\n              clientX: touchEvt.clientX,\n              clientY: touchEvt.clientY,\n              target: target,\n              rootEl: parent\n            });\n\n            if (inserted && !this.options.dragoverBubble) {\n              break;\n            }\n          }\n\n          target = parent; // store last element\n        }\n        /* jshint boss:true */\n        while (parent = parent.parentNode);\n      }\n\n      _unhideGhostForTarget();\n    }\n  },\n  _onTouchMove: function _onTouchMove(\n  /**TouchEvent*/\n  evt) {\n    if (tapEvt) {\n      var options = this.options,\n          fallbackTolerance = options.fallbackTolerance,\n          fallbackOffset = options.fallbackOffset,\n          touch = evt.touches ? evt.touches[0] : evt,\n          ghostMatrix = ghostEl && matrix(ghostEl, true),\n          scaleX = ghostEl && ghostMatrix && ghostMatrix.a,\n          scaleY = ghostEl && ghostMatrix && ghostMatrix.d,\n          relativeScrollOffset = PositionGhostAbsolutely && ghostRelativeParent && getRelativeScrollOffset(ghostRelativeParent),\n          dx = (touch.clientX - tapEvt.clientX + fallbackOffset.x) / (scaleX || 1) + (relativeScrollOffset ? relativeScrollOffset[0] - ghostRelativeParentInitialScroll[0] : 0) / (scaleX || 1),\n          dy = (touch.clientY - tapEvt.clientY + fallbackOffset.y) / (scaleY || 1) + (relativeScrollOffset ? relativeScrollOffset[1] - ghostRelativeParentInitialScroll[1] : 0) / (scaleY || 1); // only set the status to dragging, when we are actually dragging\n\n      if (!Sortable.active && !awaitingDragStarted) {\n        if (fallbackTolerance && Math.max(Math.abs(touch.clientX - this._lastX), Math.abs(touch.clientY - this._lastY)) < fallbackTolerance) {\n          return;\n        }\n\n        this._onDragStart(evt, true);\n      }\n\n      if (ghostEl) {\n        if (ghostMatrix) {\n          ghostMatrix.e += dx - (lastDx || 0);\n          ghostMatrix.f += dy - (lastDy || 0);\n        } else {\n          ghostMatrix = {\n            a: 1,\n            b: 0,\n            c: 0,\n            d: 1,\n            e: dx,\n            f: dy\n          };\n        }\n\n        var cssMatrix = \"matrix(\".concat(ghostMatrix.a, \",\").concat(ghostMatrix.b, \",\").concat(ghostMatrix.c, \",\").concat(ghostMatrix.d, \",\").concat(ghostMatrix.e, \",\").concat(ghostMatrix.f, \")\");\n        css(ghostEl, 'webkitTransform', cssMatrix);\n        css(ghostEl, 'mozTransform', cssMatrix);\n        css(ghostEl, 'msTransform', cssMatrix);\n        css(ghostEl, 'transform', cssMatrix);\n        lastDx = dx;\n        lastDy = dy;\n        touchEvt = touch;\n      }\n\n      evt.cancelable && evt.preventDefault();\n    }\n  },\n  _appendGhost: function _appendGhost() {\n    // Bug if using scale(): https://stackoverflow.com/questions/2637058\n    // Not being adjusted for\n    if (!ghostEl) {\n      var container = this.options.fallbackOnBody ? document.body : rootEl,\n          rect = getRect(dragEl, true, PositionGhostAbsolutely, true, container),\n          options = this.options; // Position absolutely\n\n      if (PositionGhostAbsolutely) {\n        // Get relatively positioned parent\n        ghostRelativeParent = container;\n\n        while (css(ghostRelativeParent, 'position') === 'static' && css(ghostRelativeParent, 'transform') === 'none' && ghostRelativeParent !== document) {\n          ghostRelativeParent = ghostRelativeParent.parentNode;\n        }\n\n        if (ghostRelativeParent !== document.body && ghostRelativeParent !== document.documentElement) {\n          if (ghostRelativeParent === document) ghostRelativeParent = getWindowScrollingElement();\n          rect.top += ghostRelativeParent.scrollTop;\n          rect.left += ghostRelativeParent.scrollLeft;\n        } else {\n          ghostRelativeParent = getWindowScrollingElement();\n        }\n\n        ghostRelativeParentInitialScroll = getRelativeScrollOffset(ghostRelativeParent);\n      }\n\n      ghostEl = dragEl.cloneNode(true);\n      toggleClass(ghostEl, options.ghostClass, false);\n      toggleClass(ghostEl, options.fallbackClass, true);\n      toggleClass(ghostEl, options.dragClass, true);\n      css(ghostEl, 'transition', '');\n      css(ghostEl, 'transform', '');\n      css(ghostEl, 'box-sizing', 'border-box');\n      css(ghostEl, 'margin', 0);\n      css(ghostEl, 'top', rect.top);\n      css(ghostEl, 'left', rect.left);\n      css(ghostEl, 'width', rect.width);\n      css(ghostEl, 'height', rect.height);\n      css(ghostEl, 'opacity', '0.8');\n      css(ghostEl, 'position', PositionGhostAbsolutely ? 'absolute' : 'fixed');\n      css(ghostEl, 'zIndex', '100000');\n      css(ghostEl, 'pointerEvents', 'none');\n      Sortable.ghost = ghostEl;\n      container.appendChild(ghostEl); // Set transform-origin\n\n      css(ghostEl, 'transform-origin', tapDistanceLeft / parseInt(ghostEl.style.width) * 100 + '% ' + tapDistanceTop / parseInt(ghostEl.style.height) * 100 + '%');\n    }\n  },\n  _onDragStart: function _onDragStart(\n  /**Event*/\n  evt,\n  /**boolean*/\n  fallback) {\n    var _this = this;\n\n    var dataTransfer = evt.dataTransfer;\n    var options = _this.options;\n    pluginEvent('dragStart', this, {\n      evt: evt\n    });\n\n    if (Sortable.eventCanceled) {\n      this._onDrop();\n\n      return;\n    }\n\n    pluginEvent('setupClone', this);\n\n    if (!Sortable.eventCanceled) {\n      cloneEl = clone(dragEl);\n      cloneEl.draggable = false;\n      cloneEl.style['will-change'] = '';\n\n      this._hideClone();\n\n      toggleClass(cloneEl, this.options.chosenClass, false);\n      Sortable.clone = cloneEl;\n    } // #1143: IFrame support workaround\n\n\n    _this.cloneId = _nextTick(function () {\n      pluginEvent('clone', _this);\n      if (Sortable.eventCanceled) return;\n\n      if (!_this.options.removeCloneOnHide) {\n        rootEl.insertBefore(cloneEl, dragEl);\n      }\n\n      _this._hideClone();\n\n      _dispatchEvent({\n        sortable: _this,\n        name: 'clone'\n      });\n    });\n    !fallback && toggleClass(dragEl, options.dragClass, true); // Set proper drop events\n\n    if (fallback) {\n      ignoreNextClick = true;\n      _this._loopId = setInterval(_this._emulateDragOver, 50);\n    } else {\n      // Undo what was set in _prepareDragStart before drag started\n      off(document, 'mouseup', _this._onDrop);\n      off(document, 'touchend', _this._onDrop);\n      off(document, 'touchcancel', _this._onDrop);\n\n      if (dataTransfer) {\n        dataTransfer.effectAllowed = 'move';\n        options.setData && options.setData.call(_this, dataTransfer, dragEl);\n      }\n\n      on(document, 'drop', _this); // #1276 fix:\n\n      css(dragEl, 'transform', 'translateZ(0)');\n    }\n\n    awaitingDragStarted = true;\n    _this._dragStartId = _nextTick(_this._dragStarted.bind(_this, fallback, evt));\n    on(document, 'selectstart', _this);\n    moved = true;\n\n    if (Safari) {\n      css(document.body, 'user-select', 'none');\n    }\n  },\n  // Returns true - if no further action is needed (either inserted or another condition)\n  _onDragOver: function _onDragOver(\n  /**Event*/\n  evt) {\n    var el = this.el,\n        target = evt.target,\n        dragRect,\n        targetRect,\n        revert,\n        options = this.options,\n        group = options.group,\n        activeSortable = Sortable.active,\n        isOwner = activeGroup === group,\n        canSort = options.sort,\n        fromSortable = putSortable || activeSortable,\n        vertical,\n        _this = this,\n        completedFired = false;\n\n    if (_silent) return;\n\n    function dragOverEvent(name, extra) {\n      pluginEvent(name, _this, _objectSpread2({\n        evt: evt,\n        isOwner: isOwner,\n        axis: vertical ? 'vertical' : 'horizontal',\n        revert: revert,\n        dragRect: dragRect,\n        targetRect: targetRect,\n        canSort: canSort,\n        fromSortable: fromSortable,\n        target: target,\n        completed: completed,\n        onMove: function onMove(target, after) {\n          return _onMove(rootEl, el, dragEl, dragRect, target, getRect(target), evt, after);\n        },\n        changed: changed\n      }, extra));\n    } // Capture animation state\n\n\n    function capture() {\n      dragOverEvent('dragOverAnimationCapture');\n\n      _this.captureAnimationState();\n\n      if (_this !== fromSortable) {\n        fromSortable.captureAnimationState();\n      }\n    } // Return invocation when dragEl is inserted (or completed)\n\n\n    function completed(insertion) {\n      dragOverEvent('dragOverCompleted', {\n        insertion: insertion\n      });\n\n      if (insertion) {\n        // Clones must be hidden before folding animation to capture dragRectAbsolute properly\n        if (isOwner) {\n          activeSortable._hideClone();\n        } else {\n          activeSortable._showClone(_this);\n        }\n\n        if (_this !== fromSortable) {\n          // Set ghost class to new sortable's ghost class\n          toggleClass(dragEl, putSortable ? putSortable.options.ghostClass : activeSortable.options.ghostClass, false);\n          toggleClass(dragEl, options.ghostClass, true);\n        }\n\n        if (putSortable !== _this && _this !== Sortable.active) {\n          putSortable = _this;\n        } else if (_this === Sortable.active && putSortable) {\n          putSortable = null;\n        } // Animation\n\n\n        if (fromSortable === _this) {\n          _this._ignoreWhileAnimating = target;\n        }\n\n        _this.animateAll(function () {\n          dragOverEvent('dragOverAnimationComplete');\n          _this._ignoreWhileAnimating = null;\n        });\n\n        if (_this !== fromSortable) {\n          fromSortable.animateAll();\n          fromSortable._ignoreWhileAnimating = null;\n        }\n      } // Null lastTarget if it is not inside a previously swapped element\n\n\n      if (target === dragEl && !dragEl.animated || target === el && !target.animated) {\n        lastTarget = null;\n      } // no bubbling and not fallback\n\n\n      if (!options.dragoverBubble && !evt.rootEl && target !== document) {\n        dragEl.parentNode[expando]._isOutsideThisEl(evt.target); // Do not detect for empty insert if already inserted\n\n\n        !insertion && nearestEmptyInsertDetectEvent(evt);\n      }\n\n      !options.dragoverBubble && evt.stopPropagation && evt.stopPropagation();\n      return completedFired = true;\n    } // Call when dragEl has been inserted\n\n\n    function changed() {\n      newIndex = index(dragEl);\n      newDraggableIndex = index(dragEl, options.draggable);\n\n      _dispatchEvent({\n        sortable: _this,\n        name: 'change',\n        toEl: el,\n        newIndex: newIndex,\n        newDraggableIndex: newDraggableIndex,\n        originalEvent: evt\n      });\n    }\n\n    if (evt.preventDefault !== void 0) {\n      evt.cancelable && evt.preventDefault();\n    }\n\n    target = closest(target, options.draggable, el, true);\n    dragOverEvent('dragOver');\n    if (Sortable.eventCanceled) return completedFired;\n\n    if (dragEl.contains(evt.target) || target.animated && target.animatingX && target.animatingY || _this._ignoreWhileAnimating === target) {\n      return completed(false);\n    }\n\n    ignoreNextClick = false;\n\n    if (activeSortable && !options.disabled && (isOwner ? canSort || (revert = parentEl !== rootEl) // Reverting item into the original list\n    : putSortable === this || (this.lastPutMode = activeGroup.checkPull(this, activeSortable, dragEl, evt)) && group.checkPut(this, activeSortable, dragEl, evt))) {\n      vertical = this._getDirection(evt, target) === 'vertical';\n      dragRect = getRect(dragEl);\n      dragOverEvent('dragOverValid');\n      if (Sortable.eventCanceled) return completedFired;\n\n      if (revert) {\n        parentEl = rootEl; // actualization\n\n        capture();\n\n        this._hideClone();\n\n        dragOverEvent('revert');\n\n        if (!Sortable.eventCanceled) {\n          if (nextEl) {\n            rootEl.insertBefore(dragEl, nextEl);\n          } else {\n            rootEl.appendChild(dragEl);\n          }\n        }\n\n        return completed(true);\n      }\n\n      var elLastChild = lastChild(el, options.draggable);\n\n      if (!elLastChild || _ghostIsLast(evt, vertical, this) && !elLastChild.animated) {\n        // Insert to end of list\n        // If already at end of list: Do not insert\n        if (elLastChild === dragEl) {\n          return completed(false);\n        } // if there is a last element, it is the target\n\n\n        if (elLastChild && el === evt.target) {\n          target = elLastChild;\n        }\n\n        if (target) {\n          targetRect = getRect(target);\n        }\n\n        if (_onMove(rootEl, el, dragEl, dragRect, target, targetRect, evt, !!target) !== false) {\n          capture();\n          el.appendChild(dragEl);\n          parentEl = el; // actualization\n\n          changed();\n          return completed(true);\n        }\n      } else if (elLastChild && _ghostIsFirst(evt, vertical, this)) {\n        // Insert to start of list\n        var firstChild = getChild(el, 0, options, true);\n\n        if (firstChild === dragEl) {\n          return completed(false);\n        }\n\n        target = firstChild;\n        targetRect = getRect(target);\n\n        if (_onMove(rootEl, el, dragEl, dragRect, target, targetRect, evt, false) !== false) {\n          capture();\n          el.insertBefore(dragEl, firstChild);\n          parentEl = el; // actualization\n\n          changed();\n          return completed(true);\n        }\n      } else if (target.parentNode === el) {\n        targetRect = getRect(target);\n        var direction = 0,\n            targetBeforeFirstSwap,\n            differentLevel = dragEl.parentNode !== el,\n            differentRowCol = !_dragElInRowColumn(dragEl.animated && dragEl.toRect || dragRect, target.animated && target.toRect || targetRect, vertical),\n            side1 = vertical ? 'top' : 'left',\n            scrolledPastTop = isScrolledPast(target, 'top', 'top') || isScrolledPast(dragEl, 'top', 'top'),\n            scrollBefore = scrolledPastTop ? scrolledPastTop.scrollTop : void 0;\n\n        if (lastTarget !== target) {\n          targetBeforeFirstSwap = targetRect[side1];\n          pastFirstInvertThresh = false;\n          isCircumstantialInvert = !differentRowCol && options.invertSwap || differentLevel;\n        }\n\n        direction = _getSwapDirection(evt, target, targetRect, vertical, differentRowCol ? 1 : options.swapThreshold, options.invertedSwapThreshold == null ? options.swapThreshold : options.invertedSwapThreshold, isCircumstantialInvert, lastTarget === target);\n        var sibling;\n\n        if (direction !== 0) {\n          // Check if target is beside dragEl in respective direction (ignoring hidden elements)\n          var dragIndex = index(dragEl);\n\n          do {\n            dragIndex -= direction;\n            sibling = parentEl.children[dragIndex];\n          } while (sibling && (css(sibling, 'display') === 'none' || sibling === ghostEl));\n        } // If dragEl is already beside target: Do not insert\n\n\n        if (direction === 0 || sibling === target) {\n          return completed(false);\n        }\n\n        lastTarget = target;\n        lastDirection = direction;\n        var nextSibling = target.nextElementSibling,\n            after = false;\n        after = direction === 1;\n\n        var moveVector = _onMove(rootEl, el, dragEl, dragRect, target, targetRect, evt, after);\n\n        if (moveVector !== false) {\n          if (moveVector === 1 || moveVector === -1) {\n            after = moveVector === 1;\n          }\n\n          _silent = true;\n          setTimeout(_unsilent, 30);\n          capture();\n\n          if (after && !nextSibling) {\n            el.appendChild(dragEl);\n          } else {\n            target.parentNode.insertBefore(dragEl, after ? nextSibling : target);\n          } // Undo chrome's scroll adjustment (has no effect on other browsers)\n\n\n          if (scrolledPastTop) {\n            scrollBy(scrolledPastTop, 0, scrollBefore - scrolledPastTop.scrollTop);\n          }\n\n          parentEl = dragEl.parentNode; // actualization\n          // must be done before animation\n\n          if (targetBeforeFirstSwap !== undefined && !isCircumstantialInvert) {\n            targetMoveDistance = Math.abs(targetBeforeFirstSwap - getRect(target)[side1]);\n          }\n\n          changed();\n          return completed(true);\n        }\n      }\n\n      if (el.contains(dragEl)) {\n        return completed(false);\n      }\n    }\n\n    return false;\n  },\n  _ignoreWhileAnimating: null,\n  _offMoveEvents: function _offMoveEvents() {\n    off(document, 'mousemove', this._onTouchMove);\n    off(document, 'touchmove', this._onTouchMove);\n    off(document, 'pointermove', this._onTouchMove);\n    off(document, 'dragover', nearestEmptyInsertDetectEvent);\n    off(document, 'mousemove', nearestEmptyInsertDetectEvent);\n    off(document, 'touchmove', nearestEmptyInsertDetectEvent);\n  },\n  _offUpEvents: function _offUpEvents() {\n    var ownerDocument = this.el.ownerDocument;\n    off(ownerDocument, 'mouseup', this._onDrop);\n    off(ownerDocument, 'touchend', this._onDrop);\n    off(ownerDocument, 'pointerup', this._onDrop);\n    off(ownerDocument, 'touchcancel', this._onDrop);\n    off(document, 'selectstart', this);\n  },\n  _onDrop: function _onDrop(\n  /**Event*/\n  evt) {\n    var el = this.el,\n        options = this.options; // Get the index of the dragged element within its parent\n\n    newIndex = index(dragEl);\n    newDraggableIndex = index(dragEl, options.draggable);\n    pluginEvent('drop', this, {\n      evt: evt\n    });\n    parentEl = dragEl && dragEl.parentNode; // Get again after plugin event\n\n    newIndex = index(dragEl);\n    newDraggableIndex = index(dragEl, options.draggable);\n\n    if (Sortable.eventCanceled) {\n      this._nulling();\n\n      return;\n    }\n\n    awaitingDragStarted = false;\n    isCircumstantialInvert = false;\n    pastFirstInvertThresh = false;\n    clearInterval(this._loopId);\n    clearTimeout(this._dragStartTimer);\n\n    _cancelNextTick(this.cloneId);\n\n    _cancelNextTick(this._dragStartId); // Unbind events\n\n\n    if (this.nativeDraggable) {\n      off(document, 'drop', this);\n      off(el, 'dragstart', this._onDragStart);\n    }\n\n    this._offMoveEvents();\n\n    this._offUpEvents();\n\n    if (Safari) {\n      css(document.body, 'user-select', '');\n    }\n\n    css(dragEl, 'transform', '');\n\n    if (evt) {\n      if (moved) {\n        evt.cancelable && evt.preventDefault();\n        !options.dropBubble && evt.stopPropagation();\n      }\n\n      ghostEl && ghostEl.parentNode && ghostEl.parentNode.removeChild(ghostEl);\n\n      if (rootEl === parentEl || putSortable && putSortable.lastPutMode !== 'clone') {\n        // Remove clone(s)\n        cloneEl && cloneEl.parentNode && cloneEl.parentNode.removeChild(cloneEl);\n      }\n\n      if (dragEl) {\n        if (this.nativeDraggable) {\n          off(dragEl, 'dragend', this);\n        }\n\n        _disableDraggable(dragEl);\n\n        dragEl.style['will-change'] = ''; // Remove classes\n        // ghostClass is added in dragStarted\n\n        if (moved && !awaitingDragStarted) {\n          toggleClass(dragEl, putSortable ? putSortable.options.ghostClass : this.options.ghostClass, false);\n        }\n\n        toggleClass(dragEl, this.options.chosenClass, false); // Drag stop event\n\n        _dispatchEvent({\n          sortable: this,\n          name: 'unchoose',\n          toEl: parentEl,\n          newIndex: null,\n          newDraggableIndex: null,\n          originalEvent: evt\n        });\n\n        if (rootEl !== parentEl) {\n          if (newIndex >= 0) {\n            // Add event\n            _dispatchEvent({\n              rootEl: parentEl,\n              name: 'add',\n              toEl: parentEl,\n              fromEl: rootEl,\n              originalEvent: evt\n            }); // Remove event\n\n\n            _dispatchEvent({\n              sortable: this,\n              name: 'remove',\n              toEl: parentEl,\n              originalEvent: evt\n            }); // drag from one list and drop into another\n\n\n            _dispatchEvent({\n              rootEl: parentEl,\n              name: 'sort',\n              toEl: parentEl,\n              fromEl: rootEl,\n              originalEvent: evt\n            });\n\n            _dispatchEvent({\n              sortable: this,\n              name: 'sort',\n              toEl: parentEl,\n              originalEvent: evt\n            });\n          }\n\n          putSortable && putSortable.save();\n        } else {\n          if (newIndex !== oldIndex) {\n            if (newIndex >= 0) {\n              // drag & drop within the same list\n              _dispatchEvent({\n                sortable: this,\n                name: 'update',\n                toEl: parentEl,\n                originalEvent: evt\n              });\n\n              _dispatchEvent({\n                sortable: this,\n                name: 'sort',\n                toEl: parentEl,\n                originalEvent: evt\n              });\n            }\n          }\n        }\n\n        if (Sortable.active) {\n          /* jshint eqnull:true */\n          if (newIndex == null || newIndex === -1) {\n            newIndex = oldIndex;\n            newDraggableIndex = oldDraggableIndex;\n          }\n\n          _dispatchEvent({\n            sortable: this,\n            name: 'end',\n            toEl: parentEl,\n            originalEvent: evt\n          }); // Save sorting\n\n\n          this.save();\n        }\n      }\n    }\n\n    this._nulling();\n  },\n  _nulling: function _nulling() {\n    pluginEvent('nulling', this);\n    rootEl = dragEl = parentEl = ghostEl = nextEl = cloneEl = lastDownEl = cloneHidden = tapEvt = touchEvt = moved = newIndex = newDraggableIndex = oldIndex = oldDraggableIndex = lastTarget = lastDirection = putSortable = activeGroup = Sortable.dragged = Sortable.ghost = Sortable.clone = Sortable.active = null;\n    savedInputChecked.forEach(function (el) {\n      el.checked = true;\n    });\n    savedInputChecked.length = lastDx = lastDy = 0;\n  },\n  handleEvent: function handleEvent(\n  /**Event*/\n  evt) {\n    switch (evt.type) {\n      case 'drop':\n      case 'dragend':\n        this._onDrop(evt);\n\n        break;\n\n      case 'dragenter':\n      case 'dragover':\n        if (dragEl) {\n          this._onDragOver(evt);\n\n          _globalDragOver(evt);\n        }\n\n        break;\n\n      case 'selectstart':\n        evt.preventDefault();\n        break;\n    }\n  },\n\n  /**\n   * Serializes the item into an array of string.\n   * @returns {String[]}\n   */\n  toArray: function toArray() {\n    var order = [],\n        el,\n        children = this.el.children,\n        i = 0,\n        n = children.length,\n        options = this.options;\n\n    for (; i < n; i++) {\n      el = children[i];\n\n      if (closest(el, options.draggable, this.el, false)) {\n        order.push(el.getAttribute(options.dataIdAttr) || _generateId(el));\n      }\n    }\n\n    return order;\n  },\n\n  /**\n   * Sorts the elements according to the array.\n   * @param  {String[]}  order  order of the items\n   */\n  sort: function sort(order, useAnimation) {\n    var items = {},\n        rootEl = this.el;\n    this.toArray().forEach(function (id, i) {\n      var el = rootEl.children[i];\n\n      if (closest(el, this.options.draggable, rootEl, false)) {\n        items[id] = el;\n      }\n    }, this);\n    useAnimation && this.captureAnimationState();\n    order.forEach(function (id) {\n      if (items[id]) {\n        rootEl.removeChild(items[id]);\n        rootEl.appendChild(items[id]);\n      }\n    });\n    useAnimation && this.animateAll();\n  },\n\n  /**\n   * Save the current sorting\n   */\n  save: function save() {\n    var store = this.options.store;\n    store && store.set && store.set(this);\n  },\n\n  /**\n   * For each element in the set, get the first element that matches the selector by testing the element itself and traversing up through its ancestors in the DOM tree.\n   * @param   {HTMLElement}  el\n   * @param   {String}       [selector]  default: `options.draggable`\n   * @returns {HTMLElement|null}\n   */\n  closest: function closest$1(el, selector) {\n    return closest(el, selector || this.options.draggable, this.el, false);\n  },\n\n  /**\n   * Set/get option\n   * @param   {string} name\n   * @param   {*}      [value]\n   * @returns {*}\n   */\n  option: function option(name, value) {\n    var options = this.options;\n\n    if (value === void 0) {\n      return options[name];\n    } else {\n      var modifiedValue = PluginManager.modifyOption(this, name, value);\n\n      if (typeof modifiedValue !== 'undefined') {\n        options[name] = modifiedValue;\n      } else {\n        options[name] = value;\n      }\n\n      if (name === 'group') {\n        _prepareGroup(options);\n      }\n    }\n  },\n\n  /**\n   * Destroy\n   */\n  destroy: function destroy() {\n    pluginEvent('destroy', this);\n    var el = this.el;\n    el[expando] = null;\n    off(el, 'mousedown', this._onTapStart);\n    off(el, 'touchstart', this._onTapStart);\n    off(el, 'pointerdown', this._onTapStart);\n\n    if (this.nativeDraggable) {\n      off(el, 'dragover', this);\n      off(el, 'dragenter', this);\n    } // Remove draggable attributes\n\n\n    Array.prototype.forEach.call(el.querySelectorAll('[draggable]'), function (el) {\n      el.removeAttribute('draggable');\n    });\n\n    this._onDrop();\n\n    this._disableDelayedDragEvents();\n\n    sortables.splice(sortables.indexOf(this.el), 1);\n    this.el = el = null;\n  },\n  _hideClone: function _hideClone() {\n    if (!cloneHidden) {\n      pluginEvent('hideClone', this);\n      if (Sortable.eventCanceled) return;\n      css(cloneEl, 'display', 'none');\n\n      if (this.options.removeCloneOnHide && cloneEl.parentNode) {\n        cloneEl.parentNode.removeChild(cloneEl);\n      }\n\n      cloneHidden = true;\n    }\n  },\n  _showClone: function _showClone(putSortable) {\n    if (putSortable.lastPutMode !== 'clone') {\n      this._hideClone();\n\n      return;\n    }\n\n    if (cloneHidden) {\n      pluginEvent('showClone', this);\n      if (Sortable.eventCanceled) return; // show clone at dragEl or original position\n\n      if (dragEl.parentNode == rootEl && !this.options.group.revertClone) {\n        rootEl.insertBefore(cloneEl, dragEl);\n      } else if (nextEl) {\n        rootEl.insertBefore(cloneEl, nextEl);\n      } else {\n        rootEl.appendChild(cloneEl);\n      }\n\n      if (this.options.group.revertClone) {\n        this.animate(dragEl, cloneEl);\n      }\n\n      css(cloneEl, 'display', '');\n      cloneHidden = false;\n    }\n  }\n};\n\nfunction _globalDragOver(\n/**Event*/\nevt) {\n  if (evt.dataTransfer) {\n    evt.dataTransfer.dropEffect = 'move';\n  }\n\n  evt.cancelable && evt.preventDefault();\n}\n\nfunction _onMove(fromEl, toEl, dragEl, dragRect, targetEl, targetRect, originalEvent, willInsertAfter) {\n  var evt,\n      sortable = fromEl[expando],\n      onMoveFn = sortable.options.onMove,\n      retVal; // Support for new CustomEvent feature\n\n  if (window.CustomEvent && !IE11OrLess && !Edge) {\n    evt = new CustomEvent('move', {\n      bubbles: true,\n      cancelable: true\n    });\n  } else {\n    evt = document.createEvent('Event');\n    evt.initEvent('move', true, true);\n  }\n\n  evt.to = toEl;\n  evt.from = fromEl;\n  evt.dragged = dragEl;\n  evt.draggedRect = dragRect;\n  evt.related = targetEl || toEl;\n  evt.relatedRect = targetRect || getRect(toEl);\n  evt.willInsertAfter = willInsertAfter;\n  evt.originalEvent = originalEvent;\n  fromEl.dispatchEvent(evt);\n\n  if (onMoveFn) {\n    retVal = onMoveFn.call(sortable, evt, originalEvent);\n  }\n\n  return retVal;\n}\n\nfunction _disableDraggable(el) {\n  el.draggable = false;\n}\n\nfunction _unsilent() {\n  _silent = false;\n}\n\nfunction _ghostIsFirst(evt, vertical, sortable) {\n  var rect = getRect(getChild(sortable.el, 0, sortable.options, true));\n  var spacer = 10;\n  return vertical ? evt.clientX < rect.left - spacer || evt.clientY < rect.top && evt.clientX < rect.right : evt.clientY < rect.top - spacer || evt.clientY < rect.bottom && evt.clientX < rect.left;\n}\n\nfunction _ghostIsLast(evt, vertical, sortable) {\n  var rect = getRect(lastChild(sortable.el, sortable.options.draggable));\n  var spacer = 10;\n  return vertical ? evt.clientX > rect.right + spacer || evt.clientX <= rect.right && evt.clientY > rect.bottom && evt.clientX >= rect.left : evt.clientX > rect.right && evt.clientY > rect.top || evt.clientX <= rect.right && evt.clientY > rect.bottom + spacer;\n}\n\nfunction _getSwapDirection(evt, target, targetRect, vertical, swapThreshold, invertedSwapThreshold, invertSwap, isLastTarget) {\n  var mouseOnAxis = vertical ? evt.clientY : evt.clientX,\n      targetLength = vertical ? targetRect.height : targetRect.width,\n      targetS1 = vertical ? targetRect.top : targetRect.left,\n      targetS2 = vertical ? targetRect.bottom : targetRect.right,\n      invert = false;\n\n  if (!invertSwap) {\n    // Never invert or create dragEl shadow when target movemenet causes mouse to move past the end of regular swapThreshold\n    if (isLastTarget && targetMoveDistance < targetLength * swapThreshold) {\n      // multiplied only by swapThreshold because mouse will already be inside target by (1 - threshold) * targetLength / 2\n      // check if past first invert threshold on side opposite of lastDirection\n      if (!pastFirstInvertThresh && (lastDirection === 1 ? mouseOnAxis > targetS1 + targetLength * invertedSwapThreshold / 2 : mouseOnAxis < targetS2 - targetLength * invertedSwapThreshold / 2)) {\n        // past first invert threshold, do not restrict inverted threshold to dragEl shadow\n        pastFirstInvertThresh = true;\n      }\n\n      if (!pastFirstInvertThresh) {\n        // dragEl shadow (target move distance shadow)\n        if (lastDirection === 1 ? mouseOnAxis < targetS1 + targetMoveDistance // over dragEl shadow\n        : mouseOnAxis > targetS2 - targetMoveDistance) {\n          return -lastDirection;\n        }\n      } else {\n        invert = true;\n      }\n    } else {\n      // Regular\n      if (mouseOnAxis > targetS1 + targetLength * (1 - swapThreshold) / 2 && mouseOnAxis < targetS2 - targetLength * (1 - swapThreshold) / 2) {\n        return _getInsertDirection(target);\n      }\n    }\n  }\n\n  invert = invert || invertSwap;\n\n  if (invert) {\n    // Invert of regular\n    if (mouseOnAxis < targetS1 + targetLength * invertedSwapThreshold / 2 || mouseOnAxis > targetS2 - targetLength * invertedSwapThreshold / 2) {\n      return mouseOnAxis > targetS1 + targetLength / 2 ? 1 : -1;\n    }\n  }\n\n  return 0;\n}\n/**\n * Gets the direction dragEl must be swapped relative to target in order to make it\n * seem that dragEl has been \"inserted\" into that element's position\n * @param  {HTMLElement} target       The target whose position dragEl is being inserted at\n * @return {Number}                   Direction dragEl must be swapped\n */\n\n\nfunction _getInsertDirection(target) {\n  if (index(dragEl) < index(target)) {\n    return 1;\n  } else {\n    return -1;\n  }\n}\n/**\n * Generate id\n * @param   {HTMLElement} el\n * @returns {String}\n * @private\n */\n\n\nfunction _generateId(el) {\n  var str = el.tagName + el.className + el.src + el.href + el.textContent,\n      i = str.length,\n      sum = 0;\n\n  while (i--) {\n    sum += str.charCodeAt(i);\n  }\n\n  return sum.toString(36);\n}\n\nfunction _saveInputCheckedState(root) {\n  savedInputChecked.length = 0;\n  var inputs = root.getElementsByTagName('input');\n  var idx = inputs.length;\n\n  while (idx--) {\n    var el = inputs[idx];\n    el.checked && savedInputChecked.push(el);\n  }\n}\n\nfunction _nextTick(fn) {\n  return setTimeout(fn, 0);\n}\n\nfunction _cancelNextTick(id) {\n  return clearTimeout(id);\n} // Fixed #973:\n\n\nif (documentExists) {\n  on(document, 'touchmove', function (evt) {\n    if ((Sortable.active || awaitingDragStarted) && evt.cancelable) {\n      evt.preventDefault();\n    }\n  });\n} // Export utils\n\n\nSortable.utils = {\n  on: on,\n  off: off,\n  css: css,\n  find: find,\n  is: function is(el, selector) {\n    return !!closest(el, selector, el, false);\n  },\n  extend: extend,\n  throttle: throttle,\n  closest: closest,\n  toggleClass: toggleClass,\n  clone: clone,\n  index: index,\n  nextTick: _nextTick,\n  cancelNextTick: _cancelNextTick,\n  detectDirection: _detectDirection,\n  getChild: getChild\n};\n/**\n * Get the Sortable instance of an element\n * @param  {HTMLElement} element The element\n * @return {Sortable|undefined}         The instance of Sortable\n */\n\nSortable.get = function (element) {\n  return element[expando];\n};\n/**\n * Mount a plugin to Sortable\n * @param  {...SortablePlugin|SortablePlugin[]} plugins       Plugins being mounted\n */\n\n\nSortable.mount = function () {\n  for (var _len = arguments.length, plugins = new Array(_len), _key = 0; _key < _len; _key++) {\n    plugins[_key] = arguments[_key];\n  }\n\n  if (plugins[0].constructor === Array) plugins = plugins[0];\n  plugins.forEach(function (plugin) {\n    if (!plugin.prototype || !plugin.prototype.constructor) {\n      throw \"Sortable: Mounted plugin must be a constructor function, not \".concat({}.toString.call(plugin));\n    }\n\n    if (plugin.utils) Sortable.utils = _objectSpread2(_objectSpread2({}, Sortable.utils), plugin.utils);\n    PluginManager.mount(plugin);\n  });\n};\n/**\n * Create sortable instance\n * @param {HTMLElement}  el\n * @param {Object}      [options]\n */\n\n\nSortable.create = function (el, options) {\n  return new Sortable(el, options);\n}; // Export\n\n\nSortable.version = version;\n\nvar autoScrolls = [],\n    scrollEl,\n    scrollRootEl,\n    scrolling = false,\n    lastAutoScrollX,\n    lastAutoScrollY,\n    touchEvt$1,\n    pointerElemChangedInterval;\n\nfunction AutoScrollPlugin() {\n  function AutoScroll() {\n    this.defaults = {\n      scroll: true,\n      forceAutoScrollFallback: false,\n      scrollSensitivity: 30,\n      scrollSpeed: 10,\n      bubbleScroll: true\n    }; // Bind all private methods\n\n    for (var fn in this) {\n      if (fn.charAt(0) === '_' && typeof this[fn] === 'function') {\n        this[fn] = this[fn].bind(this);\n      }\n    }\n  }\n\n  AutoScroll.prototype = {\n    dragStarted: function dragStarted(_ref) {\n      var originalEvent = _ref.originalEvent;\n\n      if (this.sortable.nativeDraggable) {\n        on(document, 'dragover', this._handleAutoScroll);\n      } else {\n        if (this.options.supportPointer) {\n          on(document, 'pointermove', this._handleFallbackAutoScroll);\n        } else if (originalEvent.touches) {\n          on(document, 'touchmove', this._handleFallbackAutoScroll);\n        } else {\n          on(document, 'mousemove', this._handleFallbackAutoScroll);\n        }\n      }\n    },\n    dragOverCompleted: function dragOverCompleted(_ref2) {\n      var originalEvent = _ref2.originalEvent;\n\n      // For when bubbling is canceled and using fallback (fallback 'touchmove' always reached)\n      if (!this.options.dragOverBubble && !originalEvent.rootEl) {\n        this._handleAutoScroll(originalEvent);\n      }\n    },\n    drop: function drop() {\n      if (this.sortable.nativeDraggable) {\n        off(document, 'dragover', this._handleAutoScroll);\n      } else {\n        off(document, 'pointermove', this._handleFallbackAutoScroll);\n        off(document, 'touchmove', this._handleFallbackAutoScroll);\n        off(document, 'mousemove', this._handleFallbackAutoScroll);\n      }\n\n      clearPointerElemChangedInterval();\n      clearAutoScrolls();\n      cancelThrottle();\n    },\n    nulling: function nulling() {\n      touchEvt$1 = scrollRootEl = scrollEl = scrolling = pointerElemChangedInterval = lastAutoScrollX = lastAutoScrollY = null;\n      autoScrolls.length = 0;\n    },\n    _handleFallbackAutoScroll: function _handleFallbackAutoScroll(evt) {\n      this._handleAutoScroll(evt, true);\n    },\n    _handleAutoScroll: function _handleAutoScroll(evt, fallback) {\n      var _this = this;\n\n      var x = (evt.touches ? evt.touches[0] : evt).clientX,\n          y = (evt.touches ? evt.touches[0] : evt).clientY,\n          elem = document.elementFromPoint(x, y);\n      touchEvt$1 = evt; // IE does not seem to have native autoscroll,\n      // Edge's autoscroll seems too conditional,\n      // MACOS Safari does not have autoscroll,\n      // Firefox and Chrome are good\n\n      if (fallback || this.options.forceAutoScrollFallback || Edge || IE11OrLess || Safari) {\n        autoScroll(evt, this.options, elem, fallback); // Listener for pointer element change\n\n        var ogElemScroller = getParentAutoScrollElement(elem, true);\n\n        if (scrolling && (!pointerElemChangedInterval || x !== lastAutoScrollX || y !== lastAutoScrollY)) {\n          pointerElemChangedInterval && clearPointerElemChangedInterval(); // Detect for pointer elem change, emulating native DnD behaviour\n\n          pointerElemChangedInterval = setInterval(function () {\n            var newElem = getParentAutoScrollElement(document.elementFromPoint(x, y), true);\n\n            if (newElem !== ogElemScroller) {\n              ogElemScroller = newElem;\n              clearAutoScrolls();\n            }\n\n            autoScroll(evt, _this.options, newElem, fallback);\n          }, 10);\n          lastAutoScrollX = x;\n          lastAutoScrollY = y;\n        }\n      } else {\n        // if DnD is enabled (and browser has good autoscrolling), first autoscroll will already scroll, so get parent autoscroll of first autoscroll\n        if (!this.options.bubbleScroll || getParentAutoScrollElement(elem, true) === getWindowScrollingElement()) {\n          clearAutoScrolls();\n          return;\n        }\n\n        autoScroll(evt, this.options, getParentAutoScrollElement(elem, false), false);\n      }\n    }\n  };\n  return _extends(AutoScroll, {\n    pluginName: 'scroll',\n    initializeByDefault: true\n  });\n}\n\nfunction clearAutoScrolls() {\n  autoScrolls.forEach(function (autoScroll) {\n    clearInterval(autoScroll.pid);\n  });\n  autoScrolls = [];\n}\n\nfunction clearPointerElemChangedInterval() {\n  clearInterval(pointerElemChangedInterval);\n}\n\nvar autoScroll = throttle(function (evt, options, rootEl, isFallback) {\n  // Bug: https://bugzilla.mozilla.org/show_bug.cgi?id=505521\n  if (!options.scroll) return;\n  var x = (evt.touches ? evt.touches[0] : evt).clientX,\n      y = (evt.touches ? evt.touches[0] : evt).clientY,\n      sens = options.scrollSensitivity,\n      speed = options.scrollSpeed,\n      winScroller = getWindowScrollingElement();\n  var scrollThisInstance = false,\n      scrollCustomFn; // New scroll root, set scrollEl\n\n  if (scrollRootEl !== rootEl) {\n    scrollRootEl = rootEl;\n    clearAutoScrolls();\n    scrollEl = options.scroll;\n    scrollCustomFn = options.scrollFn;\n\n    if (scrollEl === true) {\n      scrollEl = getParentAutoScrollElement(rootEl, true);\n    }\n  }\n\n  var layersOut = 0;\n  var currentParent = scrollEl;\n\n  do {\n    var el = currentParent,\n        rect = getRect(el),\n        top = rect.top,\n        bottom = rect.bottom,\n        left = rect.left,\n        right = rect.right,\n        width = rect.width,\n        height = rect.height,\n        canScrollX = void 0,\n        canScrollY = void 0,\n        scrollWidth = el.scrollWidth,\n        scrollHeight = el.scrollHeight,\n        elCSS = css(el),\n        scrollPosX = el.scrollLeft,\n        scrollPosY = el.scrollTop;\n\n    if (el === winScroller) {\n      canScrollX = width < scrollWidth && (elCSS.overflowX === 'auto' || elCSS.overflowX === 'scroll' || elCSS.overflowX === 'visible');\n      canScrollY = height < scrollHeight && (elCSS.overflowY === 'auto' || elCSS.overflowY === 'scroll' || elCSS.overflowY === 'visible');\n    } else {\n      canScrollX = width < scrollWidth && (elCSS.overflowX === 'auto' || elCSS.overflowX === 'scroll');\n      canScrollY = height < scrollHeight && (elCSS.overflowY === 'auto' || elCSS.overflowY === 'scroll');\n    }\n\n    var vx = canScrollX && (Math.abs(right - x) <= sens && scrollPosX + width < scrollWidth) - (Math.abs(left - x) <= sens && !!scrollPosX);\n    var vy = canScrollY && (Math.abs(bottom - y) <= sens && scrollPosY + height < scrollHeight) - (Math.abs(top - y) <= sens && !!scrollPosY);\n\n    if (!autoScrolls[layersOut]) {\n      for (var i = 0; i <= layersOut; i++) {\n        if (!autoScrolls[i]) {\n          autoScrolls[i] = {};\n        }\n      }\n    }\n\n    if (autoScrolls[layersOut].vx != vx || autoScrolls[layersOut].vy != vy || autoScrolls[layersOut].el !== el) {\n      autoScrolls[layersOut].el = el;\n      autoScrolls[layersOut].vx = vx;\n      autoScrolls[layersOut].vy = vy;\n      clearInterval(autoScrolls[layersOut].pid);\n\n      if (vx != 0 || vy != 0) {\n        scrollThisInstance = true;\n        /* jshint loopfunc:true */\n\n        autoScrolls[layersOut].pid = setInterval(function () {\n          // emulate drag over during autoscroll (fallback), emulating native DnD behaviour\n          if (isFallback && this.layer === 0) {\n            Sortable.active._onTouchMove(touchEvt$1); // To move ghost if it is positioned absolutely\n\n          }\n\n          var scrollOffsetY = autoScrolls[this.layer].vy ? autoScrolls[this.layer].vy * speed : 0;\n          var scrollOffsetX = autoScrolls[this.layer].vx ? autoScrolls[this.layer].vx * speed : 0;\n\n          if (typeof scrollCustomFn === 'function') {\n            if (scrollCustomFn.call(Sortable.dragged.parentNode[expando], scrollOffsetX, scrollOffsetY, evt, touchEvt$1, autoScrolls[this.layer].el) !== 'continue') {\n              return;\n            }\n          }\n\n          scrollBy(autoScrolls[this.layer].el, scrollOffsetX, scrollOffsetY);\n        }.bind({\n          layer: layersOut\n        }), 24);\n      }\n    }\n\n    layersOut++;\n  } while (options.bubbleScroll && currentParent !== winScroller && (currentParent = getParentAutoScrollElement(currentParent, false)));\n\n  scrolling = scrollThisInstance; // in case another function catches scrolling as false in between when it is not\n}, 30);\n\nvar drop = function drop(_ref) {\n  var originalEvent = _ref.originalEvent,\n      putSortable = _ref.putSortable,\n      dragEl = _ref.dragEl,\n      activeSortable = _ref.activeSortable,\n      dispatchSortableEvent = _ref.dispatchSortableEvent,\n      hideGhostForTarget = _ref.hideGhostForTarget,\n      unhideGhostForTarget = _ref.unhideGhostForTarget;\n  if (!originalEvent) return;\n  var toSortable = putSortable || activeSortable;\n  hideGhostForTarget();\n  var touch = originalEvent.changedTouches && originalEvent.changedTouches.length ? originalEvent.changedTouches[0] : originalEvent;\n  var target = document.elementFromPoint(touch.clientX, touch.clientY);\n  unhideGhostForTarget();\n\n  if (toSortable && !toSortable.el.contains(target)) {\n    dispatchSortableEvent('spill');\n    this.onSpill({\n      dragEl: dragEl,\n      putSortable: putSortable\n    });\n  }\n};\n\nfunction Revert() {}\n\nRevert.prototype = {\n  startIndex: null,\n  dragStart: function dragStart(_ref2) {\n    var oldDraggableIndex = _ref2.oldDraggableIndex;\n    this.startIndex = oldDraggableIndex;\n  },\n  onSpill: function onSpill(_ref3) {\n    var dragEl = _ref3.dragEl,\n        putSortable = _ref3.putSortable;\n    this.sortable.captureAnimationState();\n\n    if (putSortable) {\n      putSortable.captureAnimationState();\n    }\n\n    var nextSibling = getChild(this.sortable.el, this.startIndex, this.options);\n\n    if (nextSibling) {\n      this.sortable.el.insertBefore(dragEl, nextSibling);\n    } else {\n      this.sortable.el.appendChild(dragEl);\n    }\n\n    this.sortable.animateAll();\n\n    if (putSortable) {\n      putSortable.animateAll();\n    }\n  },\n  drop: drop\n};\n\n_extends(Revert, {\n  pluginName: 'revertOnSpill'\n});\n\nfunction Remove() {}\n\nRemove.prototype = {\n  onSpill: function onSpill(_ref4) {\n    var dragEl = _ref4.dragEl,\n        putSortable = _ref4.putSortable;\n    var parentSortable = putSortable || this.sortable;\n    parentSortable.captureAnimationState();\n    dragEl.parentNode && dragEl.parentNode.removeChild(dragEl);\n    parentSortable.animateAll();\n  },\n  drop: drop\n};\n\n_extends(Remove, {\n  pluginName: 'removeOnSpill'\n});\n\nvar lastSwapEl;\n\nfunction SwapPlugin() {\n  function Swap() {\n    this.defaults = {\n      swapClass: 'sortable-swap-highlight'\n    };\n  }\n\n  Swap.prototype = {\n    dragStart: function dragStart(_ref) {\n      var dragEl = _ref.dragEl;\n      lastSwapEl = dragEl;\n    },\n    dragOverValid: function dragOverValid(_ref2) {\n      var completed = _ref2.completed,\n          target = _ref2.target,\n          onMove = _ref2.onMove,\n          activeSortable = _ref2.activeSortable,\n          changed = _ref2.changed,\n          cancel = _ref2.cancel;\n      if (!activeSortable.options.swap) return;\n      var el = this.sortable.el,\n          options = this.options;\n\n      if (target && target !== el) {\n        var prevSwapEl = lastSwapEl;\n\n        if (onMove(target) !== false) {\n          toggleClass(target, options.swapClass, true);\n          lastSwapEl = target;\n        } else {\n          lastSwapEl = null;\n        }\n\n        if (prevSwapEl && prevSwapEl !== lastSwapEl) {\n          toggleClass(prevSwapEl, options.swapClass, false);\n        }\n      }\n\n      changed();\n      completed(true);\n      cancel();\n    },\n    drop: function drop(_ref3) {\n      var activeSortable = _ref3.activeSortable,\n          putSortable = _ref3.putSortable,\n          dragEl = _ref3.dragEl;\n      var toSortable = putSortable || this.sortable;\n      var options = this.options;\n      lastSwapEl && toggleClass(lastSwapEl, options.swapClass, false);\n\n      if (lastSwapEl && (options.swap || putSortable && putSortable.options.swap)) {\n        if (dragEl !== lastSwapEl) {\n          toSortable.captureAnimationState();\n          if (toSortable !== activeSortable) activeSortable.captureAnimationState();\n          swapNodes(dragEl, lastSwapEl);\n          toSortable.animateAll();\n          if (toSortable !== activeSortable) activeSortable.animateAll();\n        }\n      }\n    },\n    nulling: function nulling() {\n      lastSwapEl = null;\n    }\n  };\n  return _extends(Swap, {\n    pluginName: 'swap',\n    eventProperties: function eventProperties() {\n      return {\n        swapItem: lastSwapEl\n      };\n    }\n  });\n}\n\nfunction swapNodes(n1, n2) {\n  var p1 = n1.parentNode,\n      p2 = n2.parentNode,\n      i1,\n      i2;\n  if (!p1 || !p2 || p1.isEqualNode(n2) || p2.isEqualNode(n1)) return;\n  i1 = index(n1);\n  i2 = index(n2);\n\n  if (p1.isEqualNode(p2) && i1 < i2) {\n    i2++;\n  }\n\n  p1.insertBefore(n2, p1.children[i1]);\n  p2.insertBefore(n1, p2.children[i2]);\n}\n\nvar multiDragElements = [],\n    multiDragClones = [],\n    lastMultiDragSelect,\n    // for selection with modifier key down (SHIFT)\nmultiDragSortable,\n    initialFolding = false,\n    // Initial multi-drag fold when drag started\nfolding = false,\n    // Folding any other time\ndragStarted = false,\n    dragEl$1,\n    clonesFromRect,\n    clonesHidden;\n\nfunction MultiDragPlugin() {\n  function MultiDrag(sortable) {\n    // Bind all private methods\n    for (var fn in this) {\n      if (fn.charAt(0) === '_' && typeof this[fn] === 'function') {\n        this[fn] = this[fn].bind(this);\n      }\n    }\n\n    if (sortable.options.supportPointer) {\n      on(document, 'pointerup', this._deselectMultiDrag);\n    } else {\n      on(document, 'mouseup', this._deselectMultiDrag);\n      on(document, 'touchend', this._deselectMultiDrag);\n    }\n\n    on(document, 'keydown', this._checkKeyDown);\n    on(document, 'keyup', this._checkKeyUp);\n    this.defaults = {\n      selectedClass: 'sortable-selected',\n      multiDragKey: null,\n      setData: function setData(dataTransfer, dragEl) {\n        var data = '';\n\n        if (multiDragElements.length && multiDragSortable === sortable) {\n          multiDragElements.forEach(function (multiDragElement, i) {\n            data += (!i ? '' : ', ') + multiDragElement.textContent;\n          });\n        } else {\n          data = dragEl.textContent;\n        }\n\n        dataTransfer.setData('Text', data);\n      }\n    };\n  }\n\n  MultiDrag.prototype = {\n    multiDragKeyDown: false,\n    isMultiDrag: false,\n    delayStartGlobal: function delayStartGlobal(_ref) {\n      var dragged = _ref.dragEl;\n      dragEl$1 = dragged;\n    },\n    delayEnded: function delayEnded() {\n      this.isMultiDrag = ~multiDragElements.indexOf(dragEl$1);\n    },\n    setupClone: function setupClone(_ref2) {\n      var sortable = _ref2.sortable,\n          cancel = _ref2.cancel;\n      if (!this.isMultiDrag) return;\n\n      for (var i = 0; i < multiDragElements.length; i++) {\n        multiDragClones.push(clone(multiDragElements[i]));\n        multiDragClones[i].sortableIndex = multiDragElements[i].sortableIndex;\n        multiDragClones[i].draggable = false;\n        multiDragClones[i].style['will-change'] = '';\n        toggleClass(multiDragClones[i], this.options.selectedClass, false);\n        multiDragElements[i] === dragEl$1 && toggleClass(multiDragClones[i], this.options.chosenClass, false);\n      }\n\n      sortable._hideClone();\n\n      cancel();\n    },\n    clone: function clone(_ref3) {\n      var sortable = _ref3.sortable,\n          rootEl = _ref3.rootEl,\n          dispatchSortableEvent = _ref3.dispatchSortableEvent,\n          cancel = _ref3.cancel;\n      if (!this.isMultiDrag) return;\n\n      if (!this.options.removeCloneOnHide) {\n        if (multiDragElements.length && multiDragSortable === sortable) {\n          insertMultiDragClones(true, rootEl);\n          dispatchSortableEvent('clone');\n          cancel();\n        }\n      }\n    },\n    showClone: function showClone(_ref4) {\n      var cloneNowShown = _ref4.cloneNowShown,\n          rootEl = _ref4.rootEl,\n          cancel = _ref4.cancel;\n      if (!this.isMultiDrag) return;\n      insertMultiDragClones(false, rootEl);\n      multiDragClones.forEach(function (clone) {\n        css(clone, 'display', '');\n      });\n      cloneNowShown();\n      clonesHidden = false;\n      cancel();\n    },\n    hideClone: function hideClone(_ref5) {\n      var _this = this;\n\n      var sortable = _ref5.sortable,\n          cloneNowHidden = _ref5.cloneNowHidden,\n          cancel = _ref5.cancel;\n      if (!this.isMultiDrag) return;\n      multiDragClones.forEach(function (clone) {\n        css(clone, 'display', 'none');\n\n        if (_this.options.removeCloneOnHide && clone.parentNode) {\n          clone.parentNode.removeChild(clone);\n        }\n      });\n      cloneNowHidden();\n      clonesHidden = true;\n      cancel();\n    },\n    dragStartGlobal: function dragStartGlobal(_ref6) {\n      var sortable = _ref6.sortable;\n\n      if (!this.isMultiDrag && multiDragSortable) {\n        multiDragSortable.multiDrag._deselectMultiDrag();\n      }\n\n      multiDragElements.forEach(function (multiDragElement) {\n        multiDragElement.sortableIndex = index(multiDragElement);\n      }); // Sort multi-drag elements\n\n      multiDragElements = multiDragElements.sort(function (a, b) {\n        return a.sortableIndex - b.sortableIndex;\n      });\n      dragStarted = true;\n    },\n    dragStarted: function dragStarted(_ref7) {\n      var _this2 = this;\n\n      var sortable = _ref7.sortable;\n      if (!this.isMultiDrag) return;\n\n      if (this.options.sort) {\n        // Capture rects,\n        // hide multi drag elements (by positioning them absolute),\n        // set multi drag elements rects to dragRect,\n        // show multi drag elements,\n        // animate to rects,\n        // unset rects & remove from DOM\n        sortable.captureAnimationState();\n\n        if (this.options.animation) {\n          multiDragElements.forEach(function (multiDragElement) {\n            if (multiDragElement === dragEl$1) return;\n            css(multiDragElement, 'position', 'absolute');\n          });\n          var dragRect = getRect(dragEl$1, false, true, true);\n          multiDragElements.forEach(function (multiDragElement) {\n            if (multiDragElement === dragEl$1) return;\n            setRect(multiDragElement, dragRect);\n          });\n          folding = true;\n          initialFolding = true;\n        }\n      }\n\n      sortable.animateAll(function () {\n        folding = false;\n        initialFolding = false;\n\n        if (_this2.options.animation) {\n          multiDragElements.forEach(function (multiDragElement) {\n            unsetRect(multiDragElement);\n          });\n        } // Remove all auxiliary multidrag items from el, if sorting enabled\n\n\n        if (_this2.options.sort) {\n          removeMultiDragElements();\n        }\n      });\n    },\n    dragOver: function dragOver(_ref8) {\n      var target = _ref8.target,\n          completed = _ref8.completed,\n          cancel = _ref8.cancel;\n\n      if (folding && ~multiDragElements.indexOf(target)) {\n        completed(false);\n        cancel();\n      }\n    },\n    revert: function revert(_ref9) {\n      var fromSortable = _ref9.fromSortable,\n          rootEl = _ref9.rootEl,\n          sortable = _ref9.sortable,\n          dragRect = _ref9.dragRect;\n\n      if (multiDragElements.length > 1) {\n        // Setup unfold animation\n        multiDragElements.forEach(function (multiDragElement) {\n          sortable.addAnimationState({\n            target: multiDragElement,\n            rect: folding ? getRect(multiDragElement) : dragRect\n          });\n          unsetRect(multiDragElement);\n          multiDragElement.fromRect = dragRect;\n          fromSortable.removeAnimationState(multiDragElement);\n        });\n        folding = false;\n        insertMultiDragElements(!this.options.removeCloneOnHide, rootEl);\n      }\n    },\n    dragOverCompleted: function dragOverCompleted(_ref10) {\n      var sortable = _ref10.sortable,\n          isOwner = _ref10.isOwner,\n          insertion = _ref10.insertion,\n          activeSortable = _ref10.activeSortable,\n          parentEl = _ref10.parentEl,\n          putSortable = _ref10.putSortable;\n      var options = this.options;\n\n      if (insertion) {\n        // Clones must be hidden before folding animation to capture dragRectAbsolute properly\n        if (isOwner) {\n          activeSortable._hideClone();\n        }\n\n        initialFolding = false; // If leaving sort:false root, or already folding - Fold to new location\n\n        if (options.animation && multiDragElements.length > 1 && (folding || !isOwner && !activeSortable.options.sort && !putSortable)) {\n          // Fold: Set all multi drag elements's rects to dragEl's rect when multi-drag elements are invisible\n          var dragRectAbsolute = getRect(dragEl$1, false, true, true);\n          multiDragElements.forEach(function (multiDragElement) {\n            if (multiDragElement === dragEl$1) return;\n            setRect(multiDragElement, dragRectAbsolute); // Move element(s) to end of parentEl so that it does not interfere with multi-drag clones insertion if they are inserted\n            // while folding, and so that we can capture them again because old sortable will no longer be fromSortable\n\n            parentEl.appendChild(multiDragElement);\n          });\n          folding = true;\n        } // Clones must be shown (and check to remove multi drags) after folding when interfering multiDragElements are moved out\n\n\n        if (!isOwner) {\n          // Only remove if not folding (folding will remove them anyways)\n          if (!folding) {\n            removeMultiDragElements();\n          }\n\n          if (multiDragElements.length > 1) {\n            var clonesHiddenBefore = clonesHidden;\n\n            activeSortable._showClone(sortable); // Unfold animation for clones if showing from hidden\n\n\n            if (activeSortable.options.animation && !clonesHidden && clonesHiddenBefore) {\n              multiDragClones.forEach(function (clone) {\n                activeSortable.addAnimationState({\n                  target: clone,\n                  rect: clonesFromRect\n                });\n                clone.fromRect = clonesFromRect;\n                clone.thisAnimationDuration = null;\n              });\n            }\n          } else {\n            activeSortable._showClone(sortable);\n          }\n        }\n      }\n    },\n    dragOverAnimationCapture: function dragOverAnimationCapture(_ref11) {\n      var dragRect = _ref11.dragRect,\n          isOwner = _ref11.isOwner,\n          activeSortable = _ref11.activeSortable;\n      multiDragElements.forEach(function (multiDragElement) {\n        multiDragElement.thisAnimationDuration = null;\n      });\n\n      if (activeSortable.options.animation && !isOwner && activeSortable.multiDrag.isMultiDrag) {\n        clonesFromRect = _extends({}, dragRect);\n        var dragMatrix = matrix(dragEl$1, true);\n        clonesFromRect.top -= dragMatrix.f;\n        clonesFromRect.left -= dragMatrix.e;\n      }\n    },\n    dragOverAnimationComplete: function dragOverAnimationComplete() {\n      if (folding) {\n        folding = false;\n        removeMultiDragElements();\n      }\n    },\n    drop: function drop(_ref12) {\n      var evt = _ref12.originalEvent,\n          rootEl = _ref12.rootEl,\n          parentEl = _ref12.parentEl,\n          sortable = _ref12.sortable,\n          dispatchSortableEvent = _ref12.dispatchSortableEvent,\n          oldIndex = _ref12.oldIndex,\n          putSortable = _ref12.putSortable;\n      var toSortable = putSortable || this.sortable;\n      if (!evt) return;\n      var options = this.options,\n          children = parentEl.children; // Multi-drag selection\n\n      if (!dragStarted) {\n        if (options.multiDragKey && !this.multiDragKeyDown) {\n          this._deselectMultiDrag();\n        }\n\n        toggleClass(dragEl$1, options.selectedClass, !~multiDragElements.indexOf(dragEl$1));\n\n        if (!~multiDragElements.indexOf(dragEl$1)) {\n          multiDragElements.push(dragEl$1);\n          dispatchEvent({\n            sortable: sortable,\n            rootEl: rootEl,\n            name: 'select',\n            targetEl: dragEl$1,\n            originalEvt: evt\n          }); // Modifier activated, select from last to dragEl\n\n          if (evt.shiftKey && lastMultiDragSelect && sortable.el.contains(lastMultiDragSelect)) {\n            var lastIndex = index(lastMultiDragSelect),\n                currentIndex = index(dragEl$1);\n\n            if (~lastIndex && ~currentIndex && lastIndex !== currentIndex) {\n              // Must include lastMultiDragSelect (select it), in case modified selection from no selection\n              // (but previous selection existed)\n              var n, i;\n\n              if (currentIndex > lastIndex) {\n                i = lastIndex;\n                n = currentIndex;\n              } else {\n                i = currentIndex;\n                n = lastIndex + 1;\n              }\n\n              for (; i < n; i++) {\n                if (~multiDragElements.indexOf(children[i])) continue;\n                toggleClass(children[i], options.selectedClass, true);\n                multiDragElements.push(children[i]);\n                dispatchEvent({\n                  sortable: sortable,\n                  rootEl: rootEl,\n                  name: 'select',\n                  targetEl: children[i],\n                  originalEvt: evt\n                });\n              }\n            }\n          } else {\n            lastMultiDragSelect = dragEl$1;\n          }\n\n          multiDragSortable = toSortable;\n        } else {\n          multiDragElements.splice(multiDragElements.indexOf(dragEl$1), 1);\n          lastMultiDragSelect = null;\n          dispatchEvent({\n            sortable: sortable,\n            rootEl: rootEl,\n            name: 'deselect',\n            targetEl: dragEl$1,\n            originalEvt: evt\n          });\n        }\n      } // Multi-drag drop\n\n\n      if (dragStarted && this.isMultiDrag) {\n        folding = false; // Do not \"unfold\" after around dragEl if reverted\n\n        if ((parentEl[expando].options.sort || parentEl !== rootEl) && multiDragElements.length > 1) {\n          var dragRect = getRect(dragEl$1),\n              multiDragIndex = index(dragEl$1, ':not(.' + this.options.selectedClass + ')');\n          if (!initialFolding && options.animation) dragEl$1.thisAnimationDuration = null;\n          toSortable.captureAnimationState();\n\n          if (!initialFolding) {\n            if (options.animation) {\n              dragEl$1.fromRect = dragRect;\n              multiDragElements.forEach(function (multiDragElement) {\n                multiDragElement.thisAnimationDuration = null;\n\n                if (multiDragElement !== dragEl$1) {\n                  var rect = folding ? getRect(multiDragElement) : dragRect;\n                  multiDragElement.fromRect = rect; // Prepare unfold animation\n\n                  toSortable.addAnimationState({\n                    target: multiDragElement,\n                    rect: rect\n                  });\n                }\n              });\n            } // Multi drag elements are not necessarily removed from the DOM on drop, so to reinsert\n            // properly they must all be removed\n\n\n            removeMultiDragElements();\n            multiDragElements.forEach(function (multiDragElement) {\n              if (children[multiDragIndex]) {\n                parentEl.insertBefore(multiDragElement, children[multiDragIndex]);\n              } else {\n                parentEl.appendChild(multiDragElement);\n              }\n\n              multiDragIndex++;\n            }); // If initial folding is done, the elements may have changed position because they are now\n            // unfolding around dragEl, even though dragEl may not have his index changed, so update event\n            // must be fired here as Sortable will not.\n\n            if (oldIndex === index(dragEl$1)) {\n              var update = false;\n              multiDragElements.forEach(function (multiDragElement) {\n                if (multiDragElement.sortableIndex !== index(multiDragElement)) {\n                  update = true;\n                  return;\n                }\n              });\n\n              if (update) {\n                dispatchSortableEvent('update');\n              }\n            }\n          } // Must be done after capturing individual rects (scroll bar)\n\n\n          multiDragElements.forEach(function (multiDragElement) {\n            unsetRect(multiDragElement);\n          });\n          toSortable.animateAll();\n        }\n\n        multiDragSortable = toSortable;\n      } // Remove clones if necessary\n\n\n      if (rootEl === parentEl || putSortable && putSortable.lastPutMode !== 'clone') {\n        multiDragClones.forEach(function (clone) {\n          clone.parentNode && clone.parentNode.removeChild(clone);\n        });\n      }\n    },\n    nullingGlobal: function nullingGlobal() {\n      this.isMultiDrag = dragStarted = false;\n      multiDragClones.length = 0;\n    },\n    destroyGlobal: function destroyGlobal() {\n      this._deselectMultiDrag();\n\n      off(document, 'pointerup', this._deselectMultiDrag);\n      off(document, 'mouseup', this._deselectMultiDrag);\n      off(document, 'touchend', this._deselectMultiDrag);\n      off(document, 'keydown', this._checkKeyDown);\n      off(document, 'keyup', this._checkKeyUp);\n    },\n    _deselectMultiDrag: function _deselectMultiDrag(evt) {\n      if (typeof dragStarted !== \"undefined\" && dragStarted) return; // Only deselect if selection is in this sortable\n\n      if (multiDragSortable !== this.sortable) return; // Only deselect if target is not item in this sortable\n\n      if (evt && closest(evt.target, this.options.draggable, this.sortable.el, false)) return; // Only deselect if left click\n\n      if (evt && evt.button !== 0) return;\n\n      while (multiDragElements.length) {\n        var el = multiDragElements[0];\n        toggleClass(el, this.options.selectedClass, false);\n        multiDragElements.shift();\n        dispatchEvent({\n          sortable: this.sortable,\n          rootEl: this.sortable.el,\n          name: 'deselect',\n          targetEl: el,\n          originalEvt: evt\n        });\n      }\n    },\n    _checkKeyDown: function _checkKeyDown(evt) {\n      if (evt.key === this.options.multiDragKey) {\n        this.multiDragKeyDown = true;\n      }\n    },\n    _checkKeyUp: function _checkKeyUp(evt) {\n      if (evt.key === this.options.multiDragKey) {\n        this.multiDragKeyDown = false;\n      }\n    }\n  };\n  return _extends(MultiDrag, {\n    // Static methods & properties\n    pluginName: 'multiDrag',\n    utils: {\n      /**\n       * Selects the provided multi-drag item\n       * @param  {HTMLElement} el    The element to be selected\n       */\n      select: function select(el) {\n        var sortable = el.parentNode[expando];\n        if (!sortable || !sortable.options.multiDrag || ~multiDragElements.indexOf(el)) return;\n\n        if (multiDragSortable && multiDragSortable !== sortable) {\n          multiDragSortable.multiDrag._deselectMultiDrag();\n\n          multiDragSortable = sortable;\n        }\n\n        toggleClass(el, sortable.options.selectedClass, true);\n        multiDragElements.push(el);\n      },\n\n      /**\n       * Deselects the provided multi-drag item\n       * @param  {HTMLElement} el    The element to be deselected\n       */\n      deselect: function deselect(el) {\n        var sortable = el.parentNode[expando],\n            index = multiDragElements.indexOf(el);\n        if (!sortable || !sortable.options.multiDrag || !~index) return;\n        toggleClass(el, sortable.options.selectedClass, false);\n        multiDragElements.splice(index, 1);\n      }\n    },\n    eventProperties: function eventProperties() {\n      var _this3 = this;\n\n      var oldIndicies = [],\n          newIndicies = [];\n      multiDragElements.forEach(function (multiDragElement) {\n        oldIndicies.push({\n          multiDragElement: multiDragElement,\n          index: multiDragElement.sortableIndex\n        }); // multiDragElements will already be sorted if folding\n\n        var newIndex;\n\n        if (folding && multiDragElement !== dragEl$1) {\n          newIndex = -1;\n        } else if (folding) {\n          newIndex = index(multiDragElement, ':not(.' + _this3.options.selectedClass + ')');\n        } else {\n          newIndex = index(multiDragElement);\n        }\n\n        newIndicies.push({\n          multiDragElement: multiDragElement,\n          index: newIndex\n        });\n      });\n      return {\n        items: _toConsumableArray(multiDragElements),\n        clones: [].concat(multiDragClones),\n        oldIndicies: oldIndicies,\n        newIndicies: newIndicies\n      };\n    },\n    optionListeners: {\n      multiDragKey: function multiDragKey(key) {\n        key = key.toLowerCase();\n\n        if (key === 'ctrl') {\n          key = 'Control';\n        } else if (key.length > 1) {\n          key = key.charAt(0).toUpperCase() + key.substr(1);\n        }\n\n        return key;\n      }\n    }\n  });\n}\n\nfunction insertMultiDragElements(clonesInserted, rootEl) {\n  multiDragElements.forEach(function (multiDragElement, i) {\n    var target = rootEl.children[multiDragElement.sortableIndex + (clonesInserted ? Number(i) : 0)];\n\n    if (target) {\n      rootEl.insertBefore(multiDragElement, target);\n    } else {\n      rootEl.appendChild(multiDragElement);\n    }\n  });\n}\n/**\n * Insert multi-drag clones\n * @param  {[Boolean]} elementsInserted  Whether the multi-drag elements are inserted\n * @param  {HTMLElement} rootEl\n */\n\n\nfunction insertMultiDragClones(elementsInserted, rootEl) {\n  multiDragClones.forEach(function (clone, i) {\n    var target = rootEl.children[clone.sortableIndex + (elementsInserted ? Number(i) : 0)];\n\n    if (target) {\n      rootEl.insertBefore(clone, target);\n    } else {\n      rootEl.appendChild(clone);\n    }\n  });\n}\n\nfunction removeMultiDragElements() {\n  multiDragElements.forEach(function (multiDragElement) {\n    if (multiDragElement === dragEl$1) return;\n    multiDragElement.parentNode && multiDragElement.parentNode.removeChild(multiDragElement);\n  });\n}\n\nSortable.mount(new AutoScrollPlugin());\nSortable.mount(Remove, Revert);\n\nexport default Sortable;\nexport { MultiDragPlugin as MultiDrag, Sortable, SwapPlugin as Swap };\n", "function removeNode(node) {\r\n  if (node.parentElement !== null) {\r\n    node.parentElement.removeChild(node);\r\n  }\r\n}\r\n\r\nfunction insertNodeAt(fatherNode, node, position) {\r\n  const refNode =\r\n    position === 0\r\n      ? fatherNode.children[0]\r\n      : fatherNode.children[position - 1].nextSibling;\r\n  fatherNode.insertBefore(node, refNode);\r\n}\r\n\r\nexport { insertNodeAt, removeNode };\r\n", "function getConsole() {\r\n  if (typeof window !== \"undefined\") {\r\n    return window.console;\r\n  }\r\n  return global.console;\r\n}\r\nconst console = getConsole();\r\n\r\nexport { console };\r\n", "function cached(fn) {\r\n  const cache = Object.create(null);\r\n  return function cachedFn(str) {\r\n    const hit = cache[str];\r\n    return hit || (cache[str] = fn(str));\r\n  };\r\n}\r\n\r\nconst regex = /-(\\w)/g;\r\nconst camelize = cached(str => str.replace(regex, (_, c) => c.toUpperCase()));\r\n\r\nexport { camelize };\r\n", "const manageAndEmit = [\"Start\", \"Add\", \"Remove\", \"Update\", \"End\"];\r\nconst emit = [\"Choose\", \"Unchoose\", \"Sort\", \"Filter\", \"Clone\"];\r\nconst manage = [\"Move\"];\r\nconst eventHandlerNames = [manage, manageAndEmit, emit]\r\n  .flatMap(events => events)\r\n  .map(evt => `on${evt}`);\r\n\r\nconst events = {\r\n  manage,\r\n  manageAndEmit,\r\n  emit\r\n};\r\n\r\nfunction isReadOnly(eventName) {\r\n  return eventHandlerNames.indexOf(eventName) !== -1;\r\n}\r\n\r\nexport { events, isReadOnly };\r\n", "const tags = [\r\n  \"a\",\r\n  \"abbr\",\r\n  \"address\",\r\n  \"area\",\r\n  \"article\",\r\n  \"aside\",\r\n  \"audio\",\r\n  \"b\",\r\n  \"base\",\r\n  \"bdi\",\r\n  \"bdo\",\r\n  \"blockquote\",\r\n  \"body\",\r\n  \"br\",\r\n  \"button\",\r\n  \"canvas\",\r\n  \"caption\",\r\n  \"cite\",\r\n  \"code\",\r\n  \"col\",\r\n  \"colgroup\",\r\n  \"data\",\r\n  \"datalist\",\r\n  \"dd\",\r\n  \"del\",\r\n  \"details\",\r\n  \"dfn\",\r\n  \"dialog\",\r\n  \"div\",\r\n  \"dl\",\r\n  \"dt\",\r\n  \"em\",\r\n  \"embed\",\r\n  \"fieldset\",\r\n  \"figcaption\",\r\n  \"figure\",\r\n  \"footer\",\r\n  \"form\",\r\n  \"h1\",\r\n  \"h2\",\r\n  \"h3\",\r\n  \"h4\",\r\n  \"h5\",\r\n  \"h6\",\r\n  \"head\",\r\n  \"header\",\r\n  \"hgroup\",\r\n  \"hr\",\r\n  \"html\",\r\n  \"i\",\r\n  \"iframe\",\r\n  \"img\",\r\n  \"input\",\r\n  \"ins\",\r\n  \"kbd\",\r\n  \"label\",\r\n  \"legend\",\r\n  \"li\",\r\n  \"link\",\r\n  \"main\",\r\n  \"map\",\r\n  \"mark\",\r\n  \"math\",\r\n  \"menu\",\r\n  \"menuitem\",\r\n  \"meta\",\r\n  \"meter\",\r\n  \"nav\",\r\n  \"noscript\",\r\n  \"object\",\r\n  \"ol\",\r\n  \"optgroup\",\r\n  \"option\",\r\n  \"output\",\r\n  \"p\",\r\n  \"param\",\r\n  \"picture\",\r\n  \"pre\",\r\n  \"progress\",\r\n  \"q\",\r\n  \"rb\",\r\n  \"rp\",\r\n  \"rt\",\r\n  \"rtc\",\r\n  \"ruby\",\r\n  \"s\",\r\n  \"samp\",\r\n  \"script\",\r\n  \"section\",\r\n  \"select\",\r\n  \"slot\",\r\n  \"small\",\r\n  \"source\",\r\n  \"span\",\r\n  \"strong\",\r\n  \"style\",\r\n  \"sub\",\r\n  \"summary\",\r\n  \"sup\",\r\n  \"svg\",\r\n  \"table\",\r\n  \"tbody\",\r\n  \"td\",\r\n  \"template\",\r\n  \"textarea\",\r\n  \"tfoot\",\r\n  \"th\",\r\n  \"thead\",\r\n  \"time\",\r\n  \"title\",\r\n  \"tr\",\r\n  \"track\",\r\n  \"u\",\r\n  \"ul\",\r\n  \"var\",\r\n  \"video\",\r\n  \"wbr\"\r\n];\r\n\r\nfunction isHtmlTag(name) {\r\n  return tags.includes(name);\r\n}\r\n\r\nfunction isTransition(name) {\r\n  return [\"transition-group\", \"TransitionGroup\"].includes(name);\r\n}\r\n\r\nfunction isHtmlAttribute(value) {\r\n  return (\r\n    [\"id\", \"class\", \"role\", \"style\"].includes(value) ||\r\n    value.startsWith(\"data-\") ||\r\n    value.startsWith(\"aria-\") ||\r\n    value.startsWith(\"on\")\r\n  );\r\n}\r\n\r\nexport { isHtmlTag, isHtmlAttribute, isTransition };\r\n", "import { camelize } from \"../util/string\";\r\nimport { events, isReadOnly } from \"./sortableEvents\";\r\nimport { isHtmlAttribute } from \"../util/tags\";\r\n\r\nfunction project(entries) {\r\n  return entries.reduce((res, [key, value]) => {\r\n    res[key] = value;\r\n    return res;\r\n  }, {});\r\n}\r\n\r\nfunction getComponentAttributes({ $attrs, componentData = {} }) {\r\n  const attributes = project(\r\n    Object.entries($attrs).filter(([key, _]) => isHtmlAttribute(key))\r\n  );\r\n  return {\r\n    ...attributes,\r\n    ...componentData\r\n  };\r\n}\r\n\r\nfunction createSortableOption({ $attrs, callBackBuilder }) {\r\n  const options = project(getValidSortableEntries($attrs));\r\n  Object.entries(callBackBuilder).forEach(([eventType, eventBuilder]) => {\r\n    events[eventType].forEach(event => {\r\n      options[`on${event}`] = eventBuilder(event);\r\n    });\r\n  });\r\n  const draggable = `[data-draggable]${options.draggable || \"\"}`;\r\n  return {\r\n    ...options,\r\n    draggable\r\n  };\r\n}\r\n\r\nfunction getValidSortableEntries(value) {\r\n  return Object.entries(value)\r\n    .filter(([key, _]) => !isHtmlAttribute(key))\r\n    .map(([key, value]) => [camelize(key), value])\r\n    .filter(([key, _]) => !isReadOnly(key));\r\n}\r\n\r\nexport {\r\n  getComponentAttributes,\r\n  createSortableOption,\r\n  getValidSortableEntries\r\n};\r\n", "const getHtmlElementFromNode = ({ el }) => el;\r\nconst addContext = (domElement, context) =>\r\n  (domElement.__draggable_context = context);\r\nconst getContext = domElement => domElement.__draggable_context;\r\n\r\nclass ComponentStructure {\r\n  constructor({\r\n    nodes: { header, default: defaultNodes, footer },\r\n    root,\r\n    realList\r\n  }) {\r\n    this.defaultNodes = defaultNodes;\r\n    this.children = [...header, ...defaultNodes, ...footer];\r\n    this.externalComponent = root.externalComponent;\r\n    this.rootTransition = root.transition;\r\n    this.tag = root.tag;\r\n    this.realList = realList;\r\n  }\r\n\r\n  get _isRootComponent() {\r\n    return this.externalComponent || this.rootTransition;\r\n  }\r\n\r\n  render(h, attributes) {\r\n    const { tag, children, _isRootComponent } = this;\r\n    const option = !_isRootComponent ? children : { default: () => children };\r\n    return h(tag, attributes, option);\r\n  }\r\n\r\n  updated() {\r\n    const { defaultNodes, realList } = this;\r\n    defaultNodes.forEach((node, index) => {\r\n      addContext(getHtmlElementFromNode(node), {\r\n        element: realList[index],\r\n        index\r\n      });\r\n    });\r\n  }\r\n\r\n  getUnderlyingVm(domElement) {\r\n    return getContext(domElement);\r\n  }\r\n\r\n  getVmIndexFromDomIndex(domIndex, element) {\r\n    const { defaultNodes } = this;\r\n    const { length } = defaultNodes;\r\n    const domChildren = element.children;\r\n    const domElement = domChildren.item(domIndex);\r\n\r\n    if (domElement === null) {\r\n      return length;\r\n    }\r\n    const context = getContext(domElement);\r\n    if (context) {\r\n      return context.index;\r\n    }\r\n\r\n    if (length === 0) {\r\n      return 0;\r\n    }\r\n    const firstDomListElement = getHtmlElementFromNode(defaultNodes[0]);\r\n    const indexFirstDomListElement = [...domChildren].findIndex(\r\n      element => element === firstDomListElement\r\n    );\r\n    return domIndex < indexFirstDomListElement ? 0 : length;\r\n  }\r\n}\r\n\r\nexport { ComponentStructure };\r\n", "import { ComponentStructure } from \"./componentStructure\";\r\nimport { isHtmlTag, isTransition } from \"../util/tags\";\r\nimport { resolveComponent, TransitionGroup } from \"vue\";\r\n\r\nfunction getSlot(slots, key) {\r\n  const slotValue = slots[key];\r\n  return slotValue ? slotValue() : [];\r\n}\r\n\r\nfunction computeNodes({ $slots, realList, getKey }) {\r\n  const normalizedList = realList || [];\r\n  const [header, footer] = [\"header\", \"footer\"].map(name =>\r\n    getSlot($slots, name)\r\n  );\r\n  const { item } = $slots;\r\n  if (!item) {\r\n    throw new Error(\"draggable element must have an item slot\");\r\n  }\r\n  const defaultNodes = normalizedList.flatMap((element, index) =>\r\n    item({ element, index }).map(node => {\r\n      node.key = getKey(element);\r\n      node.props = { ...(node.props || {}), \"data-draggable\": true };\r\n      return node;\r\n    })\r\n  );\r\n  if (defaultNodes.length !== normalizedList.length) {\r\n    throw new Error(\"Item slot must have only one child\");\r\n  }\r\n  return {\r\n    header,\r\n    footer,\r\n    default: defaultNodes\r\n  };\r\n}\r\n\r\nfunction getRootInformation(tag) {\r\n  const transition = isTransition(tag);\r\n  const externalComponent = !isHtmlTag(tag) && !transition;\r\n  return {\r\n    transition,\r\n    externalComponent,\r\n    tag: externalComponent\r\n      ? resolveComponent(tag)\r\n      : transition\r\n      ? TransitionGroup\r\n      : tag\r\n  };\r\n}\r\n\r\nfunction computeComponentStructure({ $slots, tag, realList, getKey }) {\r\n  const nodes = computeNodes({ $slots, realList, getKey });\r\n  const root = getRootInformation(tag);\r\n  return new ComponentStructure({ nodes, root, realList });\r\n}\r\n\r\nexport { computeComponentStructure };\r\n", "import Sortable from \"sortablejs\";\r\nimport { insertNodeAt, removeNode } from \"./util/htmlHelper\";\r\nimport { console } from \"./util/console\";\r\nimport {\r\n  getComponentAttributes,\r\n  createSortableOption,\r\n  getValidSortableEntries\r\n} from \"./core/componentBuilderHelper\";\r\nimport { computeComponentStructure } from \"./core/renderHelper\";\r\nimport { events } from \"./core/sortableEvents\";\r\nimport { h, defineComponent, nextTick } from \"vue\";\r\n\r\nfunction emit(evtName, evtData) {\r\n  nextTick(() => this.$emit(evtName.toLowerCase(), evtData));\r\n}\r\n\r\nfunction manage(evtName) {\r\n  return (evtData, originalElement) => {\r\n    if (this.realList !== null) {\r\n      return this[`onDrag${evtName}`](evtData, originalElement);\r\n    }\r\n  };\r\n}\r\n\r\nfunction manageAndEmit(evtName) {\r\n  const delegateCallBack = manage.call(this, evtName);\r\n  return (evtData, originalElement) => {\r\n    delegateCallBack.call(this, evtData, originalElement);\r\n    emit.call(this, evtName, evtData);\r\n  };\r\n}\r\n\r\nlet draggingElement = null;\r\n\r\nconst props = {\r\n  list: {\r\n    type: Array,\r\n    required: false,\r\n    default: null\r\n  },\r\n  modelValue: {\r\n    type: Array,\r\n    required: false,\r\n    default: null\r\n  },\r\n  itemKey: {\r\n    type: [String, Function],\r\n    required: true\r\n  },\r\n  clone: {\r\n    type: Function,\r\n    default: original => {\r\n      return original;\r\n    }\r\n  },\r\n  tag: {\r\n    type: String,\r\n    default: \"div\"\r\n  },\r\n  move: {\r\n    type: Function,\r\n    default: null\r\n  },\r\n  componentData: {\r\n    type: Object,\r\n    required: false,\r\n    default: null\r\n  }\r\n};\r\n\r\nconst emits = [\r\n  \"update:modelValue\",\r\n  \"change\",\r\n  ...[...events.manageAndEmit, ...events.emit].map(evt => evt.toLowerCase())\r\n];\r\n\r\nconst draggableComponent = defineComponent({\r\n  name: \"draggable\",\r\n\r\n  inheritAttrs: false,\r\n\r\n  props,\r\n\r\n  emits,\r\n\r\n  data() {\r\n    return {\r\n      error: false\r\n    };\r\n  },\r\n\r\n  render() {\r\n    try {\r\n      this.error = false;\r\n      const { $slots, $attrs, tag, componentData, realList, getKey } = this;\r\n      const componentStructure = computeComponentStructure({\r\n        $slots,\r\n        tag,\r\n        realList,\r\n        getKey\r\n      });\r\n      this.componentStructure = componentStructure;\r\n      const attributes = getComponentAttributes({ $attrs, componentData });\r\n      return componentStructure.render(h, attributes);\r\n    } catch (err) {\r\n      this.error = true;\r\n      return h(\"pre\", { style: { color: \"red\" } }, err.stack);\r\n    }\r\n  },\r\n\r\n  created() {\r\n    if (this.list !== null && this.modelValue !== null) {\r\n      console.error(\r\n        \"modelValue and list props are mutually exclusive! Please set one or another.\"\r\n      );\r\n    }\r\n  },\r\n\r\n  mounted() {\r\n    if (this.error) {\r\n      return;\r\n    }\r\n\r\n    const { $attrs, $el, componentStructure } = this;\r\n    componentStructure.updated();\r\n\r\n    const sortableOptions = createSortableOption({\r\n      $attrs,\r\n      callBackBuilder: {\r\n        manageAndEmit: event => manageAndEmit.call(this, event),\r\n        emit: event => emit.bind(this, event),\r\n        manage: event => manage.call(this, event)\r\n      }\r\n    });\r\n    const targetDomElement = $el.nodeType === 1 ? $el : $el.parentElement;\r\n    this._sortable = new Sortable(targetDomElement, sortableOptions);\r\n    this.targetDomElement = targetDomElement;\r\n    targetDomElement.__draggable_component__ = this;\r\n  },\r\n\r\n  updated() {\r\n    this.componentStructure.updated();\r\n  },\r\n\r\n  beforeUnmount() {\r\n    if (this._sortable !== undefined) this._sortable.destroy();\r\n  },\r\n\r\n  computed: {\r\n    realList() {\r\n      const { list } = this;\r\n      return list ? list : this.modelValue;\r\n    },\r\n\r\n    getKey() {\r\n      const { itemKey } = this;\r\n      if (typeof itemKey === \"function\") {\r\n        return itemKey;\r\n      }\r\n      return element => element[itemKey];\r\n    }\r\n  },\r\n\r\n  watch: {\r\n    $attrs: {\r\n      handler(newOptionValue) {\r\n        const { _sortable } = this;\r\n        if (!_sortable) return;\r\n        getValidSortableEntries(newOptionValue).forEach(([key, value]) => {\r\n          _sortable.option(key, value);\r\n        });\r\n      },\r\n      deep: true\r\n    }\r\n  },\r\n\r\n  methods: {\r\n    getUnderlyingVm(domElement) {\r\n      return this.componentStructure.getUnderlyingVm(domElement) || null;\r\n    },\r\n\r\n    getUnderlyingPotencialDraggableComponent(htmElement) {\r\n      //TODO check case where you need to see component children\r\n      return htmElement.__draggable_component__;\r\n    },\r\n\r\n    emitChanges(evt) {\r\n      nextTick(() => this.$emit(\"change\", evt));\r\n    },\r\n\r\n    alterList(onList) {\r\n      if (this.list) {\r\n        onList(this.list);\r\n        return;\r\n      }\r\n      const newList = [...this.modelValue];\r\n      onList(newList);\r\n      this.$emit(\"update:modelValue\", newList);\r\n    },\r\n\r\n    spliceList() {\r\n      const spliceList = list => list.splice(...arguments);\r\n      this.alterList(spliceList);\r\n    },\r\n\r\n    updatePosition(oldIndex, newIndex) {\r\n      const updatePosition = list =>\r\n        list.splice(newIndex, 0, list.splice(oldIndex, 1)[0]);\r\n      this.alterList(updatePosition);\r\n    },\r\n\r\n    getRelatedContextFromMoveEvent({ to, related }) {\r\n      const component = this.getUnderlyingPotencialDraggableComponent(to);\r\n      if (!component) {\r\n        return { component };\r\n      }\r\n      const list = component.realList;\r\n      const context = { list, component };\r\n      if (to !== related && list) {\r\n        const destination = component.getUnderlyingVm(related) || {};\r\n        return { ...destination, ...context };\r\n      }\r\n      return context;\r\n    },\r\n\r\n    getVmIndexFromDomIndex(domIndex) {\r\n      return this.componentStructure.getVmIndexFromDomIndex(\r\n        domIndex,\r\n        this.targetDomElement\r\n      );\r\n    },\r\n\r\n    onDragStart(evt) {\r\n      this.context = this.getUnderlyingVm(evt.item);\r\n      evt.item._underlying_vm_ = this.clone(this.context.element);\r\n      draggingElement = evt.item;\r\n    },\r\n\r\n    onDragAdd(evt) {\r\n      const element = evt.item._underlying_vm_;\r\n      if (element === undefined) {\r\n        return;\r\n      }\r\n      removeNode(evt.item);\r\n      const newIndex = this.getVmIndexFromDomIndex(evt.newIndex);\r\n      this.spliceList(newIndex, 0, element);\r\n      const added = { element, newIndex };\r\n      this.emitChanges({ added });\r\n    },\r\n\r\n    onDragRemove(evt) {\r\n      insertNodeAt(this.$el, evt.item, evt.oldIndex);\r\n      if (evt.pullMode === \"clone\") {\r\n        removeNode(evt.clone);\r\n        return;\r\n      }\r\n      const { index: oldIndex, element } = this.context;\r\n      this.spliceList(oldIndex, 1);\r\n      const removed = { element, oldIndex };\r\n      this.emitChanges({ removed });\r\n    },\r\n\r\n    onDragUpdate(evt) {\r\n      removeNode(evt.item);\r\n      insertNodeAt(evt.from, evt.item, evt.oldIndex);\r\n      const oldIndex = this.context.index;\r\n      const newIndex = this.getVmIndexFromDomIndex(evt.newIndex);\r\n      this.updatePosition(oldIndex, newIndex);\r\n      const moved = { element: this.context.element, oldIndex, newIndex };\r\n      this.emitChanges({ moved });\r\n    },\r\n\r\n    computeFutureIndex(relatedContext, evt) {\r\n      if (!relatedContext.element) {\r\n        return 0;\r\n      }\r\n      const domChildren = [...evt.to.children].filter(\r\n        el => el.style[\"display\"] !== \"none\"\r\n      );\r\n      const currentDomIndex = domChildren.indexOf(evt.related);\r\n      const currentIndex = relatedContext.component.getVmIndexFromDomIndex(\r\n        currentDomIndex\r\n      );\r\n      const draggedInList = domChildren.indexOf(draggingElement) !== -1;\r\n      return draggedInList || !evt.willInsertAfter\r\n        ? currentIndex\r\n        : currentIndex + 1;\r\n    },\r\n\r\n    onDragMove(evt, originalEvent) {\r\n      const { move, realList } = this;\r\n      if (!move || !realList) {\r\n        return true;\r\n      }\r\n\r\n      const relatedContext = this.getRelatedContextFromMoveEvent(evt);\r\n      const futureIndex = this.computeFutureIndex(relatedContext, evt);\r\n      const draggedContext = {\r\n        ...this.context,\r\n        futureIndex\r\n      };\r\n      const sendEvent = {\r\n        ...evt,\r\n        relatedContext,\r\n        draggedContext\r\n      };\r\n      return move(sendEvent, originalEvent);\r\n    },\r\n\r\n    onDragEnd() {\r\n      draggingElement = null;\r\n    }\r\n  }\r\n});\r\n\r\nexport default draggableComponent;\r\n"], "mappings": ";;;;;;;;;;AAMA,SAAS,QAAQ,QAAQ,gBAAgB;AACvC,MAAI,OAAO,OAAO,KAAK,MAAM;AAE7B,MAAI,OAAO,uBAAuB;AAChC,QAAI,UAAU,OAAO,sBAAsB,MAAM;AAEjD,QAAI,gBAAgB;AAClB,gBAAU,QAAQ,OAAO,SAAU,KAAK;AACtC,eAAO,OAAO,yBAAyB,QAAQ,GAAG,EAAE;AAAA,MACtD,CAAC;AAAA,IACH;AAEA,SAAK,KAAK,MAAM,MAAM,OAAO;AAAA,EAC/B;AAEA,SAAO;AACT;AAEA,SAAS,eAAe,QAAQ;AAC9B,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,QAAI,SAAS,UAAU,CAAC,KAAK,OAAO,UAAU,CAAC,IAAI,CAAC;AAEpD,QAAI,IAAI,GAAG;AACT,cAAQ,OAAO,MAAM,GAAG,IAAI,EAAE,QAAQ,SAAU,KAAK;AACnD,wBAAgB,QAAQ,KAAK,OAAO,GAAG,CAAC;AAAA,MAC1C,CAAC;AAAA,IACH,WAAW,OAAO,2BAA2B;AAC3C,aAAO,iBAAiB,QAAQ,OAAO,0BAA0B,MAAM,CAAC;AAAA,IAC1E,OAAO;AACL,cAAQ,OAAO,MAAM,CAAC,EAAE,QAAQ,SAAU,KAAK;AAC7C,eAAO,eAAe,QAAQ,KAAK,OAAO,yBAAyB,QAAQ,GAAG,CAAC;AAAA,MACjF,CAAC;AAAA,IACH;AAAA,EACF;AAEA,SAAO;AACT;AAEA,SAAS,QAAQ,KAAK;AACpB;AAEA,MAAI,OAAO,WAAW,cAAc,OAAO,OAAO,aAAa,UAAU;AACvE,cAAU,SAAUA,MAAK;AACvB,aAAO,OAAOA;AAAA,IAChB;AAAA,EACF,OAAO;AACL,cAAU,SAAUA,MAAK;AACvB,aAAOA,QAAO,OAAO,WAAW,cAAcA,KAAI,gBAAgB,UAAUA,SAAQ,OAAO,YAAY,WAAW,OAAOA;AAAA,IAC3H;AAAA,EACF;AAEA,SAAO,QAAQ,GAAG;AACpB;AAEA,SAAS,gBAAgB,KAAK,KAAK,OAAO;AACxC,MAAI,OAAO,KAAK;AACd,WAAO,eAAe,KAAK,KAAK;AAAA,MAC9B;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,OAAO;AACL,QAAI,GAAG,IAAI;AAAA,EACb;AAEA,SAAO;AACT;AAEA,SAAS,WAAW;AAClB,aAAW,OAAO,UAAU,SAAU,QAAQ;AAC5C,aAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,UAAI,SAAS,UAAU,CAAC;AAExB,eAAS,OAAO,QAAQ;AACtB,YAAI,OAAO,UAAU,eAAe,KAAK,QAAQ,GAAG,GAAG;AACrD,iBAAO,GAAG,IAAI,OAAO,GAAG;AAAA,QAC1B;AAAA,MACF;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAEA,SAAO,SAAS,MAAM,MAAM,SAAS;AACvC;AAEA,SAAS,8BAA8B,QAAQ,UAAU;AACvD,MAAI,UAAU;AAAM,WAAO,CAAC;AAC5B,MAAI,SAAS,CAAC;AACd,MAAI,aAAa,OAAO,KAAK,MAAM;AACnC,MAAI,KAAK;AAET,OAAK,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AACtC,UAAM,WAAW,CAAC;AAClB,QAAI,SAAS,QAAQ,GAAG,KAAK;AAAG;AAChC,WAAO,GAAG,IAAI,OAAO,GAAG;AAAA,EAC1B;AAEA,SAAO;AACT;AAEA,SAAS,yBAAyB,QAAQ,UAAU;AAClD,MAAI,UAAU;AAAM,WAAO,CAAC;AAE5B,MAAI,SAAS,8BAA8B,QAAQ,QAAQ;AAE3D,MAAI,KAAK;AAET,MAAI,OAAO,uBAAuB;AAChC,QAAI,mBAAmB,OAAO,sBAAsB,MAAM;AAE1D,SAAK,IAAI,GAAG,IAAI,iBAAiB,QAAQ,KAAK;AAC5C,YAAM,iBAAiB,CAAC;AACxB,UAAI,SAAS,QAAQ,GAAG,KAAK;AAAG;AAChC,UAAI,CAAC,OAAO,UAAU,qBAAqB,KAAK,QAAQ,GAAG;AAAG;AAC9D,aAAO,GAAG,IAAI,OAAO,GAAG;AAAA,IAC1B;AAAA,EACF;AAEA,SAAO;AACT;AAmCA,IAAI,UAAU;AAEd,SAAS,UAAU,SAAS;AAC1B,MAAI,OAAO,WAAW,eAAe,OAAO,WAAW;AACrD,WAAO,CAAC,CAAe,UAAU,UAAU,MAAM,OAAO;AAAA,EAC1D;AACF;AAEA,IAAI,aAAa,UAAU,uDAAuD;AAClF,IAAI,OAAO,UAAU,OAAO;AAC5B,IAAI,UAAU,UAAU,UAAU;AAClC,IAAI,SAAS,UAAU,SAAS,KAAK,CAAC,UAAU,SAAS,KAAK,CAAC,UAAU,UAAU;AACnF,IAAI,MAAM,UAAU,iBAAiB;AACrC,IAAI,mBAAmB,UAAU,SAAS,KAAK,UAAU,UAAU;AAEnE,IAAI,cAAc;AAAA,EAChB,SAAS;AAAA,EACT,SAAS;AACX;AAEA,SAAS,GAAG,IAAI,OAAO,IAAI;AACzB,KAAG,iBAAiB,OAAO,IAAI,CAAC,cAAc,WAAW;AAC3D;AAEA,SAAS,IAAI,IAAI,OAAO,IAAI;AAC1B,KAAG,oBAAoB,OAAO,IAAI,CAAC,cAAc,WAAW;AAC9D;AAEA,SAAS,QAET,IAEA,UAAU;AACR,MAAI,CAAC;AAAU;AACf,WAAS,CAAC,MAAM,QAAQ,WAAW,SAAS,UAAU,CAAC;AAEvD,MAAI,IAAI;AACN,QAAI;AACF,UAAI,GAAG,SAAS;AACd,eAAO,GAAG,QAAQ,QAAQ;AAAA,MAC5B,WAAW,GAAG,mBAAmB;AAC/B,eAAO,GAAG,kBAAkB,QAAQ;AAAA,MACtC,WAAW,GAAG,uBAAuB;AACnC,eAAO,GAAG,sBAAsB,QAAQ;AAAA,MAC1C;AAAA,IACF,SAAS,GAAG;AACV,aAAO;AAAA,IACT;AAAA,EACF;AAEA,SAAO;AACT;AAEA,SAAS,gBAAgB,IAAI;AAC3B,SAAO,GAAG,QAAQ,OAAO,YAAY,GAAG,KAAK,WAAW,GAAG,OAAO,GAAG;AACvE;AAEA,SAAS,QAET,IAEA,UAEA,KAAK,YAAY;AACf,MAAI,IAAI;AACN,UAAM,OAAO;AAEb,OAAG;AACD,UAAI,YAAY,SAAS,SAAS,CAAC,MAAM,MAAM,GAAG,eAAe,OAAO,QAAQ,IAAI,QAAQ,IAAI,QAAQ,IAAI,QAAQ,MAAM,cAAc,OAAO,KAAK;AAClJ,eAAO;AAAA,MACT;AAEA,UAAI,OAAO;AAAK;AAAA,IAElB,SAAS,KAAK,gBAAgB,EAAE;AAAA,EAClC;AAEA,SAAO;AACT;AAEA,IAAI,UAAU;AAEd,SAAS,YAAY,IAAI,MAAM,OAAO;AACpC,MAAI,MAAM,MAAM;AACd,QAAI,GAAG,WAAW;AAChB,SAAG,UAAU,QAAQ,QAAQ,QAAQ,EAAE,IAAI;AAAA,IAC7C,OAAO;AACL,UAAI,aAAa,MAAM,GAAG,YAAY,KAAK,QAAQ,SAAS,GAAG,EAAE,QAAQ,MAAM,OAAO,KAAK,GAAG;AAC9F,SAAG,aAAa,aAAa,QAAQ,MAAM,OAAO,KAAK,QAAQ,SAAS,GAAG;AAAA,IAC7E;AAAA,EACF;AACF;AAEA,SAAS,IAAI,IAAI,MAAM,KAAK;AAC1B,MAAI,QAAQ,MAAM,GAAG;AAErB,MAAI,OAAO;AACT,QAAI,QAAQ,QAAQ;AAClB,UAAI,SAAS,eAAe,SAAS,YAAY,kBAAkB;AACjE,cAAM,SAAS,YAAY,iBAAiB,IAAI,EAAE;AAAA,MACpD,WAAW,GAAG,cAAc;AAC1B,cAAM,GAAG;AAAA,MACX;AAEA,aAAO,SAAS,SAAS,MAAM,IAAI,IAAI;AAAA,IACzC,OAAO;AACL,UAAI,EAAE,QAAQ,UAAU,KAAK,QAAQ,QAAQ,MAAM,IAAI;AACrD,eAAO,aAAa;AAAA,MACtB;AAEA,YAAM,IAAI,IAAI,OAAO,OAAO,QAAQ,WAAW,KAAK;AAAA,IACtD;AAAA,EACF;AACF;AAEA,SAAS,OAAO,IAAI,UAAU;AAC5B,MAAI,oBAAoB;AAExB,MAAI,OAAO,OAAO,UAAU;AAC1B,wBAAoB;AAAA,EACtB,OAAO;AACL,OAAG;AACD,UAAI,YAAY,IAAI,IAAI,WAAW;AAEnC,UAAI,aAAa,cAAc,QAAQ;AACrC,4BAAoB,YAAY,MAAM;AAAA,MACxC;AAAA,IAGF,SAAS,CAAC,aAAa,KAAK,GAAG;AAAA,EACjC;AAEA,MAAI,WAAW,OAAO,aAAa,OAAO,mBAAmB,OAAO,aAAa,OAAO;AAGxF,SAAO,YAAY,IAAI,SAAS,iBAAiB;AACnD;AAEA,SAAS,KAAK,KAAK,SAAS,UAAU;AACpC,MAAI,KAAK;AACP,QAAI,OAAO,IAAI,qBAAqB,OAAO,GACvC,IAAI,GACJ,IAAI,KAAK;AAEb,QAAI,UAAU;AACZ,aAAO,IAAI,GAAG,KAAK;AACjB,iBAAS,KAAK,CAAC,GAAG,CAAC;AAAA,MACrB;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAEA,SAAO,CAAC;AACV;AAEA,SAAS,4BAA4B;AACnC,MAAI,mBAAmB,SAAS;AAEhC,MAAI,kBAAkB;AACpB,WAAO;AAAA,EACT,OAAO;AACL,WAAO,SAAS;AAAA,EAClB;AACF;AAYA,SAAS,QAAQ,IAAI,2BAA2B,2BAA2B,WAAW,WAAW;AAC/F,MAAI,CAAC,GAAG,yBAAyB,OAAO;AAAQ;AAChD,MAAI,QAAQ,KAAK,MAAM,QAAQ,OAAO,QAAQ;AAE9C,MAAI,OAAO,UAAU,GAAG,cAAc,OAAO,0BAA0B,GAAG;AACxE,aAAS,GAAG,sBAAsB;AAClC,UAAM,OAAO;AACb,WAAO,OAAO;AACd,aAAS,OAAO;AAChB,YAAQ,OAAO;AACf,aAAS,OAAO;AAChB,YAAQ,OAAO;AAAA,EACjB,OAAO;AACL,UAAM;AACN,WAAO;AACP,aAAS,OAAO;AAChB,YAAQ,OAAO;AACf,aAAS,OAAO;AAChB,YAAQ,OAAO;AAAA,EACjB;AAEA,OAAK,6BAA6B,8BAA8B,OAAO,QAAQ;AAE7E,gBAAY,aAAa,GAAG;AAG5B,QAAI,CAAC,YAAY;AACf,SAAG;AACD,YAAI,aAAa,UAAU,0BAA0B,IAAI,WAAW,WAAW,MAAM,UAAU,6BAA6B,IAAI,WAAW,UAAU,MAAM,WAAW;AACpK,cAAI,gBAAgB,UAAU,sBAAsB;AAEpD,iBAAO,cAAc,MAAM,SAAS,IAAI,WAAW,kBAAkB,CAAC;AACtE,kBAAQ,cAAc,OAAO,SAAS,IAAI,WAAW,mBAAmB,CAAC;AACzE,mBAAS,MAAM,OAAO;AACtB,kBAAQ,OAAO,OAAO;AACtB;AAAA,QACF;AAAA,MAGF,SAAS,YAAY,UAAU;AAAA,IACjC;AAAA,EACF;AAEA,MAAI,aAAa,OAAO,QAAQ;AAE9B,QAAI,WAAW,OAAO,aAAa,EAAE,GACjC,SAAS,YAAY,SAAS,GAC9B,SAAS,YAAY,SAAS;AAElC,QAAI,UAAU;AACZ,aAAO;AACP,cAAQ;AACR,eAAS;AACT,gBAAU;AACV,eAAS,MAAM;AACf,cAAQ,OAAO;AAAA,IACjB;AAAA,EACF;AAEA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAUA,SAAS,eAAe,IAAI,QAAQ,YAAY;AAC9C,MAAI,SAAS,2BAA2B,IAAI,IAAI,GAC5C,YAAY,QAAQ,EAAE,EAAE,MAAM;AAGlC,SAAO,QAAQ;AACb,QAAI,gBAAgB,QAAQ,MAAM,EAAE,UAAU,GAC1C,UAAU;AAEd,QAAI,eAAe,SAAS,eAAe,QAAQ;AACjD,gBAAU,aAAa;AAAA,IACzB,OAAO;AACL,gBAAU,aAAa;AAAA,IACzB;AAEA,QAAI,CAAC;AAAS,aAAO;AACrB,QAAI,WAAW,0BAA0B;AAAG;AAC5C,aAAS,2BAA2B,QAAQ,KAAK;AAAA,EACnD;AAEA,SAAO;AACT;AAWA,SAAS,SAAS,IAAI,UAAU,SAAS,eAAe;AACtD,MAAI,eAAe,GACf,IAAI,GACJ,WAAW,GAAG;AAElB,SAAO,IAAI,SAAS,QAAQ;AAC1B,QAAI,SAAS,CAAC,EAAE,MAAM,YAAY,UAAU,SAAS,CAAC,MAAM,SAAS,UAAU,iBAAiB,SAAS,CAAC,MAAM,SAAS,YAAY,QAAQ,SAAS,CAAC,GAAG,QAAQ,WAAW,IAAI,KAAK,GAAG;AACvL,UAAI,iBAAiB,UAAU;AAC7B,eAAO,SAAS,CAAC;AAAA,MACnB;AAEA;AAAA,IACF;AAEA;AAAA,EACF;AAEA,SAAO;AACT;AASA,SAAS,UAAU,IAAI,UAAU;AAC/B,MAAI,OAAO,GAAG;AAEd,SAAO,SAAS,SAAS,SAAS,SAAS,IAAI,MAAM,SAAS,MAAM,UAAU,YAAY,CAAC,QAAQ,MAAM,QAAQ,IAAI;AACnH,WAAO,KAAK;AAAA,EACd;AAEA,SAAO,QAAQ;AACjB;AAUA,SAAS,MAAM,IAAI,UAAU;AAC3B,MAAIC,SAAQ;AAEZ,MAAI,CAAC,MAAM,CAAC,GAAG,YAAY;AACzB,WAAO;AAAA,EACT;AAIA,SAAO,KAAK,GAAG,wBAAwB;AACrC,QAAI,GAAG,SAAS,YAAY,MAAM,cAAc,OAAO,SAAS,UAAU,CAAC,YAAY,QAAQ,IAAI,QAAQ,IAAI;AAC7G,MAAAA;AAAA,IACF;AAAA,EACF;AAEA,SAAOA;AACT;AASA,SAAS,wBAAwB,IAAI;AACnC,MAAI,aAAa,GACb,YAAY,GACZ,cAAc,0BAA0B;AAE5C,MAAI,IAAI;AACN,OAAG;AACD,UAAI,WAAW,OAAO,EAAE,GACpB,SAAS,SAAS,GAClB,SAAS,SAAS;AACtB,oBAAc,GAAG,aAAa;AAC9B,mBAAa,GAAG,YAAY;AAAA,IAC9B,SAAS,OAAO,gBAAgB,KAAK,GAAG;AAAA,EAC1C;AAEA,SAAO,CAAC,YAAY,SAAS;AAC/B;AASA,SAAS,cAAc,KAAK,KAAK;AAC/B,WAAS,KAAK,KAAK;AACjB,QAAI,CAAC,IAAI,eAAe,CAAC;AAAG;AAE5B,aAAS,OAAO,KAAK;AACnB,UAAI,IAAI,eAAe,GAAG,KAAK,IAAI,GAAG,MAAM,IAAI,CAAC,EAAE,GAAG;AAAG,eAAO,OAAO,CAAC;AAAA,IAC1E;AAAA,EACF;AAEA,SAAO;AACT;AAEA,SAAS,2BAA2B,IAAI,aAAa;AAEnD,MAAI,CAAC,MAAM,CAAC,GAAG;AAAuB,WAAO,0BAA0B;AACvE,MAAI,OAAO;AACX,MAAI,UAAU;AAEd,KAAG;AAED,QAAI,KAAK,cAAc,KAAK,eAAe,KAAK,eAAe,KAAK,cAAc;AAChF,UAAI,UAAU,IAAI,IAAI;AAEtB,UAAI,KAAK,cAAc,KAAK,gBAAgB,QAAQ,aAAa,UAAU,QAAQ,aAAa,aAAa,KAAK,eAAe,KAAK,iBAAiB,QAAQ,aAAa,UAAU,QAAQ,aAAa,WAAW;AACpN,YAAI,CAAC,KAAK,yBAAyB,SAAS,SAAS;AAAM,iBAAO,0BAA0B;AAC5F,YAAI,WAAW;AAAa,iBAAO;AACnC,kBAAU;AAAA,MACZ;AAAA,IACF;AAAA,EAGF,SAAS,OAAO,KAAK;AAErB,SAAO,0BAA0B;AACnC;AAEA,SAAS,OAAO,KAAK,KAAK;AACxB,MAAI,OAAO,KAAK;AACd,aAAS,OAAO,KAAK;AACnB,UAAI,IAAI,eAAe,GAAG,GAAG;AAC3B,YAAI,GAAG,IAAI,IAAI,GAAG;AAAA,MACpB;AAAA,IACF;AAAA,EACF;AAEA,SAAO;AACT;AAEA,SAAS,YAAY,OAAO,OAAO;AACjC,SAAO,KAAK,MAAM,MAAM,GAAG,MAAM,KAAK,MAAM,MAAM,GAAG,KAAK,KAAK,MAAM,MAAM,IAAI,MAAM,KAAK,MAAM,MAAM,IAAI,KAAK,KAAK,MAAM,MAAM,MAAM,MAAM,KAAK,MAAM,MAAM,MAAM,KAAK,KAAK,MAAM,MAAM,KAAK,MAAM,KAAK,MAAM,MAAM,KAAK;AAC5N;AAEA,IAAI;AAEJ,SAAS,SAAS,UAAU,IAAI;AAC9B,SAAO,WAAY;AACjB,QAAI,CAAC,kBAAkB;AACrB,UAAI,OAAO,WACP,QAAQ;AAEZ,UAAI,KAAK,WAAW,GAAG;AACrB,iBAAS,KAAK,OAAO,KAAK,CAAC,CAAC;AAAA,MAC9B,OAAO;AACL,iBAAS,MAAM,OAAO,IAAI;AAAA,MAC5B;AAEA,yBAAmB,WAAW,WAAY;AACxC,2BAAmB;AAAA,MACrB,GAAG,EAAE;AAAA,IACP;AAAA,EACF;AACF;AAEA,SAAS,iBAAiB;AACxB,eAAa,gBAAgB;AAC7B,qBAAmB;AACrB;AAEA,SAAS,SAAS,IAAI,GAAG,GAAG;AAC1B,KAAG,cAAc;AACjB,KAAG,aAAa;AAClB;AAEA,SAAS,MAAM,IAAI;AACjB,MAAI,UAAU,OAAO;AACrB,MAAI,IAAI,OAAO,UAAU,OAAO;AAEhC,MAAI,WAAW,QAAQ,KAAK;AAC1B,WAAO,QAAQ,IAAI,EAAE,EAAE,UAAU,IAAI;AAAA,EACvC,WAAW,GAAG;AACZ,WAAO,EAAE,EAAE,EAAE,MAAM,IAAI,EAAE,CAAC;AAAA,EAC5B,OAAO;AACL,WAAO,GAAG,UAAU,IAAI;AAAA,EAC1B;AACF;AAkBA,IAAI,UAAU,cAAa,oBAAI,KAAK,GAAE,QAAQ;AAE9C,SAAS,wBAAwB;AAC/B,MAAI,kBAAkB,CAAC,GACnB;AACJ,SAAO;AAAA,IACL,uBAAuB,SAAS,wBAAwB;AACtD,wBAAkB,CAAC;AACnB,UAAI,CAAC,KAAK,QAAQ;AAAW;AAC7B,UAAI,WAAW,CAAC,EAAE,MAAM,KAAK,KAAK,GAAG,QAAQ;AAC7C,eAAS,QAAQ,SAAU,OAAO;AAChC,YAAI,IAAI,OAAO,SAAS,MAAM,UAAU,UAAU,SAAS;AAAO;AAClE,wBAAgB,KAAK;AAAA,UACnB,QAAQ;AAAA,UACR,MAAM,QAAQ,KAAK;AAAA,QACrB,CAAC;AAED,YAAI,WAAW,eAAe,CAAC,GAAG,gBAAgB,gBAAgB,SAAS,CAAC,EAAE,IAAI;AAGlF,YAAI,MAAM,uBAAuB;AAC/B,cAAI,cAAc,OAAO,OAAO,IAAI;AAEpC,cAAI,aAAa;AACf,qBAAS,OAAO,YAAY;AAC5B,qBAAS,QAAQ,YAAY;AAAA,UAC/B;AAAA,QACF;AAEA,cAAM,WAAW;AAAA,MACnB,CAAC;AAAA,IACH;AAAA,IACA,mBAAmB,SAAS,kBAAkB,OAAO;AACnD,sBAAgB,KAAK,KAAK;AAAA,IAC5B;AAAA,IACA,sBAAsB,SAAS,qBAAqB,QAAQ;AAC1D,sBAAgB,OAAO,cAAc,iBAAiB;AAAA,QACpD;AAAA,MACF,CAAC,GAAG,CAAC;AAAA,IACP;AAAA,IACA,YAAY,SAAS,WAAW,UAAU;AACxC,UAAI,QAAQ;AAEZ,UAAI,CAAC,KAAK,QAAQ,WAAW;AAC3B,qBAAa,mBAAmB;AAChC,YAAI,OAAO,aAAa;AAAY,mBAAS;AAC7C;AAAA,MACF;AAEA,UAAI,YAAY,OACZ,gBAAgB;AACpB,sBAAgB,QAAQ,SAAU,OAAO;AACvC,YAAI,OAAO,GACP,SAAS,MAAM,QACf,WAAW,OAAO,UAClB,SAAS,QAAQ,MAAM,GACvB,eAAe,OAAO,cACtB,aAAa,OAAO,YACpB,gBAAgB,MAAM,MACtB,eAAe,OAAO,QAAQ,IAAI;AAEtC,YAAI,cAAc;AAEhB,iBAAO,OAAO,aAAa;AAC3B,iBAAO,QAAQ,aAAa;AAAA,QAC9B;AAEA,eAAO,SAAS;AAEhB,YAAI,OAAO,uBAAuB;AAEhC,cAAI,YAAY,cAAc,MAAM,KAAK,CAAC,YAAY,UAAU,MAAM;AAAA,WACrE,cAAc,MAAM,OAAO,QAAQ,cAAc,OAAO,OAAO,WAAW,SAAS,MAAM,OAAO,QAAQ,SAAS,OAAO,OAAO,OAAO;AAErI,mBAAO,kBAAkB,eAAe,cAAc,YAAY,MAAM,OAAO;AAAA,UACjF;AAAA,QACF;AAGA,YAAI,CAAC,YAAY,QAAQ,QAAQ,GAAG;AAClC,iBAAO,eAAe;AACtB,iBAAO,aAAa;AAEpB,cAAI,CAAC,MAAM;AACT,mBAAO,MAAM,QAAQ;AAAA,UACvB;AAEA,gBAAM,QAAQ,QAAQ,eAAe,QAAQ,IAAI;AAAA,QACnD;AAEA,YAAI,MAAM;AACR,sBAAY;AACZ,0BAAgB,KAAK,IAAI,eAAe,IAAI;AAC5C,uBAAa,OAAO,mBAAmB;AACvC,iBAAO,sBAAsB,WAAW,WAAY;AAClD,mBAAO,gBAAgB;AACvB,mBAAO,eAAe;AACtB,mBAAO,WAAW;AAClB,mBAAO,aAAa;AACpB,mBAAO,wBAAwB;AAAA,UACjC,GAAG,IAAI;AACP,iBAAO,wBAAwB;AAAA,QACjC;AAAA,MACF,CAAC;AACD,mBAAa,mBAAmB;AAEhC,UAAI,CAAC,WAAW;AACd,YAAI,OAAO,aAAa;AAAY,mBAAS;AAAA,MAC/C,OAAO;AACL,8BAAsB,WAAW,WAAY;AAC3C,cAAI,OAAO,aAAa;AAAY,qBAAS;AAAA,QAC/C,GAAG,aAAa;AAAA,MAClB;AAEA,wBAAkB,CAAC;AAAA,IACrB;AAAA,IACA,SAAS,SAAS,QAAQ,QAAQ,aAAa,QAAQ,UAAU;AAC/D,UAAI,UAAU;AACZ,YAAI,QAAQ,cAAc,EAAE;AAC5B,YAAI,QAAQ,aAAa,EAAE;AAC3B,YAAI,WAAW,OAAO,KAAK,EAAE,GACzB,SAAS,YAAY,SAAS,GAC9B,SAAS,YAAY,SAAS,GAC9B,cAAc,YAAY,OAAO,OAAO,SAAS,UAAU,IAC3D,cAAc,YAAY,MAAM,OAAO,QAAQ,UAAU;AAC7D,eAAO,aAAa,CAAC,CAAC;AACtB,eAAO,aAAa,CAAC,CAAC;AACtB,YAAI,QAAQ,aAAa,iBAAiB,aAAa,QAAQ,aAAa,OAAO;AACnF,aAAK,kBAAkB,QAAQ,MAAM;AAErC,YAAI,QAAQ,cAAc,eAAe,WAAW,QAAQ,KAAK,QAAQ,SAAS,MAAM,KAAK,QAAQ,SAAS,GAAG;AACjH,YAAI,QAAQ,aAAa,oBAAoB;AAC7C,eAAO,OAAO,aAAa,YAAY,aAAa,OAAO,QAAQ;AACnE,eAAO,WAAW,WAAW,WAAY;AACvC,cAAI,QAAQ,cAAc,EAAE;AAC5B,cAAI,QAAQ,aAAa,EAAE;AAC3B,iBAAO,WAAW;AAClB,iBAAO,aAAa;AACpB,iBAAO,aAAa;AAAA,QACtB,GAAG,QAAQ;AAAA,MACb;AAAA,IACF;AAAA,EACF;AACF;AAEA,SAAS,QAAQ,QAAQ;AACvB,SAAO,OAAO;AAChB;AAEA,SAAS,kBAAkB,eAAe,UAAU,QAAQ,SAAS;AACnE,SAAO,KAAK,KAAK,KAAK,IAAI,SAAS,MAAM,cAAc,KAAK,CAAC,IAAI,KAAK,IAAI,SAAS,OAAO,cAAc,MAAM,CAAC,CAAC,IAAI,KAAK,KAAK,KAAK,IAAI,SAAS,MAAM,OAAO,KAAK,CAAC,IAAI,KAAK,IAAI,SAAS,OAAO,OAAO,MAAM,CAAC,CAAC,IAAI,QAAQ;AAC7N;AAEA,IAAI,UAAU,CAAC;AACf,IAAI,WAAW;AAAA,EACb,qBAAqB;AACvB;AACA,IAAI,gBAAgB;AAAA,EAClB,OAAO,SAAS,MAAM,QAAQ;AAE5B,aAASC,WAAU,UAAU;AAC3B,UAAI,SAAS,eAAeA,OAAM,KAAK,EAAEA,WAAU,SAAS;AAC1D,eAAOA,OAAM,IAAI,SAASA,OAAM;AAAA,MAClC;AAAA,IACF;AAEA,YAAQ,QAAQ,SAAU,GAAG;AAC3B,UAAI,EAAE,eAAe,OAAO,YAAY;AACtC,cAAM,iCAAiC,OAAO,OAAO,YAAY,iBAAiB;AAAA,MACpF;AAAA,IACF,CAAC;AACD,YAAQ,KAAK,MAAM;AAAA,EACrB;AAAA,EACA,aAAa,SAAS,YAAY,WAAW,UAAU,KAAK;AAC1D,QAAI,QAAQ;AAEZ,SAAK,gBAAgB;AAErB,QAAI,SAAS,WAAY;AACvB,YAAM,gBAAgB;AAAA,IACxB;AAEA,QAAI,kBAAkB,YAAY;AAClC,YAAQ,QAAQ,SAAU,QAAQ;AAChC,UAAI,CAAC,SAAS,OAAO,UAAU;AAAG;AAElC,UAAI,SAAS,OAAO,UAAU,EAAE,eAAe,GAAG;AAChD,iBAAS,OAAO,UAAU,EAAE,eAAe,EAAE,eAAe;AAAA,UAC1D;AAAA,QACF,GAAG,GAAG,CAAC;AAAA,MACT;AAIA,UAAI,SAAS,QAAQ,OAAO,UAAU,KAAK,SAAS,OAAO,UAAU,EAAE,SAAS,GAAG;AACjF,iBAAS,OAAO,UAAU,EAAE,SAAS,EAAE,eAAe;AAAA,UACpD;AAAA,QACF,GAAG,GAAG,CAAC;AAAA,MACT;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,mBAAmB,SAAS,kBAAkB,UAAU,IAAIC,WAAU,SAAS;AAC7E,YAAQ,QAAQ,SAAU,QAAQ;AAChC,UAAI,aAAa,OAAO;AACxB,UAAI,CAAC,SAAS,QAAQ,UAAU,KAAK,CAAC,OAAO;AAAqB;AAClE,UAAI,cAAc,IAAI,OAAO,UAAU,IAAI,SAAS,OAAO;AAC3D,kBAAY,WAAW;AACvB,kBAAY,UAAU,SAAS;AAC/B,eAAS,UAAU,IAAI;AAEvB,eAASA,WAAU,YAAY,QAAQ;AAAA,IACzC,CAAC;AAED,aAASD,WAAU,SAAS,SAAS;AACnC,UAAI,CAAC,SAAS,QAAQ,eAAeA,OAAM;AAAG;AAC9C,UAAI,WAAW,KAAK,aAAa,UAAUA,SAAQ,SAAS,QAAQA,OAAM,CAAC;AAE3E,UAAI,OAAO,aAAa,aAAa;AACnC,iBAAS,QAAQA,OAAM,IAAI;AAAA,MAC7B;AAAA,IACF;AAAA,EACF;AAAA,EACA,oBAAoB,SAAS,mBAAmB,MAAM,UAAU;AAC9D,QAAI,kBAAkB,CAAC;AACvB,YAAQ,QAAQ,SAAU,QAAQ;AAChC,UAAI,OAAO,OAAO,oBAAoB;AAAY;AAElD,eAAS,iBAAiB,OAAO,gBAAgB,KAAK,SAAS,OAAO,UAAU,GAAG,IAAI,CAAC;AAAA,IAC1F,CAAC;AACD,WAAO;AAAA,EACT;AAAA,EACA,cAAc,SAAS,aAAa,UAAU,MAAM,OAAO;AACzD,QAAI;AACJ,YAAQ,QAAQ,SAAU,QAAQ;AAEhC,UAAI,CAAC,SAAS,OAAO,UAAU;AAAG;AAElC,UAAI,OAAO,mBAAmB,OAAO,OAAO,gBAAgB,IAAI,MAAM,YAAY;AAChF,wBAAgB,OAAO,gBAAgB,IAAI,EAAE,KAAK,SAAS,OAAO,UAAU,GAAG,KAAK;AAAA,MACtF;AAAA,IACF,CAAC;AACD,WAAO;AAAA,EACT;AACF;AAEA,SAAS,cAAc,MAAM;AAC3B,MAAI,WAAW,KAAK,UAChBE,UAAS,KAAK,QACd,OAAO,KAAK,MACZ,WAAW,KAAK,UAChBC,WAAU,KAAK,SACf,OAAO,KAAK,MACZ,SAAS,KAAK,QACdC,YAAW,KAAK,UAChBC,YAAW,KAAK,UAChBC,qBAAoB,KAAK,mBACzBC,qBAAoB,KAAK,mBACzB,gBAAgB,KAAK,eACrBC,eAAc,KAAK,aACnB,uBAAuB,KAAK;AAChC,aAAW,YAAYN,WAAUA,QAAO,OAAO;AAC/C,MAAI,CAAC;AAAU;AACf,MAAI,KACA,UAAU,SAAS,SACnB,SAAS,OAAO,KAAK,OAAO,CAAC,EAAE,YAAY,IAAI,KAAK,OAAO,CAAC;AAEhE,MAAI,OAAO,eAAe,CAAC,cAAc,CAAC,MAAM;AAC9C,UAAM,IAAI,YAAY,MAAM;AAAA,MAC1B,SAAS;AAAA,MACT,YAAY;AAAA,IACd,CAAC;AAAA,EACH,OAAO;AACL,UAAM,SAAS,YAAY,OAAO;AAClC,QAAI,UAAU,MAAM,MAAM,IAAI;AAAA,EAChC;AAEA,MAAI,KAAK,QAAQA;AACjB,MAAI,OAAO,UAAUA;AACrB,MAAI,OAAO,YAAYA;AACvB,MAAI,QAAQC;AACZ,MAAI,WAAWC;AACf,MAAI,WAAWC;AACf,MAAI,oBAAoBC;AACxB,MAAI,oBAAoBC;AACxB,MAAI,gBAAgB;AACpB,MAAI,WAAWC,eAAcA,aAAY,cAAc;AAEvD,MAAI,qBAAqB,eAAe,eAAe,CAAC,GAAG,oBAAoB,GAAG,cAAc,mBAAmB,MAAM,QAAQ,CAAC;AAElI,WAASR,WAAU,oBAAoB;AACrC,QAAIA,OAAM,IAAI,mBAAmBA,OAAM;AAAA,EACzC;AAEA,MAAIE,SAAQ;AACV,IAAAA,QAAO,cAAc,GAAG;AAAA,EAC1B;AAEA,MAAI,QAAQ,MAAM,GAAG;AACnB,YAAQ,MAAM,EAAE,KAAK,UAAU,GAAG;AAAA,EACpC;AACF;AAEA,IAAI,YAAY,CAAC,KAAK;AAEtB,IAAIO,eAAc,SAASA,aAAY,WAAW,UAAU;AAC1D,MAAI,OAAO,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC,GAC5E,gBAAgB,KAAK,KACrB,OAAO,yBAAyB,MAAM,SAAS;AAEnD,gBAAc,YAAY,KAAK,QAAQ,EAAE,WAAW,UAAU,eAAe;AAAA,IAC3E;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,aAAa;AAAA,IACb;AAAA,IACA,gBAAgB,SAAS;AAAA,IACzB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,oBAAoB;AAAA,IACpB,sBAAsB;AAAA,IACtB,gBAAgB,SAAS,iBAAiB;AACxC,oBAAc;AAAA,IAChB;AAAA,IACA,eAAe,SAAS,gBAAgB;AACtC,oBAAc;AAAA,IAChB;AAAA,IACA,uBAAuB,SAAS,sBAAsB,MAAM;AAC1D,qBAAe;AAAA,QACb;AAAA,QACA;AAAA,QACA;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF,GAAG,IAAI,CAAC;AACV;AAEA,SAAS,eAAe,MAAM;AAC5B,gBAAc,eAAe;AAAA,IAC3B;AAAA,IACA;AAAA,IACA,UAAU;AAAA,IACV;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,GAAG,IAAI,CAAC;AACV;AAEA,IAAI;AAAJ,IACI;AADJ,IAEI;AAFJ,IAGI;AAHJ,IAII;AAJJ,IAKI;AALJ,IAMI;AANJ,IAOI;AAPJ,IAQI;AARJ,IASI;AATJ,IAUI;AAVJ,IAWI;AAXJ,IAYI;AAZJ,IAaI;AAbJ,IAcI,sBAAsB;AAd1B,IAeI,kBAAkB;AAftB,IAgBI,YAAY,CAAC;AAhBjB,IAiBI;AAjBJ,IAkBI;AAlBJ,IAmBI;AAnBJ,IAoBI;AApBJ,IAqBI;AArBJ,IAsBI;AAtBJ,IAuBI;AAvBJ,IAwBI;AAxBJ,IAyBI;AAzBJ,IA0BI,wBAAwB;AA1B5B,IA2BI,yBAAyB;AA3B7B,IA4BI;AA5BJ,IA8BA;AA9BA,IA+BI,mCAAmC,CAAC;AA/BxC,IAiCA,UAAU;AAjCV,IAkCI,oBAAoB,CAAC;AAGzB,IAAI,iBAAiB,OAAO,aAAa;AAAzC,IACI,0BAA0B;AAD9B,IAEI,mBAAmB,QAAQ,aAAa,aAAa;AAFzD,IAIA,mBAAmB,kBAAkB,CAAC,oBAAoB,CAAC,OAAO,eAAe,SAAS,cAAc,KAAK;AAJ7G,IAKI,0BAA0B,WAAY;AACxC,MAAI,CAAC;AAAgB;AAErB,MAAI,YAAY;AACd,WAAO;AAAA,EACT;AAEA,MAAI,KAAK,SAAS,cAAc,GAAG;AACnC,KAAG,MAAM,UAAU;AACnB,SAAO,GAAG,MAAM,kBAAkB;AACpC,EAAE;AAfF,IAgBI,mBAAmB,SAASC,kBAAiB,IAAI,SAAS;AAC5D,MAAI,QAAQ,IAAI,EAAE,GACd,UAAU,SAAS,MAAM,KAAK,IAAI,SAAS,MAAM,WAAW,IAAI,SAAS,MAAM,YAAY,IAAI,SAAS,MAAM,eAAe,IAAI,SAAS,MAAM,gBAAgB,GAChK,SAAS,SAAS,IAAI,GAAG,OAAO,GAChC,SAAS,SAAS,IAAI,GAAG,OAAO,GAChC,gBAAgB,UAAU,IAAI,MAAM,GACpC,iBAAiB,UAAU,IAAI,MAAM,GACrC,kBAAkB,iBAAiB,SAAS,cAAc,UAAU,IAAI,SAAS,cAAc,WAAW,IAAI,QAAQ,MAAM,EAAE,OAC9H,mBAAmB,kBAAkB,SAAS,eAAe,UAAU,IAAI,SAAS,eAAe,WAAW,IAAI,QAAQ,MAAM,EAAE;AAEtI,MAAI,MAAM,YAAY,QAAQ;AAC5B,WAAO,MAAM,kBAAkB,YAAY,MAAM,kBAAkB,mBAAmB,aAAa;AAAA,EACrG;AAEA,MAAI,MAAM,YAAY,QAAQ;AAC5B,WAAO,MAAM,oBAAoB,MAAM,GAAG,EAAE,UAAU,IAAI,aAAa;AAAA,EACzE;AAEA,MAAI,UAAU,cAAc,OAAO,KAAK,cAAc,OAAO,MAAM,QAAQ;AACzE,QAAI,qBAAqB,cAAc,OAAO,MAAM,SAAS,SAAS;AACtE,WAAO,WAAW,eAAe,UAAU,UAAU,eAAe,UAAU,sBAAsB,aAAa;AAAA,EACnH;AAEA,SAAO,WAAW,cAAc,YAAY,WAAW,cAAc,YAAY,UAAU,cAAc,YAAY,WAAW,cAAc,YAAY,UAAU,mBAAmB,WAAW,MAAM,gBAAgB,MAAM,UAAU,UAAU,MAAM,gBAAgB,MAAM,UAAU,kBAAkB,mBAAmB,WAAW,aAAa;AACvV;AAxCA,IAyCI,qBAAqB,SAASC,oBAAmB,UAAU,YAAY,UAAU;AACnF,MAAI,cAAc,WAAW,SAAS,OAAO,SAAS,KAClD,cAAc,WAAW,SAAS,QAAQ,SAAS,QACnD,kBAAkB,WAAW,SAAS,QAAQ,SAAS,QACvD,cAAc,WAAW,WAAW,OAAO,WAAW,KACtD,cAAc,WAAW,WAAW,QAAQ,WAAW,QACvD,kBAAkB,WAAW,WAAW,QAAQ,WAAW;AAC/D,SAAO,gBAAgB,eAAe,gBAAgB,eAAe,cAAc,kBAAkB,MAAM,cAAc,kBAAkB;AAC7I;AAjDA,IAyDA,8BAA8B,SAASC,6BAA4B,GAAG,GAAG;AACvE,MAAI;AACJ,YAAU,KAAK,SAAU,UAAU;AACjC,QAAI,YAAY,SAAS,OAAO,EAAE,QAAQ;AAC1C,QAAI,CAAC,aAAa,UAAU,QAAQ;AAAG;AACvC,QAAI,OAAO,QAAQ,QAAQ,GACvB,qBAAqB,KAAK,KAAK,OAAO,aAAa,KAAK,KAAK,QAAQ,WACrE,mBAAmB,KAAK,KAAK,MAAM,aAAa,KAAK,KAAK,SAAS;AAEvE,QAAI,sBAAsB,kBAAkB;AAC1C,aAAO,MAAM;AAAA,IACf;AAAA,EACF,CAAC;AACD,SAAO;AACT;AAvEA,IAwEI,gBAAgB,SAASC,eAAc,SAAS;AAClD,WAAS,KAAK,OAAO,MAAM;AACzB,WAAO,SAAU,IAAI,MAAMC,SAAQ,KAAK;AACtC,UAAI,YAAY,GAAG,QAAQ,MAAM,QAAQ,KAAK,QAAQ,MAAM,QAAQ,GAAG,QAAQ,MAAM,SAAS,KAAK,QAAQ,MAAM;AAEjH,UAAI,SAAS,SAAS,QAAQ,YAAY;AAGxC,eAAO;AAAA,MACT,WAAW,SAAS,QAAQ,UAAU,OAAO;AAC3C,eAAO;AAAA,MACT,WAAW,QAAQ,UAAU,SAAS;AACpC,eAAO;AAAA,MACT,WAAW,OAAO,UAAU,YAAY;AACtC,eAAO,KAAK,MAAM,IAAI,MAAMA,SAAQ,GAAG,GAAG,IAAI,EAAE,IAAI,MAAMA,SAAQ,GAAG;AAAA,MACvE,OAAO;AACL,YAAI,cAAc,OAAO,KAAK,MAAM,QAAQ,MAAM;AAClD,eAAO,UAAU,QAAQ,OAAO,UAAU,YAAY,UAAU,cAAc,MAAM,QAAQ,MAAM,QAAQ,UAAU,IAAI;AAAA,MAC1H;AAAA,IACF;AAAA,EACF;AAEA,MAAI,QAAQ,CAAC;AACb,MAAI,gBAAgB,QAAQ;AAE5B,MAAI,CAAC,iBAAiB,QAAQ,aAAa,KAAK,UAAU;AACxD,oBAAgB;AAAA,MACd,MAAM;AAAA,IACR;AAAA,EACF;AAEA,QAAM,OAAO,cAAc;AAC3B,QAAM,YAAY,KAAK,cAAc,MAAM,IAAI;AAC/C,QAAM,WAAW,KAAK,cAAc,GAAG;AACvC,QAAM,cAAc,cAAc;AAClC,UAAQ,QAAQ;AAClB;AA5GA,IA6GI,sBAAsB,SAASC,uBAAsB;AACvD,MAAI,CAAC,2BAA2B,SAAS;AACvC,QAAI,SAAS,WAAW,MAAM;AAAA,EAChC;AACF;AAjHA,IAkHI,wBAAwB,SAASC,yBAAwB;AAC3D,MAAI,CAAC,2BAA2B,SAAS;AACvC,QAAI,SAAS,WAAW,EAAE;AAAA,EAC5B;AACF;AAGA,IAAI,gBAAgB;AAClB,WAAS,iBAAiB,SAAS,SAAU,KAAK;AAChD,QAAI,iBAAiB;AACnB,UAAI,eAAe;AACnB,UAAI,mBAAmB,IAAI,gBAAgB;AAC3C,UAAI,4BAA4B,IAAI,yBAAyB;AAC7D,wBAAkB;AAClB,aAAO;AAAA,IACT;AAAA,EACF,GAAG,IAAI;AACT;AAEA,IAAI,gCAAgC,SAASC,+BAA8B,KAAK;AAC9E,MAAI,QAAQ;AACV,UAAM,IAAI,UAAU,IAAI,QAAQ,CAAC,IAAI;AAErC,QAAI,UAAU,4BAA4B,IAAI,SAAS,IAAI,OAAO;AAElE,QAAI,SAAS;AAEX,UAAI,QAAQ,CAAC;AAEb,eAAS,KAAK,KAAK;AACjB,YAAI,IAAI,eAAe,CAAC,GAAG;AACzB,gBAAM,CAAC,IAAI,IAAI,CAAC;AAAA,QAClB;AAAA,MACF;AAEA,YAAM,SAAS,MAAM,SAAS;AAC9B,YAAM,iBAAiB;AACvB,YAAM,kBAAkB;AAExB,cAAQ,OAAO,EAAE,YAAY,KAAK;AAAA,IACpC;AAAA,EACF;AACF;AAEA,IAAI,wBAAwB,SAASC,uBAAsB,KAAK;AAC9D,MAAI,QAAQ;AACV,WAAO,WAAW,OAAO,EAAE,iBAAiB,IAAI,MAAM;AAAA,EACxD;AACF;AAQA,SAAS,SAAS,IAAI,SAAS;AAC7B,MAAI,EAAE,MAAM,GAAG,YAAY,GAAG,aAAa,IAAI;AAC7C,UAAM,8CAA8C,OAAO,CAAC,EAAE,SAAS,KAAK,EAAE,CAAC;AAAA,EACjF;AAEA,OAAK,KAAK;AAEV,OAAK,UAAU,UAAU,SAAS,CAAC,GAAG,OAAO;AAE7C,KAAG,OAAO,IAAI;AACd,MAAIjB,YAAW;AAAA,IACb,OAAO;AAAA,IACP,MAAM;AAAA,IACN,UAAU;AAAA,IACV,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,WAAW,WAAW,KAAK,GAAG,QAAQ,IAAI,QAAQ;AAAA,IAClD,eAAe;AAAA;AAAA,IAEf,YAAY;AAAA;AAAA,IAEZ,uBAAuB;AAAA;AAAA,IAEvB,mBAAmB;AAAA,IACnB,WAAW,SAAS,YAAY;AAC9B,aAAO,iBAAiB,IAAI,KAAK,OAAO;AAAA,IAC1C;AAAA,IACA,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,iBAAiB;AAAA,IACjB,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,SAAS,SAAS,QAAQ,cAAca,SAAQ;AAC9C,mBAAa,QAAQ,QAAQA,QAAO,WAAW;AAAA,IACjD;AAAA,IACA,YAAY;AAAA,IACZ,gBAAgB;AAAA,IAChB,YAAY;AAAA,IACZ,OAAO;AAAA,IACP,kBAAkB;AAAA,IAClB,sBAAsB,OAAO,WAAW,SAAS,QAAQ,SAAS,OAAO,kBAAkB,EAAE,KAAK;AAAA,IAClG,eAAe;AAAA,IACf,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,mBAAmB;AAAA,IACnB,gBAAgB;AAAA,MACd,GAAG;AAAA,MACH,GAAG;AAAA,IACL;AAAA,IACA,gBAAgB,SAAS,mBAAmB,SAAS,kBAAkB,UAAU,CAAC;AAAA,IAClF,sBAAsB;AAAA,EACxB;AACA,gBAAc,kBAAkB,MAAM,IAAIb,SAAQ;AAElD,WAAS,QAAQA,WAAU;AACzB,MAAE,QAAQ,aAAa,QAAQ,IAAI,IAAIA,UAAS,IAAI;AAAA,EACtD;AAEA,gBAAc,OAAO;AAGrB,WAAS,MAAM,MAAM;AACnB,QAAI,GAAG,OAAO,CAAC,MAAM,OAAO,OAAO,KAAK,EAAE,MAAM,YAAY;AAC1D,WAAK,EAAE,IAAI,KAAK,EAAE,EAAE,KAAK,IAAI;AAAA,IAC/B;AAAA,EACF;AAGA,OAAK,kBAAkB,QAAQ,gBAAgB,QAAQ;AAEvD,MAAI,KAAK,iBAAiB;AAExB,SAAK,QAAQ,sBAAsB;AAAA,EACrC;AAGA,MAAI,QAAQ,gBAAgB;AAC1B,OAAG,IAAI,eAAe,KAAK,WAAW;AAAA,EACxC,OAAO;AACL,OAAG,IAAI,aAAa,KAAK,WAAW;AACpC,OAAG,IAAI,cAAc,KAAK,WAAW;AAAA,EACvC;AAEA,MAAI,KAAK,iBAAiB;AACxB,OAAG,IAAI,YAAY,IAAI;AACvB,OAAG,IAAI,aAAa,IAAI;AAAA,EAC1B;AAEA,YAAU,KAAK,KAAK,EAAE;AAEtB,UAAQ,SAAS,QAAQ,MAAM,OAAO,KAAK,KAAK,QAAQ,MAAM,IAAI,IAAI,KAAK,CAAC,CAAC;AAE7E,WAAS,MAAM,sBAAsB,CAAC;AACxC;AAEA,SAAS;AAET;AAAA,EACE,aAAa;AAAA,EACb,kBAAkB,SAAS,iBAAiB,QAAQ;AAClD,QAAI,CAAC,KAAK,GAAG,SAAS,MAAM,KAAK,WAAW,KAAK,IAAI;AACnD,mBAAa;AAAA,IACf;AAAA,EACF;AAAA,EACA,eAAe,SAAS,cAAc,KAAK,QAAQ;AACjD,WAAO,OAAO,KAAK,QAAQ,cAAc,aAAa,KAAK,QAAQ,UAAU,KAAK,MAAM,KAAK,QAAQ,MAAM,IAAI,KAAK,QAAQ;AAAA,EAC9H;AAAA,EACA,aAAa,SAAS,YAEtB,KAAK;AACH,QAAI,CAAC,IAAI;AAAY;AAErB,QAAI,QAAQ,MACR,KAAK,KAAK,IACV,UAAU,KAAK,SACf,kBAAkB,QAAQ,iBAC1B,OAAO,IAAI,MACX,QAAQ,IAAI,WAAW,IAAI,QAAQ,CAAC,KAAK,IAAI,eAAe,IAAI,gBAAgB,WAAW,KAC3F,UAAU,SAAS,KAAK,QACxB,iBAAiB,IAAI,OAAO,eAAe,IAAI,QAAQ,IAAI,KAAK,CAAC,KAAK,IAAI,gBAAgB,IAAI,aAAa,EAAE,CAAC,MAAM,QACpH,SAAS,QAAQ;AAErB,2BAAuB,EAAE;AAGzB,QAAI,QAAQ;AACV;AAAA,IACF;AAEA,QAAI,wBAAwB,KAAK,IAAI,KAAK,IAAI,WAAW,KAAK,QAAQ,UAAU;AAC9E;AAAA,IACF;AAGA,QAAI,eAAe,mBAAmB;AACpC;AAAA,IACF;AAGA,QAAI,CAAC,KAAK,mBAAmB,UAAU,UAAU,OAAO,QAAQ,YAAY,MAAM,UAAU;AAC1F;AAAA,IACF;AAEA,aAAS,QAAQ,QAAQ,QAAQ,WAAW,IAAI,KAAK;AAErD,QAAI,UAAU,OAAO,UAAU;AAC7B;AAAA,IACF;AAEA,QAAI,eAAe,QAAQ;AAEzB;AAAA,IACF;AAGA,eAAW,MAAM,MAAM;AACvB,wBAAoB,MAAM,QAAQ,QAAQ,SAAS;AAEnD,QAAI,OAAO,WAAW,YAAY;AAChC,UAAI,OAAO,KAAK,MAAM,KAAK,QAAQ,IAAI,GAAG;AACxC,uBAAe;AAAA,UACb,UAAU;AAAA,UACV,QAAQ;AAAA,UACR,MAAM;AAAA,UACN,UAAU;AAAA,UACV,MAAM;AAAA,UACN,QAAQ;AAAA,QACV,CAAC;AAED,QAAAQ,aAAY,UAAU,OAAO;AAAA,UAC3B;AAAA,QACF,CAAC;AACD,2BAAmB,IAAI,cAAc,IAAI,eAAe;AACxD;AAAA,MACF;AAAA,IACF,WAAW,QAAQ;AACjB,eAAS,OAAO,MAAM,GAAG,EAAE,KAAK,SAAU,UAAU;AAClD,mBAAW,QAAQ,gBAAgB,SAAS,KAAK,GAAG,IAAI,KAAK;AAE7D,YAAI,UAAU;AACZ,yBAAe;AAAA,YACb,UAAU;AAAA,YACV,QAAQ;AAAA,YACR,MAAM;AAAA,YACN,UAAU;AAAA,YACV,QAAQ;AAAA,YACR,MAAM;AAAA,UACR,CAAC;AAED,UAAAA,aAAY,UAAU,OAAO;AAAA,YAC3B;AAAA,UACF,CAAC;AACD,iBAAO;AAAA,QACT;AAAA,MACF,CAAC;AAED,UAAI,QAAQ;AACV,2BAAmB,IAAI,cAAc,IAAI,eAAe;AACxD;AAAA,MACF;AAAA,IACF;AAEA,QAAI,QAAQ,UAAU,CAAC,QAAQ,gBAAgB,QAAQ,QAAQ,IAAI,KAAK,GAAG;AACzE;AAAA,IACF;AAGA,SAAK,kBAAkB,KAAK,OAAO,MAAM;AAAA,EAC3C;AAAA,EACA,mBAAmB,SAAS,kBAE5B,KAEA,OAEA,QAAQ;AACN,QAAI,QAAQ,MACR,KAAK,MAAM,IACX,UAAU,MAAM,SAChB,gBAAgB,GAAG,eACnB;AAEJ,QAAI,UAAU,CAAC,UAAU,OAAO,eAAe,IAAI;AACjD,UAAI,WAAW,QAAQ,MAAM;AAC7B,eAAS;AACT,eAAS;AACT,iBAAW,OAAO;AAClB,eAAS,OAAO;AAChB,mBAAa;AACb,oBAAc,QAAQ;AACtB,eAAS,UAAU;AACnB,eAAS;AAAA,QACP,QAAQ;AAAA,QACR,UAAU,SAAS,KAAK;AAAA,QACxB,UAAU,SAAS,KAAK;AAAA,MAC1B;AACA,wBAAkB,OAAO,UAAU,SAAS;AAC5C,uBAAiB,OAAO,UAAU,SAAS;AAC3C,WAAK,UAAU,SAAS,KAAK;AAC7B,WAAK,UAAU,SAAS,KAAK;AAC7B,aAAO,MAAM,aAAa,IAAI;AAE9B,oBAAc,SAASU,eAAc;AACnC,QAAAV,aAAY,cAAc,OAAO;AAAA,UAC/B;AAAA,QACF,CAAC;AAED,YAAI,SAAS,eAAe;AAC1B,gBAAM,QAAQ;AAEd;AAAA,QACF;AAIA,cAAM,0BAA0B;AAEhC,YAAI,CAAC,WAAW,MAAM,iBAAiB;AACrC,iBAAO,YAAY;AAAA,QACrB;AAGA,cAAM,kBAAkB,KAAK,KAAK;AAGlC,uBAAe;AAAA,UACb,UAAU;AAAA,UACV,MAAM;AAAA,UACN,eAAe;AAAA,QACjB,CAAC;AAGD,oBAAY,QAAQ,QAAQ,aAAa,IAAI;AAAA,MAC/C;AAGA,cAAQ,OAAO,MAAM,GAAG,EAAE,QAAQ,SAAU,UAAU;AACpD,aAAK,QAAQ,SAAS,KAAK,GAAG,iBAAiB;AAAA,MACjD,CAAC;AACD,SAAG,eAAe,YAAY,6BAA6B;AAC3D,SAAG,eAAe,aAAa,6BAA6B;AAC5D,SAAG,eAAe,aAAa,6BAA6B;AAC5D,SAAG,eAAe,WAAW,MAAM,OAAO;AAC1C,SAAG,eAAe,YAAY,MAAM,OAAO;AAC3C,SAAG,eAAe,eAAe,MAAM,OAAO;AAE9C,UAAI,WAAW,KAAK,iBAAiB;AACnC,aAAK,QAAQ,sBAAsB;AACnC,eAAO,YAAY;AAAA,MACrB;AAEA,MAAAA,aAAY,cAAc,MAAM;AAAA,QAC9B;AAAA,MACF,CAAC;AAED,UAAI,QAAQ,UAAU,CAAC,QAAQ,oBAAoB,WAAW,CAAC,KAAK,mBAAmB,EAAE,QAAQ,cAAc;AAC7G,YAAI,SAAS,eAAe;AAC1B,eAAK,QAAQ;AAEb;AAAA,QACF;AAKA,WAAG,eAAe,WAAW,MAAM,mBAAmB;AACtD,WAAG,eAAe,YAAY,MAAM,mBAAmB;AACvD,WAAG,eAAe,eAAe,MAAM,mBAAmB;AAC1D,WAAG,eAAe,aAAa,MAAM,4BAA4B;AACjE,WAAG,eAAe,aAAa,MAAM,4BAA4B;AACjE,gBAAQ,kBAAkB,GAAG,eAAe,eAAe,MAAM,4BAA4B;AAC7F,cAAM,kBAAkB,WAAW,aAAa,QAAQ,KAAK;AAAA,MAC/D,OAAO;AACL,oBAAY;AAAA,MACd;AAAA,IACF;AAAA,EACF;AAAA,EACA,8BAA8B,SAAS,6BAEvC,GAAG;AACD,QAAI,QAAQ,EAAE,UAAU,EAAE,QAAQ,CAAC,IAAI;AAEvC,QAAI,KAAK,IAAI,KAAK,IAAI,MAAM,UAAU,KAAK,MAAM,GAAG,KAAK,IAAI,MAAM,UAAU,KAAK,MAAM,CAAC,KAAK,KAAK,MAAM,KAAK,QAAQ,uBAAuB,KAAK,mBAAmB,OAAO,oBAAoB,EAAE,GAAG;AACnM,WAAK,oBAAoB;AAAA,IAC3B;AAAA,EACF;AAAA,EACA,qBAAqB,SAAS,sBAAsB;AAClD,cAAU,kBAAkB,MAAM;AAClC,iBAAa,KAAK,eAAe;AAEjC,SAAK,0BAA0B;AAAA,EACjC;AAAA,EACA,2BAA2B,SAAS,4BAA4B;AAC9D,QAAI,gBAAgB,KAAK,GAAG;AAC5B,QAAI,eAAe,WAAW,KAAK,mBAAmB;AACtD,QAAI,eAAe,YAAY,KAAK,mBAAmB;AACvD,QAAI,eAAe,eAAe,KAAK,mBAAmB;AAC1D,QAAI,eAAe,aAAa,KAAK,4BAA4B;AACjE,QAAI,eAAe,aAAa,KAAK,4BAA4B;AACjE,QAAI,eAAe,eAAe,KAAK,4BAA4B;AAAA,EACrE;AAAA,EACA,mBAAmB,SAAS,kBAE5B,KAEA,OAAO;AACL,YAAQ,SAAS,IAAI,eAAe,WAAW;AAE/C,QAAI,CAAC,KAAK,mBAAmB,OAAO;AAClC,UAAI,KAAK,QAAQ,gBAAgB;AAC/B,WAAG,UAAU,eAAe,KAAK,YAAY;AAAA,MAC/C,WAAW,OAAO;AAChB,WAAG,UAAU,aAAa,KAAK,YAAY;AAAA,MAC7C,OAAO;AACL,WAAG,UAAU,aAAa,KAAK,YAAY;AAAA,MAC7C;AAAA,IACF,OAAO;AACL,SAAG,QAAQ,WAAW,IAAI;AAC1B,SAAG,QAAQ,aAAa,KAAK,YAAY;AAAA,IAC3C;AAEA,QAAI;AACF,UAAI,SAAS,WAAW;AAEtB,kBAAU,WAAY;AACpB,mBAAS,UAAU,MAAM;AAAA,QAC3B,CAAC;AAAA,MACH,OAAO;AACL,eAAO,aAAa,EAAE,gBAAgB;AAAA,MACxC;AAAA,IACF,SAAS,KAAK;AAAA,IAAC;AAAA,EACjB;AAAA,EACA,cAAc,SAAS,aAAa,UAAU,KAAK;AAEjD,0BAAsB;AAEtB,QAAI,UAAU,QAAQ;AACpB,MAAAA,aAAY,eAAe,MAAM;AAAA,QAC/B;AAAA,MACF,CAAC;AAED,UAAI,KAAK,iBAAiB;AACxB,WAAG,UAAU,YAAY,qBAAqB;AAAA,MAChD;AAEA,UAAI,UAAU,KAAK;AAEnB,OAAC,YAAY,YAAY,QAAQ,QAAQ,WAAW,KAAK;AACzD,kBAAY,QAAQ,QAAQ,YAAY,IAAI;AAC5C,eAAS,SAAS;AAClB,kBAAY,KAAK,aAAa;AAE9B,qBAAe;AAAA,QACb,UAAU;AAAA,QACV,MAAM;AAAA,QACN,eAAe;AAAA,MACjB,CAAC;AAAA,IACH,OAAO;AACL,WAAK,SAAS;AAAA,IAChB;AAAA,EACF;AAAA,EACA,kBAAkB,SAAS,mBAAmB;AAC5C,QAAI,UAAU;AACZ,WAAK,SAAS,SAAS;AACvB,WAAK,SAAS,SAAS;AAEvB,0BAAoB;AAEpB,UAAI,SAAS,SAAS,iBAAiB,SAAS,SAAS,SAAS,OAAO;AACzE,UAAI,SAAS;AAEb,aAAO,UAAU,OAAO,YAAY;AAClC,iBAAS,OAAO,WAAW,iBAAiB,SAAS,SAAS,SAAS,OAAO;AAC9E,YAAI,WAAW;AAAQ;AACvB,iBAAS;AAAA,MACX;AAEA,aAAO,WAAW,OAAO,EAAE,iBAAiB,MAAM;AAElD,UAAI,QAAQ;AACV,WAAG;AACD,cAAI,OAAO,OAAO,GAAG;AACnB,gBAAI,WAAW;AACf,uBAAW,OAAO,OAAO,EAAE,YAAY;AAAA,cACrC,SAAS,SAAS;AAAA,cAClB,SAAS,SAAS;AAAA,cAClB;AAAA,cACA,QAAQ;AAAA,YACV,CAAC;AAED,gBAAI,YAAY,CAAC,KAAK,QAAQ,gBAAgB;AAC5C;AAAA,YACF;AAAA,UACF;AAEA,mBAAS;AAAA,QACX,SAEO,SAAS,OAAO;AAAA,MACzB;AAEA,4BAAsB;AAAA,IACxB;AAAA,EACF;AAAA,EACA,cAAc,SAAS,aAEvB,KAAK;AACH,QAAI,QAAQ;AACV,UAAI,UAAU,KAAK,SACf,oBAAoB,QAAQ,mBAC5B,iBAAiB,QAAQ,gBACzB,QAAQ,IAAI,UAAU,IAAI,QAAQ,CAAC,IAAI,KACvC,cAAc,WAAW,OAAO,SAAS,IAAI,GAC7C,SAAS,WAAW,eAAe,YAAY,GAC/C,SAAS,WAAW,eAAe,YAAY,GAC/C,uBAAuB,2BAA2B,uBAAuB,wBAAwB,mBAAmB,GACpH,MAAM,MAAM,UAAU,OAAO,UAAU,eAAe,MAAM,UAAU,MAAM,uBAAuB,qBAAqB,CAAC,IAAI,iCAAiC,CAAC,IAAI,MAAM,UAAU,IACnL,MAAM,MAAM,UAAU,OAAO,UAAU,eAAe,MAAM,UAAU,MAAM,uBAAuB,qBAAqB,CAAC,IAAI,iCAAiC,CAAC,IAAI,MAAM,UAAU;AAEvL,UAAI,CAAC,SAAS,UAAU,CAAC,qBAAqB;AAC5C,YAAI,qBAAqB,KAAK,IAAI,KAAK,IAAI,MAAM,UAAU,KAAK,MAAM,GAAG,KAAK,IAAI,MAAM,UAAU,KAAK,MAAM,CAAC,IAAI,mBAAmB;AACnI;AAAA,QACF;AAEA,aAAK,aAAa,KAAK,IAAI;AAAA,MAC7B;AAEA,UAAI,SAAS;AACX,YAAI,aAAa;AACf,sBAAY,KAAK,MAAM,UAAU;AACjC,sBAAY,KAAK,MAAM,UAAU;AAAA,QACnC,OAAO;AACL,wBAAc;AAAA,YACZ,GAAG;AAAA,YACH,GAAG;AAAA,YACH,GAAG;AAAA,YACH,GAAG;AAAA,YACH,GAAG;AAAA,YACH,GAAG;AAAA,UACL;AAAA,QACF;AAEA,YAAI,YAAY,UAAU,OAAO,YAAY,GAAG,GAAG,EAAE,OAAO,YAAY,GAAG,GAAG,EAAE,OAAO,YAAY,GAAG,GAAG,EAAE,OAAO,YAAY,GAAG,GAAG,EAAE,OAAO,YAAY,GAAG,GAAG,EAAE,OAAO,YAAY,GAAG,GAAG;AAC1L,YAAI,SAAS,mBAAmB,SAAS;AACzC,YAAI,SAAS,gBAAgB,SAAS;AACtC,YAAI,SAAS,eAAe,SAAS;AACrC,YAAI,SAAS,aAAa,SAAS;AACnC,iBAAS;AACT,iBAAS;AACT,mBAAW;AAAA,MACb;AAEA,UAAI,cAAc,IAAI,eAAe;AAAA,IACvC;AAAA,EACF;AAAA,EACA,cAAc,SAAS,eAAe;AAGpC,QAAI,CAAC,SAAS;AACZ,UAAI,YAAY,KAAK,QAAQ,iBAAiB,SAAS,OAAO,QAC1D,OAAO,QAAQ,QAAQ,MAAM,yBAAyB,MAAM,SAAS,GACrE,UAAU,KAAK;AAEnB,UAAI,yBAAyB;AAE3B,8BAAsB;AAEtB,eAAO,IAAI,qBAAqB,UAAU,MAAM,YAAY,IAAI,qBAAqB,WAAW,MAAM,UAAU,wBAAwB,UAAU;AAChJ,gCAAsB,oBAAoB;AAAA,QAC5C;AAEA,YAAI,wBAAwB,SAAS,QAAQ,wBAAwB,SAAS,iBAAiB;AAC7F,cAAI,wBAAwB;AAAU,kCAAsB,0BAA0B;AACtF,eAAK,OAAO,oBAAoB;AAChC,eAAK,QAAQ,oBAAoB;AAAA,QACnC,OAAO;AACL,gCAAsB,0BAA0B;AAAA,QAClD;AAEA,2CAAmC,wBAAwB,mBAAmB;AAAA,MAChF;AAEA,gBAAU,OAAO,UAAU,IAAI;AAC/B,kBAAY,SAAS,QAAQ,YAAY,KAAK;AAC9C,kBAAY,SAAS,QAAQ,eAAe,IAAI;AAChD,kBAAY,SAAS,QAAQ,WAAW,IAAI;AAC5C,UAAI,SAAS,cAAc,EAAE;AAC7B,UAAI,SAAS,aAAa,EAAE;AAC5B,UAAI,SAAS,cAAc,YAAY;AACvC,UAAI,SAAS,UAAU,CAAC;AACxB,UAAI,SAAS,OAAO,KAAK,GAAG;AAC5B,UAAI,SAAS,QAAQ,KAAK,IAAI;AAC9B,UAAI,SAAS,SAAS,KAAK,KAAK;AAChC,UAAI,SAAS,UAAU,KAAK,MAAM;AAClC,UAAI,SAAS,WAAW,KAAK;AAC7B,UAAI,SAAS,YAAY,0BAA0B,aAAa,OAAO;AACvE,UAAI,SAAS,UAAU,QAAQ;AAC/B,UAAI,SAAS,iBAAiB,MAAM;AACpC,eAAS,QAAQ;AACjB,gBAAU,YAAY,OAAO;AAE7B,UAAI,SAAS,oBAAoB,kBAAkB,SAAS,QAAQ,MAAM,KAAK,IAAI,MAAM,OAAO,iBAAiB,SAAS,QAAQ,MAAM,MAAM,IAAI,MAAM,GAAG;AAAA,IAC7J;AAAA,EACF;AAAA,EACA,cAAc,SAAS,aAEvB,KAEA,UAAU;AACR,QAAI,QAAQ;AAEZ,QAAI,eAAe,IAAI;AACvB,QAAI,UAAU,MAAM;AACpB,IAAAA,aAAY,aAAa,MAAM;AAAA,MAC7B;AAAA,IACF,CAAC;AAED,QAAI,SAAS,eAAe;AAC1B,WAAK,QAAQ;AAEb;AAAA,IACF;AAEA,IAAAA,aAAY,cAAc,IAAI;AAE9B,QAAI,CAAC,SAAS,eAAe;AAC3B,gBAAU,MAAM,MAAM;AACtB,cAAQ,YAAY;AACpB,cAAQ,MAAM,aAAa,IAAI;AAE/B,WAAK,WAAW;AAEhB,kBAAY,SAAS,KAAK,QAAQ,aAAa,KAAK;AACpD,eAAS,QAAQ;AAAA,IACnB;AAGA,UAAM,UAAU,UAAU,WAAY;AACpC,MAAAA,aAAY,SAAS,KAAK;AAC1B,UAAI,SAAS;AAAe;AAE5B,UAAI,CAAC,MAAM,QAAQ,mBAAmB;AACpC,eAAO,aAAa,SAAS,MAAM;AAAA,MACrC;AAEA,YAAM,WAAW;AAEjB,qBAAe;AAAA,QACb,UAAU;AAAA,QACV,MAAM;AAAA,MACR,CAAC;AAAA,IACH,CAAC;AACD,KAAC,YAAY,YAAY,QAAQ,QAAQ,WAAW,IAAI;AAExD,QAAI,UAAU;AACZ,wBAAkB;AAClB,YAAM,UAAU,YAAY,MAAM,kBAAkB,EAAE;AAAA,IACxD,OAAO;AAEL,UAAI,UAAU,WAAW,MAAM,OAAO;AACtC,UAAI,UAAU,YAAY,MAAM,OAAO;AACvC,UAAI,UAAU,eAAe,MAAM,OAAO;AAE1C,UAAI,cAAc;AAChB,qBAAa,gBAAgB;AAC7B,gBAAQ,WAAW,QAAQ,QAAQ,KAAK,OAAO,cAAc,MAAM;AAAA,MACrE;AAEA,SAAG,UAAU,QAAQ,KAAK;AAE1B,UAAI,QAAQ,aAAa,eAAe;AAAA,IAC1C;AAEA,0BAAsB;AACtB,UAAM,eAAe,UAAU,MAAM,aAAa,KAAK,OAAO,UAAU,GAAG,CAAC;AAC5E,OAAG,UAAU,eAAe,KAAK;AACjC,YAAQ;AAER,QAAI,QAAQ;AACV,UAAI,SAAS,MAAM,eAAe,MAAM;AAAA,IAC1C;AAAA,EACF;AAAA;AAAA,EAEA,aAAa,SAAS,YAEtB,KAAK;AACH,QAAI,KAAK,KAAK,IACV,SAAS,IAAI,QACb,UACA,YACA,QACA,UAAU,KAAK,SACf,QAAQ,QAAQ,OAChB,iBAAiB,SAAS,QAC1B,UAAU,gBAAgB,OAC1B,UAAU,QAAQ,MAClB,eAAe,eAAe,gBAC9B,UACA,QAAQ,MACR,iBAAiB;AAErB,QAAI;AAAS;AAEb,aAAS,cAAc,MAAM,OAAO;AAClC,MAAAA,aAAY,MAAM,OAAO,eAAe;AAAA,QACtC;AAAA,QACA;AAAA,QACA,MAAM,WAAW,aAAa;AAAA,QAC9B;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,QAAQ,SAAS,OAAOW,SAAQC,QAAO;AACrC,iBAAO,QAAQ,QAAQ,IAAI,QAAQ,UAAUD,SAAQ,QAAQA,OAAM,GAAG,KAAKC,MAAK;AAAA,QAClF;AAAA,QACA;AAAA,MACF,GAAG,KAAK,CAAC;AAAA,IACX;AAGA,aAAS,UAAU;AACjB,oBAAc,0BAA0B;AAExC,YAAM,sBAAsB;AAE5B,UAAI,UAAU,cAAc;AAC1B,qBAAa,sBAAsB;AAAA,MACrC;AAAA,IACF;AAGA,aAAS,UAAU,WAAW;AAC5B,oBAAc,qBAAqB;AAAA,QACjC;AAAA,MACF,CAAC;AAED,UAAI,WAAW;AAEb,YAAI,SAAS;AACX,yBAAe,WAAW;AAAA,QAC5B,OAAO;AACL,yBAAe,WAAW,KAAK;AAAA,QACjC;AAEA,YAAI,UAAU,cAAc;AAE1B,sBAAY,QAAQ,cAAc,YAAY,QAAQ,aAAa,eAAe,QAAQ,YAAY,KAAK;AAC3G,sBAAY,QAAQ,QAAQ,YAAY,IAAI;AAAA,QAC9C;AAEA,YAAI,gBAAgB,SAAS,UAAU,SAAS,QAAQ;AACtD,wBAAc;AAAA,QAChB,WAAW,UAAU,SAAS,UAAU,aAAa;AACnD,wBAAc;AAAA,QAChB;AAGA,YAAI,iBAAiB,OAAO;AAC1B,gBAAM,wBAAwB;AAAA,QAChC;AAEA,cAAM,WAAW,WAAY;AAC3B,wBAAc,2BAA2B;AACzC,gBAAM,wBAAwB;AAAA,QAChC,CAAC;AAED,YAAI,UAAU,cAAc;AAC1B,uBAAa,WAAW;AACxB,uBAAa,wBAAwB;AAAA,QACvC;AAAA,MACF;AAGA,UAAI,WAAW,UAAU,CAAC,OAAO,YAAY,WAAW,MAAM,CAAC,OAAO,UAAU;AAC9E,qBAAa;AAAA,MACf;AAGA,UAAI,CAAC,QAAQ,kBAAkB,CAAC,IAAI,UAAU,WAAW,UAAU;AACjE,eAAO,WAAW,OAAO,EAAE,iBAAiB,IAAI,MAAM;AAGtD,SAAC,aAAa,8BAA8B,GAAG;AAAA,MACjD;AAEA,OAAC,QAAQ,kBAAkB,IAAI,mBAAmB,IAAI,gBAAgB;AACtE,aAAO,iBAAiB;AAAA,IAC1B;AAGA,aAAS,UAAU;AACjB,iBAAW,MAAM,MAAM;AACvB,0BAAoB,MAAM,QAAQ,QAAQ,SAAS;AAEnD,qBAAe;AAAA,QACb,UAAU;AAAA,QACV,MAAM;AAAA,QACN,MAAM;AAAA,QACN;AAAA,QACA;AAAA,QACA,eAAe;AAAA,MACjB,CAAC;AAAA,IACH;AAEA,QAAI,IAAI,mBAAmB,QAAQ;AACjC,UAAI,cAAc,IAAI,eAAe;AAAA,IACvC;AAEA,aAAS,QAAQ,QAAQ,QAAQ,WAAW,IAAI,IAAI;AACpD,kBAAc,UAAU;AACxB,QAAI,SAAS;AAAe,aAAO;AAEnC,QAAI,OAAO,SAAS,IAAI,MAAM,KAAK,OAAO,YAAY,OAAO,cAAc,OAAO,cAAc,MAAM,0BAA0B,QAAQ;AACtI,aAAO,UAAU,KAAK;AAAA,IACxB;AAEA,sBAAkB;AAElB,QAAI,kBAAkB,CAAC,QAAQ,aAAa,UAAU,YAAY,SAAS,aAAa,UACtF,gBAAgB,SAAS,KAAK,cAAc,YAAY,UAAU,MAAM,gBAAgB,QAAQ,GAAG,MAAM,MAAM,SAAS,MAAM,gBAAgB,QAAQ,GAAG,IAAI;AAC7J,iBAAW,KAAK,cAAc,KAAK,MAAM,MAAM;AAC/C,iBAAW,QAAQ,MAAM;AACzB,oBAAc,eAAe;AAC7B,UAAI,SAAS;AAAe,eAAO;AAEnC,UAAI,QAAQ;AACV,mBAAW;AAEX,gBAAQ;AAER,aAAK,WAAW;AAEhB,sBAAc,QAAQ;AAEtB,YAAI,CAAC,SAAS,eAAe;AAC3B,cAAI,QAAQ;AACV,mBAAO,aAAa,QAAQ,MAAM;AAAA,UACpC,OAAO;AACL,mBAAO,YAAY,MAAM;AAAA,UAC3B;AAAA,QACF;AAEA,eAAO,UAAU,IAAI;AAAA,MACvB;AAEA,UAAI,cAAc,UAAU,IAAI,QAAQ,SAAS;AAEjD,UAAI,CAAC,eAAe,aAAa,KAAK,UAAU,IAAI,KAAK,CAAC,YAAY,UAAU;AAG9E,YAAI,gBAAgB,QAAQ;AAC1B,iBAAO,UAAU,KAAK;AAAA,QACxB;AAGA,YAAI,eAAe,OAAO,IAAI,QAAQ;AACpC,mBAAS;AAAA,QACX;AAEA,YAAI,QAAQ;AACV,uBAAa,QAAQ,MAAM;AAAA,QAC7B;AAEA,YAAI,QAAQ,QAAQ,IAAI,QAAQ,UAAU,QAAQ,YAAY,KAAK,CAAC,CAAC,MAAM,MAAM,OAAO;AACtF,kBAAQ;AACR,aAAG,YAAY,MAAM;AACrB,qBAAW;AAEX,kBAAQ;AACR,iBAAO,UAAU,IAAI;AAAA,QACvB;AAAA,MACF,WAAW,eAAe,cAAc,KAAK,UAAU,IAAI,GAAG;AAE5D,YAAI,aAAa,SAAS,IAAI,GAAG,SAAS,IAAI;AAE9C,YAAI,eAAe,QAAQ;AACzB,iBAAO,UAAU,KAAK;AAAA,QACxB;AAEA,iBAAS;AACT,qBAAa,QAAQ,MAAM;AAE3B,YAAI,QAAQ,QAAQ,IAAI,QAAQ,UAAU,QAAQ,YAAY,KAAK,KAAK,MAAM,OAAO;AACnF,kBAAQ;AACR,aAAG,aAAa,QAAQ,UAAU;AAClC,qBAAW;AAEX,kBAAQ;AACR,iBAAO,UAAU,IAAI;AAAA,QACvB;AAAA,MACF,WAAW,OAAO,eAAe,IAAI;AACnC,qBAAa,QAAQ,MAAM;AAC3B,YAAI,YAAY,GACZ,uBACA,iBAAiB,OAAO,eAAe,IACvC,kBAAkB,CAAC,mBAAmB,OAAO,YAAY,OAAO,UAAU,UAAU,OAAO,YAAY,OAAO,UAAU,YAAY,QAAQ,GAC5I,QAAQ,WAAW,QAAQ,QAC3B,kBAAkB,eAAe,QAAQ,OAAO,KAAK,KAAK,eAAe,QAAQ,OAAO,KAAK,GAC7F,eAAe,kBAAkB,gBAAgB,YAAY;AAEjE,YAAI,eAAe,QAAQ;AACzB,kCAAwB,WAAW,KAAK;AACxC,kCAAwB;AACxB,mCAAyB,CAAC,mBAAmB,QAAQ,cAAc;AAAA,QACrE;AAEA,oBAAY,kBAAkB,KAAK,QAAQ,YAAY,UAAU,kBAAkB,IAAI,QAAQ,eAAe,QAAQ,yBAAyB,OAAO,QAAQ,gBAAgB,QAAQ,uBAAuB,wBAAwB,eAAe,MAAM;AAC1P,YAAI;AAEJ,YAAI,cAAc,GAAG;AAEnB,cAAI,YAAY,MAAM,MAAM;AAE5B,aAAG;AACD,yBAAa;AACb,sBAAU,SAAS,SAAS,SAAS;AAAA,UACvC,SAAS,YAAY,IAAI,SAAS,SAAS,MAAM,UAAU,YAAY;AAAA,QACzE;AAGA,YAAI,cAAc,KAAK,YAAY,QAAQ;AACzC,iBAAO,UAAU,KAAK;AAAA,QACxB;AAEA,qBAAa;AACb,wBAAgB;AAChB,YAAI,cAAc,OAAO,oBACrB,QAAQ;AACZ,gBAAQ,cAAc;AAEtB,YAAI,aAAa,QAAQ,QAAQ,IAAI,QAAQ,UAAU,QAAQ,YAAY,KAAK,KAAK;AAErF,YAAI,eAAe,OAAO;AACxB,cAAI,eAAe,KAAK,eAAe,IAAI;AACzC,oBAAQ,eAAe;AAAA,UACzB;AAEA,oBAAU;AACV,qBAAW,WAAW,EAAE;AACxB,kBAAQ;AAER,cAAI,SAAS,CAAC,aAAa;AACzB,eAAG,YAAY,MAAM;AAAA,UACvB,OAAO;AACL,mBAAO,WAAW,aAAa,QAAQ,QAAQ,cAAc,MAAM;AAAA,UACrE;AAGA,cAAI,iBAAiB;AACnB,qBAAS,iBAAiB,GAAG,eAAe,gBAAgB,SAAS;AAAA,UACvE;AAEA,qBAAW,OAAO;AAGlB,cAAI,0BAA0B,UAAa,CAAC,wBAAwB;AAClE,iCAAqB,KAAK,IAAI,wBAAwB,QAAQ,MAAM,EAAE,KAAK,CAAC;AAAA,UAC9E;AAEA,kBAAQ;AACR,iBAAO,UAAU,IAAI;AAAA,QACvB;AAAA,MACF;AAEA,UAAI,GAAG,SAAS,MAAM,GAAG;AACvB,eAAO,UAAU,KAAK;AAAA,MACxB;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAAA,EACA,uBAAuB;AAAA,EACvB,gBAAgB,SAAS,iBAAiB;AACxC,QAAI,UAAU,aAAa,KAAK,YAAY;AAC5C,QAAI,UAAU,aAAa,KAAK,YAAY;AAC5C,QAAI,UAAU,eAAe,KAAK,YAAY;AAC9C,QAAI,UAAU,YAAY,6BAA6B;AACvD,QAAI,UAAU,aAAa,6BAA6B;AACxD,QAAI,UAAU,aAAa,6BAA6B;AAAA,EAC1D;AAAA,EACA,cAAc,SAAS,eAAe;AACpC,QAAI,gBAAgB,KAAK,GAAG;AAC5B,QAAI,eAAe,WAAW,KAAK,OAAO;AAC1C,QAAI,eAAe,YAAY,KAAK,OAAO;AAC3C,QAAI,eAAe,aAAa,KAAK,OAAO;AAC5C,QAAI,eAAe,eAAe,KAAK,OAAO;AAC9C,QAAI,UAAU,eAAe,IAAI;AAAA,EACnC;AAAA,EACA,SAAS,SAAS,QAElB,KAAK;AACH,QAAI,KAAK,KAAK,IACV,UAAU,KAAK;AAEnB,eAAW,MAAM,MAAM;AACvB,wBAAoB,MAAM,QAAQ,QAAQ,SAAS;AACnD,IAAAZ,aAAY,QAAQ,MAAM;AAAA,MACxB;AAAA,IACF,CAAC;AACD,eAAW,UAAU,OAAO;AAE5B,eAAW,MAAM,MAAM;AACvB,wBAAoB,MAAM,QAAQ,QAAQ,SAAS;AAEnD,QAAI,SAAS,eAAe;AAC1B,WAAK,SAAS;AAEd;AAAA,IACF;AAEA,0BAAsB;AACtB,6BAAyB;AACzB,4BAAwB;AACxB,kBAAc,KAAK,OAAO;AAC1B,iBAAa,KAAK,eAAe;AAEjC,oBAAgB,KAAK,OAAO;AAE5B,oBAAgB,KAAK,YAAY;AAGjC,QAAI,KAAK,iBAAiB;AACxB,UAAI,UAAU,QAAQ,IAAI;AAC1B,UAAI,IAAI,aAAa,KAAK,YAAY;AAAA,IACxC;AAEA,SAAK,eAAe;AAEpB,SAAK,aAAa;AAElB,QAAI,QAAQ;AACV,UAAI,SAAS,MAAM,eAAe,EAAE;AAAA,IACtC;AAEA,QAAI,QAAQ,aAAa,EAAE;AAE3B,QAAI,KAAK;AACP,UAAI,OAAO;AACT,YAAI,cAAc,IAAI,eAAe;AACrC,SAAC,QAAQ,cAAc,IAAI,gBAAgB;AAAA,MAC7C;AAEA,iBAAW,QAAQ,cAAc,QAAQ,WAAW,YAAY,OAAO;AAEvE,UAAI,WAAW,YAAY,eAAe,YAAY,gBAAgB,SAAS;AAE7E,mBAAW,QAAQ,cAAc,QAAQ,WAAW,YAAY,OAAO;AAAA,MACzE;AAEA,UAAI,QAAQ;AACV,YAAI,KAAK,iBAAiB;AACxB,cAAI,QAAQ,WAAW,IAAI;AAAA,QAC7B;AAEA,0BAAkB,MAAM;AAExB,eAAO,MAAM,aAAa,IAAI;AAG9B,YAAI,SAAS,CAAC,qBAAqB;AACjC,sBAAY,QAAQ,cAAc,YAAY,QAAQ,aAAa,KAAK,QAAQ,YAAY,KAAK;AAAA,QACnG;AAEA,oBAAY,QAAQ,KAAK,QAAQ,aAAa,KAAK;AAEnD,uBAAe;AAAA,UACb,UAAU;AAAA,UACV,MAAM;AAAA,UACN,MAAM;AAAA,UACN,UAAU;AAAA,UACV,mBAAmB;AAAA,UACnB,eAAe;AAAA,QACjB,CAAC;AAED,YAAI,WAAW,UAAU;AACvB,cAAI,YAAY,GAAG;AAEjB,2BAAe;AAAA,cACb,QAAQ;AAAA,cACR,MAAM;AAAA,cACN,MAAM;AAAA,cACN,QAAQ;AAAA,cACR,eAAe;AAAA,YACjB,CAAC;AAGD,2BAAe;AAAA,cACb,UAAU;AAAA,cACV,MAAM;AAAA,cACN,MAAM;AAAA,cACN,eAAe;AAAA,YACjB,CAAC;AAGD,2BAAe;AAAA,cACb,QAAQ;AAAA,cACR,MAAM;AAAA,cACN,MAAM;AAAA,cACN,QAAQ;AAAA,cACR,eAAe;AAAA,YACjB,CAAC;AAED,2BAAe;AAAA,cACb,UAAU;AAAA,cACV,MAAM;AAAA,cACN,MAAM;AAAA,cACN,eAAe;AAAA,YACjB,CAAC;AAAA,UACH;AAEA,yBAAe,YAAY,KAAK;AAAA,QAClC,OAAO;AACL,cAAI,aAAa,UAAU;AACzB,gBAAI,YAAY,GAAG;AAEjB,6BAAe;AAAA,gBACb,UAAU;AAAA,gBACV,MAAM;AAAA,gBACN,MAAM;AAAA,gBACN,eAAe;AAAA,cACjB,CAAC;AAED,6BAAe;AAAA,gBACb,UAAU;AAAA,gBACV,MAAM;AAAA,gBACN,MAAM;AAAA,gBACN,eAAe;AAAA,cACjB,CAAC;AAAA,YACH;AAAA,UACF;AAAA,QACF;AAEA,YAAI,SAAS,QAAQ;AAEnB,cAAI,YAAY,QAAQ,aAAa,IAAI;AACvC,uBAAW;AACX,gCAAoB;AAAA,UACtB;AAEA,yBAAe;AAAA,YACb,UAAU;AAAA,YACV,MAAM;AAAA,YACN,MAAM;AAAA,YACN,eAAe;AAAA,UACjB,CAAC;AAGD,eAAK,KAAK;AAAA,QACZ;AAAA,MACF;AAAA,IACF;AAEA,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,UAAU,SAAS,WAAW;AAC5B,IAAAA,aAAY,WAAW,IAAI;AAC3B,aAAS,SAAS,WAAW,UAAU,SAAS,UAAU,aAAa,cAAc,SAAS,WAAW,QAAQ,WAAW,oBAAoB,WAAW,oBAAoB,aAAa,gBAAgB,cAAc,cAAc,SAAS,UAAU,SAAS,QAAQ,SAAS,QAAQ,SAAS,SAAS;AAC/S,sBAAkB,QAAQ,SAAU,IAAI;AACtC,SAAG,UAAU;AAAA,IACf,CAAC;AACD,sBAAkB,SAAS,SAAS,SAAS;AAAA,EAC/C;AAAA,EACA,aAAa,SAAS,YAEtB,KAAK;AACH,YAAQ,IAAI,MAAM;AAAA,MAChB,KAAK;AAAA,MACL,KAAK;AACH,aAAK,QAAQ,GAAG;AAEhB;AAAA,MAEF,KAAK;AAAA,MACL,KAAK;AACH,YAAI,QAAQ;AACV,eAAK,YAAY,GAAG;AAEpB,0BAAgB,GAAG;AAAA,QACrB;AAEA;AAAA,MAEF,KAAK;AACH,YAAI,eAAe;AACnB;AAAA,IACJ;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,SAAS,SAAS,UAAU;AAC1B,QAAI,QAAQ,CAAC,GACT,IACA,WAAW,KAAK,GAAG,UACnB,IAAI,GACJ,IAAI,SAAS,QACb,UAAU,KAAK;AAEnB,WAAO,IAAI,GAAG,KAAK;AACjB,WAAK,SAAS,CAAC;AAEf,UAAI,QAAQ,IAAI,QAAQ,WAAW,KAAK,IAAI,KAAK,GAAG;AAClD,cAAM,KAAK,GAAG,aAAa,QAAQ,UAAU,KAAK,YAAY,EAAE,CAAC;AAAA,MACnE;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,SAAS,KAAK,OAAO,cAAc;AACvC,QAAI,QAAQ,CAAC,GACTP,UAAS,KAAK;AAClB,SAAK,QAAQ,EAAE,QAAQ,SAAU,IAAI,GAAG;AACtC,UAAI,KAAKA,QAAO,SAAS,CAAC;AAE1B,UAAI,QAAQ,IAAI,KAAK,QAAQ,WAAWA,SAAQ,KAAK,GAAG;AACtD,cAAM,EAAE,IAAI;AAAA,MACd;AAAA,IACF,GAAG,IAAI;AACP,oBAAgB,KAAK,sBAAsB;AAC3C,UAAM,QAAQ,SAAU,IAAI;AAC1B,UAAI,MAAM,EAAE,GAAG;AACb,QAAAA,QAAO,YAAY,MAAM,EAAE,CAAC;AAC5B,QAAAA,QAAO,YAAY,MAAM,EAAE,CAAC;AAAA,MAC9B;AAAA,IACF,CAAC;AACD,oBAAgB,KAAK,WAAW;AAAA,EAClC;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,SAAS,OAAO;AACpB,QAAI,QAAQ,KAAK,QAAQ;AACzB,aAAS,MAAM,OAAO,MAAM,IAAI,IAAI;AAAA,EACtC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,SAAS,SAAS,UAAU,IAAI,UAAU;AACxC,WAAO,QAAQ,IAAI,YAAY,KAAK,QAAQ,WAAW,KAAK,IAAI,KAAK;AAAA,EACvE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,QAAQ,SAAS,OAAO,MAAM,OAAO;AACnC,QAAI,UAAU,KAAK;AAEnB,QAAI,UAAU,QAAQ;AACpB,aAAO,QAAQ,IAAI;AAAA,IACrB,OAAO;AACL,UAAI,gBAAgB,cAAc,aAAa,MAAM,MAAM,KAAK;AAEhE,UAAI,OAAO,kBAAkB,aAAa;AACxC,gBAAQ,IAAI,IAAI;AAAA,MAClB,OAAO;AACL,gBAAQ,IAAI,IAAI;AAAA,MAClB;AAEA,UAAI,SAAS,SAAS;AACpB,sBAAc,OAAO;AAAA,MACvB;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,SAAS,SAAS,UAAU;AAC1B,IAAAO,aAAY,WAAW,IAAI;AAC3B,QAAI,KAAK,KAAK;AACd,OAAG,OAAO,IAAI;AACd,QAAI,IAAI,aAAa,KAAK,WAAW;AACrC,QAAI,IAAI,cAAc,KAAK,WAAW;AACtC,QAAI,IAAI,eAAe,KAAK,WAAW;AAEvC,QAAI,KAAK,iBAAiB;AACxB,UAAI,IAAI,YAAY,IAAI;AACxB,UAAI,IAAI,aAAa,IAAI;AAAA,IAC3B;AAGA,UAAM,UAAU,QAAQ,KAAK,GAAG,iBAAiB,aAAa,GAAG,SAAUa,KAAI;AAC7E,MAAAA,IAAG,gBAAgB,WAAW;AAAA,IAChC,CAAC;AAED,SAAK,QAAQ;AAEb,SAAK,0BAA0B;AAE/B,cAAU,OAAO,UAAU,QAAQ,KAAK,EAAE,GAAG,CAAC;AAC9C,SAAK,KAAK,KAAK;AAAA,EACjB;AAAA,EACA,YAAY,SAAS,aAAa;AAChC,QAAI,CAAC,aAAa;AAChB,MAAAb,aAAY,aAAa,IAAI;AAC7B,UAAI,SAAS;AAAe;AAC5B,UAAI,SAAS,WAAW,MAAM;AAE9B,UAAI,KAAK,QAAQ,qBAAqB,QAAQ,YAAY;AACxD,gBAAQ,WAAW,YAAY,OAAO;AAAA,MACxC;AAEA,oBAAc;AAAA,IAChB;AAAA,EACF;AAAA,EACA,YAAY,SAAS,WAAWD,cAAa;AAC3C,QAAIA,aAAY,gBAAgB,SAAS;AACvC,WAAK,WAAW;AAEhB;AAAA,IACF;AAEA,QAAI,aAAa;AACf,MAAAC,aAAY,aAAa,IAAI;AAC7B,UAAI,SAAS;AAAe;AAE5B,UAAI,OAAO,cAAc,UAAU,CAAC,KAAK,QAAQ,MAAM,aAAa;AAClE,eAAO,aAAa,SAAS,MAAM;AAAA,MACrC,WAAW,QAAQ;AACjB,eAAO,aAAa,SAAS,MAAM;AAAA,MACrC,OAAO;AACL,eAAO,YAAY,OAAO;AAAA,MAC5B;AAEA,UAAI,KAAK,QAAQ,MAAM,aAAa;AAClC,aAAK,QAAQ,QAAQ,OAAO;AAAA,MAC9B;AAEA,UAAI,SAAS,WAAW,EAAE;AAC1B,oBAAc;AAAA,IAChB;AAAA,EACF;AACF;AAEA,SAAS,gBAET,KAAK;AACH,MAAI,IAAI,cAAc;AACpB,QAAI,aAAa,aAAa;AAAA,EAChC;AAEA,MAAI,cAAc,IAAI,eAAe;AACvC;AAEA,SAAS,QAAQ,QAAQ,MAAMK,SAAQ,UAAU,UAAU,YAAY,eAAe,iBAAiB;AACrG,MAAI,KACA,WAAW,OAAO,OAAO,GACzB,WAAW,SAAS,QAAQ,QAC5B;AAEJ,MAAI,OAAO,eAAe,CAAC,cAAc,CAAC,MAAM;AAC9C,UAAM,IAAI,YAAY,QAAQ;AAAA,MAC5B,SAAS;AAAA,MACT,YAAY;AAAA,IACd,CAAC;AAAA,EACH,OAAO;AACL,UAAM,SAAS,YAAY,OAAO;AAClC,QAAI,UAAU,QAAQ,MAAM,IAAI;AAAA,EAClC;AAEA,MAAI,KAAK;AACT,MAAI,OAAO;AACX,MAAI,UAAUA;AACd,MAAI,cAAc;AAClB,MAAI,UAAU,YAAY;AAC1B,MAAI,cAAc,cAAc,QAAQ,IAAI;AAC5C,MAAI,kBAAkB;AACtB,MAAI,gBAAgB;AACpB,SAAO,cAAc,GAAG;AAExB,MAAI,UAAU;AACZ,aAAS,SAAS,KAAK,UAAU,KAAK,aAAa;AAAA,EACrD;AAEA,SAAO;AACT;AAEA,SAAS,kBAAkB,IAAI;AAC7B,KAAG,YAAY;AACjB;AAEA,SAAS,YAAY;AACnB,YAAU;AACZ;AAEA,SAAS,cAAc,KAAK,UAAU,UAAU;AAC9C,MAAI,OAAO,QAAQ,SAAS,SAAS,IAAI,GAAG,SAAS,SAAS,IAAI,CAAC;AACnE,MAAI,SAAS;AACb,SAAO,WAAW,IAAI,UAAU,KAAK,OAAO,UAAU,IAAI,UAAU,KAAK,OAAO,IAAI,UAAU,KAAK,QAAQ,IAAI,UAAU,KAAK,MAAM,UAAU,IAAI,UAAU,KAAK,UAAU,IAAI,UAAU,KAAK;AAChM;AAEA,SAAS,aAAa,KAAK,UAAU,UAAU;AAC7C,MAAI,OAAO,QAAQ,UAAU,SAAS,IAAI,SAAS,QAAQ,SAAS,CAAC;AACrE,MAAI,SAAS;AACb,SAAO,WAAW,IAAI,UAAU,KAAK,QAAQ,UAAU,IAAI,WAAW,KAAK,SAAS,IAAI,UAAU,KAAK,UAAU,IAAI,WAAW,KAAK,OAAO,IAAI,UAAU,KAAK,SAAS,IAAI,UAAU,KAAK,OAAO,IAAI,WAAW,KAAK,SAAS,IAAI,UAAU,KAAK,SAAS;AAC7P;AAEA,SAAS,kBAAkB,KAAK,QAAQ,YAAY,UAAU,eAAe,uBAAuB,YAAY,cAAc;AAC5H,MAAI,cAAc,WAAW,IAAI,UAAU,IAAI,SAC3C,eAAe,WAAW,WAAW,SAAS,WAAW,OACzD,WAAW,WAAW,WAAW,MAAM,WAAW,MAClD,WAAW,WAAW,WAAW,SAAS,WAAW,OACrD,SAAS;AAEb,MAAI,CAAC,YAAY;AAEf,QAAI,gBAAgB,qBAAqB,eAAe,eAAe;AAGrE,UAAI,CAAC,0BAA0B,kBAAkB,IAAI,cAAc,WAAW,eAAe,wBAAwB,IAAI,cAAc,WAAW,eAAe,wBAAwB,IAAI;AAE3L,gCAAwB;AAAA,MAC1B;AAEA,UAAI,CAAC,uBAAuB;AAE1B,YAAI,kBAAkB,IAAI,cAAc,WAAW,qBACjD,cAAc,WAAW,oBAAoB;AAC7C,iBAAO,CAAC;AAAA,QACV;AAAA,MACF,OAAO;AACL,iBAAS;AAAA,MACX;AAAA,IACF,OAAO;AAEL,UAAI,cAAc,WAAW,gBAAgB,IAAI,iBAAiB,KAAK,cAAc,WAAW,gBAAgB,IAAI,iBAAiB,GAAG;AACtI,eAAO,oBAAoB,MAAM;AAAA,MACnC;AAAA,IACF;AAAA,EACF;AAEA,WAAS,UAAU;AAEnB,MAAI,QAAQ;AAEV,QAAI,cAAc,WAAW,eAAe,wBAAwB,KAAK,cAAc,WAAW,eAAe,wBAAwB,GAAG;AAC1I,aAAO,cAAc,WAAW,eAAe,IAAI,IAAI;AAAA,IACzD;AAAA,EACF;AAEA,SAAO;AACT;AASA,SAAS,oBAAoB,QAAQ;AACnC,MAAI,MAAM,MAAM,IAAI,MAAM,MAAM,GAAG;AACjC,WAAO;AAAA,EACT,OAAO;AACL,WAAO;AAAA,EACT;AACF;AASA,SAAS,YAAY,IAAI;AACvB,MAAI,MAAM,GAAG,UAAU,GAAG,YAAY,GAAG,MAAM,GAAG,OAAO,GAAG,aACxD,IAAI,IAAI,QACR,MAAM;AAEV,SAAO,KAAK;AACV,WAAO,IAAI,WAAW,CAAC;AAAA,EACzB;AAEA,SAAO,IAAI,SAAS,EAAE;AACxB;AAEA,SAAS,uBAAuB,MAAM;AACpC,oBAAkB,SAAS;AAC3B,MAAI,SAAS,KAAK,qBAAqB,OAAO;AAC9C,MAAI,MAAM,OAAO;AAEjB,SAAO,OAAO;AACZ,QAAI,KAAK,OAAO,GAAG;AACnB,OAAG,WAAW,kBAAkB,KAAK,EAAE;AAAA,EACzC;AACF;AAEA,SAAS,UAAU,IAAI;AACrB,SAAO,WAAW,IAAI,CAAC;AACzB;AAEA,SAAS,gBAAgB,IAAI;AAC3B,SAAO,aAAa,EAAE;AACxB;AAGA,IAAI,gBAAgB;AAClB,KAAG,UAAU,aAAa,SAAU,KAAK;AACvC,SAAK,SAAS,UAAU,wBAAwB,IAAI,YAAY;AAC9D,UAAI,eAAe;AAAA,IACrB;AAAA,EACF,CAAC;AACH;AAGA,SAAS,QAAQ;AAAA,EACf;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,IAAI,SAAS,GAAG,IAAI,UAAU;AAC5B,WAAO,CAAC,CAAC,QAAQ,IAAI,UAAU,IAAI,KAAK;AAAA,EAC1C;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,UAAU;AAAA,EACV,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB;AACF;AAOA,SAAS,MAAM,SAAU,SAAS;AAChC,SAAO,QAAQ,OAAO;AACxB;AAOA,SAAS,QAAQ,WAAY;AAC3B,WAAS,OAAO,UAAU,QAAQS,WAAU,IAAI,MAAM,IAAI,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AAC1F,IAAAA,SAAQ,IAAI,IAAI,UAAU,IAAI;AAAA,EAChC;AAEA,MAAIA,SAAQ,CAAC,EAAE,gBAAgB;AAAO,IAAAA,WAAUA,SAAQ,CAAC;AACzD,EAAAA,SAAQ,QAAQ,SAAU,QAAQ;AAChC,QAAI,CAAC,OAAO,aAAa,CAAC,OAAO,UAAU,aAAa;AACtD,YAAM,gEAAgE,OAAO,CAAC,EAAE,SAAS,KAAK,MAAM,CAAC;AAAA,IACvG;AAEA,QAAI,OAAO;AAAO,eAAS,QAAQ,eAAe,eAAe,CAAC,GAAG,SAAS,KAAK,GAAG,OAAO,KAAK;AAClG,kBAAc,MAAM,MAAM;AAAA,EAC5B,CAAC;AACH;AAQA,SAAS,SAAS,SAAU,IAAI,SAAS;AACvC,SAAO,IAAI,SAAS,IAAI,OAAO;AACjC;AAGA,SAAS,UAAU;AAEnB,IAAI,cAAc,CAAC;AAAnB,IACI;AADJ,IAEI;AAFJ,IAGI,YAAY;AAHhB,IAII;AAJJ,IAKI;AALJ,IAMI;AANJ,IAOI;AAEJ,SAAS,mBAAmB;AAC1B,WAAS,aAAa;AACpB,SAAK,WAAW;AAAA,MACd,QAAQ;AAAA,MACR,yBAAyB;AAAA,MACzB,mBAAmB;AAAA,MACnB,aAAa;AAAA,MACb,cAAc;AAAA,IAChB;AAEA,aAAS,MAAM,MAAM;AACnB,UAAI,GAAG,OAAO,CAAC,MAAM,OAAO,OAAO,KAAK,EAAE,MAAM,YAAY;AAC1D,aAAK,EAAE,IAAI,KAAK,EAAE,EAAE,KAAK,IAAI;AAAA,MAC/B;AAAA,IACF;AAAA,EACF;AAEA,aAAW,YAAY;AAAA,IACrB,aAAa,SAAS,YAAY,MAAM;AACtC,UAAI,gBAAgB,KAAK;AAEzB,UAAI,KAAK,SAAS,iBAAiB;AACjC,WAAG,UAAU,YAAY,KAAK,iBAAiB;AAAA,MACjD,OAAO;AACL,YAAI,KAAK,QAAQ,gBAAgB;AAC/B,aAAG,UAAU,eAAe,KAAK,yBAAyB;AAAA,QAC5D,WAAW,cAAc,SAAS;AAChC,aAAG,UAAU,aAAa,KAAK,yBAAyB;AAAA,QAC1D,OAAO;AACL,aAAG,UAAU,aAAa,KAAK,yBAAyB;AAAA,QAC1D;AAAA,MACF;AAAA,IACF;AAAA,IACA,mBAAmB,SAAS,kBAAkB,OAAO;AACnD,UAAI,gBAAgB,MAAM;AAG1B,UAAI,CAAC,KAAK,QAAQ,kBAAkB,CAAC,cAAc,QAAQ;AACzD,aAAK,kBAAkB,aAAa;AAAA,MACtC;AAAA,IACF;AAAA,IACA,MAAM,SAASC,QAAO;AACpB,UAAI,KAAK,SAAS,iBAAiB;AACjC,YAAI,UAAU,YAAY,KAAK,iBAAiB;AAAA,MAClD,OAAO;AACL,YAAI,UAAU,eAAe,KAAK,yBAAyB;AAC3D,YAAI,UAAU,aAAa,KAAK,yBAAyB;AACzD,YAAI,UAAU,aAAa,KAAK,yBAAyB;AAAA,MAC3D;AAEA,sCAAgC;AAChC,uBAAiB;AACjB,qBAAe;AAAA,IACjB;AAAA,IACA,SAAS,SAAS,UAAU;AAC1B,mBAAa,eAAe,WAAW,YAAY,6BAA6B,kBAAkB,kBAAkB;AACpH,kBAAY,SAAS;AAAA,IACvB;AAAA,IACA,2BAA2B,SAAS,0BAA0B,KAAK;AACjE,WAAK,kBAAkB,KAAK,IAAI;AAAA,IAClC;AAAA,IACA,mBAAmB,SAAS,kBAAkB,KAAK,UAAU;AAC3D,UAAI,QAAQ;AAEZ,UAAI,KAAK,IAAI,UAAU,IAAI,QAAQ,CAAC,IAAI,KAAK,SACzC,KAAK,IAAI,UAAU,IAAI,QAAQ,CAAC,IAAI,KAAK,SACzC,OAAO,SAAS,iBAAiB,GAAG,CAAC;AACzC,mBAAa;AAKb,UAAI,YAAY,KAAK,QAAQ,2BAA2B,QAAQ,cAAc,QAAQ;AACpF,mBAAW,KAAK,KAAK,SAAS,MAAM,QAAQ;AAE5C,YAAI,iBAAiB,2BAA2B,MAAM,IAAI;AAE1D,YAAI,cAAc,CAAC,8BAA8B,MAAM,mBAAmB,MAAM,kBAAkB;AAChG,wCAA8B,gCAAgC;AAE9D,uCAA6B,YAAY,WAAY;AACnD,gBAAI,UAAU,2BAA2B,SAAS,iBAAiB,GAAG,CAAC,GAAG,IAAI;AAE9E,gBAAI,YAAY,gBAAgB;AAC9B,+BAAiB;AACjB,+BAAiB;AAAA,YACnB;AAEA,uBAAW,KAAK,MAAM,SAAS,SAAS,QAAQ;AAAA,UAClD,GAAG,EAAE;AACL,4BAAkB;AAClB,4BAAkB;AAAA,QACpB;AAAA,MACF,OAAO;AAEL,YAAI,CAAC,KAAK,QAAQ,gBAAgB,2BAA2B,MAAM,IAAI,MAAM,0BAA0B,GAAG;AACxG,2BAAiB;AACjB;AAAA,QACF;AAEA,mBAAW,KAAK,KAAK,SAAS,2BAA2B,MAAM,KAAK,GAAG,KAAK;AAAA,MAC9E;AAAA,IACF;AAAA,EACF;AACA,SAAO,SAAS,YAAY;AAAA,IAC1B,YAAY;AAAA,IACZ,qBAAqB;AAAA,EACvB,CAAC;AACH;AAEA,SAAS,mBAAmB;AAC1B,cAAY,QAAQ,SAAUC,aAAY;AACxC,kBAAcA,YAAW,GAAG;AAAA,EAC9B,CAAC;AACD,gBAAc,CAAC;AACjB;AAEA,SAAS,kCAAkC;AACzC,gBAAc,0BAA0B;AAC1C;AAEA,IAAI,aAAa,SAAS,SAAU,KAAK,SAASvB,SAAQ,YAAY;AAEpE,MAAI,CAAC,QAAQ;AAAQ;AACrB,MAAI,KAAK,IAAI,UAAU,IAAI,QAAQ,CAAC,IAAI,KAAK,SACzC,KAAK,IAAI,UAAU,IAAI,QAAQ,CAAC,IAAI,KAAK,SACzC,OAAO,QAAQ,mBACf,QAAQ,QAAQ,aAChB,cAAc,0BAA0B;AAC5C,MAAI,qBAAqB,OACrB;AAEJ,MAAI,iBAAiBA,SAAQ;AAC3B,mBAAeA;AACf,qBAAiB;AACjB,eAAW,QAAQ;AACnB,qBAAiB,QAAQ;AAEzB,QAAI,aAAa,MAAM;AACrB,iBAAW,2BAA2BA,SAAQ,IAAI;AAAA,IACpD;AAAA,EACF;AAEA,MAAI,YAAY;AAChB,MAAI,gBAAgB;AAEpB,KAAG;AACD,QAAI,KAAK,eACL,OAAO,QAAQ,EAAE,GACjB,MAAM,KAAK,KACX,SAAS,KAAK,QACd,OAAO,KAAK,MACZ,QAAQ,KAAK,OACb,QAAQ,KAAK,OACb,SAAS,KAAK,QACd,aAAa,QACb,aAAa,QACb,cAAc,GAAG,aACjB,eAAe,GAAG,cAClB,QAAQ,IAAI,EAAE,GACd,aAAa,GAAG,YAChB,aAAa,GAAG;AAEpB,QAAI,OAAO,aAAa;AACtB,mBAAa,QAAQ,gBAAgB,MAAM,cAAc,UAAU,MAAM,cAAc,YAAY,MAAM,cAAc;AACvH,mBAAa,SAAS,iBAAiB,MAAM,cAAc,UAAU,MAAM,cAAc,YAAY,MAAM,cAAc;AAAA,IAC3H,OAAO;AACL,mBAAa,QAAQ,gBAAgB,MAAM,cAAc,UAAU,MAAM,cAAc;AACvF,mBAAa,SAAS,iBAAiB,MAAM,cAAc,UAAU,MAAM,cAAc;AAAA,IAC3F;AAEA,QAAI,KAAK,eAAe,KAAK,IAAI,QAAQ,CAAC,KAAK,QAAQ,aAAa,QAAQ,gBAAgB,KAAK,IAAI,OAAO,CAAC,KAAK,QAAQ,CAAC,CAAC;AAC5H,QAAI,KAAK,eAAe,KAAK,IAAI,SAAS,CAAC,KAAK,QAAQ,aAAa,SAAS,iBAAiB,KAAK,IAAI,MAAM,CAAC,KAAK,QAAQ,CAAC,CAAC;AAE9H,QAAI,CAAC,YAAY,SAAS,GAAG;AAC3B,eAAS,IAAI,GAAG,KAAK,WAAW,KAAK;AACnC,YAAI,CAAC,YAAY,CAAC,GAAG;AACnB,sBAAY,CAAC,IAAI,CAAC;AAAA,QACpB;AAAA,MACF;AAAA,IACF;AAEA,QAAI,YAAY,SAAS,EAAE,MAAM,MAAM,YAAY,SAAS,EAAE,MAAM,MAAM,YAAY,SAAS,EAAE,OAAO,IAAI;AAC1G,kBAAY,SAAS,EAAE,KAAK;AAC5B,kBAAY,SAAS,EAAE,KAAK;AAC5B,kBAAY,SAAS,EAAE,KAAK;AAC5B,oBAAc,YAAY,SAAS,EAAE,GAAG;AAExC,UAAI,MAAM,KAAK,MAAM,GAAG;AACtB,6BAAqB;AAGrB,oBAAY,SAAS,EAAE,MAAM,aAAY,WAAY;AAEnD,cAAI,cAAc,KAAK,UAAU,GAAG;AAClC,qBAAS,OAAO,aAAa,UAAU;AAAA,UAEzC;AAEA,cAAI,gBAAgB,YAAY,KAAK,KAAK,EAAE,KAAK,YAAY,KAAK,KAAK,EAAE,KAAK,QAAQ;AACtF,cAAI,gBAAgB,YAAY,KAAK,KAAK,EAAE,KAAK,YAAY,KAAK,KAAK,EAAE,KAAK,QAAQ;AAEtF,cAAI,OAAO,mBAAmB,YAAY;AACxC,gBAAI,eAAe,KAAK,SAAS,QAAQ,WAAW,OAAO,GAAG,eAAe,eAAe,KAAK,YAAY,YAAY,KAAK,KAAK,EAAE,EAAE,MAAM,YAAY;AACvJ;AAAA,YACF;AAAA,UACF;AAEA,mBAAS,YAAY,KAAK,KAAK,EAAE,IAAI,eAAe,aAAa;AAAA,QACnE,GAAE,KAAK;AAAA,UACL,OAAO;AAAA,QACT,CAAC,GAAG,EAAE;AAAA,MACR;AAAA,IACF;AAEA;AAAA,EACF,SAAS,QAAQ,gBAAgB,kBAAkB,gBAAgB,gBAAgB,2BAA2B,eAAe,KAAK;AAElI,cAAY;AACd,GAAG,EAAE;AAEL,IAAI,OAAO,SAASsB,MAAK,MAAM;AAC7B,MAAI,gBAAgB,KAAK,eACrBhB,eAAc,KAAK,aACnBM,UAAS,KAAK,QACd,iBAAiB,KAAK,gBACtB,wBAAwB,KAAK,uBAC7B,qBAAqB,KAAK,oBAC1B,uBAAuB,KAAK;AAChC,MAAI,CAAC;AAAe;AACpB,MAAI,aAAaN,gBAAe;AAChC,qBAAmB;AACnB,MAAI,QAAQ,cAAc,kBAAkB,cAAc,eAAe,SAAS,cAAc,eAAe,CAAC,IAAI;AACpH,MAAI,SAAS,SAAS,iBAAiB,MAAM,SAAS,MAAM,OAAO;AACnE,uBAAqB;AAErB,MAAI,cAAc,CAAC,WAAW,GAAG,SAAS,MAAM,GAAG;AACjD,0BAAsB,OAAO;AAC7B,SAAK,QAAQ;AAAA,MACX,QAAQM;AAAA,MACR,aAAaN;AAAA,IACf,CAAC;AAAA,EACH;AACF;AAEA,SAAS,SAAS;AAAC;AAEnB,OAAO,YAAY;AAAA,EACjB,YAAY;AAAA,EACZ,WAAW,SAAS,UAAU,OAAO;AACnC,QAAIF,qBAAoB,MAAM;AAC9B,SAAK,aAAaA;AAAA,EACpB;AAAA,EACA,SAAS,SAAS,QAAQ,OAAO;AAC/B,QAAIQ,UAAS,MAAM,QACfN,eAAc,MAAM;AACxB,SAAK,SAAS,sBAAsB;AAEpC,QAAIA,cAAa;AACf,MAAAA,aAAY,sBAAsB;AAAA,IACpC;AAEA,QAAI,cAAc,SAAS,KAAK,SAAS,IAAI,KAAK,YAAY,KAAK,OAAO;AAE1E,QAAI,aAAa;AACf,WAAK,SAAS,GAAG,aAAaM,SAAQ,WAAW;AAAA,IACnD,OAAO;AACL,WAAK,SAAS,GAAG,YAAYA,OAAM;AAAA,IACrC;AAEA,SAAK,SAAS,WAAW;AAEzB,QAAIN,cAAa;AACf,MAAAA,aAAY,WAAW;AAAA,IACzB;AAAA,EACF;AAAA,EACA;AACF;AAEA,SAAS,QAAQ;AAAA,EACf,YAAY;AACd,CAAC;AAED,SAAS,SAAS;AAAC;AAEnB,OAAO,YAAY;AAAA,EACjB,SAAS,SAASkB,SAAQ,OAAO;AAC/B,QAAIZ,UAAS,MAAM,QACfN,eAAc,MAAM;AACxB,QAAI,iBAAiBA,gBAAe,KAAK;AACzC,mBAAe,sBAAsB;AACrC,IAAAM,QAAO,cAAcA,QAAO,WAAW,YAAYA,OAAM;AACzD,mBAAe,WAAW;AAAA,EAC5B;AAAA,EACA;AACF;AAEA,SAAS,QAAQ;AAAA,EACf,YAAY;AACd,CAAC;AAwsBD,SAAS,MAAM,IAAI,iBAAiB,CAAC;AACrC,SAAS,MAAM,QAAQ,MAAM;AAE7B,IAAO,uBAAQ;;;AC3rHf,SAAS,WAAW,MAAM;AACxB,MAAI,KAAK,kBAAkB,MAAM;AAC/B,SAAK,cAAc,YAAY,IAAI;AAAA,EACrC;AACF;AAEA,SAAS,aAAa,YAAY,MAAM,UAAU;AAChD,QAAM,UACJ,aAAa,IACT,WAAW,SAAS,CAAC,IACrB,WAAW,SAAS,WAAW,CAAC,EAAE;AACxC,aAAW,aAAa,MAAM,OAAO;AACvC;;;ACZA,SAAS,aAAa;AACpB,MAAI,OAAO,WAAW,aAAa;AACjC,WAAO,OAAO;AAAA,EAChB;AACA,SAAO,OAAO;AAChB;AACA,IAAM,UAAU,WAAW;;;ACN3B,SAAS,OAAO,IAAI;AAClB,QAAM,QAAQ,uBAAO,OAAO,IAAI;AAChC,SAAO,SAAS,SAAS,KAAK;AAC5B,UAAM,MAAM,MAAM,GAAG;AACrB,WAAO,QAAQ,MAAM,GAAG,IAAI,GAAG,GAAG;AAAA,EACpC;AACF;AAEA,IAAM,QAAQ;AACd,IAAM,WAAW,OAAO,SAAO,IAAI,QAAQ,OAAO,CAAC,GAAG,MAAM,EAAE,YAAY,CAAC,CAAC;;;ACT5E,IAAM,gBAAgB,CAAC,SAAS,OAAO,UAAU,UAAU,KAAK;AAChE,IAAM,OAAO,CAAC,UAAU,YAAY,QAAQ,UAAU,OAAO;AAC7D,IAAM,SAAS,CAAC,MAAM;AACtB,IAAM,oBAAoB,CAAC,QAAQ,eAAe,IAAI,EACnD,QAAQ,CAAAa,YAAUA,OAAM,EACxB,IAAI,SAAO,KAAK,GAAG,EAAE;AAExB,IAAM,SAAS;AAAA,EACb;AAAA,EACA;AAAA,EACA;AACF;AAEA,SAAS,WAAW,WAAW;AAC7B,SAAO,kBAAkB,QAAQ,SAAS,MAAM;AAClD;;;ACfA,IAAM,OAAO;AAAA,EACX;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAEA,SAAS,UAAU,MAAM;AACvB,SAAO,KAAK,SAAS,IAAI;AAC3B;AAEA,SAAS,aAAa,MAAM;AAC1B,SAAO,CAAC,oBAAoB,iBAAiB,EAAE,SAAS,IAAI;AAC9D;AAEA,SAAS,gBAAgB,OAAO;AAC9B,SACE,CAAC,MAAM,SAAS,QAAQ,OAAO,EAAE,SAAS,KAAK,KAC/C,MAAM,WAAW,OAAO,KACxB,MAAM,WAAW,OAAO,KACxB,MAAM,WAAW,IAAI;AAEzB;;;ACnIA,SAAS,QAAQ,SAAS;AACxB,SAAO,QAAQ,OAAO,CAAC,KAAK,CAAC,KAAK,KAAK,MAAM;AAC3C,QAAI,GAAG,IAAI;AACX,WAAO;AAAA,EACT,GAAG,CAAC,CAAC;AACP;AAEA,SAAS,uBAAuB,EAAE,QAAQ,gBAAgB,CAAC,EAAE,GAAG;AAC9D,QAAM,aAAa;AAAA,IACjB,OAAO,QAAQ,MAAM,EAAE,OAAO,CAAC,CAAC,KAAK,CAAC,MAAM,gBAAgB,GAAG,CAAC;AAAA,EAClE;AACA,SAAO;AAAA,IACL,GAAG;AAAA,IACH,GAAG;AAAA,EACL;AACF;AAEA,SAAS,qBAAqB,EAAE,QAAQ,gBAAgB,GAAG;AACzD,QAAM,UAAU,QAAQ,wBAAwB,MAAM,CAAC;AACvD,SAAO,QAAQ,eAAe,EAAE,QAAQ,CAAC,CAAC,WAAW,YAAY,MAAM;AACrE,WAAO,SAAS,EAAE,QAAQ,WAAS;AACjC,cAAQ,KAAK,KAAK,EAAE,IAAI,aAAa,KAAK;AAAA,IAC5C,CAAC;AAAA,EACH,CAAC;AACD,QAAM,YAAY,mBAAmB,QAAQ,aAAa,EAAE;AAC5D,SAAO;AAAA,IACL,GAAG;AAAA,IACH;AAAA,EACF;AACF;AAEA,SAAS,wBAAwB,OAAO;AACtC,SAAO,OAAO,QAAQ,KAAK,EACxB,OAAO,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,gBAAgB,GAAG,CAAC,EAC1C,IAAI,CAAC,CAAC,KAAKC,MAAK,MAAM,CAAC,SAAS,GAAG,GAAGA,MAAK,CAAC,EAC5C,OAAO,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,WAAW,GAAG,CAAC;AAC1C;;;ACxCA,IAAM,yBAAyB,CAAC,EAAE,GAAG,MAAM;AAC3C,IAAM,aAAa,CAAC,YAAY,YAC7B,WAAW,sBAAsB;AACpC,IAAM,aAAa,gBAAc,WAAW;AAE5C,IAAM,qBAAN,MAAyB;AAAA,EACvB,YAAY;AAAA,IACV,OAAO,EAAE,QAAQ,SAAS,cAAc,OAAO;AAAA,IAC/C;AAAA,IACA;AAAA,EACF,GAAG;AACD,SAAK,eAAe;AACpB,SAAK,WAAW,CAAC,GAAG,QAAQ,GAAG,cAAc,GAAG,MAAM;AACtD,SAAK,oBAAoB,KAAK;AAC9B,SAAK,iBAAiB,KAAK;AAC3B,SAAK,MAAM,KAAK;AAChB,SAAK,WAAW;AAAA,EAClB;AAAA,EAEA,IAAI,mBAAmB;AACrB,WAAO,KAAK,qBAAqB,KAAK;AAAA,EACxC;AAAA,EAEA,OAAOC,IAAG,YAAY;AACpB,UAAM,EAAE,KAAK,UAAU,iBAAiB,IAAI;AAC5C,UAAMC,UAAS,CAAC,mBAAmB,WAAW,EAAE,SAAS,MAAM,SAAS;AACxE,WAAOD,GAAE,KAAK,YAAYC,OAAM;AAAA,EAClC;AAAA,EAEA,UAAU;AACR,UAAM,EAAE,cAAc,SAAS,IAAI;AACnC,iBAAa,QAAQ,CAAC,MAAMC,WAAU;AACpC,iBAAW,uBAAuB,IAAI,GAAG;AAAA,QACvC,SAAS,SAASA,MAAK;AAAA,QACvB,OAAAA;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA,EAEA,gBAAgB,YAAY;AAC1B,WAAO,WAAW,UAAU;AAAA,EAC9B;AAAA,EAEA,uBAAuB,UAAU,SAAS;AACxC,UAAM,EAAE,aAAa,IAAI;AACzB,UAAM,EAAE,OAAO,IAAI;AACnB,UAAM,cAAc,QAAQ;AAC5B,UAAM,aAAa,YAAY,KAAK,QAAQ;AAE5C,QAAI,eAAe,MAAM;AACvB,aAAO;AAAA,IACT;AACA,UAAM,UAAU,WAAW,UAAU;AACrC,QAAI,SAAS;AACX,aAAO,QAAQ;AAAA,IACjB;AAEA,QAAI,WAAW,GAAG;AAChB,aAAO;AAAA,IACT;AACA,UAAM,sBAAsB,uBAAuB,aAAa,CAAC,CAAC;AAClE,UAAM,2BAA2B,CAAC,GAAG,WAAW,EAAE;AAAA,MAChD,CAAAC,aAAWA,aAAY;AAAA,IACzB;AACA,WAAO,WAAW,2BAA2B,IAAI;AAAA,EACnD;AACF;;;AC9DA,SAAS,QAAQ,OAAO,KAAK;AAC3B,QAAM,YAAY,MAAM,GAAG;AAC3B,SAAO,YAAY,UAAU,IAAI,CAAC;AACpC;AAEA,SAAS,aAAa,EAAE,QAAQ,UAAU,OAAO,GAAG;AAClD,QAAM,iBAAiB,YAAY,CAAC;AACpC,QAAM,CAAC,QAAQ,MAAM,IAAI,CAAC,UAAU,QAAQ,EAAE;AAAA,IAAI,UAChD,QAAQ,QAAQ,IAAI;AAAA,EACtB;AACA,QAAM,EAAE,KAAK,IAAI;AACjB,MAAI,CAAC,MAAM;AACT,UAAM,IAAI,MAAM,0CAA0C;AAAA,EAC5D;AACA,QAAM,eAAe,eAAe;AAAA,IAAQ,CAAC,SAASC,WACpD,KAAK,EAAE,SAAS,OAAAA,OAAM,CAAC,EAAE,IAAI,UAAQ;AACnC,WAAK,MAAM,OAAO,OAAO;AACzB,WAAK,QAAQ,EAAE,GAAI,KAAK,SAAS,CAAC,GAAI,kBAAkB,KAAK;AAC7D,aAAO;AAAA,IACT,CAAC;AAAA,EACH;AACA,MAAI,aAAa,WAAW,eAAe,QAAQ;AACjD,UAAM,IAAI,MAAM,oCAAoC;AAAA,EACtD;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,SAAS;AAAA,EACX;AACF;AAEA,SAAS,mBAAmB,KAAK;AAC/B,QAAM,aAAa,aAAa,GAAG;AACnC,QAAM,oBAAoB,CAAC,UAAU,GAAG,KAAK,CAAC;AAC9C,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,KAAK,oBACD,iBAAiB,GAAG,IACpB,aACA,kBACA;AAAA,EACN;AACF;AAEA,SAAS,0BAA0B,EAAE,QAAQ,KAAK,UAAU,OAAO,GAAG;AACpE,QAAM,QAAQ,aAAa,EAAE,QAAQ,UAAU,OAAO,CAAC;AACvD,QAAM,OAAO,mBAAmB,GAAG;AACnC,SAAO,IAAI,mBAAmB,EAAE,OAAO,MAAM,SAAS,CAAC;AACzD;;;ACzCA,SAASC,MAAK,SAAS,SAAS;AAC9B,WAAS,MAAM,KAAK,MAAM,QAAQ,YAAY,GAAG,OAAO,CAAC;AAC3D;AAEA,SAASC,QAAO,SAAS;AACvB,SAAO,CAAC,SAAS,oBAAoB;AACnC,QAAI,KAAK,aAAa,MAAM;AAC1B,aAAO,KAAK,SAAS,OAAO,EAAE,EAAE,SAAS,eAAe;AAAA,IAC1D;AAAA,EACF;AACF;AAEA,SAASC,eAAc,SAAS;AAC9B,QAAM,mBAAmBD,QAAO,KAAK,MAAM,OAAO;AAClD,SAAO,CAAC,SAAS,oBAAoB;AACnC,qBAAiB,KAAK,MAAM,SAAS,eAAe;AACpD,IAAAD,MAAK,KAAK,MAAM,SAAS,OAAO;AAAA,EAClC;AACF;AAEA,IAAI,kBAAkB;AAEtB,IAAM,QAAQ;AAAA,EACZ,MAAM;AAAA,IACJ,MAAM;AAAA,IACN,UAAU;AAAA,IACV,SAAS;AAAA,EACX;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,IACN,UAAU;AAAA,IACV,SAAS;AAAA,EACX;AAAA,EACA,SAAS;AAAA,IACP,MAAM,CAAC,QAAQ,QAAQ;AAAA,IACvB,UAAU;AAAA,EACZ;AAAA,EACA,OAAO;AAAA,IACL,MAAM;AAAA,IACN,SAAS,cAAY;AACnB,aAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,KAAK;AAAA,IACH,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AAAA,EACA,MAAM;AAAA,IACJ,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AAAA,EACA,eAAe;AAAA,IACb,MAAM;AAAA,IACN,UAAU;AAAA,IACV,SAAS;AAAA,EACX;AACF;AAEA,IAAM,QAAQ;AAAA,EACZ;AAAA,EACA;AAAA,EACA,GAAG,CAAC,GAAG,OAAO,eAAe,GAAG,OAAO,IAAI,EAAE,IAAI,SAAO,IAAI,YAAY,CAAC;AAC3E;AAEA,IAAM,qBAAqB,gBAAgB;AAAA,EACzC,MAAM;AAAA,EAEN,cAAc;AAAA,EAEd;AAAA,EAEA;AAAA,EAEA,OAAO;AACL,WAAO;AAAA,MACL,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EAEA,SAAS;AACP,QAAI;AACF,WAAK,QAAQ;AACb,YAAM,EAAE,QAAQ,QAAQ,KAAK,eAAe,UAAU,OAAO,IAAI;AACjE,YAAM,qBAAqB,0BAA0B;AAAA,QACnD;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,CAAC;AACD,WAAK,qBAAqB;AAC1B,YAAM,aAAa,uBAAuB,EAAE,QAAQ,cAAc,CAAC;AACnE,aAAO,mBAAmB,OAAO,GAAG,UAAU;AAAA,IAChD,SAAS,KAAK;AACZ,WAAK,QAAQ;AACb,aAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,MAAM,EAAE,GAAG,IAAI,KAAK;AAAA,IACxD;AAAA,EACF;AAAA,EAEA,UAAU;AACR,QAAI,KAAK,SAAS,QAAQ,KAAK,eAAe,MAAM;AAClD,cAAQ;AAAA,QACN;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EAEA,UAAU;AACR,QAAI,KAAK,OAAO;AACd;AAAA,IACF;AAEA,UAAM,EAAE,QAAQ,KAAK,mBAAmB,IAAI;AAC5C,uBAAmB,QAAQ;AAE3B,UAAM,kBAAkB,qBAAqB;AAAA,MAC3C;AAAA,MACA,iBAAiB;AAAA,QACf,eAAe,WAASE,eAAc,KAAK,MAAM,KAAK;AAAA,QACtD,MAAM,WAASF,MAAK,KAAK,MAAM,KAAK;AAAA,QACpC,QAAQ,WAASC,QAAO,KAAK,MAAM,KAAK;AAAA,MAC1C;AAAA,IACF,CAAC;AACD,UAAM,mBAAmB,IAAI,aAAa,IAAI,MAAM,IAAI;AACxD,SAAK,YAAY,IAAI,qBAAS,kBAAkB,eAAe;AAC/D,SAAK,mBAAmB;AACxB,qBAAiB,0BAA0B;AAAA,EAC7C;AAAA,EAEA,UAAU;AACR,SAAK,mBAAmB,QAAQ;AAAA,EAClC;AAAA,EAEA,gBAAgB;AACd,QAAI,KAAK,cAAc;AAAW,WAAK,UAAU,QAAQ;AAAA,EAC3D;AAAA,EAEA,UAAU;AAAA,IACR,WAAW;AACT,YAAM,EAAE,KAAK,IAAI;AACjB,aAAO,OAAO,OAAO,KAAK;AAAA,IAC5B;AAAA,IAEA,SAAS;AACP,YAAM,EAAE,QAAQ,IAAI;AACpB,UAAI,OAAO,YAAY,YAAY;AACjC,eAAO;AAAA,MACT;AACA,aAAO,aAAW,QAAQ,OAAO;AAAA,IACnC;AAAA,EACF;AAAA,EAEA,OAAO;AAAA,IACL,QAAQ;AAAA,MACN,QAAQ,gBAAgB;AACtB,cAAM,EAAE,UAAU,IAAI;AACtB,YAAI,CAAC;AAAW;AAChB,gCAAwB,cAAc,EAAE,QAAQ,CAAC,CAAC,KAAK,KAAK,MAAM;AAChE,oBAAU,OAAO,KAAK,KAAK;AAAA,QAC7B,CAAC;AAAA,MACH;AAAA,MACA,MAAM;AAAA,IACR;AAAA,EACF;AAAA,EAEA,SAAS;AAAA,IACP,gBAAgB,YAAY;AAC1B,aAAO,KAAK,mBAAmB,gBAAgB,UAAU,KAAK;AAAA,IAChE;AAAA,IAEA,yCAAyC,YAAY;AAEnD,aAAO,WAAW;AAAA,IACpB;AAAA,IAEA,YAAY,KAAK;AACf,eAAS,MAAM,KAAK,MAAM,UAAU,GAAG,CAAC;AAAA,IAC1C;AAAA,IAEA,UAAU,QAAQ;AAChB,UAAI,KAAK,MAAM;AACb,eAAO,KAAK,IAAI;AAChB;AAAA,MACF;AACA,YAAM,UAAU,CAAC,GAAG,KAAK,UAAU;AACnC,aAAO,OAAO;AACd,WAAK,MAAM,qBAAqB,OAAO;AAAA,IACzC;AAAA,IAEA,aAAa;AACX,YAAM,aAAa,UAAQ,KAAK,OAAO,GAAG,SAAS;AACnD,WAAK,UAAU,UAAU;AAAA,IAC3B;AAAA,IAEA,eAAeE,WAAUC,WAAU;AACjC,YAAM,iBAAiB,UACrB,KAAK,OAAOA,WAAU,GAAG,KAAK,OAAOD,WAAU,CAAC,EAAE,CAAC,CAAC;AACtD,WAAK,UAAU,cAAc;AAAA,IAC/B;AAAA,IAEA,+BAA+B,EAAE,IAAI,QAAQ,GAAG;AAC9C,YAAM,YAAY,KAAK,yCAAyC,EAAE;AAClE,UAAI,CAAC,WAAW;AACd,eAAO,EAAE,UAAU;AAAA,MACrB;AACA,YAAM,OAAO,UAAU;AACvB,YAAM,UAAU,EAAE,MAAM,UAAU;AAClC,UAAI,OAAO,WAAW,MAAM;AAC1B,cAAM,cAAc,UAAU,gBAAgB,OAAO,KAAK,CAAC;AAC3D,eAAO,EAAE,GAAG,aAAa,GAAG,QAAQ;AAAA,MACtC;AACA,aAAO;AAAA,IACT;AAAA,IAEA,uBAAuB,UAAU;AAC/B,aAAO,KAAK,mBAAmB;AAAA,QAC7B;AAAA,QACA,KAAK;AAAA,MACP;AAAA,IACF;AAAA,IAEA,YAAY,KAAK;AACf,WAAK,UAAU,KAAK,gBAAgB,IAAI,IAAI;AAC5C,UAAI,KAAK,kBAAkB,KAAK,MAAM,KAAK,QAAQ,OAAO;AAC1D,wBAAkB,IAAI;AAAA,IACxB;AAAA,IAEA,UAAU,KAAK;AACb,YAAM,UAAU,IAAI,KAAK;AACzB,UAAI,YAAY,QAAW;AACzB;AAAA,MACF;AACA,iBAAW,IAAI,IAAI;AACnB,YAAMC,YAAW,KAAK,uBAAuB,IAAI,QAAQ;AACzD,WAAK,WAAWA,WAAU,GAAG,OAAO;AACpC,YAAM,QAAQ,EAAE,SAAS,UAAAA,UAAS;AAClC,WAAK,YAAY,EAAE,MAAM,CAAC;AAAA,IAC5B;AAAA,IAEA,aAAa,KAAK;AAChB,mBAAa,KAAK,KAAK,IAAI,MAAM,IAAI,QAAQ;AAC7C,UAAI,IAAI,aAAa,SAAS;AAC5B,mBAAW,IAAI,KAAK;AACpB;AAAA,MACF;AACA,YAAM,EAAE,OAAOD,WAAU,QAAQ,IAAI,KAAK;AAC1C,WAAK,WAAWA,WAAU,CAAC;AAC3B,YAAM,UAAU,EAAE,SAAS,UAAAA,UAAS;AACpC,WAAK,YAAY,EAAE,QAAQ,CAAC;AAAA,IAC9B;AAAA,IAEA,aAAa,KAAK;AAChB,iBAAW,IAAI,IAAI;AACnB,mBAAa,IAAI,MAAM,IAAI,MAAM,IAAI,QAAQ;AAC7C,YAAMA,YAAW,KAAK,QAAQ;AAC9B,YAAMC,YAAW,KAAK,uBAAuB,IAAI,QAAQ;AACzD,WAAK,eAAeD,WAAUC,SAAQ;AACtC,YAAMC,SAAQ,EAAE,SAAS,KAAK,QAAQ,SAAS,UAAAF,WAAU,UAAAC,UAAS;AAClE,WAAK,YAAY,EAAE,OAAAC,OAAM,CAAC;AAAA,IAC5B;AAAA,IAEA,mBAAmB,gBAAgB,KAAK;AACtC,UAAI,CAAC,eAAe,SAAS;AAC3B,eAAO;AAAA,MACT;AACA,YAAM,cAAc,CAAC,GAAG,IAAI,GAAG,QAAQ,EAAE;AAAA,QACvC,QAAM,GAAG,MAAM,SAAS,MAAM;AAAA,MAChC;AACA,YAAM,kBAAkB,YAAY,QAAQ,IAAI,OAAO;AACvD,YAAM,eAAe,eAAe,UAAU;AAAA,QAC5C;AAAA,MACF;AACA,YAAM,gBAAgB,YAAY,QAAQ,eAAe,MAAM;AAC/D,aAAO,iBAAiB,CAAC,IAAI,kBACzB,eACA,eAAe;AAAA,IACrB;AAAA,IAEA,WAAW,KAAK,eAAe;AAC7B,YAAM,EAAE,MAAM,SAAS,IAAI;AAC3B,UAAI,CAAC,QAAQ,CAAC,UAAU;AACtB,eAAO;AAAA,MACT;AAEA,YAAM,iBAAiB,KAAK,+BAA+B,GAAG;AAC9D,YAAM,cAAc,KAAK,mBAAmB,gBAAgB,GAAG;AAC/D,YAAM,iBAAiB;AAAA,QACrB,GAAG,KAAK;AAAA,QACR;AAAA,MACF;AACA,YAAM,YAAY;AAAA,QAChB,GAAG;AAAA,QACH;AAAA,QACA;AAAA,MACF;AACA,aAAO,KAAK,WAAW,aAAa;AAAA,IACtC;AAAA,IAEA,YAAY;AACV,wBAAkB;AAAA,IACpB;AAAA,EACF;AACF,CAAC;AAED,IAAO,uBAAQ;", "names": ["obj", "index", "option", "defaults", "rootEl", "cloneEl", "oldIndex", "newIndex", "oldDraggableIndex", "newDraggableIndex", "putSortable", "pluginEvent", "_detectDirection", "_dragElInRowColumn", "_detectNearestEmptySortable", "_prepareGroup", "dragEl", "_hideGhostForTarget", "_unhideGhostForTarget", "nearestEmptyInsertDetectEvent", "_checkOutsideTargetEl", "dragStartFn", "target", "after", "el", "plugins", "drop", "autoScroll", "onSpill", "events", "value", "h", "option", "index", "element", "index", "emit", "manage", "manageAndEmit", "oldIndex", "newIndex", "moved"]}
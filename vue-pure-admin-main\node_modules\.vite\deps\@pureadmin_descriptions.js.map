{"version": 3, "sources": ["../../.pnpm/@pureadmin+descriptions@1.2_81742da1227253bc21a856313b0c71b0/node_modules/@pureadmin/descriptions/dist/index.es.js"], "sourcesContent": ["import * as f from \"vue\";\nimport { defineComponent as h, createVNode as j, Fragment as Q, ref as G, toRefs as H, computed as E, withDirectives as B, mergeProps as b, unref as u, resolveDirective as W, isVNode as X } from \"vue\";\nimport { ElDescriptions as R, ElDescriptionsItem as F } from \"element-plus\";\nconst J = { data: { type: Array, default: [] }, columns: { type: Array, default: [] }, loading: { type: Object, default: () => ({ load: !1, text: \"Loading...\", svg: \"\", spinner: \"\", svgViewBox: \"\", background: \"\" }) }, align: { type: String, default: \"left\" }, labelAlign: { type: String, default: \"\" }, ...R.props }, S = h({ name: \"Renderer\", props: { render: { type: Function }, params: { type: Object } }, setup: (M) => () => j(Q, null, [M.render(M.params)]) });\nvar P = Object.defineProperty, q = Object.getOwnPropertyDescriptor, K = Object.getOwnPropertyNames, _ = Object.prototype.hasOwnProperty, C = (M, t, l, c) => {\n  if (t && typeof t == \"object\" || typeof t == \"function\")\n    for (let o of K(t))\n      !_.call(M, o) && o !== l && P(M, o, { get: () => t[o], enumerable: !(c = q(t, o)) || c.enumerable });\n  return M;\n}, w, U, $ = typeof document < \"u\", a = {};\nfunction Y(M, { target: t = $ ? document.body : void 0 } = {}) {\n  let l = document.createElement(\"textarea\"), c = document.activeElement;\n  l.value = M, l.setAttribute(\"readonly\", \"\"), l.style.contain = \"strict\", l.style.position = \"absolute\", l.style.left = \"-9999px\", l.style.fontSize = \"12pt\";\n  let o, s = document.getSelection();\n  s && s.rangeCount > 0 && (o = s.getRangeAt(0)), t == null || t.append(l), l.select(), l.selectionStart = 0, l.selectionEnd = M.length;\n  let z = !1;\n  try {\n    z = document.execCommand(\"copy\");\n  } catch (L) {\n    throw new Error(L.message);\n  }\n  return l.remove(), o && s && (s.removeAllRanges(), s.addRange(o)), c instanceof HTMLElement && c.focus(), z;\n}\n((M, t) => {\n  for (var l in t)\n    P(M, l, { get: t[l], enumerable: !0 });\n})(a, { Vue: () => f }), C(a, w = f, \"default\"), U && C(U, w, \"default\");\nconst I = h({ name: \"PureDescriptions\", props: J, setup(M, { slots: t, attrs: l }) {\n  const c = G(-1), o = new URL(\"data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxNiIgaGVpZ2h0PSIxNiIgY2xhc3M9Imljb24iIHZpZXdCb3g9IjAgMCAxMDI0IDEwMjQiPjxwYXRoIGZpbGw9IiM0MDllZmYiIGQ9Ik01ODQuMjM1IDk5NC4zNDVIMjMxLjYwM2MtNzQuNTY1IDAtMTM1LjE1LTYwLjU4NC0xMzUuMTUtMTM1LjE1VjM2MC41NGMwLTc0LjU2NSA2MC41ODUtMTM1LjE1IDEzNS4xNS0xMzUuMTVoMzUyLjYzMmM3NC41NjYgMCAxMzUuMTUgNjAuNTg1IDEzNS4xNSAxMzUuMTV2NDk4LjY1NmMwIDc0LjU2Ni02MC41ODQgMTM1LjE1LTEzNS4xNSAxMzUuMTVNMjMxLjYwMyAzMDMuMDYyYy0zMS44NDYgMC01Ny40NzcgMjUuNjMxLTU3LjQ3NyA1Ny40Nzd2NDk4LjY1NmMwIDMxLjg0NiAyNS42MzEgNTcuNDc4IDU3LjQ3NyA1Ny40NzhoMzUyLjYzMmMzMS44NDYgMCA1Ny40NzgtMjUuNjMyIDU3LjQ3OC01Ny40NzhWMzYwLjU0YzAtMzEuODQ2LTI1LjYzMi01Ny40NzctNTcuNDc4LTU3LjQ3N3oiLz48cGF0aCBmaWxsPSIjNDA5ZWZmIiBkPSJNODMyLjAxIDc5MS42MmMtMjEuNzQ4IDAtMzguODM2LTE3LjA4OC0zOC44MzYtMzguODM2di00NTcuNDljMC04MC43NzktNjUuMjQ1LTE0Ni4wMjQtMTQ2LjAyNC0xNDYuMDI0SDMzNi40NmMtMjEuNzQ4IDAtMzguODM2LTE3LjA4OC0zOC44MzYtMzguODM2czE3LjA4OC0zOC44MzYgMzguODM3LTM4LjgzNkg2NDcuMTVjMTIzLjQ5OSAwIDIyMy42OTYgMTAwLjE5NyAyMjMuNjk2IDIyMy42OTZ2NDU3LjQ5YzAgMjAuOTcyLTE3LjA4OCAzOC44MzYtMzguODM2IDM4LjgzNiIvPjwvc3ZnPg==\", self.location).href, s = new URL(\"data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxNiIgaGVpZ2h0PSIxNiIgY2xhc3M9Imljb24iIHZpZXdCb3g9IjAgMCAxMDI0IDEwMjQiPjxwYXRoIGZpbGw9IiM2N2MyM2EiIGQ9Ik0zNTEuODA5IDg0Ni43NTJjLTE5LjE2IDAtMzcuMjU0LTcuNDUtNTEuMDkxLTIxLjI4OC0xLjA2NS0xLjA2NC0xLjA2NS0yLjEyOS0yLjEyOS0yLjEyOUwzOS45NDEgNTA1LjA4MWMtMTEuNzA4LTEzLjgzNy05LjU4LTM1LjEyNSA1LjMyMi00Ni44MzMgMTMuODM3LTExLjcwOCAzNS4xMjUtOS41OCA0Ni44MzQgNS4zMjJsMjU2LjUxOSAzMTYuMTI1YzIuMTI4IDIuMTI5IDQuMjU3IDIuMTI5IDcuNDUtMS4wNjRsNTY2LjI1OC02MDIuNDQ3YzEyLjc3My0xMy44MzcgMzQuMDYtMTMuODM3IDQ2LjgzMy0xLjA2NSAxMy44MzcgMTIuNzczIDEzLjgzNyAzNC4wNiAxLjA2NSA0Ni44MzRMNDAyLjkgODI1LjQ2NGMtMTMuODM4IDEzLjgzNy0zMS45MzIgMjEuMjg4LTUxLjA5MSAyMS4yODgiLz48L3N2Zz4=\", self.location).href, { data: z, columns: L, align: Z, labelAlign: k, loading: N } = H(M), m = { title: () => (t == null ? void 0 : t.title) && t.title({ props: M, attrs: l }) }, A = { extra: () => (t == null ? void 0 : t.extra) && t.extra({ props: M, attrs: l }) }, V = (t == null ? void 0 : t.title) && !(t != null && t.extra) ? m : (t == null ? void 0 : t.extra) && !(t != null && t.title) ? A : (t == null ? void 0 : t.title) && (t == null ? void 0 : t.extra) ? Object.assign(m, A) : null, { copied: p, update: x } = ((i = \"\") => {\n    let e = (0, a.shallowRef)(i), r = (0, a.shallowRef)(!1);\n    return (0, a.watch)(e, (n = i) => {\n      (n = (n = (0, a.isProxy)(n) || (0, a.isRef)(n) ? (0, a.unref)(n) : n).trim().length === 0 ? i : n).length > 0 ? r.value = Y(n) : r.value = !1;\n    }, { flush: \"sync\" }), { clipboardValue: e, copied: r, update: (n) => {\n      e.value = (0, a.isProxy)(n) || (0, a.isRef)(n) ? (0, a.unref)(n) : n;\n      let y = e.value.trim().length === 0 ? i : e.value;\n      y.length > 0 ? r.value = Y(y) : r.value = !1;\n    } };\n  })();\n  function T(i, e) {\n    p.value || (c.value = e, function(r) {\n      return r && Array.isArray(r);\n    }(i) ? x(i[0]) : x(i), ((r = 20) => new Promise((n) => setTimeout(n, r)))(600).then(() => p.value = !p.value));\n  }\n  const v = E(() => ({ cursor: \"pointer\", marginLeft: \"4px\", verticalAlign: \"sub\" })), O = E(() => (i) => c.value === i && p.value ? s : o);\n  return () => {\n    var i;\n    return B(j(R, b(M, l, { \"element-loading-text\": (i = u(N).text) != null ? i : \"Loading...\", \"element-loading-svg\": u(N).svg, \"element-loading-spinner\": u(N).spinner, \"element-loading-svg-view-box\": u(N).svgViewBox, \"element-loading-background\": u(N).background }), { default: () => [u(L).map((e, r) => {\n      let n = u(z).map((g) => g[e == null ? void 0 : e.prop]);\n      const y = { default: () => {\n        var g;\n        return e != null && e.cellRenderer ? j(S, { render: e.cellRenderer, params: { props: M, attrs: l, index: r, value: n[0] } }, null) : e != null && e.slot ? (g = t == null ? void 0 : t[e.slot]) == null ? void 0 : g.call(t, { props: M, attrs: l, index: r, value: n[0] }) : j(Q, null, e != null && e.value ? [u(e.value), u(e == null ? void 0 : e.copy) && j(\"img\", { src: O.value(r), style: v.value, onClick: () => T(u(e.value), r) }, null)] : [n, (e == null ? void 0 : e.copy) && j(\"img\", { src: O.value(r), style: v.value, onClick: () => T(n, r) }, null)]);\n      } }, d = e != null && e.labelRenderer ? { label: () => j(S, { render: e.labelRenderer, params: { props: M, attrs: l, index: r, value: n[0] } }, null), ...y } : y;\n      return function(g) {\n        return typeof g == \"function\";\n      }(e == null ? void 0 : e.hide) && (e == null ? void 0 : e.hide(l)) ? e == null ? void 0 : e.hide(l) : j(F, b(e, { key: r, align: e.align ? e.align : u(Z), labelAlign: e.labelAlign ? e.labelAlign : u(k) }), typeof (D = d) == \"function\" || Object.prototype.toString.call(D) === \"[object Object]\" && !X(D) ? d : { default: () => [d] });\n      var D;\n    })], ...V }), [[W(\"loading\"), u(N).load]]);\n  };\n} }), le = Object.assign(I, { install: function(M) {\n  M.component(I.name, I);\n} });\nexport {\n  le as PureDescriptions,\n  le as default\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAGA,IAAM,IAAI,EAAE,MAAM,EAAE,MAAM,OAAO,SAAS,CAAC,EAAE,GAAG,SAAS,EAAE,MAAM,OAAO,SAAS,CAAC,EAAE,GAAG,SAAS,EAAE,MAAM,QAAQ,SAAS,OAAO,EAAE,MAAM,OAAI,MAAM,cAAc,KAAK,IAAI,SAAS,IAAI,YAAY,IAAI,YAAY,GAAG,GAAG,GAAG,OAAO,EAAE,MAAM,QAAQ,SAAS,OAAO,GAAG,YAAY,EAAE,MAAM,QAAQ,SAAS,GAAG,GAAG,GAAG,eAAE,MAAM;AAA3T,IAA8T,IAAI,gBAAE,EAAE,MAAM,YAAY,OAAO,EAAE,QAAQ,EAAE,MAAM,SAAS,GAAG,QAAQ,EAAE,MAAM,OAAO,EAAE,GAAG,OAAO,CAAC,MAAM,MAAM,YAAE,UAAG,MAAM,CAAC,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC,EAAE,CAAC;AAC/c,IAAI,IAAI,OAAO;AAAf,IAA+B,IAAI,OAAO;AAA1C,IAAoE,IAAI,OAAO;AAA/E,IAAoG,IAAI,OAAO,UAAU;AAAzH,IAAyI,IAAI,CAAC,GAAG,GAAG,GAAG,MAAM;AAC3J,MAAI,KAAK,OAAO,KAAK,YAAY,OAAO,KAAK;AAC3C,aAAS,KAAK,EAAE,CAAC;AACf,OAAC,EAAE,KAAK,GAAG,CAAC,KAAK,MAAM,KAAK,EAAE,GAAG,GAAG,EAAE,KAAK,MAAM,EAAE,CAAC,GAAG,YAAY,EAAE,IAAI,EAAE,GAAG,CAAC,MAAM,EAAE,WAAW,CAAC;AACvG,SAAO;AACT;AALA,IAKG;AALH,IAKM;AALN,IAKS,IAAI,OAAO,WAAW;AAL/B,IAKoC,IAAI,CAAC;AACzC,SAAS,EAAE,GAAG,EAAE,QAAQ,IAAI,IAAI,SAAS,OAAO,OAAO,IAAI,CAAC,GAAG;AAC7D,MAAI,IAAI,SAAS,cAAc,UAAU,GAAG,IAAI,SAAS;AACzD,IAAE,QAAQ,GAAG,EAAE,aAAa,YAAY,EAAE,GAAG,EAAE,MAAM,UAAU,UAAU,EAAE,MAAM,WAAW,YAAY,EAAE,MAAM,OAAO,WAAW,EAAE,MAAM,WAAW;AACrJ,MAAI,GAAG,IAAI,SAAS,aAAa;AACjC,OAAK,EAAE,aAAa,MAAM,IAAI,EAAE,WAAW,CAAC,IAAI,KAAK,QAAQ,EAAE,OAAO,CAAC,GAAG,EAAE,OAAO,GAAG,EAAE,iBAAiB,GAAG,EAAE,eAAe,EAAE;AAC/H,MAAI,IAAI;AACR,MAAI;AACF,QAAI,SAAS,YAAY,MAAM;AAAA,EACjC,SAAS,GAAG;AACV,UAAM,IAAI,MAAM,EAAE,OAAO;AAAA,EAC3B;AACA,SAAO,EAAE,OAAO,GAAG,KAAK,MAAM,EAAE,gBAAgB,GAAG,EAAE,SAAS,CAAC,IAAI,aAAa,eAAe,EAAE,MAAM,GAAG;AAC5G;AAAA,CACC,CAAC,GAAG,MAAM;AACT,WAAS,KAAK;AACZ,MAAE,GAAG,GAAG,EAAE,KAAK,EAAE,CAAC,GAAG,YAAY,KAAG,CAAC;AACzC,GAAG,GAAG,EAAE,KAAK,MAAM,gCAAE,CAAC,GAAG,EAAE,GAAG,IAAI,iCAAG,SAAS,GAAG,KAAK,EAAE,GAAG,GAAG,SAAS;AACvE,IAAM,IAAI,gBAAE,EAAE,MAAM,oBAAoB,OAAO,GAAG,MAAM,GAAG,EAAE,OAAO,GAAG,OAAO,EAAE,GAAG;AACjF,QAAM,IAAI,IAAE,EAAE,GAAG,IAAI,IAAI,IAAI,0lCAA0lC,KAAK,QAAQ,EAAE,MAAM,IAAI,IAAI,IAAI,ktBAAktB,KAAK,QAAQ,EAAE,MAAM,EAAE,MAAM,GAAG,SAAS,GAAG,OAAO,GAAG,YAAY,GAAG,SAAS,EAAE,IAAI,OAAE,CAAC,GAAG,IAAI,EAAE,OAAO,OAAO,KAAK,OAAO,SAAS,EAAE,UAAU,EAAE,MAAM,EAAE,OAAO,GAAG,OAAO,EAAE,CAAC,EAAE,GAAG,IAAI,EAAE,OAAO,OAAO,KAAK,OAAO,SAAS,EAAE,UAAU,EAAE,MAAM,EAAE,OAAO,GAAG,OAAO,EAAE,CAAC,EAAE,GAAG,KAAK,KAAK,OAAO,SAAS,EAAE,UAAU,EAAE,KAAK,QAAQ,EAAE,SAAS,KAAK,KAAK,OAAO,SAAS,EAAE,UAAU,EAAE,KAAK,QAAQ,EAAE,SAAS,KAAK,KAAK,OAAO,SAAS,EAAE,WAAW,KAAK,OAAO,SAAS,EAAE,SAAS,OAAO,OAAO,GAAG,CAAC,IAAI,MAAM,EAAE,QAAQ,GAAG,QAAQ,EAAE,KAAK,CAAC,IAAI,OAAO;AAC73E,QAAI,KAAK,GAAG,EAAE,YAAY,CAAC,GAAG,KAAK,GAAG,EAAE,YAAY,KAAE;AACtD,YAAQ,GAAG,EAAE,OAAO,GAAG,CAAC,IAAI,MAAM;AAChC,OAAC,KAAK,KAAK,GAAG,EAAE,SAAS,CAAC,MAAM,GAAG,EAAE,OAAO,CAAC,KAAK,GAAG,EAAE,OAAO,CAAC,IAAI,GAAG,KAAK,EAAE,WAAW,IAAI,IAAI,GAAG,SAAS,IAAI,EAAE,QAAQ,EAAE,CAAC,IAAI,EAAE,QAAQ;AAAA,IAC7I,GAAG,EAAE,OAAO,OAAO,CAAC,GAAG,EAAE,gBAAgB,GAAG,QAAQ,GAAG,QAAQ,CAAC,MAAM;AACpE,QAAE,SAAS,GAAG,EAAE,SAAS,CAAC,MAAM,GAAG,EAAE,OAAO,CAAC,KAAK,GAAG,EAAE,OAAO,CAAC,IAAI;AACnE,UAAI,IAAI,EAAE,MAAM,KAAK,EAAE,WAAW,IAAI,IAAI,EAAE;AAC5C,QAAE,SAAS,IAAI,EAAE,QAAQ,EAAE,CAAC,IAAI,EAAE,QAAQ;AAAA,IAC5C,EAAE;AAAA,EACJ,GAAG;AACH,WAAS,EAAE,GAAG,GAAG;AACf,MAAE,UAAU,EAAE,QAAQ,GAAG,SAAS,GAAG;AACnC,aAAO,KAAK,MAAM,QAAQ,CAAC;AAAA,IAC7B,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,IAAI,OAAO,IAAI,QAAQ,CAAC,MAAM,WAAW,GAAG,CAAC,CAAC,GAAG,GAAG,EAAE,KAAK,MAAM,EAAE,QAAQ,CAAC,EAAE,KAAK;AAAA,EAC9G;AACA,QAAM,IAAI,SAAE,OAAO,EAAE,QAAQ,WAAW,YAAY,OAAO,eAAe,MAAM,EAAE,GAAG,IAAI,SAAE,MAAM,CAAC,MAAM,EAAE,UAAU,KAAK,EAAE,QAAQ,IAAI,CAAC;AACxI,SAAO,MAAM;AACX,QAAI;AACJ,WAAO,eAAE,YAAE,gBAAG,WAAE,GAAG,GAAG,EAAE,yBAAyB,IAAI,MAAE,CAAC,EAAE,SAAS,OAAO,IAAI,cAAc,uBAAuB,MAAE,CAAC,EAAE,KAAK,2BAA2B,MAAE,CAAC,EAAE,SAAS,gCAAgC,MAAE,CAAC,EAAE,YAAY,8BAA8B,MAAE,CAAC,EAAE,WAAW,CAAC,GAAG,EAAE,SAAS,MAAM,CAAC,MAAE,CAAC,EAAE,IAAI,CAAC,GAAG,MAAM;AAC5S,UAAI,IAAI,MAAE,CAAC,EAAE,IAAI,CAAC,MAAM,EAAE,KAAK,OAAO,SAAS,EAAE,IAAI,CAAC;AACtD,YAAM,IAAI,EAAE,SAAS,MAAM;AACzB,YAAI;AACJ,eAAO,KAAK,QAAQ,EAAE,eAAe,YAAE,GAAG,EAAE,QAAQ,EAAE,cAAc,QAAQ,EAAE,OAAO,GAAG,OAAO,GAAG,OAAO,GAAG,OAAO,EAAE,CAAC,EAAE,EAAE,GAAG,IAAI,IAAI,KAAK,QAAQ,EAAE,QAAQ,IAAI,KAAK,OAAO,SAAS,EAAE,EAAE,IAAI,MAAM,OAAO,SAAS,EAAE,KAAK,GAAG,EAAE,OAAO,GAAG,OAAO,GAAG,OAAO,GAAG,OAAO,EAAE,CAAC,EAAE,CAAC,IAAI,YAAE,UAAG,MAAM,KAAK,QAAQ,EAAE,QAAQ,CAAC,MAAE,EAAE,KAAK,GAAG,MAAE,KAAK,OAAO,SAAS,EAAE,IAAI,KAAK,YAAE,OAAO,EAAE,KAAK,EAAE,MAAM,CAAC,GAAG,OAAO,EAAE,OAAO,SAAS,MAAM,EAAE,MAAE,EAAE,KAAK,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,OAAO,SAAS,EAAE,SAAS,YAAE,OAAO,EAAE,KAAK,EAAE,MAAM,CAAC,GAAG,OAAO,EAAE,OAAO,SAAS,MAAM,EAAE,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC;AAAA,MAC1iB,EAAE,GAAG,IAAI,KAAK,QAAQ,EAAE,gBAAgB,EAAE,OAAO,MAAM,YAAE,GAAG,EAAE,QAAQ,EAAE,eAAe,QAAQ,EAAE,OAAO,GAAG,OAAO,GAAG,OAAO,GAAG,OAAO,EAAE,CAAC,EAAE,EAAE,GAAG,IAAI,GAAG,GAAG,EAAE,IAAI;AAChK,aAAO,SAAS,GAAG;AACjB,eAAO,OAAO,KAAK;AAAA,MACrB,EAAE,KAAK,OAAO,SAAS,EAAE,IAAI,MAAM,KAAK,OAAO,SAAS,EAAE,KAAK,CAAC,KAAK,KAAK,OAAO,SAAS,EAAE,KAAK,CAAC,IAAI,YAAE,oBAAG,WAAE,GAAG,EAAE,KAAK,GAAG,OAAO,EAAE,QAAQ,EAAE,QAAQ,MAAE,CAAC,GAAG,YAAY,EAAE,aAAa,EAAE,aAAa,MAAE,CAAC,EAAE,CAAC,GAAG,QAAQ,IAAI,MAAM,cAAc,OAAO,UAAU,SAAS,KAAK,CAAC,MAAM,qBAAqB,CAAC,QAAE,CAAC,IAAI,IAAI,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,CAAC;AAC3U,UAAI;AAAA,IACN,CAAC,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,iBAAE,SAAS,GAAG,MAAE,CAAC,EAAE,IAAI,CAAC,CAAC;AAAA,EAC3C;AACF,EAAE,CAAC;AA/BH,IA+BM,KAAK,OAAO,OAAO,GAAG,EAAE,SAAS,SAAS,GAAG;AACjD,IAAE,UAAU,EAAE,MAAM,CAAC;AACvB,EAAE,CAAC;", "names": []}
/**
 * 測試例外設定時間格式修復效果
 * 
 * 問題描述：
 * 後端期望的是 DateTime 格式，但註釋說明「僅考慮HH:MM:SS」
 * 這意味著後端會忽略日期部分，只使用時間部分
 * 
 * 修復方案：
 * 發送今天的日期 + 用戶選擇的時間，確保後端能正確解析
 */

console.log('🧪 例外設定時間格式修復測試開始...')

// 模擬用戶在時間選擇器中選擇的時間
function simulateUserTimeSelection() {
  console.log('\n📋 模擬用戶時間選擇:')
  
  // 用戶在時間選擇器中選擇了 01:30:20 和 04:52:46
  const userStartTime = new Date()
  userStartTime.setHours(1, 30, 20, 0)
  
  const userEndTime = new Date()
  userEndTime.setHours(4, 52, 46, 0)
  
  console.log('用戶選擇的時間:', {
    開始時間: userStartTime.toString(),
    結束時間: userEndTime.toString(),
    開始時間格式: `${userStartTime.getHours().toString().padStart(2, '0')}:${userStartTime.getMinutes().toString().padStart(2, '0')}:${userStartTime.getSeconds().toString().padStart(2, '0')}`,
    結束時間格式: `${userEndTime.getHours().toString().padStart(2, '0')}:${userEndTime.getMinutes().toString().padStart(2, '0')}:${userEndTime.getSeconds().toString().padStart(2, '0')}`
  })
  
  return { userStartTime, userEndTime }
}

// 模擬修復前的錯誤格式
function simulateBeforeFix(userStartTime, userEndTime) {
  console.log('\n❌ 修復前的錯誤格式:')
  
  // 修復前：直接發送用戶選擇的時間（可能是昨天或其他日期）
  const beforeFix = {
    AlarmExceptionStartAt: userStartTime.toISOString(),
    AlarmExceptionEndAt: userEndTime.toISOString()
  }
  
  console.log('修復前發送格式:', beforeFix)
  console.log('問題：如果用戶選擇的時間不是今天，後端可能無法正確處理')
  
  return beforeFix
}

// 模擬修復後的正確格式
function simulateAfterFix(userStartTime, userEndTime) {
  console.log('\n✅ 修復後的正確格式:')
  
  // 修復後：使用今天的日期 + 用戶選擇的時間
  function createTodayWithUserTime(userTime) {
    const today = new Date()
    const userTimeObj = new Date(userTime)
    today.setHours(userTimeObj.getHours(), userTimeObj.getMinutes(), userTimeObj.getSeconds(), 0)
    return today
  }
  
  const startDateTime = createTodayWithUserTime(userStartTime)
  const endDateTime = createTodayWithUserTime(userEndTime)
  
  const afterFix = {
    AlarmExceptionStartAt: startDateTime.toISOString(),
    AlarmExceptionEndAt: endDateTime.toISOString()
  }
  
  console.log('修復後發送格式:', afterFix)
  console.log('修復後詳細分析:', {
    開始時間: {
      完整DateTime: startDateTime.toISOString(),
      日期部分: startDateTime.toISOString().split('T')[0],
      時間部分: startDateTime.toISOString().split('T')[1],
      本地時間: startDateTime.toString()
    },
    結束時間: {
      完整DateTime: endDateTime.toISOString(),
      日期部分: endDateTime.toISOString().split('T')[0],
      時間部分: endDateTime.toISOString().split('T')[1],
      本地時間: endDateTime.toString()
    }
  })
  
  console.log('✅ 優點：確保使用今天的日期，後端只需要提取時間部分')
  
  return afterFix
}

// 模擬後端處理邏輯
function simulateBackendProcessing(dateTimeString) {
  console.log('\n🔧 模擬後端處理邏輯:')
  
  const dateTime = new Date(dateTimeString)
  const timeOnly = `${dateTime.getHours().toString().padStart(2, '0')}:${dateTime.getMinutes().toString().padStart(2, '0')}:${dateTime.getSeconds().toString().padStart(2, '0')}`
  
  console.log('後端處理過程:', {
    接收到的DateTime: dateTimeString,
    解析後的Date物件: dateTime.toString(),
    提取的時間部分: timeOnly,
    '後端註釋說明': '僅考慮HH:MM:SS'
  })
  
  return timeOnly
}

// 執行測試
const { userStartTime, userEndTime } = simulateUserTimeSelection()
const beforeFix = simulateBeforeFix(userStartTime, userEndTime)
const afterFix = simulateAfterFix(userStartTime, userEndTime)

console.log('\n🔧 後端處理結果對比:')
const beforeBackendResult = simulateBackendProcessing(beforeFix.AlarmExceptionStartAt)
const afterBackendResult = simulateBackendProcessing(afterFix.AlarmExceptionStartAt)

console.log('處理結果對比:', {
  修復前後端提取時間: beforeBackendResult,
  修復後後端提取時間: afterBackendResult,
  結果是否一致: beforeBackendResult === afterBackendResult
})

console.log('\n🎯 修復總結:')
console.log('1. 確保發送今天的日期 + 用戶選擇的時間')
console.log('2. 後端可以正確解析 DateTime 格式')
console.log('3. 後端提取時間部分用於例外設定邏輯')
console.log('4. 避免因日期不匹配導致的保存失敗')

console.log('\n✅ 例外設定時間格式修復測試完成！')

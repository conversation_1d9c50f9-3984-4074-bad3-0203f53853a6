{"version": 3, "sources": ["../../.pnpm/@howdyjs+mouse-menu@2.1.7_vue@3.5.18_typescript@5.8.3_/node_modules/@howdyjs/mouse-menu/dist/index.es.js"], "sourcesContent": ["import { defineComponent, ref, computed, watch, nextTick, onUnmounted, openBlock, createBlock, Teleport, createElementBlock, normalizeClass, normalizeStyle, Fragment, renderList, mergeProps, toHandlerKey, withModifiers, withDirectives, vShow, resolveDynamicComponent, createCommentVNode, createElementVNode, toDisplayString, createVNode, render as render$1 } from 'vue';\n\nconst clone = function(obj) {\n  let newObj = Array.isArray(obj) ? [] : {};\n  if (obj && typeof obj === \"object\") {\n    for (let key in obj) {\n      if (obj.hasOwnProperty(key)) {\n        if (obj[key] && typeof obj[key] === \"object\") {\n          newObj[key] = clone(obj[key]);\n        } else {\n          newObj[key] = obj[key];\n        }\n      }\n    }\n  }\n  return newObj;\n};\n\nvar script = defineComponent({\n  name: \"<PERSON><PERSON><PERSON><PERSON>\",\n  props: {\n    appendToBody: {\n      type: Boolean,\n      default: true\n    },\n    menuWidth: {\n      type: Number,\n      default: 200\n    },\n    menuList: {\n      type: Array,\n      required: true\n    },\n    menuHiddenFn: {\n      type: Function\n    },\n    hasIcon: {\n      type: Boolean,\n      default: false\n    },\n    iconType: {\n      type: String,\n      default: \"font-icon\"\n    },\n    menuWrapperCss: Object,\n    menuItemCss: Object,\n    el: {\n      type: Object,\n      required: true\n    },\n    params: {\n      type: [String, Number, Array, Object]\n    },\n    useLongPressInMobile: Boolean,\n    longPressDuration: Number,\n    longPressPreventDefault: [Function, Boolean],\n    injectCloseListener: {\n      type: Boolean,\n      default: true\n    },\n    customClass: String,\n    disabled: {\n      type: Function\n    }\n  },\n  emits: [\"open\", \"close\"],\n  expose: [\"show\", \"close\", \"showMenu\"],\n  setup(props, { emit }) {\n    const subLeft = ref(0);\n    const subTop = ref(0);\n    const hoverFlag = ref(false);\n    const menuTop = ref(0);\n    const menuLeft = ref(0);\n    const showMenu = ref(false);\n    const clickDomEl = ref(null);\n    const calcMenuList = ref([]);\n    const hasSubMenu = computed(() => props.menuList.some((item) => item.children && item.children.length > 0));\n    const arrowSize = ref(10);\n    const MenuWrapper = ref();\n    watch(showMenu, async (val) => {\n      if (val) {\n        await nextTick();\n        let el = MenuWrapper.value;\n        if (props.menuWrapperCss) {\n          Object.keys(props.menuWrapperCss).map((item) => {\n            el.style.setProperty(`--menu-${item}`, props.menuWrapperCss && props.menuWrapperCss[item]);\n          });\n        }\n        if (props.menuItemCss) {\n          Object.keys(props.menuItemCss).map((item) => {\n            el.style.setProperty(`--menu-item-${item}`, props.menuItemCss && props.menuItemCss[item]);\n          });\n        }\n        let _arrowSize = props.menuItemCss?.arrowSize?.match(/\\d+/);\n        if (_arrowSize) {\n          arrowSize.value = ~~_arrowSize[0] || 10;\n        } else {\n          arrowSize.value = 10;\n        }\n        el.style.setProperty(\"--menu-item-arrowRealSize\", arrowSize.value / 2 + \"px\");\n        emit(\"open\", props.params, clickDomEl.value, props.el);\n      } else {\n        emit(\"close\", props.params, clickDomEl.value, props.el);\n      }\n    });\n    const handleMenuItemClick = (item, $event) => {\n      if (item.disabled)\n        return;\n      if (item.fn && typeof item.fn === \"function\") {\n        const flag = item.fn(props.params, clickDomEl.value, props.el, $event);\n        if (flag === false)\n          return;\n      }\n      showMenu.value = false;\n    };\n    const handleSubMenuItemClick = (subItem, $event) => {\n      if (subItem.disabled)\n        return;\n      if (subItem.fn && typeof subItem.fn === \"function\" && !subItem.disabled) {\n        const flag = subItem.fn(props.params, clickDomEl.value, props.el, $event);\n        if (flag === false)\n          return;\n        hoverFlag.value = false;\n      }\n      showMenu.value = false;\n    };\n    const handleMenuMouseEnter = async ($event, item) => {\n      if (item.children && !item.disabled) {\n        hoverFlag.value = true;\n        await nextTick();\n        const el = $event.currentTarget;\n        if (!el)\n          return;\n        const { offsetWidth } = el;\n        const subEl = el.querySelector(\".__menu__sub__wrapper\");\n        if (!subEl)\n          return;\n        const { offsetWidth: subOffsetWidth, offsetHeight: subOffsetHeight } = subEl;\n        const { innerWidth: windowWidth, innerHeight: windowHeight } = window;\n        const { top, left } = el.getBoundingClientRect();\n        if (left + offsetWidth + subOffsetWidth > windowWidth - 5) {\n          subLeft.value = left - subOffsetWidth + 5;\n        } else {\n          subLeft.value = left + offsetWidth;\n        }\n        if (top + subOffsetHeight > windowHeight - 5) {\n          subTop.value = windowHeight - subOffsetHeight;\n        } else {\n          subTop.value = top + 5;\n        }\n      }\n    };\n    const formatterFnOption = (list, clickDomEl2, el, params) => {\n      return list.map((item) => {\n        if (item.children) {\n          item.children = formatterFnOption(item.children, clickDomEl2, el, params);\n        }\n        if (item.label && typeof item.label === \"function\") {\n          item.label = item.label(params, clickDomEl2, el);\n        }\n        if (item.tips && typeof item.tips === \"function\") {\n          item.tips = item.tips(params, clickDomEl2, el);\n        }\n        if (item.icon && typeof item.icon === \"function\") {\n          item.icon = item.icon(params, clickDomEl2, el);\n        }\n        if (item.hidden && typeof item.hidden === \"function\") {\n          item.hidden = item.hidden(params, clickDomEl2, el);\n        }\n        if (item.disabled && typeof item.disabled === \"function\") {\n          item.disabled = item.disabled(params, clickDomEl2, el);\n        }\n        return item;\n      });\n    };\n    const show = async (x = 0, y = 0) => {\n      clickDomEl.value = document.elementFromPoint(x - 1, y - 1);\n      if (props.menuHiddenFn) {\n        showMenu.value = !props.menuHiddenFn(props.params, clickDomEl.value, props.el);\n      } else {\n        showMenu.value = true;\n      }\n      if (!showMenu.value)\n        return;\n      calcMenuList.value = clone(props.menuList);\n      calcMenuList.value = formatterFnOption(calcMenuList.value, clickDomEl.value, props.el, props.params);\n      await nextTick();\n      const { innerWidth: windowWidth, innerHeight: windowHeight } = window;\n      const menu = MenuWrapper.value;\n      const menuHeight = menu.offsetHeight;\n      const menuWidth = props.menuWidth || 200;\n      menuLeft.value = x + menuWidth + 1 > windowWidth ? windowWidth - menuWidth - 5 : x + 1;\n      menuTop.value = y + menuHeight + 1 > windowHeight ? windowHeight - menuHeight - 5 : y + 1;\n    };\n    const close = () => {\n      showMenu.value = false;\n    };\n    const clickEventKey = computed(() => props.useLongPressInMobile && \"ontouchstart\" in window ? \"touchstart\" : \"mousedown\");\n    const listenerFn = (e) => {\n      if (MenuWrapper.value && !MenuWrapper.value.contains(e.currentTarget)) {\n        showMenu.value = false;\n        document.oncontextmenu = null;\n      }\n    };\n    watch(() => props.injectCloseListener, (val) => {\n      if (val) {\n        document.addEventListener(clickEventKey.value, listenerFn);\n      } else {\n        document.removeEventListener(clickEventKey.value, listenerFn);\n      }\n    }, {\n      immediate: true\n    });\n    onUnmounted(() => {\n      document.removeEventListener(clickEventKey.value, listenerFn);\n    });\n    return {\n      subLeft,\n      subTop,\n      hoverFlag,\n      menuTop,\n      menuLeft,\n      showMenu,\n      clickDomEl,\n      calcMenuList,\n      arrowSize,\n      hasSubMenu,\n      MenuWrapper,\n      handleMenuItemClick,\n      handleSubMenuItemClick,\n      handleMenuMouseEnter,\n      show,\n      close,\n      clickEventKey\n    };\n  }\n});\n\nconst _hoisted_1 = [\"onMouseenter\"];\nconst _hoisted_2 = {\n  key: 0,\n  class: \"__menu__item-icon\"\n};\nconst _hoisted_3 = [\"innerHTML\"];\nconst _hoisted_4 = { class: \"__menu__item-label\" };\nconst _hoisted_5 = { class: \"__menu__item-tips\" };\nconst _hoisted_6 = { class: \"__menu__item-arrow-after\" };\nconst _hoisted_7 = {\n  key: 0,\n  class: \"__menu__item-icon\"\n};\nconst _hoisted_8 = [\"innerHTML\"];\nconst _hoisted_9 = { class: \"__menu__sub__item-label\" };\nconst _hoisted_10 = { class: \"__menu__sub__item-tips\" };\nfunction render(_ctx, _cache, $props, $setup, $data, $options) {\n  return openBlock(), createBlock(Teleport, {\n    to: \"body\",\n    disabled: !_ctx.appendToBody\n  }, [\n    _ctx.showMenu ? (openBlock(), createElementBlock(\n      \"div\",\n      {\n        key: 0,\n        ref: \"MenuWrapper\",\n        class: normalizeClass([\"__menu__wrapper\", _ctx.customClass]),\n        style: normalizeStyle({ width: `${_ctx.menuWidth}px`, top: `${_ctx.menuTop}px`, left: `${_ctx.menuLeft}px` })\n      },\n      [\n        (openBlock(true), createElementBlock(\n          Fragment,\n          null,\n          renderList(_ctx.calcMenuList, (item, index) => {\n            return openBlock(), createElementBlock(\n              Fragment,\n              null,\n              [\n                !item.hidden && !item.line ? (openBlock(), createElementBlock(\"div\", mergeProps({\n                  key: index,\n                  class: [\"__menu__item\", item.disabled && \"disabled\", item.customClass]\n                }, {\n                  [toHandlerKey(_ctx.clickEventKey)]: withModifiers(($event) => _ctx.handleMenuItemClick(item, $event), [\"stop\"])\n                }, {\n                  onMouseenter: ($event) => _ctx.handleMenuMouseEnter($event, item)\n                }), [\n                  _ctx.hasIcon ? (openBlock(), createElementBlock(\"div\", _hoisted_2, [\n                    _ctx.iconType === \"font-icon\" ? withDirectives((openBlock(), createElementBlock(\n                      \"i\",\n                      {\n                        key: 0,\n                        class: normalizeClass(item.icon)\n                      },\n                      null,\n                      2\n                      /* CLASS */\n                    )), [\n                      [vShow, item.icon]\n                    ]) : _ctx.iconType === \"svg-icon\" ? withDirectives((openBlock(), createElementBlock(\"div\", {\n                      key: 1,\n                      class: \"__menu__item-icon-svg\",\n                      innerHTML: item.icon\n                    }, null, 8, _hoisted_3)), [\n                      [vShow, item.icon]\n                    ]) : _ctx.iconType === \"vnode-icon\" ? (openBlock(), createBlock(resolveDynamicComponent(item.icon), { key: 2 })) : createCommentVNode(\"v-if\", true)\n                  ])) : createCommentVNode(\"v-if\", true),\n                  createElementVNode(\n                    \"span\",\n                    _hoisted_4,\n                    toDisplayString(item.label),\n                    1\n                    /* TEXT */\n                  ),\n                  createElementVNode(\n                    \"span\",\n                    _hoisted_5,\n                    toDisplayString(item.tips || \"\"),\n                    1\n                    /* TEXT */\n                  ),\n                  _ctx.hasSubMenu ? (openBlock(), createElementBlock(\n                    \"span\",\n                    {\n                      key: 1,\n                      class: normalizeClass([\"__menu__item-arrow\", { show: _ctx.hasSubMenu && item.children }]),\n                      style: normalizeStyle({ width: _ctx.arrowSize + \"px\", height: _ctx.arrowSize + \"px\" })\n                    },\n                    [\n                      withDirectives(createElementVNode(\n                        \"span\",\n                        _hoisted_6,\n                        null,\n                        512\n                        /* NEED_PATCH */\n                      ), [\n                        [vShow, _ctx.hasSubMenu && item.children]\n                      ])\n                    ],\n                    6\n                    /* CLASS, STYLE */\n                  )) : createCommentVNode(\"v-if\", true),\n                  item.children && item.children.length > 0 && !item.disabled ? withDirectives((openBlock(), createElementBlock(\n                    \"div\",\n                    {\n                      key: 2,\n                      class: \"__menu__sub__wrapper\",\n                      style: normalizeStyle({ width: `${_ctx.menuWidth}px`, top: `${_ctx.subTop}px`, left: `${_ctx.subLeft}px` })\n                    },\n                    [\n                      (openBlock(true), createElementBlock(\n                        Fragment,\n                        null,\n                        renderList(item.children, (subItem, subIndex) => {\n                          return openBlock(), createElementBlock(\n                            Fragment,\n                            null,\n                            [\n                              !subItem.hidden && !subItem.line ? (openBlock(), createElementBlock(\n                                \"div\",\n                                mergeProps({\n                                  key: subIndex,\n                                  class: [\"__menu__sub__item\", subItem.disabled && \"disabled\", subItem.customClass]\n                                }, {\n                                  [toHandlerKey(_ctx.clickEventKey)]: withModifiers(($event) => _ctx.handleSubMenuItemClick(subItem, $event), [\"stop\"])\n                                }),\n                                [\n                                  _ctx.hasIcon ? (openBlock(), createElementBlock(\"div\", _hoisted_7, [\n                                    _ctx.iconType === \"font-icon\" ? withDirectives((openBlock(), createElementBlock(\n                                      \"i\",\n                                      {\n                                        key: 0,\n                                        class: normalizeClass(subItem.icon)\n                                      },\n                                      null,\n                                      2\n                                      /* CLASS */\n                                    )), [\n                                      [vShow, subItem.icon]\n                                    ]) : _ctx.iconType === \"svg-icon\" ? withDirectives((openBlock(), createElementBlock(\"div\", {\n                                      key: 1,\n                                      class: \"__menu__item-icon-svg\",\n                                      innerHTML: subItem.icon\n                                    }, null, 8, _hoisted_8)), [\n                                      [vShow, subItem.icon]\n                                    ]) : _ctx.iconType === \"vnode-icon\" ? (openBlock(), createBlock(resolveDynamicComponent(subItem.icon), { key: 2 })) : createCommentVNode(\"v-if\", true)\n                                  ])) : createCommentVNode(\"v-if\", true),\n                                  createElementVNode(\n                                    \"span\",\n                                    _hoisted_9,\n                                    toDisplayString(subItem.label),\n                                    1\n                                    /* TEXT */\n                                  ),\n                                  createElementVNode(\n                                    \"span\",\n                                    _hoisted_10,\n                                    toDisplayString(subItem.tips || \"\"),\n                                    1\n                                    /* TEXT */\n                                  )\n                                ],\n                                16\n                                /* FULL_PROPS */\n                              )) : createCommentVNode(\"v-if\", true),\n                              subItem.line ? (openBlock(), createElementBlock(\"div\", {\n                                key: subIndex,\n                                class: \"__menu__line\"\n                              })) : createCommentVNode(\"v-if\", true)\n                            ],\n                            64\n                            /* STABLE_FRAGMENT */\n                          );\n                        }),\n                        256\n                        /* UNKEYED_FRAGMENT */\n                      ))\n                    ],\n                    4\n                    /* STYLE */\n                  )), [\n                    [vShow, _ctx.hoverFlag]\n                  ]) : createCommentVNode(\"v-if\", true)\n                ], 16, _hoisted_1)) : createCommentVNode(\"v-if\", true),\n                !item.hidden && item.line ? (openBlock(), createElementBlock(\"div\", {\n                  key: index,\n                  class: \"__menu__line\"\n                })) : createCommentVNode(\"v-if\", true)\n              ],\n              64\n              /* STABLE_FRAGMENT */\n            );\n          }),\n          256\n          /* UNKEYED_FRAGMENT */\n        ))\n      ],\n      6\n      /* CLASS, STYLE */\n    )) : createCommentVNode(\"v-if\", true)\n  ], 8, [\"disabled\"]);\n}\n\nfunction styleInject(css, ref) {\n  if ( ref === void 0 ) ref = {};\n  var insertAt = ref.insertAt;\n\n  if (!css || typeof document === 'undefined') { return; }\n\n  var head = document.head || document.getElementsByTagName('head')[0];\n  var style = document.createElement('style');\n  style.type = 'text/css';\n\n  if (insertAt === 'top') {\n    if (head.firstChild) {\n      head.insertBefore(style, head.firstChild);\n    } else {\n      head.appendChild(style);\n    }\n  } else {\n    head.appendChild(style);\n  }\n\n  if (style.styleSheet) {\n    style.styleSheet.cssText = css;\n  } else {\n    style.appendChild(document.createTextNode(css));\n  }\n}\n\nvar css_248z = \".__menu__mask[data-v-3d21bc0a] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  bottom: 0;\\n  right: 0;\\n  z-index: 999;\\n}\\n.__menu__wrapper[data-v-3d21bc0a] {\\n  --menu-background: #c8f2f0;\\n  --menu-boxShadow: 0 1px 5px #888;\\n  --menu-padding: 5px 0;\\n  --menu-borderRadius: 0;\\n  --menu-item-height: 30px;\\n  --menu-item-padding: 0 10px;\\n  --menu-item-iconSize: 20px;\\n  --menu-item-iconFontSize: 14px;\\n  --menu-item-iconColor: #484852;\\n  --menu-item-labelColor: #484852;\\n  --menu-item-labelFontSize: 14px;\\n  --menu-item-tipsColor: #889;\\n  --menu-item-tipsFontSize: 12px;\\n  --menu-item-arrowColor: #484852;\\n  --menu-item-disabledColor: #bcc;\\n  --menu-item-hoverBackground: rgba(255, 255, 255, 0.8);\\n  --menu-item-hoverIconColor: inherit;\\n  --menu-item-hoverLabelColor: inherit;\\n  --menu-item-hoverTipsColor: inherit;\\n  --menu-item-hoverArrowColor: inherit;\\n  --menu-lineColor: #ccc;\\n  --menu-lineMargin: 5px 0;\\n}\\n.__menu__wrapper[data-v-3d21bc0a] {\\n  position: fixed;\\n  width: 200px;\\n  background: var(--menu-background);\\n  box-shadow: var(--menu-boxShadow);\\n  padding: var(--menu-padding);\\n  border-radius: var(--menu-borderRadius);\\n  z-index: 99999;\\n}\\n.__menu__line[data-v-3d21bc0a],\\n.__menu__sub__line[data-v-3d21bc0a] {\\n  border-top: 1px solid var(--menu-lineColor);\\n  margin: var(--menu-lineMargin);\\n}\\n.__menu__item[data-v-3d21bc0a],\\n.__menu__sub__item[data-v-3d21bc0a] {\\n  display: flex;\\n  height: var(--menu-item-height);\\n  align-items: center;\\n  cursor: pointer;\\n  padding: var(--menu-item-padding);\\n}\\n.__menu__item .__menu__item-icon[data-v-3d21bc0a],\\n.__menu__sub__item .__menu__item-icon[data-v-3d21bc0a] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  color: var(--menu-item-iconColor);\\n  width: var(--menu-item-iconSize);\\n  height: var(--menu-item-iconSize);\\n}\\n.__menu__item .__menu__item-icon i[data-v-3d21bc0a],\\n.__menu__sub__item .__menu__item-icon i[data-v-3d21bc0a] {\\n  font-size: var(--menu-item-iconFontSize);\\n}\\n.__menu__item .__menu__item-icon .__menu__item-icon-svg[data-v-3d21bc0a],\\n.__menu__sub__item .__menu__item-icon .__menu__item-icon-svg[data-v-3d21bc0a] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 100%;\\n  height: 100%;\\n}\\n.__menu__item .__menu__item-label[data-v-3d21bc0a],\\n.__menu__item .__menu__sub__item-label[data-v-3d21bc0a],\\n.__menu__sub__item .__menu__item-label[data-v-3d21bc0a],\\n.__menu__sub__item .__menu__sub__item-label[data-v-3d21bc0a] {\\n  width: 100%;\\n  max-height: 100%;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n  flex: 1;\\n  font-size: var(--menu-item-labelFontSize);\\n  color: var(--menu-item-labelColor);\\n  margin-right: 5px;\\n  overflow: hidden;\\n}\\n.__menu__item .__menu__item-tips[data-v-3d21bc0a],\\n.__menu__item .__menu__sub__item-tips[data-v-3d21bc0a],\\n.__menu__sub__item .__menu__item-tips[data-v-3d21bc0a],\\n.__menu__sub__item .__menu__sub__item-tips[data-v-3d21bc0a] {\\n  font-size: var(--menu-item-tipsFontSize);\\n  color: var(--menu-item-tipsColor);\\n}\\n.__menu__item .__menu__item-arrow[data-v-3d21bc0a],\\n.__menu__sub__item .__menu__item-arrow[data-v-3d21bc0a] {\\n  width: 10px;\\n  height: 10px;\\n  margin-left: 5px;\\n  position: relative;\\n}\\n.__menu__item.disabled[data-v-3d21bc0a],\\n.__menu__sub__item.disabled[data-v-3d21bc0a] {\\n  cursor: not-allowed;\\n}\\n.__menu__item.disabled .__menu__item-icon[data-v-3d21bc0a],\\n.__menu__item.disabled .__menu__item-label[data-v-3d21bc0a],\\n.__menu__item.disabled .__menu__sub__item-label[data-v-3d21bc0a],\\n.__menu__item.disabled .__menu__item-tips[data-v-3d21bc0a],\\n.__menu__item.disabled .__menu__sub__item-tips[data-v-3d21bc0a],\\n.__menu__sub__item.disabled .__menu__item-icon[data-v-3d21bc0a],\\n.__menu__sub__item.disabled .__menu__item-label[data-v-3d21bc0a],\\n.__menu__sub__item.disabled .__menu__sub__item-label[data-v-3d21bc0a],\\n.__menu__sub__item.disabled .__menu__item-tips[data-v-3d21bc0a],\\n.__menu__sub__item.disabled .__menu__sub__item-tips[data-v-3d21bc0a] {\\n  color: var(--menu-item-disabledColor);\\n}\\n.__menu__item.disabled .__menu__item-arrow .__menu__item-arrow-after[data-v-3d21bc0a],\\n.__menu__sub__item.disabled .__menu__item-arrow .__menu__item-arrow-after[data-v-3d21bc0a] {\\n  border-left: var(--menu-item-arrowRealSize) solid var(--menu-item-disabledColor);\\n}\\n.__menu__item[data-v-3d21bc0a]:not(.disabled):hover {\\n  background: var(--menu-item-hoverBackground);\\n}\\n.__menu__item:not(.disabled):hover .__menu__item-icon[data-v-3d21bc0a] {\\n  color: var(--menu-item-hoverIconColor);\\n}\\n.__menu__item:not(.disabled):hover .__menu__item-label[data-v-3d21bc0a] {\\n  color: var(--menu-item-hoverLabelColor);\\n}\\n.__menu__item:not(.disabled):hover .__menu__item-tips[data-v-3d21bc0a] {\\n  color: var(--menu-item-hoverTipsColor);\\n}\\n.__menu__item:not(.disabled):hover .__menu__item-arrow[data-v-3d21bc0a] {\\n  color: var(--menu-item-hoverArrowColor);\\n}\\n.__menu__sub__item[data-v-3d21bc0a]:not(.disabled):hover {\\n  background: var(--menu-item-hoverBackground);\\n}\\n.__menu__sub__item:not(.disabled):hover .__menu__sub__item-label[data-v-3d21bc0a] {\\n  color: var(--menu-item-hoverLabelColor);\\n}\\n.__menu__sub__item:not(.disabled):hover .__menu__sub__item-tips[data-v-3d21bc0a] {\\n  color: var(--menu-item-hoverTipsColor);\\n}\\n.__menu__item-icon[data-v-3d21bc0a] {\\n  width: 20px;\\n  height: 20px;\\n  text-align: center;\\n  line-height: 20px;\\n  margin-right: 4px;\\n}\\n.__menu__item-arrow.show .__menu__item-arrow-after[data-v-3d21bc0a] {\\n  position: absolute;\\n  width: 0;\\n  height: 0;\\n  left: 8px;\\n  border-left: var(--menu-item-arrowRealSize) solid var(--menu-item-arrowColor);\\n  border-top: var(--menu-item-arrowRealSize) solid transparent;\\n  border-bottom: var(--menu-item-arrowRealSize) solid transparent;\\n}\\n.__menu__sub__wrapper[data-v-3d21bc0a] {\\n  position: fixed;\\n  visibility: hidden;\\n  width: 200px;\\n  background: var(--menu-background);\\n  box-shadow: var(--menu-boxShadow);\\n  padding: var(--menu-padding);\\n  border-radius: var(--menu-borderRadius);\\n}\\n.__menu__item:hover .__menu__sub__wrapper[data-v-3d21bc0a] {\\n  visibility: visible;\\n}\";\nstyleInject(css_248z);\n\nscript.render = render;\nscript.__scopeId = \"data-v-3d21bc0a\";\nscript.__file = \"packages/mouse-menu/mouse-menu.vue\";\n\nfunction createClassDom(tag, className, innerText) {\n  let el = document.createElement(tag);\n  el.setAttribute(\"class\", className);\n  if (innerText)\n    el.innerText = innerText;\n  return el;\n}\nscript.install = (app) => {\n  app.component(script.name, script);\n};\nfunction CustomMouseMenu(options) {\n  const className = \"__mouse__menu__container\";\n  let container;\n  if (document.querySelector(`.${className}`)) {\n    container = document.querySelector(`.${className}`);\n  } else {\n    container = createClassDom(\"div\", className);\n  }\n  const vm = createVNode(script, options);\n  render$1(vm, container);\n  document.body.appendChild(container);\n  return vm.component?.proxy;\n}\nlet MouseMenuCtx;\nlet longPressTimer;\nlet longPressTouchStart;\nlet longPressTouchEnd;\nfunction addLongPressListener(el, fn, longPressDuration = 500, longPressPreventDefault) {\n  longPressTouchStart = (e) => {\n    MouseMenuCtx && MouseMenuCtx.close();\n    if (typeof longPressPreventDefault === \"function\") {\n      if (longPressPreventDefault(e, el)) {\n        e.preventDefault();\n      }\n    } else if (typeof longPressPreventDefault === \"boolean\") {\n      if (longPressPreventDefault) {\n        e.preventDefault();\n      }\n    }\n    if (longPressTimer)\n      clearTimeout(longPressTimer);\n    longPressTimer = window.setTimeout(() => {\n      fn(e);\n    }, longPressDuration);\n  };\n  longPressTouchEnd = () => {\n    clearTimeout(longPressTimer);\n  };\n  el.addEventListener(\"touchstart\", longPressTouchStart);\n  el.addEventListener(\"touchmove\", longPressTouchEnd);\n  el.addEventListener(\"touchend\", longPressTouchEnd);\n  el.addEventListener(\"touchcancel\", longPressTouchEnd);\n}\nfunction removeLongPressListener(el) {\n  el.removeEventListener(\"touchstart\", longPressTouchStart);\n  el.removeEventListener(\"touchmove\", longPressTouchEnd);\n  el.removeEventListener(\"touchend\", longPressTouchEnd);\n  el.removeEventListener(\"touchcancel\", longPressTouchEnd);\n}\nlet contextMenuEvent;\nlet longPressEvent;\nconst mounted = (el, binding) => {\n  const { value } = binding;\n  if (value.menuList.length > 0) {\n    contextMenuEvent = (e) => {\n      if (typeof value.disabled === \"function\" && value.disabled(value.params))\n        return;\n      e.preventDefault();\n      MouseMenuCtx = CustomMouseMenu({\n        el,\n        ...value\n      });\n      const { x, y } = e;\n      MouseMenuCtx.show(x, y);\n    };\n    el.removeEventListener(\"contextmenu\", contextMenuEvent);\n    el.addEventListener(\"contextmenu\", contextMenuEvent);\n    if (value.useLongPressInMobile && \"ontouchstart\" in window) {\n      longPressEvent = (e) => {\n        if (typeof value.disabled === \"function\" && value.disabled(value.params))\n          return;\n        MouseMenuCtx = CustomMouseMenu({\n          el,\n          ...value\n        });\n        const { touches } = e;\n        const { clientX, clientY } = touches[0];\n        MouseMenuCtx.show(clientX, clientY);\n        document.onmousedown = null;\n        el.onmousedown = null;\n        setTimeout(() => {\n          document.onmousedown = () => MouseMenuCtx.close();\n          el.onmousedown = () => MouseMenuCtx.close();\n        }, 500);\n      };\n      removeLongPressListener(el);\n      addLongPressListener(\n        el,\n        longPressEvent,\n        value.longPressDuration || 500,\n        value.longPressPreventDefault\n      );\n    }\n  } else {\n    throw new Error(\"At least set one menu list!\");\n  }\n};\nconst unmounted = (el) => {\n  el.removeEventListener(\"contextmenu\", contextMenuEvent);\n  if (\"touchstart\" in window) {\n    removeLongPressListener(el);\n  }\n};\nconst MouseMenuDirective = {\n  mounted,\n  unmounted\n};\n\nexport { CustomMouseMenu, MouseMenuCtx, MouseMenuDirective, script as default };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,IAAM,QAAQ,SAAS,KAAK;AAC1B,MAAI,SAAS,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC;AACxC,MAAI,OAAO,OAAO,QAAQ,UAAU;AAClC,aAAS,OAAO,KAAK;AACnB,UAAI,IAAI,eAAe,GAAG,GAAG;AAC3B,YAAI,IAAI,GAAG,KAAK,OAAO,IAAI,GAAG,MAAM,UAAU;AAC5C,iBAAO,GAAG,IAAI,MAAM,IAAI,GAAG,CAAC;AAAA,QAC9B,OAAO;AACL,iBAAO,GAAG,IAAI,IAAI,GAAG;AAAA,QACvB;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAEA,IAAI,SAAS,gBAAgB;AAAA,EAC3B,MAAM;AAAA,EACN,OAAO;AAAA,IACL,cAAc;AAAA,MACZ,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,WAAW;AAAA,MACT,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,UAAU;AAAA,MACR,MAAM;AAAA,MACN,UAAU;AAAA,IACZ;AAAA,IACA,cAAc;AAAA,MACZ,MAAM;AAAA,IACR;AAAA,IACA,SAAS;AAAA,MACP,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,UAAU;AAAA,MACR,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,gBAAgB;AAAA,IAChB,aAAa;AAAA,IACb,IAAI;AAAA,MACF,MAAM;AAAA,MACN,UAAU;AAAA,IACZ;AAAA,IACA,QAAQ;AAAA,MACN,MAAM,CAAC,QAAQ,QAAQ,OAAO,MAAM;AAAA,IACtC;AAAA,IACA,sBAAsB;AAAA,IACtB,mBAAmB;AAAA,IACnB,yBAAyB,CAAC,UAAU,OAAO;AAAA,IAC3C,qBAAqB;AAAA,MACnB,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,aAAa;AAAA,IACb,UAAU;AAAA,MACR,MAAM;AAAA,IACR;AAAA,EACF;AAAA,EACA,OAAO,CAAC,QAAQ,OAAO;AAAA,EACvB,QAAQ,CAAC,QAAQ,SAAS,UAAU;AAAA,EACpC,MAAM,OAAO,EAAE,KAAK,GAAG;AACrB,UAAM,UAAU,IAAI,CAAC;AACrB,UAAM,SAAS,IAAI,CAAC;AACpB,UAAM,YAAY,IAAI,KAAK;AAC3B,UAAM,UAAU,IAAI,CAAC;AACrB,UAAM,WAAW,IAAI,CAAC;AACtB,UAAM,WAAW,IAAI,KAAK;AAC1B,UAAM,aAAa,IAAI,IAAI;AAC3B,UAAM,eAAe,IAAI,CAAC,CAAC;AAC3B,UAAM,aAAa,SAAS,MAAM,MAAM,SAAS,KAAK,CAAC,SAAS,KAAK,YAAY,KAAK,SAAS,SAAS,CAAC,CAAC;AAC1G,UAAM,YAAY,IAAI,EAAE;AACxB,UAAM,cAAc,IAAI;AACxB,UAAM,UAAU,OAAO,QAAQ;AA/EnC;AAgFM,UAAI,KAAK;AACP,cAAM,SAAS;AACf,YAAI,KAAK,YAAY;AACrB,YAAI,MAAM,gBAAgB;AACxB,iBAAO,KAAK,MAAM,cAAc,EAAE,IAAI,CAAC,SAAS;AAC9C,eAAG,MAAM,YAAY,UAAU,IAAI,IAAI,MAAM,kBAAkB,MAAM,eAAe,IAAI,CAAC;AAAA,UAC3F,CAAC;AAAA,QACH;AACA,YAAI,MAAM,aAAa;AACrB,iBAAO,KAAK,MAAM,WAAW,EAAE,IAAI,CAAC,SAAS;AAC3C,eAAG,MAAM,YAAY,eAAe,IAAI,IAAI,MAAM,eAAe,MAAM,YAAY,IAAI,CAAC;AAAA,UAC1F,CAAC;AAAA,QACH;AACA,YAAI,cAAa,iBAAM,gBAAN,mBAAmB,cAAnB,mBAA8B,MAAM;AACrD,YAAI,YAAY;AACd,oBAAU,QAAQ,CAAC,CAAC,WAAW,CAAC,KAAK;AAAA,QACvC,OAAO;AACL,oBAAU,QAAQ;AAAA,QACpB;AACA,WAAG,MAAM,YAAY,6BAA6B,UAAU,QAAQ,IAAI,IAAI;AAC5E,aAAK,QAAQ,MAAM,QAAQ,WAAW,OAAO,MAAM,EAAE;AAAA,MACvD,OAAO;AACL,aAAK,SAAS,MAAM,QAAQ,WAAW,OAAO,MAAM,EAAE;AAAA,MACxD;AAAA,IACF,CAAC;AACD,UAAM,sBAAsB,CAAC,MAAM,WAAW;AAC5C,UAAI,KAAK;AACP;AACF,UAAI,KAAK,MAAM,OAAO,KAAK,OAAO,YAAY;AAC5C,cAAM,OAAO,KAAK,GAAG,MAAM,QAAQ,WAAW,OAAO,MAAM,IAAI,MAAM;AACrE,YAAI,SAAS;AACX;AAAA,MACJ;AACA,eAAS,QAAQ;AAAA,IACnB;AACA,UAAM,yBAAyB,CAAC,SAAS,WAAW;AAClD,UAAI,QAAQ;AACV;AACF,UAAI,QAAQ,MAAM,OAAO,QAAQ,OAAO,cAAc,CAAC,QAAQ,UAAU;AACvE,cAAM,OAAO,QAAQ,GAAG,MAAM,QAAQ,WAAW,OAAO,MAAM,IAAI,MAAM;AACxE,YAAI,SAAS;AACX;AACF,kBAAU,QAAQ;AAAA,MACpB;AACA,eAAS,QAAQ;AAAA,IACnB;AACA,UAAM,uBAAuB,OAAO,QAAQ,SAAS;AACnD,UAAI,KAAK,YAAY,CAAC,KAAK,UAAU;AACnC,kBAAU,QAAQ;AAClB,cAAM,SAAS;AACf,cAAM,KAAK,OAAO;AAClB,YAAI,CAAC;AACH;AACF,cAAM,EAAE,YAAY,IAAI;AACxB,cAAM,QAAQ,GAAG,cAAc,uBAAuB;AACtD,YAAI,CAAC;AACH;AACF,cAAM,EAAE,aAAa,gBAAgB,cAAc,gBAAgB,IAAI;AACvE,cAAM,EAAE,YAAY,aAAa,aAAa,aAAa,IAAI;AAC/D,cAAM,EAAE,KAAK,KAAK,IAAI,GAAG,sBAAsB;AAC/C,YAAI,OAAO,cAAc,iBAAiB,cAAc,GAAG;AACzD,kBAAQ,QAAQ,OAAO,iBAAiB;AAAA,QAC1C,OAAO;AACL,kBAAQ,QAAQ,OAAO;AAAA,QACzB;AACA,YAAI,MAAM,kBAAkB,eAAe,GAAG;AAC5C,iBAAO,QAAQ,eAAe;AAAA,QAChC,OAAO;AACL,iBAAO,QAAQ,MAAM;AAAA,QACvB;AAAA,MACF;AAAA,IACF;AACA,UAAM,oBAAoB,CAAC,MAAM,aAAa,IAAI,WAAW;AAC3D,aAAO,KAAK,IAAI,CAAC,SAAS;AACxB,YAAI,KAAK,UAAU;AACjB,eAAK,WAAW,kBAAkB,KAAK,UAAU,aAAa,IAAI,MAAM;AAAA,QAC1E;AACA,YAAI,KAAK,SAAS,OAAO,KAAK,UAAU,YAAY;AAClD,eAAK,QAAQ,KAAK,MAAM,QAAQ,aAAa,EAAE;AAAA,QACjD;AACA,YAAI,KAAK,QAAQ,OAAO,KAAK,SAAS,YAAY;AAChD,eAAK,OAAO,KAAK,KAAK,QAAQ,aAAa,EAAE;AAAA,QAC/C;AACA,YAAI,KAAK,QAAQ,OAAO,KAAK,SAAS,YAAY;AAChD,eAAK,OAAO,KAAK,KAAK,QAAQ,aAAa,EAAE;AAAA,QAC/C;AACA,YAAI,KAAK,UAAU,OAAO,KAAK,WAAW,YAAY;AACpD,eAAK,SAAS,KAAK,OAAO,QAAQ,aAAa,EAAE;AAAA,QACnD;AACA,YAAI,KAAK,YAAY,OAAO,KAAK,aAAa,YAAY;AACxD,eAAK,WAAW,KAAK,SAAS,QAAQ,aAAa,EAAE;AAAA,QACvD;AACA,eAAO;AAAA,MACT,CAAC;AAAA,IACH;AACA,UAAM,OAAO,OAAO,IAAI,GAAG,IAAI,MAAM;AACnC,iBAAW,QAAQ,SAAS,iBAAiB,IAAI,GAAG,IAAI,CAAC;AACzD,UAAI,MAAM,cAAc;AACtB,iBAAS,QAAQ,CAAC,MAAM,aAAa,MAAM,QAAQ,WAAW,OAAO,MAAM,EAAE;AAAA,MAC/E,OAAO;AACL,iBAAS,QAAQ;AAAA,MACnB;AACA,UAAI,CAAC,SAAS;AACZ;AACF,mBAAa,QAAQ,MAAM,MAAM,QAAQ;AACzC,mBAAa,QAAQ,kBAAkB,aAAa,OAAO,WAAW,OAAO,MAAM,IAAI,MAAM,MAAM;AACnG,YAAM,SAAS;AACf,YAAM,EAAE,YAAY,aAAa,aAAa,aAAa,IAAI;AAC/D,YAAM,OAAO,YAAY;AACzB,YAAM,aAAa,KAAK;AACxB,YAAM,YAAY,MAAM,aAAa;AACrC,eAAS,QAAQ,IAAI,YAAY,IAAI,cAAc,cAAc,YAAY,IAAI,IAAI;AACrF,cAAQ,QAAQ,IAAI,aAAa,IAAI,eAAe,eAAe,aAAa,IAAI,IAAI;AAAA,IAC1F;AACA,UAAM,QAAQ,MAAM;AAClB,eAAS,QAAQ;AAAA,IACnB;AACA,UAAM,gBAAgB,SAAS,MAAM,MAAM,wBAAwB,kBAAkB,SAAS,eAAe,WAAW;AACxH,UAAM,aAAa,CAAC,MAAM;AACxB,UAAI,YAAY,SAAS,CAAC,YAAY,MAAM,SAAS,EAAE,aAAa,GAAG;AACrE,iBAAS,QAAQ;AACjB,iBAAS,gBAAgB;AAAA,MAC3B;AAAA,IACF;AACA,UAAM,MAAM,MAAM,qBAAqB,CAAC,QAAQ;AAC9C,UAAI,KAAK;AACP,iBAAS,iBAAiB,cAAc,OAAO,UAAU;AAAA,MAC3D,OAAO;AACL,iBAAS,oBAAoB,cAAc,OAAO,UAAU;AAAA,MAC9D;AAAA,IACF,GAAG;AAAA,MACD,WAAW;AAAA,IACb,CAAC;AACD,gBAAY,MAAM;AAChB,eAAS,oBAAoB,cAAc,OAAO,UAAU;AAAA,IAC9D,CAAC;AACD,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACF,CAAC;AAED,IAAM,aAAa,CAAC,cAAc;AAClC,IAAM,aAAa;AAAA,EACjB,KAAK;AAAA,EACL,OAAO;AACT;AACA,IAAM,aAAa,CAAC,WAAW;AAC/B,IAAM,aAAa,EAAE,OAAO,qBAAqB;AACjD,IAAM,aAAa,EAAE,OAAO,oBAAoB;AAChD,IAAM,aAAa,EAAE,OAAO,2BAA2B;AACvD,IAAM,aAAa;AAAA,EACjB,KAAK;AAAA,EACL,OAAO;AACT;AACA,IAAM,aAAa,CAAC,WAAW;AAC/B,IAAM,aAAa,EAAE,OAAO,0BAA0B;AACtD,IAAM,cAAc,EAAE,OAAO,yBAAyB;AACtD,SAASA,QAAO,MAAM,QAAQ,QAAQ,QAAQ,OAAO,UAAU;AAC7D,SAAO,UAAU,GAAG,YAAY,UAAU;AAAA,IACxC,IAAI;AAAA,IACJ,UAAU,CAAC,KAAK;AAAA,EAClB,GAAG;AAAA,IACD,KAAK,YAAY,UAAU,GAAG;AAAA,MAC5B;AAAA,MACA;AAAA,QACE,KAAK;AAAA,QACL,KAAK;AAAA,QACL,OAAO,eAAe,CAAC,mBAAmB,KAAK,WAAW,CAAC;AAAA,QAC3D,OAAO,eAAe,EAAE,OAAO,GAAG,KAAK,SAAS,MAAM,KAAK,GAAG,KAAK,OAAO,MAAM,MAAM,GAAG,KAAK,QAAQ,KAAK,CAAC;AAAA,MAC9G;AAAA,MACA;AAAA,SACG,UAAU,IAAI,GAAG;AAAA,UAChB;AAAA,UACA;AAAA,UACA,WAAW,KAAK,cAAc,CAAC,MAAM,UAAU;AAC7C,mBAAO,UAAU,GAAG;AAAA,cAClB;AAAA,cACA;AAAA,cACA;AAAA,gBACE,CAAC,KAAK,UAAU,CAAC,KAAK,QAAQ,UAAU,GAAG,mBAAmB,OAAO,WAAW;AAAA,kBAC9E,KAAK;AAAA,kBACL,OAAO,CAAC,gBAAgB,KAAK,YAAY,YAAY,KAAK,WAAW;AAAA,gBACvE,GAAG;AAAA,kBACD,CAAC,aAAa,KAAK,aAAa,CAAC,GAAG,cAAc,CAAC,WAAW,KAAK,oBAAoB,MAAM,MAAM,GAAG,CAAC,MAAM,CAAC;AAAA,gBAChH,GAAG;AAAA,kBACD,cAAc,CAAC,WAAW,KAAK,qBAAqB,QAAQ,IAAI;AAAA,gBAClE,CAAC,GAAG;AAAA,kBACF,KAAK,WAAW,UAAU,GAAG,mBAAmB,OAAO,YAAY;AAAA,oBACjE,KAAK,aAAa,cAAc,gBAAgB,UAAU,GAAG;AAAA,sBAC3D;AAAA,sBACA;AAAA,wBACE,KAAK;AAAA,wBACL,OAAO,eAAe,KAAK,IAAI;AAAA,sBACjC;AAAA,sBACA;AAAA,sBACA;AAAA;AAAA,oBAEF,IAAI;AAAA,sBACF,CAAC,OAAO,KAAK,IAAI;AAAA,oBACnB,CAAC,IAAI,KAAK,aAAa,aAAa,gBAAgB,UAAU,GAAG,mBAAmB,OAAO;AAAA,sBACzF,KAAK;AAAA,sBACL,OAAO;AAAA,sBACP,WAAW,KAAK;AAAA,oBAClB,GAAG,MAAM,GAAG,UAAU,IAAI;AAAA,sBACxB,CAAC,OAAO,KAAK,IAAI;AAAA,oBACnB,CAAC,IAAI,KAAK,aAAa,gBAAgB,UAAU,GAAG,YAAY,wBAAwB,KAAK,IAAI,GAAG,EAAE,KAAK,EAAE,CAAC,KAAK,mBAAmB,QAAQ,IAAI;AAAA,kBACpJ,CAAC,KAAK,mBAAmB,QAAQ,IAAI;AAAA,kBACrC;AAAA,oBACE;AAAA,oBACA;AAAA,oBACA,gBAAgB,KAAK,KAAK;AAAA,oBAC1B;AAAA;AAAA,kBAEF;AAAA,kBACA;AAAA,oBACE;AAAA,oBACA;AAAA,oBACA,gBAAgB,KAAK,QAAQ,EAAE;AAAA,oBAC/B;AAAA;AAAA,kBAEF;AAAA,kBACA,KAAK,cAAc,UAAU,GAAG;AAAA,oBAC9B;AAAA,oBACA;AAAA,sBACE,KAAK;AAAA,sBACL,OAAO,eAAe,CAAC,sBAAsB,EAAE,MAAM,KAAK,cAAc,KAAK,SAAS,CAAC,CAAC;AAAA,sBACxF,OAAO,eAAe,EAAE,OAAO,KAAK,YAAY,MAAM,QAAQ,KAAK,YAAY,KAAK,CAAC;AAAA,oBACvF;AAAA,oBACA;AAAA,sBACE,eAAe;AAAA,wBACb;AAAA,wBACA;AAAA,wBACA;AAAA,wBACA;AAAA;AAAA,sBAEF,GAAG;AAAA,wBACD,CAAC,OAAO,KAAK,cAAc,KAAK,QAAQ;AAAA,sBAC1C,CAAC;AAAA,oBACH;AAAA,oBACA;AAAA;AAAA,kBAEF,KAAK,mBAAmB,QAAQ,IAAI;AAAA,kBACpC,KAAK,YAAY,KAAK,SAAS,SAAS,KAAK,CAAC,KAAK,WAAW,gBAAgB,UAAU,GAAG;AAAA,oBACzF;AAAA,oBACA;AAAA,sBACE,KAAK;AAAA,sBACL,OAAO;AAAA,sBACP,OAAO,eAAe,EAAE,OAAO,GAAG,KAAK,SAAS,MAAM,KAAK,GAAG,KAAK,MAAM,MAAM,MAAM,GAAG,KAAK,OAAO,KAAK,CAAC;AAAA,oBAC5G;AAAA,oBACA;AAAA,uBACG,UAAU,IAAI,GAAG;AAAA,wBAChB;AAAA,wBACA;AAAA,wBACA,WAAW,KAAK,UAAU,CAAC,SAAS,aAAa;AAC/C,iCAAO,UAAU,GAAG;AAAA,4BAClB;AAAA,4BACA;AAAA,4BACA;AAAA,8BACE,CAAC,QAAQ,UAAU,CAAC,QAAQ,QAAQ,UAAU,GAAG;AAAA,gCAC/C;AAAA,gCACA,WAAW;AAAA,kCACT,KAAK;AAAA,kCACL,OAAO,CAAC,qBAAqB,QAAQ,YAAY,YAAY,QAAQ,WAAW;AAAA,gCAClF,GAAG;AAAA,kCACD,CAAC,aAAa,KAAK,aAAa,CAAC,GAAG,cAAc,CAAC,WAAW,KAAK,uBAAuB,SAAS,MAAM,GAAG,CAAC,MAAM,CAAC;AAAA,gCACtH,CAAC;AAAA,gCACD;AAAA,kCACE,KAAK,WAAW,UAAU,GAAG,mBAAmB,OAAO,YAAY;AAAA,oCACjE,KAAK,aAAa,cAAc,gBAAgB,UAAU,GAAG;AAAA,sCAC3D;AAAA,sCACA;AAAA,wCACE,KAAK;AAAA,wCACL,OAAO,eAAe,QAAQ,IAAI;AAAA,sCACpC;AAAA,sCACA;AAAA,sCACA;AAAA;AAAA,oCAEF,IAAI;AAAA,sCACF,CAAC,OAAO,QAAQ,IAAI;AAAA,oCACtB,CAAC,IAAI,KAAK,aAAa,aAAa,gBAAgB,UAAU,GAAG,mBAAmB,OAAO;AAAA,sCACzF,KAAK;AAAA,sCACL,OAAO;AAAA,sCACP,WAAW,QAAQ;AAAA,oCACrB,GAAG,MAAM,GAAG,UAAU,IAAI;AAAA,sCACxB,CAAC,OAAO,QAAQ,IAAI;AAAA,oCACtB,CAAC,IAAI,KAAK,aAAa,gBAAgB,UAAU,GAAG,YAAY,wBAAwB,QAAQ,IAAI,GAAG,EAAE,KAAK,EAAE,CAAC,KAAK,mBAAmB,QAAQ,IAAI;AAAA,kCACvJ,CAAC,KAAK,mBAAmB,QAAQ,IAAI;AAAA,kCACrC;AAAA,oCACE;AAAA,oCACA;AAAA,oCACA,gBAAgB,QAAQ,KAAK;AAAA,oCAC7B;AAAA;AAAA,kCAEF;AAAA,kCACA;AAAA,oCACE;AAAA,oCACA;AAAA,oCACA,gBAAgB,QAAQ,QAAQ,EAAE;AAAA,oCAClC;AAAA;AAAA,kCAEF;AAAA,gCACF;AAAA,gCACA;AAAA;AAAA,8BAEF,KAAK,mBAAmB,QAAQ,IAAI;AAAA,8BACpC,QAAQ,QAAQ,UAAU,GAAG,mBAAmB,OAAO;AAAA,gCACrD,KAAK;AAAA,gCACL,OAAO;AAAA,8BACT,CAAC,KAAK,mBAAmB,QAAQ,IAAI;AAAA,4BACvC;AAAA,4BACA;AAAA;AAAA,0BAEF;AAAA,wBACF,CAAC;AAAA,wBACD;AAAA;AAAA,sBAEF;AAAA,oBACF;AAAA,oBACA;AAAA;AAAA,kBAEF,IAAI;AAAA,oBACF,CAAC,OAAO,KAAK,SAAS;AAAA,kBACxB,CAAC,IAAI,mBAAmB,QAAQ,IAAI;AAAA,gBACtC,GAAG,IAAI,UAAU,KAAK,mBAAmB,QAAQ,IAAI;AAAA,gBACrD,CAAC,KAAK,UAAU,KAAK,QAAQ,UAAU,GAAG,mBAAmB,OAAO;AAAA,kBAClE,KAAK;AAAA,kBACL,OAAO;AAAA,gBACT,CAAC,KAAK,mBAAmB,QAAQ,IAAI;AAAA,cACvC;AAAA,cACA;AAAA;AAAA,YAEF;AAAA,UACF,CAAC;AAAA,UACD;AAAA;AAAA,QAEF;AAAA,MACF;AAAA,MACA;AAAA;AAAA,IAEF,KAAK,mBAAmB,QAAQ,IAAI;AAAA,EACtC,GAAG,GAAG,CAAC,UAAU,CAAC;AACpB;AAEA,SAAS,YAAY,KAAKC,MAAK;AAC7B,MAAKA,SAAQ;AAAS,IAAAA,OAAM,CAAC;AAC7B,MAAI,WAAWA,KAAI;AAEnB,MAAI,CAAC,OAAO,OAAO,aAAa,aAAa;AAAE;AAAA,EAAQ;AAEvD,MAAI,OAAO,SAAS,QAAQ,SAAS,qBAAqB,MAAM,EAAE,CAAC;AACnE,MAAI,QAAQ,SAAS,cAAc,OAAO;AAC1C,QAAM,OAAO;AAEb,MAAI,aAAa,OAAO;AACtB,QAAI,KAAK,YAAY;AACnB,WAAK,aAAa,OAAO,KAAK,UAAU;AAAA,IAC1C,OAAO;AACL,WAAK,YAAY,KAAK;AAAA,IACxB;AAAA,EACF,OAAO;AACL,SAAK,YAAY,KAAK;AAAA,EACxB;AAEA,MAAI,MAAM,YAAY;AACpB,UAAM,WAAW,UAAU;AAAA,EAC7B,OAAO;AACL,UAAM,YAAY,SAAS,eAAe,GAAG,CAAC;AAAA,EAChD;AACF;AAEA,IAAI,WAAW;AACf,YAAY,QAAQ;AAEpB,OAAO,SAASD;AAChB,OAAO,YAAY;AACnB,OAAO,SAAS;AAEhB,SAAS,eAAe,KAAK,WAAW,WAAW;AACjD,MAAI,KAAK,SAAS,cAAc,GAAG;AACnC,KAAG,aAAa,SAAS,SAAS;AAClC,MAAI;AACF,OAAG,YAAY;AACjB,SAAO;AACT;AACA,OAAO,UAAU,CAAC,QAAQ;AACxB,MAAI,UAAU,OAAO,MAAM,MAAM;AACnC;AACA,SAAS,gBAAgB,SAAS;AApelC;AAqeE,QAAM,YAAY;AAClB,MAAI;AACJ,MAAI,SAAS,cAAc,IAAI,SAAS,EAAE,GAAG;AAC3C,gBAAY,SAAS,cAAc,IAAI,SAAS,EAAE;AAAA,EACpD,OAAO;AACL,gBAAY,eAAe,OAAO,SAAS;AAAA,EAC7C;AACA,QAAM,KAAK,YAAY,QAAQ,OAAO;AACtC,SAAS,IAAI,SAAS;AACtB,WAAS,KAAK,YAAY,SAAS;AACnC,UAAO,QAAG,cAAH,mBAAc;AACvB;AACA,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,SAAS,qBAAqB,IAAI,IAAI,oBAAoB,KAAK,yBAAyB;AACtF,wBAAsB,CAAC,MAAM;AAC3B,oBAAgB,aAAa,MAAM;AACnC,QAAI,OAAO,4BAA4B,YAAY;AACjD,UAAI,wBAAwB,GAAG,EAAE,GAAG;AAClC,UAAE,eAAe;AAAA,MACnB;AAAA,IACF,WAAW,OAAO,4BAA4B,WAAW;AACvD,UAAI,yBAAyB;AAC3B,UAAE,eAAe;AAAA,MACnB;AAAA,IACF;AACA,QAAI;AACF,mBAAa,cAAc;AAC7B,qBAAiB,OAAO,WAAW,MAAM;AACvC,SAAG,CAAC;AAAA,IACN,GAAG,iBAAiB;AAAA,EACtB;AACA,sBAAoB,MAAM;AACxB,iBAAa,cAAc;AAAA,EAC7B;AACA,KAAG,iBAAiB,cAAc,mBAAmB;AACrD,KAAG,iBAAiB,aAAa,iBAAiB;AAClD,KAAG,iBAAiB,YAAY,iBAAiB;AACjD,KAAG,iBAAiB,eAAe,iBAAiB;AACtD;AACA,SAAS,wBAAwB,IAAI;AACnC,KAAG,oBAAoB,cAAc,mBAAmB;AACxD,KAAG,oBAAoB,aAAa,iBAAiB;AACrD,KAAG,oBAAoB,YAAY,iBAAiB;AACpD,KAAG,oBAAoB,eAAe,iBAAiB;AACzD;AACA,IAAI;AACJ,IAAI;AACJ,IAAM,UAAU,CAAC,IAAI,YAAY;AAC/B,QAAM,EAAE,MAAM,IAAI;AAClB,MAAI,MAAM,SAAS,SAAS,GAAG;AAC7B,uBAAmB,CAAC,MAAM;AACxB,UAAI,OAAO,MAAM,aAAa,cAAc,MAAM,SAAS,MAAM,MAAM;AACrE;AACF,QAAE,eAAe;AACjB,qBAAe,gBAAgB;AAAA,QAC7B;AAAA,QACA,GAAG;AAAA,MACL,CAAC;AACD,YAAM,EAAE,GAAG,EAAE,IAAI;AACjB,mBAAa,KAAK,GAAG,CAAC;AAAA,IACxB;AACA,OAAG,oBAAoB,eAAe,gBAAgB;AACtD,OAAG,iBAAiB,eAAe,gBAAgB;AACnD,QAAI,MAAM,wBAAwB,kBAAkB,QAAQ;AAC1D,uBAAiB,CAAC,MAAM;AACtB,YAAI,OAAO,MAAM,aAAa,cAAc,MAAM,SAAS,MAAM,MAAM;AACrE;AACF,uBAAe,gBAAgB;AAAA,UAC7B;AAAA,UACA,GAAG;AAAA,QACL,CAAC;AACD,cAAM,EAAE,QAAQ,IAAI;AACpB,cAAM,EAAE,SAAS,QAAQ,IAAI,QAAQ,CAAC;AACtC,qBAAa,KAAK,SAAS,OAAO;AAClC,iBAAS,cAAc;AACvB,WAAG,cAAc;AACjB,mBAAW,MAAM;AACf,mBAAS,cAAc,MAAM,aAAa,MAAM;AAChD,aAAG,cAAc,MAAM,aAAa,MAAM;AAAA,QAC5C,GAAG,GAAG;AAAA,MACR;AACA,8BAAwB,EAAE;AAC1B;AAAA,QACE;AAAA,QACA;AAAA,QACA,MAAM,qBAAqB;AAAA,QAC3B,MAAM;AAAA,MACR;AAAA,IACF;AAAA,EACF,OAAO;AACL,UAAM,IAAI,MAAM,6BAA6B;AAAA,EAC/C;AACF;AACA,IAAM,YAAY,CAAC,OAAO;AACxB,KAAG,oBAAoB,eAAe,gBAAgB;AACtD,MAAI,gBAAgB,QAAQ;AAC1B,4BAAwB,EAAE;AAAA,EAC5B;AACF;AACA,IAAM,qBAAqB;AAAA,EACzB;AAAA,EACA;AACF;", "names": ["render", "ref"]}
import {
  require_logic_flow
} from "./chunk-MBYGZM57.js";
import {
  cloneDeep_default,
  lodash_default_default,
  throttle_default
} from "./chunk-EGVUO3GM.js";
import {
  __export,
  __toESM
} from "./chunk-PRH6DGNM.js";

// node_modules/.pnpm/@logicflow+extension@1.2.28/node_modules/@logicflow/extension/es/bpmn/events/StartEvent.js
var import_core = __toESM(require_logic_flow());

// node_modules/.pnpm/@logicflow+extension@1.2.28/node_modules/@logicflow/extension/es/bpmn/getBpmnId.js
var IDS = (
  /** @class */
  function() {
    function IDS4() {
      globalThis._ids = this;
      this._ids = /* @__PURE__ */ new Set();
    }
    IDS4.prototype.generateId = function() {
      var id = "xxxxxxx".replace(/[x]/g, function(c2) {
        var r2 = Math.random() * 16 | 0;
        var v2 = c2 === "x" ? r2 : r2 & 3 | 8;
        return v2.toString(16);
      });
      return id;
    };
    IDS4.prototype.next = function() {
      var id = this.generateId();
      while (this._ids.has(id)) {
        id = this.generateId();
      }
      this._ids.add(id);
      return id;
    };
    return IDS4;
  }()
);
var ids = (globalThis === null || globalThis === void 0 ? void 0 : globalThis._ids) || new IDS();
function getBpmnId() {
  return ids.next();
}

// node_modules/.pnpm/@logicflow+extension@1.2.28/node_modules/@logicflow/extension/es/bpmn/events/StartEvent.js
var __extends = function() {
  var extendStatics = function(d2, b) {
    extendStatics = Object.setPrototypeOf || { __proto__: [] } instanceof Array && function(d3, b2) {
      d3.__proto__ = b2;
    } || function(d3, b2) {
      for (var p2 in b2)
        if (b2.hasOwnProperty(p2))
          d3[p2] = b2[p2];
    };
    return extendStatics(d2, b);
  };
  return function(d2, b) {
    extendStatics(d2, b);
    function __() {
      this.constructor = d2;
    }
    d2.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
  };
}();
var StartEventModel = (
  /** @class */
  function(_super) {
    __extends(StartEventModel2, _super);
    function StartEventModel2(data, graphModel) {
      var _this = this;
      if (!data.id) {
        data.id = "Event_" + getBpmnId();
      }
      if (!data.text) {
        data.text = "";
      }
      if (data.text && typeof data.text === "string") {
        data.text = {
          value: data.text,
          x: data.x,
          y: data.y + 40
        };
      }
      _this = _super.call(this, data, graphModel) || this;
      return _this;
    }
    StartEventModel2.prototype.setAttributes = function() {
      this.r = 18;
    };
    StartEventModel2.prototype.getConnectedTargetRules = function() {
      var rules = _super.prototype.getConnectedTargetRules.call(this);
      var notAsTarget = {
        message: "起始节点不能作为边的终点",
        validate: function() {
          return false;
        }
      };
      rules.push(notAsTarget);
      return rules;
    };
    StartEventModel2.extendKey = "StartEventModel";
    return StartEventModel2;
  }(import_core.CircleNodeModel)
);
var StartEventView = (
  /** @class */
  function(_super) {
    __extends(StartEventView2, _super);
    function StartEventView2() {
      return _super !== null && _super.apply(this, arguments) || this;
    }
    StartEventView2.extendKey = "StartEventNode";
    return StartEventView2;
  }(import_core.CircleNode)
);
var StartEvent = {
  type: "bpmn:startEvent",
  view: StartEventView,
  model: StartEventModel
};
var StartEvent_default = StartEvent;

// node_modules/.pnpm/@logicflow+extension@1.2.28/node_modules/@logicflow/extension/es/bpmn/events/EndEvent.js
var import_core2 = __toESM(require_logic_flow());
var __extends2 = function() {
  var extendStatics = function(d2, b) {
    extendStatics = Object.setPrototypeOf || { __proto__: [] } instanceof Array && function(d3, b2) {
      d3.__proto__ = b2;
    } || function(d3, b2) {
      for (var p2 in b2)
        if (b2.hasOwnProperty(p2))
          d3[p2] = b2[p2];
    };
    return extendStatics(d2, b);
  };
  return function(d2, b) {
    extendStatics(d2, b);
    function __() {
      this.constructor = d2;
    }
    d2.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
  };
}();
var __assign = function() {
  __assign = Object.assign || function(t2) {
    for (var s2, i2 = 1, n2 = arguments.length; i2 < n2; i2++) {
      s2 = arguments[i2];
      for (var p2 in s2)
        if (Object.prototype.hasOwnProperty.call(s2, p2))
          t2[p2] = s2[p2];
    }
    return t2;
  };
  return __assign.apply(this, arguments);
};
var EndEventModel = (
  /** @class */
  function(_super) {
    __extends2(EndEventModel2, _super);
    function EndEventModel2(data, graphModel) {
      var _this = this;
      if (!data.id) {
        data.id = "Event_" + getBpmnId();
      }
      if (!data.text) {
        data.text = "";
      }
      if (data.text && typeof data.text === "string") {
        data.text = {
          value: data.text,
          x: data.x,
          y: data.y + 40
        };
      }
      _this = _super.call(this, data, graphModel) || this;
      return _this;
    }
    EndEventModel2.prototype.setAttributes = function() {
      this.r = 18;
    };
    EndEventModel2.prototype.getConnectedSourceRules = function() {
      var rules = _super.prototype.getConnectedSourceRules.call(this);
      var notAsSource = {
        message: "结束节点不能作为边的起点",
        validate: function() {
          return false;
        }
      };
      rules.push(notAsSource);
      return rules;
    };
    EndEventModel2.extendKey = "EndEventModel";
    return EndEventModel2;
  }(import_core2.CircleNodeModel)
);
var EndEventView = (
  /** @class */
  function(_super) {
    __extends2(EndEventView2, _super);
    function EndEventView2() {
      return _super !== null && _super.apply(this, arguments) || this;
    }
    EndEventView2.prototype.getAnchorStyle = function() {
      return {
        visibility: "hidden"
      };
    };
    EndEventView2.prototype.getShape = function() {
      var model = this.props.model;
      var style2 = model.getNodeStyle();
      var x2 = model.x, y2 = model.y, r2 = model.r;
      var outCircle = _super.prototype.getShape.call(this);
      return (0, import_core2.h)("g", {}, outCircle, (0, import_core2.h)("circle", __assign(__assign({}, style2), { cx: x2, cy: y2, r: r2 - 5 })));
    };
    EndEventView2.extendKey = "EndEventView";
    return EndEventView2;
  }(import_core2.CircleNode)
);
var EndEvent = {
  type: "bpmn:endEvent",
  view: EndEventView,
  model: EndEventModel
};
var EndEvent_default = EndEvent;

// node_modules/.pnpm/@logicflow+extension@1.2.28/node_modules/@logicflow/extension/es/bpmn/gateways/ExclusiveGateway.js
var import_core3 = __toESM(require_logic_flow());
var __extends3 = function() {
  var extendStatics = function(d2, b) {
    extendStatics = Object.setPrototypeOf || { __proto__: [] } instanceof Array && function(d3, b2) {
      d3.__proto__ = b2;
    } || function(d3, b2) {
      for (var p2 in b2)
        if (b2.hasOwnProperty(p2))
          d3[p2] = b2[p2];
    };
    return extendStatics(d2, b);
  };
  return function(d2, b) {
    extendStatics(d2, b);
    function __() {
      this.constructor = d2;
    }
    d2.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
  };
}();
var __assign2 = function() {
  __assign2 = Object.assign || function(t2) {
    for (var s2, i2 = 1, n2 = arguments.length; i2 < n2; i2++) {
      s2 = arguments[i2];
      for (var p2 in s2)
        if (Object.prototype.hasOwnProperty.call(s2, p2))
          t2[p2] = s2[p2];
    }
    return t2;
  };
  return __assign2.apply(this, arguments);
};
var ExclusiveGatewayModel = (
  /** @class */
  function(_super) {
    __extends3(ExclusiveGatewayModel2, _super);
    function ExclusiveGatewayModel2(data, graphModel) {
      var _this = this;
      if (!data.id) {
        data.id = "Gateway_" + getBpmnId();
      }
      if (!data.text) {
        data.text = "";
      }
      if (data.text && typeof data.text === "string") {
        data.text = {
          value: data.text,
          x: data.x,
          y: data.y + 40
        };
      }
      _this = _super.call(this, data, graphModel) || this;
      _this.points = [
        [25, 0],
        [50, 25],
        [25, 50],
        [0, 25]
      ];
      return _this;
    }
    ExclusiveGatewayModel2.extendKey = "ExclusiveGatewayModel";
    return ExclusiveGatewayModel2;
  }(import_core3.PolygonNodeModel)
);
var ExclusiveGatewayView = (
  /** @class */
  function(_super) {
    __extends3(ExclusiveGatewayView2, _super);
    function ExclusiveGatewayView2() {
      return _super !== null && _super.apply(this, arguments) || this;
    }
    ExclusiveGatewayView2.prototype.getShape = function() {
      var model = this.props.model;
      var x2 = model.x, y2 = model.y, width = model.width, height = model.height, points = model.points;
      var style2 = model.getNodeStyle();
      return (0, import_core3.h)("g", {
        transform: "matrix(1 0 0 1 " + (x2 - width / 2) + " " + (y2 - height / 2) + ")"
      }, (0, import_core3.h)("polygon", __assign2(__assign2({}, style2), {
        x: x2,
        y: y2,
        points
      })), (0, import_core3.h)("path", __assign2({ d: "m 16,15 7.42857142857143,9.714285714285715 -7.42857142857143,9.714285714285715 3.428571428571429,0 5.714285714285715,-7.464228571428572 5.714285714285715,7.464228571428572 3.428571428571429,0 -7.42857142857143,-9.714285714285715 7.42857142857143,-9.714285714285715 -3.428571428571429,0 -5.714285714285715,7.464228571428572 -5.714285714285715,-7.464228571428572 -3.428571428571429,0 z" }, style2)));
    };
    ExclusiveGatewayView2.extendKey = "ExclusiveGatewayNode";
    return ExclusiveGatewayView2;
  }(import_core3.PolygonNode)
);
var ExclusiveGateway = {
  type: "bpmn:exclusiveGateway",
  view: ExclusiveGatewayView,
  model: ExclusiveGatewayModel
};
var ExclusiveGateway_default = ExclusiveGateway;

// node_modules/.pnpm/@logicflow+extension@1.2.28/node_modules/@logicflow/extension/es/bpmn/tasks/UserTask.js
var import_core4 = __toESM(require_logic_flow());
var __extends4 = function() {
  var extendStatics = function(d2, b) {
    extendStatics = Object.setPrototypeOf || { __proto__: [] } instanceof Array && function(d3, b2) {
      d3.__proto__ = b2;
    } || function(d3, b2) {
      for (var p2 in b2)
        if (b2.hasOwnProperty(p2))
          d3[p2] = b2[p2];
    };
    return extendStatics(d2, b);
  };
  return function(d2, b) {
    extendStatics(d2, b);
    function __() {
      this.constructor = d2;
    }
    d2.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
  };
}();
var __assign3 = function() {
  __assign3 = Object.assign || function(t2) {
    for (var s2, i2 = 1, n2 = arguments.length; i2 < n2; i2++) {
      s2 = arguments[i2];
      for (var p2 in s2)
        if (Object.prototype.hasOwnProperty.call(s2, p2))
          t2[p2] = s2[p2];
    }
    return t2;
  };
  return __assign3.apply(this, arguments);
};
var UserTaskModel = (
  /** @class */
  function(_super) {
    __extends4(UserTaskModel2, _super);
    function UserTaskModel2(data, graphModel) {
      var _this = this;
      if (!data.id) {
        data.id = "Activity_" + getBpmnId();
      }
      _this = _super.call(this, data, graphModel) || this;
      return _this;
    }
    UserTaskModel2.extendKey = "UserTaskModel";
    return UserTaskModel2;
  }(import_core4.RectNodeModel)
);
var UserTaskView = (
  /** @class */
  function(_super) {
    __extends4(UserTaskView2, _super);
    function UserTaskView2() {
      return _super !== null && _super.apply(this, arguments) || this;
    }
    UserTaskView2.prototype.getLabelShape = function() {
      var model = this.props.model;
      var x2 = model.x, y2 = model.y, width = model.width, height = model.height;
      var style2 = model.getNodeStyle();
      return (0, import_core4.h)("svg", {
        x: x2 - width / 2 + 5,
        y: y2 - height / 2 + 5,
        width: 25,
        height: 25,
        viewBox: "0 0 1274 1024"
      }, (0, import_core4.h)("path", {
        fill: style2.stroke,
        d: "M655.807326 287.35973m-223.989415 0a218.879 218.879 0 1 0 447.978829 0 218.879 218.879 0 1 0-447.978829 0ZM1039.955839 895.482975c-0.490184-212.177424-172.287821-384.030443-384.148513-384.030443-211.862739 0-383.660376 171.85302-384.15056 384.030443L1039.955839 895.482975z"
      }));
    };
    UserTaskView2.prototype.getShape = function() {
      var model = this.props.model;
      var x2 = model.x, y2 = model.y, width = model.width, height = model.height, radius = model.radius;
      var style2 = model.getNodeStyle();
      return (0, import_core4.h)("g", {}, [
        (0, import_core4.h)("rect", __assign3(__assign3({}, style2), {
          x: x2 - width / 2,
          y: y2 - height / 2,
          rx: radius,
          ry: radius,
          width,
          height
        })),
        this.getLabelShape()
      ]);
    };
    UserTaskView2.extendKey = "UserTaskNode";
    return UserTaskView2;
  }(import_core4.RectNode)
);
var UserTask = {
  type: "bpmn:userTask",
  view: UserTaskView,
  model: UserTaskModel
};
var UserTask_default = UserTask;

// node_modules/.pnpm/@logicflow+extension@1.2.28/node_modules/@logicflow/extension/es/bpmn/tasks/ServiceTask.js
var import_core5 = __toESM(require_logic_flow());
var __extends5 = function() {
  var extendStatics = function(d2, b) {
    extendStatics = Object.setPrototypeOf || { __proto__: [] } instanceof Array && function(d3, b2) {
      d3.__proto__ = b2;
    } || function(d3, b2) {
      for (var p2 in b2)
        if (b2.hasOwnProperty(p2))
          d3[p2] = b2[p2];
    };
    return extendStatics(d2, b);
  };
  return function(d2, b) {
    extendStatics(d2, b);
    function __() {
      this.constructor = d2;
    }
    d2.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
  };
}();
var __assign4 = function() {
  __assign4 = Object.assign || function(t2) {
    for (var s2, i2 = 1, n2 = arguments.length; i2 < n2; i2++) {
      s2 = arguments[i2];
      for (var p2 in s2)
        if (Object.prototype.hasOwnProperty.call(s2, p2))
          t2[p2] = s2[p2];
    }
    return t2;
  };
  return __assign4.apply(this, arguments);
};
var ServiceTaskModel = (
  /** @class */
  function(_super) {
    __extends5(ServiceTaskModel2, _super);
    function ServiceTaskModel2(data, graphModel) {
      var _this = this;
      if (!data.id) {
        data.id = "Activity_" + getBpmnId();
      }
      _this = _super.call(this, data, graphModel) || this;
      return _this;
    }
    ServiceTaskModel2.extendKey = "ServiceTaskModel";
    return ServiceTaskModel2;
  }(import_core5.RectNodeModel)
);
var ServiceTaskView = (
  /** @class */
  function(_super) {
    __extends5(ServiceTaskView2, _super);
    function ServiceTaskView2() {
      return _super !== null && _super.apply(this, arguments) || this;
    }
    ServiceTaskView2.prototype.getLabelShape = function() {
      var model = this.props.model;
      var x2 = model.x, y2 = model.y, width = model.width, height = model.height;
      var style2 = model.getNodeStyle();
      return (0, import_core5.h)("svg", {
        x: x2 - width / 2 + 5,
        y: y2 - height / 2 + 5,
        width: 30,
        height: 30,
        viewBox: "0 0 1274 1024"
      }, (0, import_core5.h)("path", {
        fill: style2.stroke,
        d: "M882.527918 434.149934c-2.234901-5.303796-7.311523-8.853645-13.059434-9.138124l-61.390185-3.009544c-6.635117-20.973684-15.521508-41.175795-26.513864-60.282968l42.051745-47.743374c4.308119-4.889357 4.955872-12.004405 1.602498-17.59268-46.384423-77.30362-103.969956-101.422947-106.400309-102.410438-5.332449-2.170432-11.432377-1.090844-15.693424 2.77009L654.674467 240.664222c-17.004279-8.654101-35.092239-15.756869-53.995775-21.210068l-3.26537-66.490344c-0.280386-5.747911-3.833305-10.824533-9.134031-13.059434-1.683339-0.709151-30.193673-12.391215-76.866668-12.051477-46.672996-0.339738-75.18333 11.342326-76.866668 12.051477-5.300726 2.234901-8.853645 7.311523-9.134031 13.059434l-3.26537 66.490344c-18.903535 5.453199-36.991496 12.555967-53.995775 21.210068l-48.450479-43.922349c-4.261047-3.860934-10.360975-4.940522-15.693424-2.77009-2.430352 0.98749-60.015885 25.106818-106.400309 102.410438-3.353374 5.588275-2.705622 12.703323 1.602498 17.59268l42.051745 47.743374c-10.992355 19.107173-19.878746 39.309284-26.513864 60.282968l-61.390185 3.009544c-5.747911 0.284479-10.824533 3.834328-13.059434 9.138124-1.01512 2.415003-24.687262 60.190871-2.822278 147.651828 1.583055 6.324032 7.072069 10.893094 13.57518 11.308557 5.892197 0.37146 11.751648 0.523933 17.419741 0.667196 14.498202 0.372483 28.193109 0.723477 40.908712 4.63353 4.212952 1.294482 6.435573 8.270361 9.349949 18.763342 1.287319 4.640694 2.617617 9.43693 4.484128 14.010085 1.794879 4.393054 3.75758 8.570189 5.66093 12.607132 1.302669 2.765997 2.529613 5.380544 3.689019 8.018627 2.986007 6.803963 2.682086 9.773598 2.578732 10.349719-3.061732 3.672646-6.391571 7.238868-9.91379 11.015891-1.810229 1.943258-3.680832 3.949962-5.523807 5.980201l-22.560832 24.8909c-3.865028 4.261047-4.940522 10.365068-2.774183 15.693424 0.991584 2.426259 25.102724 60.011792 102.414531 106.400309 5.588275 3.353374 12.703323 2.701528 17.591657-1.603521l23.476691-20.682042c2.346441-2.061962 4.64888-4.336772 6.875594-6.534833 9.05319-8.93858 14.018272-12.95608 17.73185-11.576663 3.305279 1.222851 6.907317 3.166109 10.720156 5.228071 3.325745 1.794879 6.764054 3.650133 10.465352 5.288446 6.016017 2.662643 12.120039 4.688789 18.019399 6.65149 6.827499 2.266623 13.279445 4.409426 18.819624 7.275707 1.518586 0.782829 1.926886 0.994654 2.358721 7.830339 0.726547 11.496845 1.25048 23.276123 1.753947 34.672684 0.264013 5.900384 0.528026 11.803837 0.815575 17.700127 0.284479 5.743818 3.833305 10.82044 9.138124 13.05534 1.654686 0.698918 29.371958 12.063757 74.869175 12.063757 0.328481 0 3.65832 0 3.986801 0 45.497217 0 73.214489-11.364839 74.869175-12.063757 5.304819-2.234901 8.853645-7.311523 9.138124-13.05534 0.287549-5.89629 0.551562-11.799744 0.815575-17.700127 0.503467-11.396561 1.027399-23.175839 1.753947-34.672684 0.431835-6.835685 0.840134-7.04751 2.358721-7.830339 5.54018-2.866281 11.992125-5.009084 18.819624-7.275707 5.89936-1.962701 12.003382-3.988848 18.019399-6.65149 3.701299-1.638313 7.139607-3.493567 10.465352-5.288446 3.812839-2.061962 7.414877-4.00522 10.720156-5.228071 3.713578-1.379417 8.67866 2.638083 17.73185 11.576663 2.226714 2.198062 4.529153 4.472871 6.875594 6.534833l23.476691 20.682042c4.888334 4.305049 12.003382 4.956895 17.591657 1.603521 77.311807-46.388517 101.422947-103.97405 102.414531-106.400309 2.166339-5.328355 1.090844-11.432377-2.774183-15.693424l-22.560832-24.8909c-1.842974-2.030239-3.713578-4.036943-5.523807-5.980201-3.52222-3.777023-6.852058-7.343245-9.91379-11.015891-0.103354-0.576121-0.407276-3.545756 2.578732-10.349719 1.159406-2.638083 2.38635-5.252631 3.689019-8.018627 1.90335-4.036943 3.866051-8.214079 5.66093-12.607132 1.866511-4.573155 3.196809-9.369392 4.484128-14.010085 2.914376-10.492982 5.136997-17.46886 9.349949-18.763342 12.715603-3.910053 26.41051-4.261047 40.908712-4.63353 5.668093-0.143263 11.527544-0.295735 17.419741-0.667196 6.503111-0.415462 11.992125-4.984524 13.57518-11.308557C907.21518 494.340805 883.543038 436.564937 882.527918 434.149934zM643.49894 643.761929c-35.280528 35.280528-82.191954 54.711066-132.086317 54.711066s-96.806813-19.430538-132.086317-54.711066c-35.280528-35.279504-54.711066-82.191954-54.711066-132.086317 0-49.894364 19.430538-96.80272 54.711066-132.082224 35.283598-35.284621 82.191954-54.711066 132.086317-54.711066s96.80579 19.426445 132.086317 54.711066c35.279504 35.279504 54.711066 82.187861 54.711066 132.082224C698.210006 561.569976 678.782537 608.482425 643.49894 643.761929z"
      }));
    };
    ServiceTaskView2.prototype.getShape = function() {
      var model = this.props.model;
      var x2 = model.x, y2 = model.y, width = model.width, height = model.height, radius = model.radius;
      var style2 = model.getNodeStyle();
      return (0, import_core5.h)("g", {}, [
        (0, import_core5.h)("rect", __assign4({
          x: x2 - width / 2,
          y: y2 - height / 2,
          rx: radius,
          ry: radius,
          width,
          height
        }, style2)),
        this.getLabelShape()
      ]);
    };
    ServiceTaskView2.extendKey = "ServiceTaskNode";
    return ServiceTaskView2;
  }(import_core5.RectNode)
);
var ServiceTask = {
  type: "bpmn:serviceTask",
  view: ServiceTaskView,
  model: ServiceTaskModel
};
var ServiceTask_default = ServiceTask;

// node_modules/.pnpm/@logicflow+extension@1.2.28/node_modules/@logicflow/extension/es/bpmn/flow/SequenceFlow.js
var import_core6 = __toESM(require_logic_flow());
var __extends6 = function() {
  var extendStatics = function(d2, b) {
    extendStatics = Object.setPrototypeOf || { __proto__: [] } instanceof Array && function(d3, b2) {
      d3.__proto__ = b2;
    } || function(d3, b2) {
      for (var p2 in b2)
        if (b2.hasOwnProperty(p2))
          d3[p2] = b2[p2];
    };
    return extendStatics(d2, b);
  };
  return function(d2, b) {
    extendStatics(d2, b);
    function __() {
      this.constructor = d2;
    }
    d2.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
  };
}();
var SequenceFlowModel = (
  /** @class */
  function(_super) {
    __extends6(SequenceFlowModel2, _super);
    function SequenceFlowModel2(data, graphModel) {
      var _this = this;
      if (!data.id) {
        data.id = "Flow_" + getBpmnId();
      }
      _this = _super.call(this, data, graphModel) || this;
      return _this;
    }
    SequenceFlowModel2.extendKey = "SequenceFlowModel";
    return SequenceFlowModel2;
  }(import_core6.PolylineEdgeModel)
);
var SequenceFlowView = (
  /** @class */
  function(_super) {
    __extends6(SequenceFlowView2, _super);
    function SequenceFlowView2() {
      return _super !== null && _super.apply(this, arguments) || this;
    }
    SequenceFlowView2.extendKey = "SequenceFlowEdge";
    return SequenceFlowView2;
  }(import_core6.PolylineEdge)
);
var SequenceFlow = {
  type: "bpmn:sequenceFlow",
  view: SequenceFlowView,
  model: SequenceFlowModel
};
var SequenceFlow_default = SequenceFlow;

// node_modules/.pnpm/@logicflow+extension@1.2.28/node_modules/@logicflow/extension/es/bpmn/constant.js
var StartEventConfig = {
  width: 40,
  height: 40
};
var EndEventConfig = {
  width: 40,
  height: 40
};
var ExclusiveGatewayConfig = {
  width: 40,
  height: 40
};
var ServiceTaskConfig = {
  width: 100,
  height: 80
};
var UserTaskConfig = {
  width: 100,
  height: 80
};
var theme = {
  rect: {
    radius: 5,
    stroke: "rgb(24, 125, 255)"
  },
  circle: {
    r: 18,
    stroke: "rgb(24, 125, 255)"
  },
  polygon: {
    stroke: "rgb(24, 125, 255)"
  },
  polyline: {
    stroke: "rgb(24, 125, 255)",
    hoverStroke: "rgb(24, 125, 255)",
    selectedStroke: "rgb(24, 125, 255)"
  },
  edgeText: {
    background: {
      fill: "white",
      height: 14,
      stroke: "transparent",
      radius: 3
    }
  }
};

// node_modules/.pnpm/@logicflow+extension@1.2.28/node_modules/@logicflow/extension/es/bpmn/index.js
var BpmnElement = (
  /** @class */
  function() {
    function BpmnElement2(_a) {
      var lf = _a.lf;
      lf.setTheme(theme);
      lf.register(StartEvent_default);
      lf.register(EndEvent_default);
      lf.register(ExclusiveGateway_default);
      lf.register(UserTask_default);
      lf.register(ServiceTask_default);
      if (!lf.options.customBpmnEdge) {
        lf.register(SequenceFlow_default);
        lf.setDefaultEdgeType("bpmn:sequenceFlow");
      }
    }
    BpmnElement2.pluginName = "bpmnElement";
    return BpmnElement2;
  }()
);

// node_modules/.pnpm/@logicflow+extension@1.2.28/node_modules/@logicflow/extension/es/bpmn-adapter/bpmnIds.js
var IDS2 = (
  /** @class */
  function() {
    function IDS4() {
      globalThis._ids = this;
      this._ids = /* @__PURE__ */ new Set();
    }
    IDS4.prototype.generateId = function() {
      var id = "xxxxxxx".replace(/[x]/g, function(c2) {
        var r2 = Math.random() * 16 | 0;
        var v2 = c2 === "x" ? r2 : r2 & 3 | 8;
        return v2.toString(16);
      });
      return id;
    };
    IDS4.prototype.next = function() {
      var id = this.generateId();
      while (this._ids.has(id)) {
        id = this.generateId();
      }
      this._ids.add(id);
      return id;
    };
    return IDS4;
  }()
);
var ids2 = (globalThis === null || globalThis === void 0 ? void 0 : globalThis._ids) || new IDS2();
function getBpmnId2() {
  return ids2.next();
}

// node_modules/.pnpm/@logicflow+extension@1.2.28/node_modules/@logicflow/extension/es/bpmn-adapter/json2xml.js
function type(obj) {
  return Object.prototype.toString.call(obj);
}
function addSpace(depth) {
  return "  ".repeat(depth);
}
function handleAttributes(o2) {
  var t2 = o2;
  if (type(o2) === "[object Object]") {
    t2 = {};
    Object.keys(o2).forEach(function(k2) {
      var tk = k2;
      if (k2.charAt(0) === "-") {
        tk = k2.substring(1);
      }
      t2[tk] = handleAttributes(o2[k2]);
    });
  } else if (Array.isArray(o2)) {
    t2 = [];
    o2.forEach(function(item, index) {
      t2[index] = handleAttributes(item);
    });
  }
  return t2;
}
function getAttributes(obj) {
  var tmp = obj;
  try {
    if (typeof tmp !== "string") {
      tmp = JSON.parse(obj);
    }
  } catch (error) {
    tmp = JSON.stringify(handleAttributes(obj)).replace(/"/g, "'");
  }
  return tmp;
}
var tn = "	\n";
function toXml(obj, name, depth) {
  var frontSpace = addSpace(depth);
  var str = "";
  if (name === "#text") {
    return tn + frontSpace + obj;
  } else if (name === "#cdata-section") {
    return tn + frontSpace + "<![CDATA[" + obj + "]]>";
  } else if (name === "#comment") {
    return tn + frontSpace + "<!--" + obj + "-->";
  }
  if (("" + name).charAt(0) === "-") {
    return " " + name.substring(1) + '="' + getAttributes(obj) + '"';
  } else {
    if (Array.isArray(obj)) {
      obj.forEach(function(item) {
        str += toXml(item, name, depth + 1);
      });
    } else if (type(obj) === "[object Object]") {
      var keys = Object.keys(obj);
      var attributes_1 = "";
      var children_1 = "";
      str += (depth === 0 ? "" : tn + frontSpace) + "<" + name;
      keys.forEach(function(k2) {
        k2.charAt(0) === "-" ? attributes_1 += toXml(obj[k2], k2, depth + 1) : children_1 += toXml(obj[k2], k2, depth + 1);
      });
      str += attributes_1 + (children_1 !== "" ? ">" + children_1 + (tn + frontSpace) + "</" + name + ">" : " />");
    } else {
      str += tn + frontSpace + ("<" + name + ">" + obj.toString() + "</" + name + ">");
    }
  }
  return str;
}
function lfJson2Xml(o2) {
  var xmlStr = "";
  for (var m2 in o2) {
    xmlStr += toXml(o2[m2], m2, 0);
  }
  return xmlStr;
}

// node_modules/.pnpm/@logicflow+extension@1.2.28/node_modules/@logicflow/extension/es/bpmn-adapter/xml2json.js
var XML = function() {
};
XML.ObjTree = function() {
  return this;
};
XML.ObjTree.VERSION = "0.23";
XML.ObjTree.prototype.xmlDecl = '<?xml version="1.0" encoding="UTF-8" ?>\n';
XML.ObjTree.prototype.attr_prefix = "-";
XML.ObjTree.prototype.parseXML = function(xml) {
  var root;
  if (window.DOMParser) {
    var xmldom = new DOMParser();
    var dom = xmldom.parseFromString(xml, "application/xml");
    if (!dom)
      return;
    root = dom.documentElement;
  } else if (window.ActiveXObject) {
    xmldom = new ActiveXObject("Microsoft.XMLDOM");
    xmldom.async = false;
    xmldom.loadXML(xml);
    root = xmldom.documentElement;
  }
  if (!root)
    return;
  return this.parseDOM(root);
};
XML.ObjTree.prototype.parseHTTP = function(url, options, callback) {
  var myOpt = {};
  for (var key in options) {
    myOpt[key] = options[key];
  }
  if (!myOpt.method) {
    if (typeof myOpt.postBody == "undefined" && typeof myOpt.postbody == "undefined" && typeof myOpt.parameters == "undefined") {
      myOpt.method = "get";
    } else {
      myOpt.method = "post";
    }
  }
  if (callback) {
    myOpt.asynchronous = true;
    var __this = this;
    var __func = callback;
    var __save = myOpt.onComplete;
    myOpt.onComplete = function(trans2) {
      var tree;
      if (trans2 && trans2.responseXML && trans2.responseXML.documentElement) {
        tree = __this.parseDOM(trans2.responseXML.documentElement);
      }
      __func(tree, trans2);
      if (__save)
        __save(trans2);
    };
  } else {
    myOpt.asynchronous = false;
  }
  var trans;
  if (typeof HTTP != "undefined" && HTTP.Request) {
    myOpt.uri = url;
    var req = new HTTP.Request(myOpt);
    if (req)
      trans = req.transport;
  } else if (typeof Ajax != "undefined" && Ajax.Request) {
    var req = new Ajax.Request(url, myOpt);
    if (req)
      trans = req.transport;
  }
  if (callback)
    return trans;
  if (trans && trans.responseXML && trans.responseXML.documentElement) {
    return this.parseDOM(trans.responseXML.documentElement);
  }
};
XML.ObjTree.prototype.parseDOM = function(root) {
  if (!root)
    return;
  this.__force_array = {};
  if (this.force_array) {
    for (var i2 = 0; i2 < this.force_array.length; i2++) {
      this.__force_array[this.force_array[i2]] = 1;
    }
  }
  var json = this.parseElement(root);
  if (this.__force_array[root.nodeName]) {
    json = [json];
  }
  if (root.nodeType != 11) {
    var tmp = {};
    tmp[root.nodeName] = json;
    json = tmp;
  }
  return json;
};
XML.ObjTree.prototype.parseElement = function(elem) {
  if (elem.nodeType == 7) {
    return;
  }
  if (elem.nodeType == 3 || elem.nodeType == 4 || elem.nodeType == 8) {
    var bool = elem.nodeValue.match(/[^\x00-\x20]/);
    if (bool == null)
      return;
    return elem.nodeValue;
  }
  var retVal = null;
  var cnt = {};
  if (elem.attributes && elem.attributes.length) {
    retVal = {};
    for (var i2 = 0; i2 < elem.attributes.length; i2++) {
      var key = elem.attributes[i2].nodeName;
      if (typeof key != "string")
        continue;
      var val = elem.attributes[i2].nodeValue;
      try {
        val = JSON.parse(elem.attributes[i2].nodeValue.replace(/'/g, '"'));
      } catch (error) {
        val = elem.attributes[i2].nodeValue;
      }
      if (!val)
        continue;
      key = this.attr_prefix + key;
      if (typeof cnt[key] == "undefined")
        cnt[key] = 0;
      cnt[key]++;
      this.addNode(retVal, key, cnt[key], val);
    }
  }
  if (elem.childNodes && elem.childNodes.length) {
    var textOnly = true;
    if (retVal)
      textOnly = false;
    for (var i2 = 0; i2 < elem.childNodes.length && textOnly; i2++) {
      var nType = elem.childNodes[i2].nodeType;
      if (nType == 3 || nType == 4 || nType == 8)
        continue;
      textOnly = false;
    }
    if (textOnly) {
      if (!retVal)
        retVal = "";
      for (var i2 = 0; i2 < elem.childNodes.length; i2++) {
        retVal += elem.childNodes[i2].nodeValue;
      }
    } else {
      if (!retVal)
        retVal = {};
      for (var i2 = 0; i2 < elem.childNodes.length; i2++) {
        var key = elem.childNodes[i2].nodeName;
        if (typeof key != "string")
          continue;
        var val = this.parseElement(elem.childNodes[i2]);
        if (!val)
          continue;
        if (typeof cnt[key] == "undefined")
          cnt[key] = 0;
        cnt[key]++;
        this.addNode(retVal, key, cnt[key], val);
      }
    }
  } else {
    retVal === null && (retVal = {});
  }
  return retVal;
};
XML.ObjTree.prototype.addNode = function(hash, key, counts, val) {
  if (this.__force_array[key]) {
    if (counts == 1)
      hash[key] = [];
    hash[key][hash[key].length] = val;
  } else if (counts == 1) {
    hash[key] = val;
  } else if (counts == 2) {
    hash[key] = [hash[key], val];
  } else {
    hash[key][hash[key].length] = val;
  }
};
XML.ObjTree.prototype.writeXML = function(tree) {
  var xml = this.hash_to_xml(null, tree);
  return this.xmlDecl + xml;
};
XML.ObjTree.prototype.hash_to_xml = function(name, tree) {
  var elem = [];
  var attr = [];
  for (var key in tree) {
    if (!tree.hasOwnProperty(key))
      continue;
    var val = tree[key];
    if (key.charAt(0) != this.attr_prefix) {
      if (typeof val == "undefined" || val == null) {
        elem[elem.length] = "<" + key + " />";
      } else if (typeof val == "object" && val.constructor == Array) {
        elem[elem.length] = this.array_to_xml(key, val);
      } else if (typeof val == "object") {
        elem[elem.length] = this.hash_to_xml(key, val);
      } else {
        elem[elem.length] = this.scalar_to_xml(key, val);
      }
    } else {
      attr[attr.length] = " " + key.substring(1) + '="' + this.xml_escape(val) + '"';
    }
  }
  var jattr = attr.join("");
  var jelem = elem.join("");
  if (typeof name == "undefined" || name == null) {
  } else if (elem.length > 0) {
    if (jelem.match(/\n/)) {
      jelem = "<" + name + jattr + ">\n" + jelem + "</" + name + ">\n";
    } else {
      jelem = "<" + name + jattr + ">" + jelem + "</" + name + ">\n";
    }
  } else {
    jelem = "<" + name + jattr + " />\n";
  }
  return jelem;
};
XML.ObjTree.prototype.array_to_xml = function(name, array) {
  var out = [];
  for (var i2 = 0; i2 < array.length; i2++) {
    var val = array[i2];
    if (typeof val == "undefined" || val == null) {
      out[out.length] = "<" + name + " />";
    } else if (typeof val == "object" && val.constructor == Array) {
      out[out.length] = this.array_to_xml(name, val);
    } else if (typeof val == "object") {
      out[out.length] = this.hash_to_xml(name, val);
    } else {
      out[out.length] = this.scalar_to_xml(name, val);
    }
  }
  return out.join("");
};
XML.ObjTree.prototype.scalar_to_xml = function(name, text) {
  if (name == "#text") {
    return this.xml_escape(text);
  } else {
    return "<" + name + ">" + this.xml_escape(text) + "</" + name + ">\n";
  }
};
XML.ObjTree.prototype.xml_escape = function(text) {
  return text.replace(/&/g, "&").replace(/</g, "<").replace(/>/g, ">").replace(/"/g, '"');
};
var lfXml2Json = function(xmlData) {
  return new XML.ObjTree().parseXML(xmlData);
};

// node_modules/.pnpm/@logicflow+extension@1.2.28/node_modules/@logicflow/extension/es/bpmn-adapter/index.js
var __extends7 = function() {
  var extendStatics = function(d2, b) {
    extendStatics = Object.setPrototypeOf || { __proto__: [] } instanceof Array && function(d3, b2) {
      d3.__proto__ = b2;
    } || function(d3, b2) {
      for (var p2 in b2)
        if (b2.hasOwnProperty(p2))
          d3[p2] = b2[p2];
    };
    return extendStatics(d2, b);
  };
  return function(d2, b) {
    extendStatics(d2, b);
    function __() {
      this.constructor = d2;
    }
    d2.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
  };
}();
var __assign5 = function() {
  __assign5 = Object.assign || function(t2) {
    for (var s2, i2 = 1, n2 = arguments.length; i2 < n2; i2++) {
      s2 = arguments[i2];
      for (var p2 in s2)
        if (Object.prototype.hasOwnProperty.call(s2, p2))
          t2[p2] = s2[p2];
    }
    return t2;
  };
  return __assign5.apply(this, arguments);
};
var __read = function(o2, n2) {
  var m2 = typeof Symbol === "function" && o2[Symbol.iterator];
  if (!m2)
    return o2;
  var i2 = m2.call(o2), r2, ar = [], e2;
  try {
    while ((n2 === void 0 || n2-- > 0) && !(r2 = i2.next()).done)
      ar.push(r2.value);
  } catch (error) {
    e2 = { error };
  } finally {
    try {
      if (r2 && !r2.done && (m2 = i2["return"]))
        m2.call(i2);
    } finally {
      if (e2)
        throw e2.error;
    }
  }
  return ar;
};
var BpmnElements;
(function(BpmnElements3) {
  BpmnElements3["START"] = "bpmn:startEvent";
  BpmnElements3["END"] = "bpmn:endEvent";
  BpmnElements3["GATEWAY"] = "bpmn:exclusiveGateway";
  BpmnElements3["USER"] = "bpmn:userTask";
  BpmnElements3["SYSTEM"] = "bpmn:serviceTask";
  BpmnElements3["FLOW"] = "bpmn:sequenceFlow";
})(BpmnElements || (BpmnElements = {}));
var defaultAttrs = [
  "-name",
  "-id",
  "bpmn:incoming",
  "bpmn:outgoing",
  "-sourceRef",
  "-targetRef"
];
var defaultRetainedFields = ["properties", "startPoint", "endPoint", "pointsList"];
function toXmlJson(retainedFields) {
  var fields = retainedFields ? defaultRetainedFields.concat(retainedFields) : defaultRetainedFields;
  return function(json) {
    function ToXmlJson(obj) {
      var xmlJson = {};
      if (typeof obj === "string") {
        return obj;
      }
      if (Array.isArray(obj)) {
        return obj.map(function(j2) {
          return ToXmlJson(j2);
        });
      }
      Object.entries(obj).forEach(function(_a) {
        var _b = __read(_a, 2), key = _b[0], value = _b[1];
        if (typeof value !== "object") {
          if (key.indexOf("-") === 0 || ["#text", "#cdata-section", "#comment"].includes(key)) {
            xmlJson[key] = value;
          } else {
            xmlJson["-" + key] = value;
          }
        } else if (fields.includes(key)) {
          xmlJson["-" + key] = ToXmlJson(value);
        } else {
          xmlJson[key] = ToXmlJson(value);
        }
      });
      return xmlJson;
    }
    return ToXmlJson(json);
  };
}
function toNormalJson(xmlJson) {
  var json = {};
  Object.entries(xmlJson).forEach(function(_a) {
    var _b = __read(_a, 2), key = _b[0], value = _b[1];
    if (key.indexOf("-") === 0) {
      json[key.substring(1)] = handleAttributes(value);
    } else if (typeof value === "string") {
      json[key] = value;
    } else if (Object.prototype.toString.call(value) === "[object Object]") {
      json[key] = toNormalJson(value);
    } else if (Array.isArray(value)) {
      json[key] = value.map(function(v2) {
        return toNormalJson(v2);
      });
    } else {
      json[key] = value;
    }
  });
  return json;
}
function convertLf2ProcessData(bpmnProcessData, data, retainedFields) {
  var nodeMap = /* @__PURE__ */ new Map();
  data.nodes.forEach(function(node) {
    var _a;
    var processNode = {
      "-id": node.id
    };
    if ((_a = node.text) === null || _a === void 0 ? void 0 : _a.value) {
      processNode["-name"] = node.text.value;
    }
    if (node.properties) {
      var properties = toXmlJson(retainedFields)(node.properties);
      Object.assign(processNode, properties);
    }
    nodeMap.set(node.id, processNode);
    if (!bpmnProcessData[node.type]) {
      bpmnProcessData[node.type] = processNode;
    } else if (Array.isArray(bpmnProcessData[node.type])) {
      bpmnProcessData[node.type].push(processNode);
    } else {
      bpmnProcessData[node.type] = [bpmnProcessData[node.type], processNode];
    }
  });
  var sequenceFlow = data.edges.map(function(edge) {
    var _a, _b;
    var targetNode = nodeMap.get(edge.targetNodeId);
    if (!targetNode["bpmn:incoming"]) {
      targetNode["bpmn:incoming"] = edge.id;
    } else if (Array.isArray(targetNode["bpmn:incoming"])) {
      targetNode["bpmn:incoming"].push(edge.id);
    } else {
      targetNode["bpmn:incoming"] = [targetNode["bpmn:incoming"], edge.id];
    }
    var edgeConfig = {
      "-id": edge.id,
      "-sourceRef": edge.sourceNodeId,
      "-targetRef": edge.targetNodeId
    };
    if ((_a = edge.text) === null || _a === void 0 ? void 0 : _a.value) {
      edgeConfig["-name"] = (_b = edge.text) === null || _b === void 0 ? void 0 : _b.value;
    }
    if (edge.properties) {
      var properties = toXmlJson(retainedFields)(edge.properties);
      Object.assign(edgeConfig, properties);
    }
    return edgeConfig;
  });
  data.edges.forEach(function(edge) {
    var sourceNode = nodeMap.get(edge.sourceNodeId);
    if (!sourceNode["bpmn:outgoing"]) {
      sourceNode["bpmn:outgoing"] = edge.id;
    } else if (Array.isArray(sourceNode["bpmn:outgoing"])) {
      sourceNode["bpmn:outgoing"].push(edge.id);
    } else {
      sourceNode["bpmn:outgoing"] = [sourceNode["bpmn:outgoing"], edge.id];
    }
  });
  bpmnProcessData[BpmnElements.FLOW] = sequenceFlow;
}
function convertLf2DiagramData(bpmnDiagramData, data) {
  bpmnDiagramData["bpmndi:BPMNEdge"] = data.edges.map(function(edge) {
    var _a;
    var edgeId = edge.id;
    var pointsList = edge.pointsList.map(function(_a2) {
      var x2 = _a2.x, y2 = _a2.y;
      return {
        "-x": x2,
        "-y": y2
      };
    });
    var diagramData = {
      "-id": edgeId + "_di",
      "-bpmnElement": edgeId,
      "di:waypoint": pointsList
    };
    if ((_a = edge.text) === null || _a === void 0 ? void 0 : _a.value) {
      diagramData["bpmndi:BPMNLabel"] = {
        "dc:Bounds": {
          "-x": edge.text.x - edge.text.value.length * 10 / 2,
          "-y": edge.text.y - 7,
          "-width": edge.text.value.length * 10,
          "-height": 14
        }
      };
    }
    return diagramData;
  });
  bpmnDiagramData["bpmndi:BPMNShape"] = data.nodes.map(function(node) {
    var _a;
    var nodeId = node.id;
    var width = 100;
    var height = 80;
    var x2 = node.x, y2 = node.y;
    var shapeConfig = BpmnAdapter.shapeConfigMap.get(node.type);
    if (shapeConfig) {
      width = shapeConfig.width;
      height = shapeConfig.height;
    }
    x2 -= width / 2;
    y2 -= height / 2;
    var diagramData = {
      "-id": nodeId + "_di",
      "-bpmnElement": nodeId,
      "dc:Bounds": {
        "-x": x2,
        "-y": y2,
        "-width": width,
        "-height": height
      }
    };
    if ((_a = node.text) === null || _a === void 0 ? void 0 : _a.value) {
      diagramData["bpmndi:BPMNLabel"] = {
        "dc:Bounds": {
          "-x": node.text.x - node.text.value.length * 10 / 2,
          "-y": node.text.y - 7,
          "-width": node.text.value.length * 10,
          "-height": 14
        }
      };
    }
    return diagramData;
  });
}
function convertBpmn2LfData(bpmnData) {
  var nodes = [];
  var edges = [];
  var definitions = bpmnData["bpmn:definitions"];
  if (definitions) {
    var process_1 = definitions["bpmn:process"];
    Object.keys(process_1).forEach(function(key) {
      if (key.indexOf("bpmn:") === 0) {
        var value = process_1[key];
        if (key === BpmnElements.FLOW) {
          var bpmnEdges = definitions["bpmndi:BPMNDiagram"]["bpmndi:BPMNPlane"]["bpmndi:BPMNEdge"];
          edges = getLfEdges(value, bpmnEdges);
        } else {
          var shapes = definitions["bpmndi:BPMNDiagram"]["bpmndi:BPMNPlane"]["bpmndi:BPMNShape"];
          nodes = nodes.concat(getLfNodes(value, shapes, key));
        }
      }
    });
  }
  return {
    nodes,
    edges
  };
}
function getLfNodes(value, shapes, key) {
  var nodes = [];
  if (Array.isArray(value)) {
    value.forEach(function(val) {
      var shapeValue2;
      if (Array.isArray(shapes)) {
        shapeValue2 = shapes.find(function(shape) {
          return shape["-bpmnElement"] === val["-id"];
        });
      } else {
        shapeValue2 = shapes;
      }
      var node2 = getNodeConfig(shapeValue2, key, val);
      nodes.push(node2);
    });
  } else {
    var shapeValue = void 0;
    if (Array.isArray(shapes)) {
      shapeValue = shapes.find(function(shape) {
        return shape["-bpmnElement"] === value["-id"];
      });
    } else {
      shapeValue = shapes;
    }
    var node = getNodeConfig(shapeValue, key, value);
    nodes.push(node);
  }
  return nodes;
}
function getNodeConfig(shapeValue, type3, processValue) {
  var x2 = Number(shapeValue["dc:Bounds"]["-x"]);
  var y2 = Number(shapeValue["dc:Bounds"]["-y"]);
  var name = processValue["-name"];
  var shapeConfig = BpmnAdapter.shapeConfigMap.get(type3);
  if (shapeConfig) {
    x2 += shapeConfig.width / 2;
    y2 += shapeConfig.height / 2;
  }
  var properties;
  Object.entries(processValue).forEach(function(_a) {
    var _b = __read(_a, 2), key = _b[0], value = _b[1];
    if (defaultAttrs.indexOf(key) === -1) {
      if (!properties)
        properties = {};
      properties[key] = value;
    }
  });
  if (properties) {
    properties = toNormalJson(properties);
  }
  var text;
  if (name) {
    text = {
      x: x2,
      y: y2,
      value: name
    };
    if (shapeValue["bpmndi:BPMNLabel"] && shapeValue["bpmndi:BPMNLabel"]["dc:Bounds"]) {
      var textBounds = shapeValue["bpmndi:BPMNLabel"]["dc:Bounds"];
      text.x = Number(textBounds["-x"]) + Number(textBounds["-width"]) / 2;
      text.y = Number(textBounds["-y"]) + Number(textBounds["-height"]) / 2;
    }
  }
  var nodeConfig = {
    id: shapeValue["-bpmnElement"],
    type: type3,
    x: x2,
    y: y2,
    properties
  };
  if (text) {
    nodeConfig.text = text;
  }
  return nodeConfig;
}
function getLfEdges(value, bpmnEdges) {
  var edges = [];
  if (Array.isArray(value)) {
    value.forEach(function(val) {
      var edgeValue2;
      if (Array.isArray(bpmnEdges)) {
        edgeValue2 = bpmnEdges.find(function(edge) {
          return edge["-bpmnElement"] === val["-id"];
        });
      } else {
        edgeValue2 = bpmnEdges;
      }
      edges.push(getEdgeConfig(edgeValue2, val));
    });
  } else {
    var edgeValue = void 0;
    if (Array.isArray(bpmnEdges)) {
      edgeValue = bpmnEdges.find(function(edge) {
        return edge["-bpmnElement"] === value["-id"];
      });
    } else {
      edgeValue = bpmnEdges;
    }
    edges.push(getEdgeConfig(edgeValue, value));
  }
  return edges;
}
function getEdgeConfig(edgeValue, processValue) {
  var text;
  var textVal = processValue["-name"];
  if (textVal) {
    var textBounds = edgeValue["bpmndi:BPMNLabel"]["dc:Bounds"];
    var textLength_1 = 0;
    textVal.split("\n").forEach(function(textSpan) {
      if (textLength_1 < textSpan.length) {
        textLength_1 = textSpan.length;
      }
    });
    text = {
      value: textVal,
      x: Number(textBounds["-x"]) + textLength_1 * 10 / 2,
      y: Number(textBounds["-y"]) + 7
    };
  }
  var properties;
  Object.entries(processValue).forEach(function(_a) {
    var _b = __read(_a, 2), key = _b[0], value = _b[1];
    if (defaultAttrs.indexOf(key) === -1) {
      if (!properties)
        properties = {};
      properties[key] = value;
    }
  });
  if (properties) {
    properties = toNormalJson(properties);
  }
  var edge = {
    id: processValue["-id"],
    type: BpmnElements.FLOW,
    pointsList: edgeValue["di:waypoint"].map(function(point) {
      return {
        x: Number(point["-x"]),
        y: Number(point["-y"])
      };
    }),
    sourceNodeId: processValue["-sourceRef"],
    targetNodeId: processValue["-targetRef"],
    properties
  };
  if (text) {
    edge.text = text;
  }
  return edge;
}
var BpmnAdapter = (
  /** @class */
  function() {
    function BpmnAdapter2(_a) {
      var _this = this;
      var lf = _a.lf;
      this.adapterOut = function(data, retainedFields) {
        var bpmnProcessData = __assign5({}, _this.processAttributes);
        convertLf2ProcessData(bpmnProcessData, data, retainedFields);
        var bpmnDiagramData = {
          "-id": "BPMNPlane_1",
          "-bpmnElement": bpmnProcessData["-id"]
        };
        convertLf2DiagramData(bpmnDiagramData, data);
        var definitions = _this.definitionAttributes;
        definitions["bpmn:process"] = bpmnProcessData;
        definitions["bpmndi:BPMNDiagram"] = {
          "-id": "BPMNDiagram_1",
          "bpmndi:BPMNPlane": bpmnDiagramData
        };
        var bpmnData = {
          "bpmn:definitions": definitions
        };
        return bpmnData;
      };
      this.adapterIn = function(bpmnData) {
        if (bpmnData) {
          return convertBpmn2LfData(bpmnData);
        }
      };
      lf.adapterIn = function(data) {
        return _this.adapterIn(data);
      };
      lf.adapterOut = function(data, retainedFields) {
        return _this.adapterOut(data, retainedFields);
      };
      this.processAttributes = {
        "-isExecutable": "true",
        "-id": "Process_" + getBpmnId2()
      };
      this.definitionAttributes = {
        "-id": "Definitions_" + getBpmnId2(),
        "-xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance",
        "-xmlns:bpmn": "http://www.omg.org/spec/BPMN/20100524/MODEL",
        "-xmlns:bpmndi": "http://www.omg.org/spec/BPMN/20100524/DI",
        "-xmlns:dc": "http://www.omg.org/spec/DD/20100524/DC",
        "-xmlns:di": "http://www.omg.org/spec/DD/20100524/DI",
        "-targetNamespace": "http://logic-flow.org",
        "-exporter": "logicflow",
        "-exporterVersion": "1.2.0"
      };
    }
    BpmnAdapter2.prototype.setCustomShape = function(key, val) {
      BpmnAdapter2.shapeConfigMap.set(key, val);
    };
    BpmnAdapter2.pluginName = "bpmn-adapter";
    BpmnAdapter2.shapeConfigMap = /* @__PURE__ */ new Map();
    return BpmnAdapter2;
  }()
);
BpmnAdapter.shapeConfigMap.set(BpmnElements.START, {
  width: StartEventConfig.width,
  height: StartEventConfig.height
});
BpmnAdapter.shapeConfigMap.set(BpmnElements.END, {
  width: EndEventConfig.width,
  height: EndEventConfig.height
});
BpmnAdapter.shapeConfigMap.set(BpmnElements.GATEWAY, {
  width: ExclusiveGatewayConfig.width,
  height: ExclusiveGatewayConfig.height
});
BpmnAdapter.shapeConfigMap.set(BpmnElements.SYSTEM, {
  width: ServiceTaskConfig.width,
  height: ServiceTaskConfig.height
});
BpmnAdapter.shapeConfigMap.set(BpmnElements.USER, {
  width: UserTaskConfig.width,
  height: UserTaskConfig.height
});
var BpmnXmlAdapter = (
  /** @class */
  function(_super) {
    __extends7(BpmnXmlAdapter2, _super);
    function BpmnXmlAdapter2(data) {
      var _this = _super.call(this, data) || this;
      _this.adapterXmlIn = function(bpmnData) {
        var json = lfXml2Json(bpmnData);
        return _this.adapterIn(json);
      };
      _this.adapterXmlOut = function(data2, retainedFields) {
        var outData = _this.adapterOut(data2, retainedFields);
        return lfJson2Xml(outData);
      };
      var lf = data.lf;
      lf.adapterIn = _this.adapterXmlIn;
      lf.adapterOut = _this.adapterXmlOut;
      return _this;
    }
    BpmnXmlAdapter2.pluginName = "bpmnXmlAdapter";
    return BpmnXmlAdapter2;
  }(BpmnAdapter)
);

// node_modules/.pnpm/@logicflow+extension@1.2.28/node_modules/@logicflow/extension/es/bpmn-elements/presets/Event/EndEventFactory.js
var import_core7 = __toESM(require_logic_flow());

// node_modules/.pnpm/@logicflow+extension@1.2.28/node_modules/@logicflow/extension/es/bpmn-elements/utils.js
var utils_exports = {};
__export(utils_exports, {
  genBpmnId: () => genBpmnId,
  groupRule: () => groupRule
});
function groupRule() {
  var rule = {
    message: "分组外的节点不允许连接分组内的",
    validate: function(_sourceNode, _targetNode, _sourceAnchor, _targetAnchor) {
      var isSourceNodeInsideTheGroup = !!_sourceNode.properties.parent;
      var isTargetNodeInsideTheGroup = !!_targetNode.properties.parent;
      return !(!isSourceNodeInsideTheGroup && isTargetNodeInsideTheGroup);
    }
  };
  this.targetRules.push(rule);
}
var IDS3 = (
  /** @class */
  function() {
    function IDS4() {
      globalThis._ids = this;
      this._ids = /* @__PURE__ */ new Set();
    }
    IDS4.prototype.generateId = function() {
      var id = "xxxxxxx".replace(/[x]/g, function(c2) {
        var r2 = Math.random() * 16 | 0;
        var v2 = c2 === "x" ? r2 : r2 & 3 | 8;
        return v2.toString(16);
      });
      return id;
    };
    IDS4.prototype.next = function() {
      var id = this.generateId();
      while (this._ids.has(id)) {
        id = this.generateId();
      }
      this._ids.add(id);
      return id;
    };
    return IDS4;
  }()
);
var ids3 = (globalThis === null || globalThis === void 0 ? void 0 : globalThis._ids) || new IDS3();
function genBpmnId() {
  return ids3.next();
}

// node_modules/.pnpm/@logicflow+extension@1.2.28/node_modules/@logicflow/extension/es/bpmn-elements/presets/Event/EndEventFactory.js
var __extends8 = function() {
  var extendStatics = function(d2, b) {
    extendStatics = Object.setPrototypeOf || { __proto__: [] } instanceof Array && function(d3, b2) {
      d3.__proto__ = b2;
    } || function(d3, b2) {
      for (var p2 in b2)
        if (b2.hasOwnProperty(p2))
          d3[p2] = b2[p2];
    };
    return extendStatics(d2, b);
  };
  return function(d2, b) {
    extendStatics(d2, b);
    function __() {
      this.constructor = d2;
    }
    d2.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
  };
}();
var __assign6 = function() {
  __assign6 = Object.assign || function(t2) {
    for (var s2, i2 = 1, n2 = arguments.length; i2 < n2; i2++) {
      s2 = arguments[i2];
      for (var p2 in s2)
        if (Object.prototype.hasOwnProperty.call(s2, p2))
          t2[p2] = s2[p2];
    }
    return t2;
  };
  return __assign6.apply(this, arguments);
};
var __read2 = function(o2, n2) {
  var m2 = typeof Symbol === "function" && o2[Symbol.iterator];
  if (!m2)
    return o2;
  var i2 = m2.call(o2), r2, ar = [], e2;
  try {
    while ((n2 === void 0 || n2-- > 0) && !(r2 = i2.next()).done)
      ar.push(r2.value);
  } catch (error) {
    e2 = { error };
  } finally {
    try {
      if (r2 && !r2.done && (m2 = i2["return"]))
        m2.call(i2);
    } finally {
      if (e2)
        throw e2.error;
    }
  }
  return ar;
};
var __spread = function() {
  for (var ar = [], i2 = 0; i2 < arguments.length; i2++)
    ar = ar.concat(__read2(arguments[i2]));
  return ar;
};
function EndEventFactory(lf) {
  var _a = __read2(lf.useDefinition(), 1), definition = _a[0];
  var view = (
    /** @class */
    function(_super) {
      __extends8(view2, _super);
      function view2() {
        return _super !== null && _super.apply(this, arguments) || this;
      }
      view2.prototype.getAnchorStyle = function() {
        return {
          visibility: "hidden"
        };
      };
      view2.prototype.getShape = function() {
        var _a2;
        var model2 = this.props.model;
        var style2 = model2.getNodeStyle();
        var x2 = model2.x, y2 = model2.y, r2 = model2.r, width = model2.width, height = model2.height, properties = model2.properties;
        var outCircle = _super.prototype.getShape.call(this);
        var definitionType = properties.definitionType;
        var icon = (((_a2 = definition.endEvent) === null || _a2 === void 0 ? void 0 : _a2.get(definitionType)) || {}).icon;
        var i2 = Array.isArray(icon) ? import_core7.h.apply(void 0, __spread(["g", {
          transform: "matrix(1 0 0 1 " + (x2 - width / 2) + " " + (y2 - height / 2) + ")"
        }], icon)) : (0, import_core7.h)("path", {
          transform: "matrix(1 0 0 1 " + (x2 - width / 2) + " " + (y2 - height / 2) + ")",
          d: icon,
          style: "fill: black; stroke-linecap: round; stroke-linejoin: round; stroke: white; stroke-width: 1px;"
        });
        return (0, import_core7.h)("g", {}, outCircle, (0, import_core7.h)("circle", __assign6(__assign6({}, style2), { strokeWidth: 2, cx: x2, cy: y2, r: r2 - 2 })), i2);
      };
      return view2;
    }(import_core7.CircleNode)
  );
  var model = (
    /** @class */
    function(_super) {
      __extends8(model2, _super);
      function model2(data, graphModel) {
        var _a2, _b, _c;
        var _this = this;
        if (!data.id) {
          data.id = "Event_" + genBpmnId();
        }
        if (!data.text) {
          data.text = "";
        }
        if (data.text && typeof data.text === "string") {
          data.text = {
            value: data.text,
            x: data.x,
            y: data.y + 40
          };
        }
        var _d = (((_a2 = definition.endEvent) === null || _a2 === void 0 ? void 0 : _a2.get((_b = data.properties) === null || _b === void 0 ? void 0 : _b.definitionType)) || {}).properties, properties = _d === void 0 ? {} : _d;
        data.properties = __assign6(__assign6({}, properties), data.properties);
        ((_c = data.properties) === null || _c === void 0 ? void 0 : _c.definitionType) && (data.properties.definitionId = "Definition_" + genBpmnId());
        _this = _super.call(this, data, graphModel) || this;
        groupRule.call(_this);
        return _this;
      }
      model2.prototype.setAttributes = function() {
        this.r = 18;
      };
      model2.prototype.getConnectedSourceRules = function() {
        var _this = this;
        var rules = _super.prototype.getConnectedSourceRules.call(this);
        var notAsSource = {
          message: "结束节点不能作为边的起点",
          validate: function(source, _target) {
            if (source === _this) {
              return false;
            }
            return true;
          }
        };
        rules.push(notAsSource);
        return rules;
      };
      return model2;
    }(import_core7.CircleNodeModel)
  );
  return {
    type: "bpmn:endEvent",
    view,
    model
  };
}

// node_modules/.pnpm/@logicflow+extension@1.2.28/node_modules/@logicflow/extension/es/bpmn-elements/presets/Event/IntermediateCatchEvent.js
var import_core8 = __toESM(require_logic_flow());
var __extends9 = function() {
  var extendStatics = function(d2, b) {
    extendStatics = Object.setPrototypeOf || { __proto__: [] } instanceof Array && function(d3, b2) {
      d3.__proto__ = b2;
    } || function(d3, b2) {
      for (var p2 in b2)
        if (b2.hasOwnProperty(p2))
          d3[p2] = b2[p2];
    };
    return extendStatics(d2, b);
  };
  return function(d2, b) {
    extendStatics(d2, b);
    function __() {
      this.constructor = d2;
    }
    d2.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
  };
}();
var __assign7 = function() {
  __assign7 = Object.assign || function(t2) {
    for (var s2, i2 = 1, n2 = arguments.length; i2 < n2; i2++) {
      s2 = arguments[i2];
      for (var p2 in s2)
        if (Object.prototype.hasOwnProperty.call(s2, p2))
          t2[p2] = s2[p2];
    }
    return t2;
  };
  return __assign7.apply(this, arguments);
};
var __read3 = function(o2, n2) {
  var m2 = typeof Symbol === "function" && o2[Symbol.iterator];
  if (!m2)
    return o2;
  var i2 = m2.call(o2), r2, ar = [], e2;
  try {
    while ((n2 === void 0 || n2-- > 0) && !(r2 = i2.next()).done)
      ar.push(r2.value);
  } catch (error) {
    e2 = { error };
  } finally {
    try {
      if (r2 && !r2.done && (m2 = i2["return"]))
        m2.call(i2);
    } finally {
      if (e2)
        throw e2.error;
    }
  }
  return ar;
};
var __spread2 = function() {
  for (var ar = [], i2 = 0; i2 < arguments.length; i2++)
    ar = ar.concat(__read3(arguments[i2]));
  return ar;
};
function IntermediateCatchEventFactory(lf) {
  var _a = __read3(lf.useDefinition(), 1), definition = _a[0];
  var view = (
    /** @class */
    function(_super) {
      __extends9(view2, _super);
      function view2() {
        return _super !== null && _super.apply(this, arguments) || this;
      }
      view2.prototype.getAnchorStyle = function() {
        return {
          visibility: "hidden"
        };
      };
      view2.prototype.getShape = function() {
        var _a2;
        var model2 = this.props.model;
        var style2 = model2.getNodeStyle();
        var x2 = model2.x, y2 = model2.y, r2 = model2.r, width = model2.width, height = model2.height, properties = model2.properties;
        var definitionType = properties.definitionType;
        var icon = (((_a2 = definition.intermediateCatchEvent) === null || _a2 === void 0 ? void 0 : _a2.get(definitionType)) || {}).icon;
        var i2 = Array.isArray(icon) ? import_core8.h.apply(void 0, __spread2(["g", {
          transform: "matrix(1 0 0 1 " + (x2 - width / 2) + " " + (y2 - height / 2) + ")"
        }], icon)) : (0, import_core8.h)("path", {
          transform: "matrix(1 0 0 1 " + (x2 - width / 2) + " " + (y2 - height / 2) + ")",
          d: icon
        });
        return (0, import_core8.h)("g", {}, (0, import_core8.h)("circle", __assign7(__assign7({}, style2), { cx: x2, cy: y2, r: r2, strokeWidth: 1.5 })), (0, import_core8.h)("circle", __assign7(__assign7({}, style2), { cx: x2, cy: y2, r: r2 - 3, strokeWidth: 1.5 })), i2);
      };
      return view2;
    }(import_core8.CircleNode)
  );
  var model = (
    /** @class */
    function(_super) {
      __extends9(model2, _super);
      function model2(data, graphModel) {
        var _a2, _b, _c;
        var _this = this;
        if (!data.id) {
          data.id = "Event_" + genBpmnId();
        }
        if (!data.text) {
          data.text = "";
        }
        if (data.text && typeof data.text === "string") {
          data.text = {
            value: data.text,
            x: data.x,
            y: data.y + 40
          };
        }
        var _d = (((_a2 = definition.intermediateCatchEvent) === null || _a2 === void 0 ? void 0 : _a2.get((_b = data.properties) === null || _b === void 0 ? void 0 : _b.definitionType)) || {}).properties, properties = _d === void 0 ? {} : _d;
        data.properties = __assign7(__assign7({}, properties), data.properties);
        ((_c = data.properties) === null || _c === void 0 ? void 0 : _c.definitionType) && (data.properties.definitionId = "Definition_" + genBpmnId());
        _this = _super.call(this, data, graphModel) || this;
        groupRule.call(_this);
        return _this;
      }
      model2.prototype.setAttributes = function() {
        this.r = 18;
      };
      return model2;
    }(import_core8.CircleNodeModel)
  );
  return {
    type: "bpmn:intermediateCatchEvent",
    view,
    model
  };
}

// node_modules/.pnpm/@logicflow+extension@1.2.28/node_modules/@logicflow/extension/es/bpmn-elements/presets/Event/StartEventFactory.js
var import_core9 = __toESM(require_logic_flow());
var __extends10 = function() {
  var extendStatics = function(d2, b) {
    extendStatics = Object.setPrototypeOf || { __proto__: [] } instanceof Array && function(d3, b2) {
      d3.__proto__ = b2;
    } || function(d3, b2) {
      for (var p2 in b2)
        if (b2.hasOwnProperty(p2))
          d3[p2] = b2[p2];
    };
    return extendStatics(d2, b);
  };
  return function(d2, b) {
    extendStatics(d2, b);
    function __() {
      this.constructor = d2;
    }
    d2.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
  };
}();
var __assign8 = function() {
  __assign8 = Object.assign || function(t2) {
    for (var s2, i2 = 1, n2 = arguments.length; i2 < n2; i2++) {
      s2 = arguments[i2];
      for (var p2 in s2)
        if (Object.prototype.hasOwnProperty.call(s2, p2))
          t2[p2] = s2[p2];
    }
    return t2;
  };
  return __assign8.apply(this, arguments);
};
var __read4 = function(o2, n2) {
  var m2 = typeof Symbol === "function" && o2[Symbol.iterator];
  if (!m2)
    return o2;
  var i2 = m2.call(o2), r2, ar = [], e2;
  try {
    while ((n2 === void 0 || n2-- > 0) && !(r2 = i2.next()).done)
      ar.push(r2.value);
  } catch (error) {
    e2 = { error };
  } finally {
    try {
      if (r2 && !r2.done && (m2 = i2["return"]))
        m2.call(i2);
    } finally {
      if (e2)
        throw e2.error;
    }
  }
  return ar;
};
var __spread3 = function() {
  for (var ar = [], i2 = 0; i2 < arguments.length; i2++)
    ar = ar.concat(__read4(arguments[i2]));
  return ar;
};
function StartEventFactory(lf) {
  var _a = __read4(lf.useDefinition(), 1), definition = _a[0];
  var view = (
    /** @class */
    function(_super) {
      __extends10(view2, _super);
      function view2() {
        return _super !== null && _super.apply(this, arguments) || this;
      }
      view2.prototype.getAnchorStyle = function() {
        return {
          visibility: "hidden"
        };
      };
      view2.prototype.getShape = function() {
        var _a2;
        var model2 = this.props.model;
        var style2 = model2.getNodeStyle();
        var x2 = model2.x, y2 = model2.y, r2 = model2.r, width = model2.width, height = model2.height, properties = model2.properties;
        var definitionType = properties.definitionType, isInterrupting = properties.isInterrupting;
        var icon = (((_a2 = definition.startEvent) === null || _a2 === void 0 ? void 0 : _a2.get(definitionType)) || {}).icon;
        var i2 = Array.isArray(icon) ? import_core9.h.apply(void 0, __spread3(["g", {
          transform: "matrix(1 0 0 1 " + (x2 - width / 2) + " " + (y2 - height / 2) + ")"
        }], icon)) : (0, import_core9.h)("path", {
          transform: "matrix(1 0 0 1 " + (x2 - width / 2) + " " + (y2 - height / 2) + ")",
          d: icon,
          style: "fill: white; stroke-linecap: round; stroke-linejoin: round; stroke: rgb(34, 36, 42); stroke-width: 1px;"
        });
        return (0, import_core9.h)("g", {}, (0, import_core9.h)("circle", __assign8(__assign8({}, style2), { cx: x2, cy: y2, r: r2, strokeDasharray: isInterrupting ? "5,5" : "", strokeWidth: 2 })), i2);
      };
      return view2;
    }(import_core9.CircleNode)
  );
  var model = (
    /** @class */
    function(_super) {
      __extends10(model2, _super);
      function model2(data, graphModel) {
        var _a2, _b, _c;
        var _this = this;
        if (!data.id) {
          data.id = "Event_" + genBpmnId();
        }
        if (!data.text) {
          data.text = "";
        }
        if (data.text && typeof data.text === "string") {
          data.text = {
            value: data.text,
            x: data.x,
            y: data.y + 40
          };
        }
        var _d = (((_a2 = definition.startEvent) === null || _a2 === void 0 ? void 0 : _a2.get((_b = data.properties) === null || _b === void 0 ? void 0 : _b.definitionType)) || {}).properties, properties = _d === void 0 ? {} : _d;
        data.properties = __assign8(__assign8({}, properties), data.properties);
        ((_c = data.properties) === null || _c === void 0 ? void 0 : _c.definitionType) && (data.properties.definitionId = "Definition_" + genBpmnId());
        _this = _super.call(this, data, graphModel) || this;
        return _this;
      }
      model2.prototype.setAttributes = function() {
        this.r = 18;
      };
      model2.prototype.getConnectedTargetRules = function() {
        var _this = this;
        var rules = _super.prototype.getConnectedTargetRules.call(this);
        var notAsSource = {
          message: "起始节点不能作为边的终点",
          validate: function(_source, target) {
            if (target === _this) {
              return false;
            }
            return true;
          }
        };
        rules.push(notAsSource);
        return rules;
      };
      return model2;
    }(import_core9.CircleNodeModel)
  );
  return {
    type: "bpmn:startEvent",
    view,
    model
  };
}

// node_modules/.pnpm/@logicflow+extension@1.2.28/node_modules/@logicflow/extension/es/bpmn-elements/presets/Event/boundaryEventFactory.js
var import_core10 = __toESM(require_logic_flow());
var __extends11 = function() {
  var extendStatics = function(d2, b) {
    extendStatics = Object.setPrototypeOf || { __proto__: [] } instanceof Array && function(d3, b2) {
      d3.__proto__ = b2;
    } || function(d3, b2) {
      for (var p2 in b2)
        if (b2.hasOwnProperty(p2))
          d3[p2] = b2[p2];
    };
    return extendStatics(d2, b);
  };
  return function(d2, b) {
    extendStatics(d2, b);
    function __() {
      this.constructor = d2;
    }
    d2.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
  };
}();
var __assign9 = function() {
  __assign9 = Object.assign || function(t2) {
    for (var s2, i2 = 1, n2 = arguments.length; i2 < n2; i2++) {
      s2 = arguments[i2];
      for (var p2 in s2)
        if (Object.prototype.hasOwnProperty.call(s2, p2))
          t2[p2] = s2[p2];
    }
    return t2;
  };
  return __assign9.apply(this, arguments);
};
var __read5 = function(o2, n2) {
  var m2 = typeof Symbol === "function" && o2[Symbol.iterator];
  if (!m2)
    return o2;
  var i2 = m2.call(o2), r2, ar = [], e2;
  try {
    while ((n2 === void 0 || n2-- > 0) && !(r2 = i2.next()).done)
      ar.push(r2.value);
  } catch (error) {
    e2 = { error };
  } finally {
    try {
      if (r2 && !r2.done && (m2 = i2["return"]))
        m2.call(i2);
    } finally {
      if (e2)
        throw e2.error;
    }
  }
  return ar;
};
var __spread4 = function() {
  for (var ar = [], i2 = 0; i2 < arguments.length; i2++)
    ar = ar.concat(__read5(arguments[i2]));
  return ar;
};
function BoundaryEventFactory(lf) {
  var _a = __read5(lf.useDefinition(), 1), definition = _a[0];
  var view = (
    /** @class */
    function(_super) {
      __extends11(view2, _super);
      function view2() {
        return _super !== null && _super.apply(this, arguments) || this;
      }
      view2.prototype.getAnchorStyle = function() {
        return {
          visibility: "hidden"
        };
      };
      view2.prototype.getShape = function() {
        var _a2;
        var model2 = this.props.model;
        var style2 = model2.getNodeStyle();
        var x2 = model2.x, y2 = model2.y, r2 = model2.r, width = model2.width, height = model2.height, properties = model2.properties;
        var definitionType = properties.definitionType, cancelActivity = properties.cancelActivity;
        var icon = (((_a2 = definition.boundaryEvent) === null || _a2 === void 0 ? void 0 : _a2.get(definitionType)) || {}).icon;
        var i2 = Array.isArray(icon) ? import_core10.h.apply(void 0, __spread4(["g", {
          transform: "matrix(1 0 0 1 " + (x2 - width / 2) + " " + (y2 - height / 2) + ")"
        }], icon)) : (0, import_core10.h)("path", {
          transform: "matrix(1 0 0 1 " + (x2 - width / 2) + " " + (y2 - height / 2) + ")",
          d: icon
        });
        return (0, import_core10.h)("g", {}, (0, import_core10.h)("circle", __assign9(__assign9({}, style2), { cx: x2, cy: y2, r: r2, strokeDasharray: cancelActivity ? "" : "5,5", strokeWidth: 1.5 })), (0, import_core10.h)("circle", __assign9(__assign9({}, style2), { cx: x2, cy: y2, r: r2 - 3, strokeDasharray: cancelActivity ? "" : "5,5", strokeWidth: 1.5 })), i2);
      };
      return view2;
    }(import_core10.CircleNode)
  );
  var model = (
    /** @class */
    function(_super) {
      __extends11(model2, _super);
      function model2(data, graphModel) {
        var _a2, _b, _c;
        var _this = this;
        if (!data.id) {
          data.id = "Event_" + genBpmnId();
        }
        if (!data.text) {
          data.text = "";
        }
        if (data.text && typeof data.text === "string") {
          data.text = {
            value: data.text,
            x: data.x,
            y: data.y + 40
          };
        }
        var _d = (((_a2 = definition.boundaryEvent) === null || _a2 === void 0 ? void 0 : _a2.get((_b = data.properties) === null || _b === void 0 ? void 0 : _b.definitionType)) || {}).properties, properties = _d === void 0 ? {} : _d;
        data.properties = __assign9(__assign9({ attachedToRef: "", cancelActivity: true }, properties), data.properties);
        ((_c = data.properties) === null || _c === void 0 ? void 0 : _c.definitionType) && (data.properties.definitionId = "Definition_" + genBpmnId());
        _this = _super.call(this, data, graphModel) || this;
        groupRule.call(_this);
        return _this;
      }
      model2.prototype.initNodeData = function(data) {
        _super.prototype.initNodeData.call(this, data);
        this.r = 20;
        this.autoToFront = false;
        this.zIndex = 99999;
      };
      model2.prototype.setAttributes = function() {
        this.r = 18;
      };
      return model2;
    }(import_core10.CircleNodeModel)
  );
  return {
    type: "bpmn:boundaryEvent",
    view,
    model
  };
}

// node_modules/.pnpm/@logicflow+extension@1.2.28/node_modules/@logicflow/extension/es/bpmn-elements/presets/Event/IntermediateThrowEvent.js
var import_core11 = __toESM(require_logic_flow());
var __extends12 = function() {
  var extendStatics = function(d2, b) {
    extendStatics = Object.setPrototypeOf || { __proto__: [] } instanceof Array && function(d3, b2) {
      d3.__proto__ = b2;
    } || function(d3, b2) {
      for (var p2 in b2)
        if (b2.hasOwnProperty(p2))
          d3[p2] = b2[p2];
    };
    return extendStatics(d2, b);
  };
  return function(d2, b) {
    extendStatics(d2, b);
    function __() {
      this.constructor = d2;
    }
    d2.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
  };
}();
var __assign10 = function() {
  __assign10 = Object.assign || function(t2) {
    for (var s2, i2 = 1, n2 = arguments.length; i2 < n2; i2++) {
      s2 = arguments[i2];
      for (var p2 in s2)
        if (Object.prototype.hasOwnProperty.call(s2, p2))
          t2[p2] = s2[p2];
    }
    return t2;
  };
  return __assign10.apply(this, arguments);
};
var __read6 = function(o2, n2) {
  var m2 = typeof Symbol === "function" && o2[Symbol.iterator];
  if (!m2)
    return o2;
  var i2 = m2.call(o2), r2, ar = [], e2;
  try {
    while ((n2 === void 0 || n2-- > 0) && !(r2 = i2.next()).done)
      ar.push(r2.value);
  } catch (error) {
    e2 = { error };
  } finally {
    try {
      if (r2 && !r2.done && (m2 = i2["return"]))
        m2.call(i2);
    } finally {
      if (e2)
        throw e2.error;
    }
  }
  return ar;
};
var __spread5 = function() {
  for (var ar = [], i2 = 0; i2 < arguments.length; i2++)
    ar = ar.concat(__read6(arguments[i2]));
  return ar;
};
function IntermediateThrowEventFactory(lf) {
  var _a = __read6(lf.useDefinition(), 1), definition = _a[0];
  var view = (
    /** @class */
    function(_super) {
      __extends12(view2, _super);
      function view2() {
        return _super !== null && _super.apply(this, arguments) || this;
      }
      view2.prototype.getAnchorStyle = function() {
        return {
          visibility: "hidden"
        };
      };
      view2.prototype.getShape = function() {
        var _a2;
        var model2 = this.props.model;
        var style2 = model2.getNodeStyle();
        var x2 = model2.x, y2 = model2.y, r2 = model2.r, width = model2.width, height = model2.height, properties = model2.properties;
        var definitionType = properties.definitionType;
        var icon = (((_a2 = definition.intermediateThrowEvent) === null || _a2 === void 0 ? void 0 : _a2.get(definitionType)) || {}).icon;
        var i2 = Array.isArray(icon) ? import_core11.h.apply(void 0, __spread5(["g", {
          transform: "matrix(1 0 0 1 " + (x2 - width / 2) + " " + (y2 - height / 2) + ")"
        }], icon)) : (0, import_core11.h)("path", {
          transform: "matrix(1 0 0 1 " + (x2 - width / 2) + " " + (y2 - height / 2) + ")",
          d: icon,
          style: "fill: black"
        });
        return (0, import_core11.h)("g", {}, (0, import_core11.h)("circle", __assign10(__assign10({}, style2), { cx: x2, cy: y2, r: r2, strokeWidth: 1.5 })), (0, import_core11.h)("circle", __assign10(__assign10({}, style2), { cx: x2, cy: y2, r: r2 - 3, strokeWidth: 1.5 })), i2);
      };
      return view2;
    }(import_core11.CircleNode)
  );
  var model = (
    /** @class */
    function(_super) {
      __extends12(model2, _super);
      function model2(data, graphModel) {
        var _a2, _b, _c;
        var _this = this;
        if (!data.id) {
          data.id = "Event_" + genBpmnId();
        }
        if (!data.text) {
          data.text = "";
        }
        if (data.text && typeof data.text === "string") {
          data.text = {
            value: data.text,
            x: data.x,
            y: data.y + 40
          };
        }
        var _d = (((_a2 = definition.intermediateThrowEvent) === null || _a2 === void 0 ? void 0 : _a2.get((_b = data.properties) === null || _b === void 0 ? void 0 : _b.definitionType)) || {}).properties, properties = _d === void 0 ? {} : _d;
        data.properties = __assign10(__assign10({}, properties), data.properties);
        ((_c = data.properties) === null || _c === void 0 ? void 0 : _c.definitionType) && (data.properties.definitionId = "Definition_" + genBpmnId());
        _this = _super.call(this, data, graphModel) || this;
        groupRule.call(_this);
        return _this;
      }
      model2.prototype.setAttributes = function() {
        this.r = 18;
      };
      return model2;
    }(import_core11.CircleNodeModel)
  );
  return {
    type: "bpmn:intermediateThrowEvent",
    view,
    model
  };
}

// node_modules/.pnpm/@logicflow+extension@1.2.28/node_modules/@logicflow/extension/es/bpmn-elements/presets/Event/index.js
function registerEventNodes(lf) {
  lf.register(StartEventFactory(lf));
  lf.register(EndEventFactory(lf));
  lf.register(IntermediateCatchEventFactory(lf));
  lf.register(IntermediateThrowEventFactory(lf));
  lf.register(BoundaryEventFactory(lf));
}

// node_modules/.pnpm/@logicflow+extension@1.2.28/node_modules/@logicflow/extension/es/bpmn-elements/presets/icons.js
var icons_exports = {};
__export(icons_exports, {
  compensationIcon: () => compensationIcon,
  conditionalIcon: () => conditionalIcon,
  errorIcon: () => errorIcon,
  escalationIcon: () => escalationIcon,
  exclusiveIcon: () => exclusiveIcon,
  inclusiveIcon: () => inclusiveIcon,
  linkIcon: () => linkIcon,
  loopMarker: () => loopMarker,
  manualTaskIcon: () => manualTaskIcon,
  messageIcon: () => messageIcon,
  parallelIcon: () => parallelIcon,
  parallelMarker: () => parallelMarker,
  scriptTaskIcon: () => scriptTaskIcon,
  sequentialMarker: () => sequentialMarker,
  serviceTaskIcon: () => serviceTaskIcon,
  signalIcon: () => signalIcon,
  style: () => style,
  terminateIcon: () => terminateIcon,
  timerIcon: () => timerIcon,
  userTaskIcon: () => userTaskIcon
});
var import_core12 = __toESM(require_logic_flow());
var messageIcon = "m 8.459999999999999,11.34 l 0,12.6 l 18.900000000000002,0 l 0,-12.6 z l 9.450000000000001,5.4 l 9.450000000000001,-5.4";
var timerIcon = [
  (0, import_core12.h)("circle", {
    cx: 18,
    cy: 18,
    r: 11,
    style: "stroke-linecap: round;stroke-linejoin: round;stroke: rgb(34, 36, 42);stroke-width: 2px;fill: white"
  }),
  (0, import_core12.h)("path", {
    d: "M 18,18 l 2.25,-7.5 m -2.25,7.5 l 5.25,1.5",
    style: "fill: none; stroke-linecap: round; stroke-linejoin: round; stroke: rgb(34, 36, 42); stroke-width: 2px;"
  }),
  (0, import_core12.h)("path", {
    d: "M 18,18 m 0,7.5 l -0,2.25",
    transform: "rotate(0,18,18)",
    style: "fill: none; stroke-linecap: round; stroke-linejoin: round; stroke: rgb(34, 36, 42); stroke-width: 1px;"
  }),
  (0, import_core12.h)("path", {
    d: "M 18,18 m 0,7.5 l -0,2.25",
    transform: "rotate(30,18,18)",
    style: "fill: none; stroke-linecap: round; stroke-linejoin: round; stroke: rgb(34, 36, 42); stroke-width: 1px;"
  }),
  (0, import_core12.h)("path", {
    d: "M 18,18 m 0,7.5 l -0,2.25",
    transform: "rotate(60,18,18)",
    style: "fill: none; stroke-linecap: round; stroke-linejoin: round; stroke: rgb(34, 36, 42); stroke-width: 1px;"
  }),
  (0, import_core12.h)("path", {
    d: "M 18,18 m 0,7.5 l -0,2.25",
    transform: "rotate(90,18,18)",
    style: "fill: none; stroke-linecap: round; stroke-linejoin: round; stroke: rgb(34, 36, 42); stroke-width: 1px;"
  }),
  (0, import_core12.h)("path", {
    d: "M 18,18 m 0,7.5 l -0,2.25",
    transform: "rotate(120,18,18)",
    style: "fill: none; stroke-linecap: round; stroke-linejoin: round; stroke: rgb(34, 36, 42); stroke-width: 1px;"
  }),
  (0, import_core12.h)("path", {
    d: "M 18,18 m 0,7.5 l -0,2.25",
    transform: "rotate(150,18,18)",
    style: "fill: none; stroke-linecap: round; stroke-linejoin: round; stroke: rgb(34, 36, 42); stroke-width: 1px;"
  }),
  (0, import_core12.h)("path", {
    d: "M 18,18 m 0,7.5 l -0,2.25",
    transform: "rotate(180,18,18)",
    style: "fill: none; stroke-linecap: round; stroke-linejoin: round; stroke: rgb(34, 36, 42); stroke-width: 1px;"
  }),
  (0, import_core12.h)("path", {
    d: "M 18,18 m 0,7.5 l -0,2.25",
    transform: "rotate(210,18,18)",
    style: "fill: none; stroke-linecap: round; stroke-linejoin: round; stroke: rgb(34, 36, 42); stroke-width: 1px;"
  }),
  (0, import_core12.h)("path", {
    d: "M 18,18 m 0,7.5 l -0,2.25",
    transform: "rotate(240,18,18)",
    style: "fill: none; stroke-linecap: round; stroke-linejoin: round; stroke: rgb(34, 36, 42); stroke-width: 1px;"
  }),
  (0, import_core12.h)("path", {
    d: "M 18,18 m 0,7.5 l -0,2.25",
    transform: "rotate(270,18,18)",
    style: "fill: none; stroke-linecap: round; stroke-linejoin: round; stroke: rgb(34, 36, 42); stroke-width: 1px;"
  }),
  (0, import_core12.h)("path", {
    d: "M 18,18 m 0,7.5 l -0,2.25",
    transform: "rotate(300,18,18)",
    style: "fill: none; stroke-linecap: round; stroke-linejoin: round; stroke: rgb(34, 36, 42); stroke-width: 1px;"
  }),
  (0, import_core12.h)("path", {
    d: "M 18,18 m 0,7.5 l -0,2.25",
    transform: "rotate(330,18,18)",
    style: "fill: none; stroke-linecap: round; stroke-linejoin: round; stroke: rgb(34, 36, 42); stroke-width: 1px;"
  })
];
var errorIcon = "m 7.2,25.991999999999997 0.09350000000000001,-0.025300000000000003 7.3392,-9.610700000000001 7.667000000000001,8.9661 4.7003,-18.2204 -5.8707,11.6501 -7.299600000000001,-9.585400000000002 z";
var escalationIcon = "M 18,7.2 l 8,20 l -8,-7 l -8,7 Z";
var compensationIcon = "m 7.92,18 9,-6.5 0,13 z m 9.3,-0.4 8.7,-6.1 0,13 -8.7,-6.1 z";
var conditionalIcon = "M 10.5,8.5 l 14.5,0 l 0,18 l -14.5,0 Z M 12.5,11.5 l 10.5,0 M 12.5,14.5 l 10.5,0 M 12.5,17.5 l 10.5,0 M 12.5,20.5 l 10.5,0 M 12.5,23.5 l 10.5,0 M 12.5,26.5 l 10.5,0 ";
var linkIcon = "m 20.52,9.468 0,4.4375 -13.5,0 0,6.75 13.5,0 0,4.4375 9.84375,-7.8125 -9.84375,-7.8125 z";
var signalIcon = "M 18,7.2 l 9,16.2 l -18,0 Z";
var terminateIcon = [
  (0, import_core12.h)("circle", {
    cx: 18,
    cy: 18,
    r: 10,
    style: "stroke-linecap: round; stroke-linejoin: round; stroke: rgb(34, 36, 42); stroke-width: 4px; fill: rgb(34, 36, 42);"
  })
];
var exclusiveIcon = "m 16,15 7.42857142857143,9.714285714285715 -7.42857142857143,9.714285714285715 3.428571428571429,0 5.714285714285715,-7.464228571428572 5.714285714285715,7.464228571428572 3.428571428571429,0 -7.42857142857143,-9.714285714285715 7.42857142857143,-9.714285714285715 -3.428571428571429,0 -5.714285714285715,7.464228571428572 -5.714285714285715,-7.464228571428572 -3.428571428571429,0 z";
var parallelIcon = "m 23,10 0,12.5 -12.5,0 0,5 12.5,0 0,12.5 5,0 0,-12.5 12.5,0 0,-5 -12.5,0 0,-12.5 -5,0 z";
var inclusiveIcon = (0, import_core12.h)("circle", {
  cx: 25,
  cy: 25,
  r: 13,
  style: "stroke-linecap: round; stroke-linejoin: round; stroke: rgb(34, 36, 42); stroke-width: 2.5px; fill: white;"
});
var serviceTaskIcon = "M882.527918 434.149934c-2.234901-5.303796-7.311523-8.853645-13.059434-9.138124l-61.390185-3.009544c-6.635117-20.973684-15.521508-41.175795-26.513864-60.282968l42.051745-47.743374c4.308119-4.889357 4.955872-12.004405 1.602498-17.59268-46.384423-77.30362-103.969956-101.422947-106.400309-102.410438-5.332449-2.170432-11.432377-1.090844-15.693424 2.77009L654.674467 240.664222c-17.004279-8.654101-35.092239-15.756869-53.995775-21.210068l-3.26537-66.490344c-0.280386-5.747911-3.833305-10.824533-9.134031-13.059434-1.683339-0.709151-30.193673-12.391215-76.866668-12.051477-46.672996-0.339738-75.18333 11.342326-76.866668 12.051477-5.300726 2.234901-8.853645 7.311523-9.134031 13.059434l-3.26537 66.490344c-18.903535 5.453199-36.991496 12.555967-53.995775 21.210068l-48.450479-43.922349c-4.261047-3.860934-10.360975-4.940522-15.693424-2.77009-2.430352 0.98749-60.015885 25.106818-106.400309 102.410438-3.353374 5.588275-2.705622 12.703323 1.602498 17.59268l42.051745 47.743374c-10.992355 19.107173-19.878746 39.309284-26.513864 60.282968l-61.390185 3.009544c-5.747911 0.284479-10.824533 3.834328-13.059434 9.138124-1.01512 2.415003-24.687262 60.190871-2.822278 147.651828 1.583055 6.324032 7.072069 10.893094 13.57518 11.308557 5.892197 0.37146 11.751648 0.523933 17.419741 0.667196 14.498202 0.372483 28.193109 0.723477 40.908712 4.63353 4.212952 1.294482 6.435573 8.270361 9.349949 18.763342 1.287319 4.640694 2.617617 9.43693 4.484128 14.010085 1.794879 4.393054 3.75758 8.570189 5.66093 12.607132 1.302669 2.765997 2.529613 5.380544 3.689019 8.018627 2.986007 6.803963 2.682086 9.773598 2.578732 10.349719-3.061732 3.672646-6.391571 7.238868-9.91379 11.015891-1.810229 1.943258-3.680832 3.949962-5.523807 5.980201l-22.560832 24.8909c-3.865028 4.261047-4.940522 10.365068-2.774183 15.693424 0.991584 2.426259 25.102724 60.011792 102.414531 106.400309 5.588275 3.353374 12.703323 2.701528 17.591657-1.603521l23.476691-20.682042c2.346441-2.061962 4.64888-4.336772 6.875594-6.534833 9.05319-8.93858 14.018272-12.95608 17.73185-11.576663 3.305279 1.222851 6.907317 3.166109 10.720156 5.228071 3.325745 1.794879 6.764054 3.650133 10.465352 5.288446 6.016017 2.662643 12.120039 4.688789 18.019399 6.65149 6.827499 2.266623 13.279445 4.409426 18.819624 7.275707 1.518586 0.782829 1.926886 0.994654 2.358721 7.830339 0.726547 11.496845 1.25048 23.276123 1.753947 34.672684 0.264013 5.900384 0.528026 11.803837 0.815575 17.700127 0.284479 5.743818 3.833305 10.82044 9.138124 13.05534 1.654686 0.698918 29.371958 12.063757 74.869175 12.063757 0.328481 0 3.65832 0 3.986801 0 45.497217 0 73.214489-11.364839 74.869175-12.063757 5.304819-2.234901 8.853645-7.311523 9.138124-13.05534 0.287549-5.89629 0.551562-11.799744 0.815575-17.700127 0.503467-11.396561 1.027399-23.175839 1.753947-34.672684 0.431835-6.835685 0.840134-7.04751 2.358721-7.830339 5.54018-2.866281 11.992125-5.009084 18.819624-7.275707 5.89936-1.962701 12.003382-3.988848 18.019399-6.65149 3.701299-1.638313 7.139607-3.493567 10.465352-5.288446 3.812839-2.061962 7.414877-4.00522 10.720156-5.228071 3.713578-1.379417 8.67866 2.638083 17.73185 11.576663 2.226714 2.198062 4.529153 4.472871 6.875594 6.534833l23.476691 20.682042c4.888334 4.305049 12.003382 4.956895 17.591657 1.603521 77.311807-46.388517 101.422947-103.97405 102.414531-106.400309 2.166339-5.328355 1.090844-11.432377-2.774183-15.693424l-22.560832-24.8909c-1.842974-2.030239-3.713578-4.036943-5.523807-5.980201-3.52222-3.777023-6.852058-7.343245-9.91379-11.015891-0.103354-0.576121-0.407276-3.545756 2.578732-10.349719 1.159406-2.638083 2.38635-5.252631 3.689019-8.018627 1.90335-4.036943 3.866051-8.214079 5.66093-12.607132 1.866511-4.573155 3.196809-9.369392 4.484128-14.010085 2.914376-10.492982 5.136997-17.46886 9.349949-18.763342 12.715603-3.910053 26.41051-4.261047 40.908712-4.63353 5.668093-0.143263 11.527544-0.295735 17.419741-0.667196 6.503111-0.415462 11.992125-4.984524 13.57518-11.308557C907.21518 494.340805 883.543038 436.564937 882.527918 434.149934zM643.49894 643.761929c-35.280528 35.280528-82.191954 54.711066-132.086317 54.711066s-96.806813-19.430538-132.086317-54.711066c-35.280528-35.279504-54.711066-82.191954-54.711066-132.086317 0-49.894364 19.430538-96.80272 54.711066-132.082224 35.283598-35.284621 82.191954-54.711066 132.086317-54.711066s96.80579 19.426445 132.086317 54.711066c35.279504 35.279504 54.711066 82.187861 54.711066 132.082224C698.210006 561.569976 678.782537 608.482425 643.49894 643.761929z";
var userTaskIcon = "M655.807326 287.35973m-223.989415 0a218.879 218.879 0 1 0 447.978829 0 218.879 218.879 0 1 0-447.978829 0ZM1039.955839 895.482975c-0.490184-212.177424-172.287821-384.030443-384.148513-384.030443-211.862739 0-383.660376 171.85302-384.15056 384.030443L1039.955839 895.482975z";
var scriptTaskIcon = "M6.402,0.5H20.902C20.902,0.5,15.069,3.333,15.069,6.083S19.486,12.083,19.486,15.25S15.319,20.333,15.319,20.333H0.235C0.235,20.333,5.235,17.665999999999997,5.235,15.332999999999998S0.6520000000000001,8.582999999999998,0.6520000000000001,6.082999999999998S6.402,0.5,6.402,0.5ZM3.5,4.5L13.5,4.5M3.8,8.5L13.8,8.5M6.3,12.5L16.3,12.5M6.5,16.5L16.5,16.5";
var manualTaskIcon = "M0.5,3.751L4.583,0.5009999999999999C4.583,0.5009999999999999,15.749,0.5839999999999999,16.666,0.5839999999999999S14.249,3.5009999999999994,15.166,3.5009999999999994S26.833,3.5009999999999994,27.75,3.5009999999999994C28.916,5.209,27.582,6.667999999999999,26.916,7.167999999999999S27.791,9.084999999999999,25.916,11.584999999999999C25.166,11.834999999999999,26.666,13.459999999999999,24.583000000000002,14.918C23.416,15.501,25.166,16.46,23.333000000000002,17.750999999999998C22.166,17.750999999999998,2.5000000000000036,17.833999999999996,2.5000000000000036,17.833999999999996L0.5000000000000036,16.500999999999998V3.751ZM13.5,7L27,7M13.5,11L26,11M14,14.5L25,14.5M8.2,3.1L15,3.1";
var style = {
  throw: "fill: rgb(34, 36, 42); stroke-linecap: round; stroke-linejoin: round; stroke: white; stroke-width: 1px;",
  catch: "fill: white; stroke-linecap: round; stroke-linejoin: round; stroke: rgb(34, 36, 42); stroke-width: 1px;",
  nonIntermediate: "stroke-width: 1.5; stroke-dash-array: 6",
  intermediate: "stroke-width: 1.5"
};
var parallelMarker = "m44,60 m 3,2 l 0,10 m 3,-10 l 0,10 m 3,-10 l 0,10";
var sequentialMarker = "m47,61 m 0,3 l 10,0 m -10,3 l 10,0 m -10,3 l 10,0";
var loopMarker = "m 50,73 c 3.526979,0 6.386161,-2.829858 6.386161,-6.320661 0,-3.490806 -2.859182,-6.320661 -6.386161,-6.320661 -3.526978,0 -6.38616,2.829855 -6.38616,6.320661 0,1.745402 0.714797,3.325567 1.870463,4.469381 0.577834,0.571908 1.265885,1.034728 2.029916,1.35457 l -0.718163,-3.909793 m 0.718163,3.909793 -3.885211,0.802902";

// node_modules/.pnpm/@logicflow+extension@1.2.28/node_modules/@logicflow/extension/es/bpmn-elements/presets/Gateway/gateway.js
var import_core13 = __toESM(require_logic_flow());
var __extends13 = function() {
  var extendStatics = function(d2, b) {
    extendStatics = Object.setPrototypeOf || { __proto__: [] } instanceof Array && function(d3, b2) {
      d3.__proto__ = b2;
    } || function(d3, b2) {
      for (var p2 in b2)
        if (b2.hasOwnProperty(p2))
          d3[p2] = b2[p2];
    };
    return extendStatics(d2, b);
  };
  return function(d2, b) {
    extendStatics(d2, b);
    function __() {
      this.constructor = d2;
    }
    d2.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
  };
}();
var __assign11 = function() {
  __assign11 = Object.assign || function(t2) {
    for (var s2, i2 = 1, n2 = arguments.length; i2 < n2; i2++) {
      s2 = arguments[i2];
      for (var p2 in s2)
        if (Object.prototype.hasOwnProperty.call(s2, p2))
          t2[p2] = s2[p2];
    }
    return t2;
  };
  return __assign11.apply(this, arguments);
};
var gatewayComposable = [
  [1, 1, 0],
  [0, 0, 1],
  [0, 1, 1]
];
function GatewayNodeFactory(type3, icon, props) {
  var view = (
    /** @class */
    function(_super) {
      __extends13(view2, _super);
      function view2() {
        return _super !== null && _super.apply(this, arguments) || this;
      }
      view2.prototype.getShape = function() {
        var model2 = this.props.model;
        var x2 = model2.x, y2 = model2.y, width = model2.width, height = model2.height, points = model2.points;
        var style2 = model2.getNodeStyle();
        return (0, import_core13.h)("g", {
          transform: "matrix(1 0 0 1 " + (x2 - width / 2) + " " + (y2 - height / 2) + ")"
        }, (0, import_core13.h)("polygon", __assign11(__assign11({}, style2), {
          x: x2,
          y: y2,
          points
        })), typeof icon === "string" ? (0, import_core13.h)("path", __assign11(__assign11({ d: icon }, style2), { fill: "rgb(34, 36, 42)", strokeWidth: 1 })) : icon);
      };
      return view2;
    }(import_core13.PolygonNode)
  );
  var model = (
    /** @class */
    function(_super) {
      __extends13(model2, _super);
      function model2(data, graphModel) {
        var _this = this;
        if (!data.id) {
          data.id = "Gateway_" + genBpmnId();
        }
        if (!data.text) {
          data.text = "";
        }
        if (data.text && typeof data.text === "string") {
          data.text = {
            value: data.text,
            x: data.x,
            y: data.y + 40
          };
        }
        data.properties = __assign11(__assign11({}, props || {}), data.properties);
        _this = _super.call(this, data, graphModel) || this;
        _this.points = [
          [25, 0],
          [50, 25],
          [25, 50],
          [0, 25]
        ];
        groupRule.call(_this);
        return _this;
      }
      return model2;
    }(import_core13.PolygonNodeModel)
  );
  return {
    type: type3,
    view,
    model
  };
}

// node_modules/.pnpm/@logicflow+extension@1.2.28/node_modules/@logicflow/extension/es/bpmn-elements/presets/Gateway/index.js
function registerGatewayNodes(lf) {
  var ExclusiveGateway2 = GatewayNodeFactory("bpmn:exclusiveGateway", exclusiveIcon);
  var ParallelGateway = GatewayNodeFactory("bpmn:parallelGateway", parallelIcon);
  var InclusiveGateway = GatewayNodeFactory("bpmn:inclusiveGateway", inclusiveIcon);
  lf.register(ExclusiveGateway2);
  lf.register(InclusiveGateway);
  lf.register(ParallelGateway);
}

// node_modules/.pnpm/@logicflow+extension@1.2.28/node_modules/@logicflow/extension/es/bpmn-elements/presets/Task/task.js
var import_core14 = __toESM(require_logic_flow());
var __extends14 = function() {
  var extendStatics = function(d2, b) {
    extendStatics = Object.setPrototypeOf || { __proto__: [] } instanceof Array && function(d3, b2) {
      d3.__proto__ = b2;
    } || function(d3, b2) {
      for (var p2 in b2)
        if (b2.hasOwnProperty(p2))
          d3[p2] = b2[p2];
    };
    return extendStatics(d2, b);
  };
  return function(d2, b) {
    extendStatics(d2, b);
    function __() {
      this.constructor = d2;
    }
    d2.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
  };
}();
var __assign12 = function() {
  __assign12 = Object.assign || function(t2) {
    for (var s2, i2 = 1, n2 = arguments.length; i2 < n2; i2++) {
      s2 = arguments[i2];
      for (var p2 in s2)
        if (Object.prototype.hasOwnProperty.call(s2, p2))
          t2[p2] = s2[p2];
    }
    return t2;
  };
  return __assign12.apply(this, arguments);
};
var __read7 = function(o2, n2) {
  var m2 = typeof Symbol === "function" && o2[Symbol.iterator];
  if (!m2)
    return o2;
  var i2 = m2.call(o2), r2, ar = [], e2;
  try {
    while ((n2 === void 0 || n2-- > 0) && !(r2 = i2.next()).done)
      ar.push(r2.value);
  } catch (error) {
    e2 = { error };
  } finally {
    try {
      if (r2 && !r2.done && (m2 = i2["return"]))
        m2.call(i2);
    } finally {
      if (e2)
        throw e2.error;
    }
  }
  return ar;
};
var __spread6 = function() {
  for (var ar = [], i2 = 0; i2 < arguments.length; i2++)
    ar = ar.concat(__read7(arguments[i2]));
  return ar;
};
var multiInstanceIcon = {
  parallel: parallelMarker,
  sequential: sequentialMarker,
  loop: loopMarker
};
function TaskNodeFactory(type3, icon, props) {
  var view = (
    /** @class */
    function(_super) {
      __extends14(view2, _super);
      function view2() {
        return _super !== null && _super.apply(this, arguments) || this;
      }
      view2.prototype.getLabelShape = function() {
        var model2 = this.props.model;
        var x2 = model2.x, y2 = model2.y, width = model2.width, height = model2.height;
        var style2 = model2.getNodeStyle();
        var i2 = Array.isArray(icon) ? import_core14.h.apply(void 0, __spread6(["g", {
          transform: "matrix(1 0 0 1 " + (x2 - width / 2) + " " + (y2 - height / 2) + ")"
        }], icon)) : (0, import_core14.h)("path", {
          fill: style2.stroke,
          d: icon
        });
        return (0, import_core14.h)("svg", {
          x: x2 - width / 2 + 5,
          y: y2 - height / 2 + 5,
          width: 25,
          height: 25,
          viewBox: "0 0 1274 1024"
        }, i2);
      };
      view2.prototype.getShape = function() {
        var model2 = this.props.model;
        var x2 = model2.x, y2 = model2.y, width = model2.width, height = model2.height, radius = model2.radius, properties = model2.properties;
        var style2 = model2.getNodeStyle();
        return (0, import_core14.h)("g", {}, [
          (0, import_core14.h)("rect", __assign12(__assign12({}, style2), {
            x: x2 - width / 2,
            y: y2 - height / 2,
            rx: radius,
            ry: radius,
            width,
            height,
            opacity: 0.95
          })),
          this.getLabelShape(),
          (0, import_core14.h)("g", {
            transform: "matrix(1 0 0 1 " + (x2 - width / 2) + " " + (y2 - height / 2) + ")"
          }, (0, import_core14.h)("path", {
            fill: "white",
            strokeLinecap: "round",
            strokeLinejoin: "round",
            stroke: "rgb(34, 36, 42)",
            strokeWidth: "2",
            d: multiInstanceIcon[properties.multiInstanceType] || ""
          }))
        ]);
      };
      return view2;
    }(import_core14.RectNode)
  );
  var model = (
    /** @class */
    function(_super) {
      __extends14(model2, _super);
      function model2(data, graphModel) {
        var _a;
        var _this = this;
        if (!data.id) {
          data.id = "Activity_" + genBpmnId();
        }
        var properties = __assign12(__assign12({}, props || {}), data.properties);
        data.properties = properties;
        _this = _super.call(this, data, graphModel) || this;
        (_a = properties === null || properties === void 0 ? void 0 : properties.boundaryEvents) === null || _a === void 0 ? void 0 : _a.forEach(function(id) {
          _this.addBoundaryEvent(id);
        });
        _this.deleteProperty("boundaryEvents");
        groupRule.call(_this);
        return _this;
      }
      model2.prototype.initNodeData = function(data) {
        _super.prototype.initNodeData.call(this, data);
        this.isTaskNode = true;
        this.boundaryEvents = [];
      };
      model2.prototype.getNodeStyle = function() {
        var style2 = _super.prototype.getNodeStyle.call(this);
        var isBoundaryEventTouchingTask = this.properties.isBoundaryEventTouchingTask;
        if (isBoundaryEventTouchingTask) {
          style2.stroke = "#00acff";
          style2.strokeWidth = 2;
        }
        return style2;
      };
      model2.prototype.getOutlineStyle = function() {
        var style2 = _super.prototype.getOutlineStyle.call(this);
        style2.stroke = "transparent";
        !style2.hover && (style2.hover = {});
        style2.hover.stroke = "transparent";
        return style2;
      };
      model2.prototype.setTouching = function(flag) {
        this.setProperty("isBoundaryEventTouchingTask", flag);
      };
      model2.prototype.addBoundaryEvent = function(nodeId) {
        this.setTouching(false);
        if (this.boundaryEvents.find(function(item) {
          return item === nodeId;
        })) {
          return false;
        }
        var boundaryEvent2 = this.graphModel.getNodeModelById(nodeId);
        boundaryEvent2 === null || boundaryEvent2 === void 0 ? void 0 : boundaryEvent2.setProperties({
          attachedToRef: this.id
        });
        this.boundaryEvents.push(nodeId);
        return true;
      };
      model2.prototype.deleteBoundaryEvent = function(nodeId) {
        this.boundaryEvents = this.boundaryEvents.filter(function(item) {
          return item !== nodeId;
        });
      };
      return model2;
    }(import_core14.RectNodeModel)
  );
  return {
    type: type3,
    view,
    model
  };
}

// node_modules/.pnpm/@logicflow+extension@1.2.28/node_modules/@logicflow/extension/es/bpmn-elements/presets/Task/subProcess.js
var import_core24 = __toESM(require_logic_flow());

// node_modules/.pnpm/@logicflow+extension@1.2.28/node_modules/@logicflow/extension/es/materials/group/index.js
var import_core23 = __toESM(require_logic_flow());

// node_modules/.pnpm/@logicflow+extension@1.2.28/node_modules/@logicflow/extension/es/materials/group/GroupNode.js
var import_core22 = __toESM(require_logic_flow());

// node_modules/.pnpm/@logicflow+extension@1.2.28/node_modules/@logicflow/extension/es/NodeResize/Node/RectResize.js
var import_core17 = __toESM(require_logic_flow());

// node_modules/.pnpm/preact@10.26.9/node_modules/preact/dist/preact.module.js
var n;
var l;
var u;
var t;
var i;
var r;
var o;
var e;
var f;
var c;
var s;
var a;
var h13;
var p = {};
var v = [];
var y = /acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i;
var w = Array.isArray;
function d(n2, l2) {
  for (var u2 in l2)
    n2[u2] = l2[u2];
  return n2;
}
function g(n2) {
  n2 && n2.parentNode && n2.parentNode.removeChild(n2);
}
function _(l2, u2, t2) {
  var i2, r2, o2, e2 = {};
  for (o2 in u2)
    "key" == o2 ? i2 = u2[o2] : "ref" == o2 ? r2 = u2[o2] : e2[o2] = u2[o2];
  if (arguments.length > 2 && (e2.children = arguments.length > 3 ? n.call(arguments, 2) : t2), "function" == typeof l2 && null != l2.defaultProps)
    for (o2 in l2.defaultProps)
      void 0 === e2[o2] && (e2[o2] = l2.defaultProps[o2]);
  return m(l2, e2, i2, r2, null);
}
function m(n2, t2, i2, r2, o2) {
  var e2 = { type: n2, props: t2, key: i2, ref: r2, __k: null, __: null, __b: 0, __e: null, __c: null, constructor: void 0, __v: null == o2 ? ++u : o2, __i: -1, __u: 0 };
  return null == o2 && null != l.vnode && l.vnode(e2), e2;
}
function k(n2) {
  return n2.children;
}
function x(n2, l2) {
  this.props = n2, this.context = l2;
}
function S(n2, l2) {
  if (null == l2)
    return n2.__ ? S(n2.__, n2.__i + 1) : null;
  for (var u2; l2 < n2.__k.length; l2++)
    if (null != (u2 = n2.__k[l2]) && null != u2.__e)
      return u2.__e;
  return "function" == typeof n2.type ? S(n2) : null;
}
function C(n2) {
  var l2, u2;
  if (null != (n2 = n2.__) && null != n2.__c) {
    for (n2.__e = n2.__c.base = null, l2 = 0; l2 < n2.__k.length; l2++)
      if (null != (u2 = n2.__k[l2]) && null != u2.__e) {
        n2.__e = n2.__c.base = u2.__e;
        break;
      }
    return C(n2);
  }
}
function M(n2) {
  (!n2.__d && (n2.__d = true) && i.push(n2) && !$.__r++ || r != l.debounceRendering) && ((r = l.debounceRendering) || o)($);
}
function $() {
  for (var n2, u2, t2, r2, o2, f2, c2, s2 = 1; i.length; )
    i.length > s2 && i.sort(e), n2 = i.shift(), s2 = i.length, n2.__d && (t2 = void 0, o2 = (r2 = (u2 = n2).__v).__e, f2 = [], c2 = [], u2.__P && ((t2 = d({}, r2)).__v = r2.__v + 1, l.vnode && l.vnode(t2), O(u2.__P, t2, r2, u2.__n, u2.__P.namespaceURI, 32 & r2.__u ? [o2] : null, f2, null == o2 ? S(r2) : o2, !!(32 & r2.__u), c2), t2.__v = r2.__v, t2.__.__k[t2.__i] = t2, z(f2, t2, c2), t2.__e != o2 && C(t2)));
  $.__r = 0;
}
function I(n2, l2, u2, t2, i2, r2, o2, e2, f2, c2, s2) {
  var a2, h24, y2, w2, d2, g2, _2 = t2 && t2.__k || v, m2 = l2.length;
  for (f2 = P(u2, l2, _2, f2, m2), a2 = 0; a2 < m2; a2++)
    null != (y2 = u2.__k[a2]) && (h24 = -1 == y2.__i ? p : _2[y2.__i] || p, y2.__i = a2, g2 = O(n2, y2, h24, i2, r2, o2, e2, f2, c2, s2), w2 = y2.__e, y2.ref && h24.ref != y2.ref && (h24.ref && q(h24.ref, null, y2), s2.push(y2.ref, y2.__c || w2, y2)), null == d2 && null != w2 && (d2 = w2), 4 & y2.__u || h24.__k === y2.__k ? f2 = A(y2, f2, n2) : "function" == typeof y2.type && void 0 !== g2 ? f2 = g2 : w2 && (f2 = w2.nextSibling), y2.__u &= -7);
  return u2.__e = d2, f2;
}
function P(n2, l2, u2, t2, i2) {
  var r2, o2, e2, f2, c2, s2 = u2.length, a2 = s2, h24 = 0;
  for (n2.__k = new Array(i2), r2 = 0; r2 < i2; r2++)
    null != (o2 = l2[r2]) && "boolean" != typeof o2 && "function" != typeof o2 ? (f2 = r2 + h24, (o2 = n2.__k[r2] = "string" == typeof o2 || "number" == typeof o2 || "bigint" == typeof o2 || o2.constructor == String ? m(null, o2, null, null, null) : w(o2) ? m(k, { children: o2 }, null, null, null) : null == o2.constructor && o2.__b > 0 ? m(o2.type, o2.props, o2.key, o2.ref ? o2.ref : null, o2.__v) : o2).__ = n2, o2.__b = n2.__b + 1, e2 = null, -1 != (c2 = o2.__i = L(o2, u2, f2, a2)) && (a2--, (e2 = u2[c2]) && (e2.__u |= 2)), null == e2 || null == e2.__v ? (-1 == c2 && (i2 > s2 ? h24-- : i2 < s2 && h24++), "function" != typeof o2.type && (o2.__u |= 4)) : c2 != f2 && (c2 == f2 - 1 ? h24-- : c2 == f2 + 1 ? h24++ : (c2 > f2 ? h24-- : h24++, o2.__u |= 4))) : n2.__k[r2] = null;
  if (a2)
    for (r2 = 0; r2 < s2; r2++)
      null != (e2 = u2[r2]) && 0 == (2 & e2.__u) && (e2.__e == t2 && (t2 = S(e2)), B(e2, e2));
  return t2;
}
function A(n2, l2, u2) {
  var t2, i2;
  if ("function" == typeof n2.type) {
    for (t2 = n2.__k, i2 = 0; t2 && i2 < t2.length; i2++)
      t2[i2] && (t2[i2].__ = n2, l2 = A(t2[i2], l2, u2));
    return l2;
  }
  n2.__e != l2 && (l2 && n2.type && !u2.contains(l2) && (l2 = S(n2)), u2.insertBefore(n2.__e, l2 || null), l2 = n2.__e);
  do {
    l2 = l2 && l2.nextSibling;
  } while (null != l2 && 8 == l2.nodeType);
  return l2;
}
function L(n2, l2, u2, t2) {
  var i2, r2, o2 = n2.key, e2 = n2.type, f2 = l2[u2];
  if (null === f2 && null == n2.key || f2 && o2 == f2.key && e2 == f2.type && 0 == (2 & f2.__u))
    return u2;
  if (t2 > (null != f2 && 0 == (2 & f2.__u) ? 1 : 0))
    for (i2 = u2 - 1, r2 = u2 + 1; i2 >= 0 || r2 < l2.length; ) {
      if (i2 >= 0) {
        if ((f2 = l2[i2]) && 0 == (2 & f2.__u) && o2 == f2.key && e2 == f2.type)
          return i2;
        i2--;
      }
      if (r2 < l2.length) {
        if ((f2 = l2[r2]) && 0 == (2 & f2.__u) && o2 == f2.key && e2 == f2.type)
          return r2;
        r2++;
      }
    }
  return -1;
}
function T(n2, l2, u2) {
  "-" == l2[0] ? n2.setProperty(l2, null == u2 ? "" : u2) : n2[l2] = null == u2 ? "" : "number" != typeof u2 || y.test(l2) ? u2 : u2 + "px";
}
function j(n2, l2, u2, t2, i2) {
  var r2, o2;
  n:
    if ("style" == l2)
      if ("string" == typeof u2)
        n2.style.cssText = u2;
      else {
        if ("string" == typeof t2 && (n2.style.cssText = t2 = ""), t2)
          for (l2 in t2)
            u2 && l2 in u2 || T(n2.style, l2, "");
        if (u2)
          for (l2 in u2)
            t2 && u2[l2] == t2[l2] || T(n2.style, l2, u2[l2]);
      }
    else if ("o" == l2[0] && "n" == l2[1])
      r2 = l2 != (l2 = l2.replace(f, "$1")), o2 = l2.toLowerCase(), l2 = o2 in n2 || "onFocusOut" == l2 || "onFocusIn" == l2 ? o2.slice(2) : l2.slice(2), n2.l || (n2.l = {}), n2.l[l2 + r2] = u2, u2 ? t2 ? u2.u = t2.u : (u2.u = c, n2.addEventListener(l2, r2 ? a : s, r2)) : n2.removeEventListener(l2, r2 ? a : s, r2);
    else {
      if ("http://www.w3.org/2000/svg" == i2)
        l2 = l2.replace(/xlink(H|:h)/, "h").replace(/sName$/, "s");
      else if ("width" != l2 && "height" != l2 && "href" != l2 && "list" != l2 && "form" != l2 && "tabIndex" != l2 && "download" != l2 && "rowSpan" != l2 && "colSpan" != l2 && "role" != l2 && "popover" != l2 && l2 in n2)
        try {
          n2[l2] = null == u2 ? "" : u2;
          break n;
        } catch (n3) {
        }
      "function" == typeof u2 || (null == u2 || false === u2 && "-" != l2[4] ? n2.removeAttribute(l2) : n2.setAttribute(l2, "popover" == l2 && 1 == u2 ? "" : u2));
    }
}
function F(n2) {
  return function(u2) {
    if (this.l) {
      var t2 = this.l[u2.type + n2];
      if (null == u2.t)
        u2.t = c++;
      else if (u2.t < t2.u)
        return;
      return t2(l.event ? l.event(u2) : u2);
    }
  };
}
function O(n2, u2, t2, i2, r2, o2, e2, f2, c2, s2) {
  var a2, h24, p2, v2, y2, _2, m2, b, S2, C2, M2, $2, P2, A2, H, L2, T2, j2 = u2.type;
  if (null != u2.constructor)
    return null;
  128 & t2.__u && (c2 = !!(32 & t2.__u), o2 = [f2 = u2.__e = t2.__e]), (a2 = l.__b) && a2(u2);
  n:
    if ("function" == typeof j2)
      try {
        if (b = u2.props, S2 = "prototype" in j2 && j2.prototype.render, C2 = (a2 = j2.contextType) && i2[a2.__c], M2 = a2 ? C2 ? C2.props.value : a2.__ : i2, t2.__c ? m2 = (h24 = u2.__c = t2.__c).__ = h24.__E : (S2 ? u2.__c = h24 = new j2(b, M2) : (u2.__c = h24 = new x(b, M2), h24.constructor = j2, h24.render = D), C2 && C2.sub(h24), h24.props = b, h24.state || (h24.state = {}), h24.context = M2, h24.__n = i2, p2 = h24.__d = true, h24.__h = [], h24._sb = []), S2 && null == h24.__s && (h24.__s = h24.state), S2 && null != j2.getDerivedStateFromProps && (h24.__s == h24.state && (h24.__s = d({}, h24.__s)), d(h24.__s, j2.getDerivedStateFromProps(b, h24.__s))), v2 = h24.props, y2 = h24.state, h24.__v = u2, p2)
          S2 && null == j2.getDerivedStateFromProps && null != h24.componentWillMount && h24.componentWillMount(), S2 && null != h24.componentDidMount && h24.__h.push(h24.componentDidMount);
        else {
          if (S2 && null == j2.getDerivedStateFromProps && b !== v2 && null != h24.componentWillReceiveProps && h24.componentWillReceiveProps(b, M2), !h24.__e && null != h24.shouldComponentUpdate && false === h24.shouldComponentUpdate(b, h24.__s, M2) || u2.__v == t2.__v) {
            for (u2.__v != t2.__v && (h24.props = b, h24.state = h24.__s, h24.__d = false), u2.__e = t2.__e, u2.__k = t2.__k, u2.__k.some(function(n3) {
              n3 && (n3.__ = u2);
            }), $2 = 0; $2 < h24._sb.length; $2++)
              h24.__h.push(h24._sb[$2]);
            h24._sb = [], h24.__h.length && e2.push(h24);
            break n;
          }
          null != h24.componentWillUpdate && h24.componentWillUpdate(b, h24.__s, M2), S2 && null != h24.componentDidUpdate && h24.__h.push(function() {
            h24.componentDidUpdate(v2, y2, _2);
          });
        }
        if (h24.context = M2, h24.props = b, h24.__P = n2, h24.__e = false, P2 = l.__r, A2 = 0, S2) {
          for (h24.state = h24.__s, h24.__d = false, P2 && P2(u2), a2 = h24.render(h24.props, h24.state, h24.context), H = 0; H < h24._sb.length; H++)
            h24.__h.push(h24._sb[H]);
          h24._sb = [];
        } else
          do {
            h24.__d = false, P2 && P2(u2), a2 = h24.render(h24.props, h24.state, h24.context), h24.state = h24.__s;
          } while (h24.__d && ++A2 < 25);
        h24.state = h24.__s, null != h24.getChildContext && (i2 = d(d({}, i2), h24.getChildContext())), S2 && !p2 && null != h24.getSnapshotBeforeUpdate && (_2 = h24.getSnapshotBeforeUpdate(v2, y2)), L2 = a2, null != a2 && a2.type === k && null == a2.key && (L2 = N(a2.props.children)), f2 = I(n2, w(L2) ? L2 : [L2], u2, t2, i2, r2, o2, e2, f2, c2, s2), h24.base = u2.__e, u2.__u &= -161, h24.__h.length && e2.push(h24), m2 && (h24.__E = h24.__ = null);
      } catch (n3) {
        if (u2.__v = null, c2 || null != o2)
          if (n3.then) {
            for (u2.__u |= c2 ? 160 : 128; f2 && 8 == f2.nodeType && f2.nextSibling; )
              f2 = f2.nextSibling;
            o2[o2.indexOf(f2)] = null, u2.__e = f2;
          } else
            for (T2 = o2.length; T2--; )
              g(o2[T2]);
        else
          u2.__e = t2.__e, u2.__k = t2.__k;
        l.__e(n3, u2, t2);
      }
    else
      null == o2 && u2.__v == t2.__v ? (u2.__k = t2.__k, u2.__e = t2.__e) : f2 = u2.__e = V(t2.__e, u2, t2, i2, r2, o2, e2, c2, s2);
  return (a2 = l.diffed) && a2(u2), 128 & u2.__u ? void 0 : f2;
}
function z(n2, u2, t2) {
  for (var i2 = 0; i2 < t2.length; i2++)
    q(t2[i2], t2[++i2], t2[++i2]);
  l.__c && l.__c(u2, n2), n2.some(function(u3) {
    try {
      n2 = u3.__h, u3.__h = [], n2.some(function(n3) {
        n3.call(u3);
      });
    } catch (n3) {
      l.__e(n3, u3.__v);
    }
  });
}
function N(n2) {
  return "object" != typeof n2 || null == n2 || n2.__b && n2.__b > 0 ? n2 : w(n2) ? n2.map(N) : d({}, n2);
}
function V(u2, t2, i2, r2, o2, e2, f2, c2, s2) {
  var a2, h24, v2, y2, d2, _2, m2, b = i2.props, k2 = t2.props, x2 = t2.type;
  if ("svg" == x2 ? o2 = "http://www.w3.org/2000/svg" : "math" == x2 ? o2 = "http://www.w3.org/1998/Math/MathML" : o2 || (o2 = "http://www.w3.org/1999/xhtml"), null != e2) {
    for (a2 = 0; a2 < e2.length; a2++)
      if ((d2 = e2[a2]) && "setAttribute" in d2 == !!x2 && (x2 ? d2.localName == x2 : 3 == d2.nodeType)) {
        u2 = d2, e2[a2] = null;
        break;
      }
  }
  if (null == u2) {
    if (null == x2)
      return document.createTextNode(k2);
    u2 = document.createElementNS(o2, x2, k2.is && k2), c2 && (l.__m && l.__m(t2, e2), c2 = false), e2 = null;
  }
  if (null == x2)
    b === k2 || c2 && u2.data == k2 || (u2.data = k2);
  else {
    if (e2 = e2 && n.call(u2.childNodes), b = i2.props || p, !c2 && null != e2)
      for (b = {}, a2 = 0; a2 < u2.attributes.length; a2++)
        b[(d2 = u2.attributes[a2]).name] = d2.value;
    for (a2 in b)
      if (d2 = b[a2], "children" == a2)
        ;
      else if ("dangerouslySetInnerHTML" == a2)
        v2 = d2;
      else if (!(a2 in k2)) {
        if ("value" == a2 && "defaultValue" in k2 || "checked" == a2 && "defaultChecked" in k2)
          continue;
        j(u2, a2, null, d2, o2);
      }
    for (a2 in k2)
      d2 = k2[a2], "children" == a2 ? y2 = d2 : "dangerouslySetInnerHTML" == a2 ? h24 = d2 : "value" == a2 ? _2 = d2 : "checked" == a2 ? m2 = d2 : c2 && "function" != typeof d2 || b[a2] === d2 || j(u2, a2, d2, b[a2], o2);
    if (h24)
      c2 || v2 && (h24.__html == v2.__html || h24.__html == u2.innerHTML) || (u2.innerHTML = h24.__html), t2.__k = [];
    else if (v2 && (u2.innerHTML = ""), I("template" == t2.type ? u2.content : u2, w(y2) ? y2 : [y2], t2, i2, r2, "foreignObject" == x2 ? "http://www.w3.org/1999/xhtml" : o2, e2, f2, e2 ? e2[0] : i2.__k && S(i2, 0), c2, s2), null != e2)
      for (a2 = e2.length; a2--; )
        g(e2[a2]);
    c2 || (a2 = "value", "progress" == x2 && null == _2 ? u2.removeAttribute("value") : null != _2 && (_2 !== u2[a2] || "progress" == x2 && !_2 || "option" == x2 && _2 != b[a2]) && j(u2, a2, _2, b[a2], o2), a2 = "checked", null != m2 && m2 != u2[a2] && j(u2, a2, m2, b[a2], o2));
  }
  return u2;
}
function q(n2, u2, t2) {
  try {
    if ("function" == typeof n2) {
      var i2 = "function" == typeof n2.__u;
      i2 && n2.__u(), i2 && null == u2 || (n2.__u = n2(u2));
    } else
      n2.current = u2;
  } catch (n3) {
    l.__e(n3, t2);
  }
}
function B(n2, u2, t2) {
  var i2, r2;
  if (l.unmount && l.unmount(n2), (i2 = n2.ref) && (i2.current && i2.current != n2.__e || q(i2, null, u2)), null != (i2 = n2.__c)) {
    if (i2.componentWillUnmount)
      try {
        i2.componentWillUnmount();
      } catch (n3) {
        l.__e(n3, u2);
      }
    i2.base = i2.__P = null;
  }
  if (i2 = n2.__k)
    for (r2 = 0; r2 < i2.length; r2++)
      i2[r2] && B(i2[r2], u2, t2 || "function" != typeof n2.type);
  t2 || g(n2.__e), n2.__c = n2.__ = n2.__e = void 0;
}
function D(n2, l2, u2) {
  return this.constructor(n2, u2);
}
n = v.slice, l = { __e: function(n2, l2, u2, t2) {
  for (var i2, r2, o2; l2 = l2.__; )
    if ((i2 = l2.__c) && !i2.__)
      try {
        if ((r2 = i2.constructor) && null != r2.getDerivedStateFromError && (i2.setState(r2.getDerivedStateFromError(n2)), o2 = i2.__d), null != i2.componentDidCatch && (i2.componentDidCatch(n2, t2 || {}), o2 = i2.__d), o2)
          return i2.__E = i2;
      } catch (l3) {
        n2 = l3;
      }
  throw n2;
} }, u = 0, t = function(n2) {
  return null != n2 && null == n2.constructor;
}, x.prototype.setState = function(n2, l2) {
  var u2;
  u2 = null != this.__s && this.__s != this.state ? this.__s : this.__s = d({}, this.state), "function" == typeof n2 && (n2 = n2(d({}, u2), this.props)), n2 && d(u2, n2), null != n2 && this.__v && (l2 && this._sb.push(l2), M(this));
}, x.prototype.forceUpdate = function(n2) {
  this.__v && (this.__e = true, n2 && this.__h.push(n2), M(this));
}, x.prototype.render = k, i = [], o = "function" == typeof Promise ? Promise.prototype.then.bind(Promise.resolve()) : setTimeout, e = function(n2, l2) {
  return n2.__v.__b - l2.__v.__b;
}, $.__r = 0, f = /(PointerCapture)$|Capture$/i, c = 0, s = F(false), a = F(true), h13 = 0;

// node_modules/.pnpm/@logicflow+extension@1.2.28/node_modules/@logicflow/extension/es/NodeResize/Control/Control.js
var import_core16 = __toESM(require_logic_flow());

// node_modules/.pnpm/@logicflow+extension@1.2.28/node_modules/@logicflow/extension/es/NodeResize/BasicShape/Rect.js
var import_core15 = __toESM(require_logic_flow());
var __assign13 = function() {
  __assign13 = Object.assign || function(t2) {
    for (var s2, i2 = 1, n2 = arguments.length; i2 < n2; i2++) {
      s2 = arguments[i2];
      for (var p2 in s2)
        if (Object.prototype.hasOwnProperty.call(s2, p2))
          t2[p2] = s2[p2];
    }
    return t2;
  };
  return __assign13.apply(this, arguments);
};
function Rect(props) {
  var x2 = props.x, y2 = props.y, width = props.width, height = props.height, radius = props.radius, className = props.className;
  var leftTopX = x2 - width / 2;
  var leftTopY = y2 - height / 2;
  var attrs = __assign13(__assign13({
    // default
    width: 10,
    height: 10,
    cx: 0,
    cy: 0,
    rx: radius || 0,
    ry: radius || 0,
    fill: "transparent",
    fillOpacity: 1,
    strokeWidth: "1px",
    stroke: "#000",
    strokeOpacity: 1,
    className: "lf-basic-shape " + className
  }, props), { x: leftTopX, y: leftTopY });
  return (0, import_core15.h)("rect", __assign13({}, attrs));
}
Rect.defaultProps = {
  radius: 0,
  stroke: "",
  strokeDasharray: "",
  className: ""
};

// node_modules/.pnpm/@logicflow+extension@1.2.28/node_modules/@logicflow/extension/es/NodeResize/Control/Util.js
var ModelType;
(function(ModelType2) {
  ModelType2["NODE"] = "node";
  ModelType2["CIRCLE_NODE"] = "circle-node";
  ModelType2["POLYGON_NODE"] = "polygon-node";
  ModelType2["RECT_NODE"] = "rect-node";
  ModelType2["HTML_NODE"] = "html-node";
  ModelType2["TEXT_NODE"] = "text-node";
  ModelType2["ELLIPSE_NODE"] = "ellipse-node";
  ModelType2["DIAMOND_NODE"] = "diamond-node";
  ModelType2["EDGE"] = "edge";
  ModelType2["LINE_EDGE"] = "line-edge";
  ModelType2["POLYLINE_EDGE"] = "polyline-edge";
  ModelType2["BEZIER_EDGE"] = "bezier-edge";
  ModelType2["GRAPH"] = "graph";
})(ModelType || (ModelType = {}));

// node_modules/.pnpm/@logicflow+extension@1.2.28/node_modules/@logicflow/extension/es/NodeResize/Control/Control.js
var __extends15 = function() {
  var extendStatics = function(d2, b) {
    extendStatics = Object.setPrototypeOf || { __proto__: [] } instanceof Array && function(d3, b2) {
      d3.__proto__ = b2;
    } || function(d3, b2) {
      for (var p2 in b2)
        if (b2.hasOwnProperty(p2))
          d3[p2] = b2[p2];
    };
    return extendStatics(d2, b);
  };
  return function(d2, b) {
    extendStatics(d2, b);
    function __() {
      this.constructor = d2;
    }
    d2.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
  };
}();
var __assign14 = function() {
  __assign14 = Object.assign || function(t2) {
    for (var s2, i2 = 1, n2 = arguments.length; i2 < n2; i2++) {
      s2 = arguments[i2];
      for (var p2 in s2)
        if (Object.prototype.hasOwnProperty.call(s2, p2))
          t2[p2] = s2[p2];
    }
    return t2;
  };
  return __assign14.apply(this, arguments);
};
var __read8 = function(o2, n2) {
  var m2 = typeof Symbol === "function" && o2[Symbol.iterator];
  if (!m2)
    return o2;
  var i2 = m2.call(o2), r2, ar = [], e2;
  try {
    while ((n2 === void 0 || n2-- > 0) && !(r2 = i2.next()).done)
      ar.push(r2.value);
  } catch (error) {
    e2 = { error };
  } finally {
    try {
      if (r2 && !r2.done && (m2 = i2["return"]))
        m2.call(i2);
    } finally {
      if (e2)
        throw e2.error;
    }
  }
  return ar;
};
var StepDrag = import_core16.LogicFlowUtil.StepDrag;
var Control = (
  /** @class */
  function(_super) {
    __extends15(Control3, _super);
    function Control3(props) {
      var _this = _super.call(this) || this;
      _this.updatePosition = function(_a) {
        var deltaX = _a.deltaX, deltaY = _a.deltaY;
        var _b = _this.nodeModel, x2 = _b.x, y2 = _b.y;
        _this.nodeModel.x = x2 + deltaX / 2;
        _this.nodeModel.y = y2 + deltaY / 2;
        _this.nodeModel.moveText(deltaX / 2, deltaY / 2);
      };
      _this.getResize = function(_a) {
        var index = _a.index, deltaX = _a.deltaX, deltaY = _a.deltaY, width = _a.width, height = _a.height, PCTResizeInfo = _a.PCTResizeInfo, _b = _a.pct, pct = _b === void 0 ? 1 : _b, _c = _a.freezeWidth, freezeWidth = _c === void 0 ? false : _c, _d = _a.freezeHeight, freezeHeight = _d === void 0 ? false : _d;
        var resize = { width, height, deltaX, deltaY };
        if (PCTResizeInfo) {
          var sensitivity = 4;
          var deltaScale = 0;
          var combineDelta = 0;
          switch (index) {
            case 0:
              combineDelta = (deltaX * -1 - deltaY) / sensitivity;
              break;
            case 1:
              combineDelta = (deltaX - deltaY) / sensitivity;
              break;
            case 2:
              combineDelta = (deltaX + deltaY) / sensitivity;
              break;
            case 3:
              combineDelta = (deltaX * -1 + deltaY) / sensitivity;
              break;
            default:
              break;
          }
          if (combineDelta !== 0) {
            deltaScale = Math.round(combineDelta / PCTResizeInfo.ResizeBasis.basisHeight * 1e5) / 1e3;
          }
          PCTResizeInfo.ResizePCT.widthPCT = Math.max(Math.min(PCTResizeInfo.ResizePCT.widthPCT + deltaScale, PCTResizeInfo.ScaleLimit.maxScaleLimit), PCTResizeInfo.ScaleLimit.minScaleLimit);
          PCTResizeInfo.ResizePCT.hightPCT = Math.max(Math.min(PCTResizeInfo.ResizePCT.hightPCT + deltaScale, PCTResizeInfo.ScaleLimit.maxScaleLimit), PCTResizeInfo.ScaleLimit.minScaleLimit);
          var spcWidth = Math.round(PCTResizeInfo.ResizePCT.widthPCT * PCTResizeInfo.ResizeBasis.basisWidth / 100);
          var spcHeight = Math.round(PCTResizeInfo.ResizePCT.hightPCT * PCTResizeInfo.ResizeBasis.basisHeight / 100);
          switch (index) {
            case 0:
              deltaX = width - spcWidth;
              deltaY = height - spcHeight;
              break;
            case 1:
              deltaX = spcWidth - width;
              deltaY = height - spcHeight;
              break;
            case 2:
              deltaX = spcWidth - width;
              deltaY = spcHeight - height;
              break;
            case 3:
              deltaX = width - spcWidth;
              deltaY = spcHeight - height;
              break;
            default:
              break;
          }
          resize.width = spcWidth;
          resize.height = spcHeight;
          resize.deltaX = deltaX / pct;
          resize.deltaY = deltaY / pct;
          return resize;
        }
        switch (index) {
          case 0:
            resize.width = freezeWidth ? width : width - deltaX * pct;
            resize.height = freezeHeight ? height : height - deltaY * pct;
            break;
          case 1:
            resize.width = freezeWidth ? width : width + deltaX * pct;
            resize.height = freezeHeight ? height : height - deltaY * pct;
            break;
          case 2:
            resize.width = freezeWidth ? width : width + deltaX * pct;
            resize.height = freezeHeight ? height : height + deltaY * pct;
            break;
          case 3:
            resize.width = freezeWidth ? width : width - deltaX * pct;
            resize.height = freezeHeight ? height : height + deltaY * pct;
            break;
          default:
            break;
        }
        return resize;
      };
      _this.updateEdgePointByAnchors = function() {
        var _a = _this.nodeModel, id = _a.id, anchors = _a.anchors;
        var edges = _this.getNodeEdges(id);
        edges.sourceEdges.forEach(function(item) {
          var anchorItem = anchors.find(function(anchor) {
            return anchor.id === item.sourceAnchorId;
          });
          item.updateStartPoint({
            x: anchorItem.x,
            y: anchorItem.y
          });
        });
        edges.targetEdges.forEach(function(item) {
          var anchorItem = anchors.find(function(anchor) {
            return anchor.id === item.targetAnchorId;
          });
          item.updateEndPoint({
            x: anchorItem.x,
            y: anchorItem.y
          });
        });
      };
      _this.updateRect = function(_a) {
        var deltaX = _a.deltaX, deltaY = _a.deltaY;
        var _b = _this.nodeModel, id = _b.id, x2 = _b.x, y2 = _b.y, width = _b.width, height = _b.height, radius = _b.radius, PCTResizeInfo = _b.PCTResizeInfo;
        var _c = _this.nodeModel, minWidth = _c.minWidth, minHeight = _c.minHeight, maxWidth = _c.maxWidth, maxHeight = _c.maxHeight;
        var index = _this.index;
        var freezeWidth = minWidth === maxWidth;
        var freezeHeight = minHeight === maxHeight;
        var size = _this.getResize({
          index,
          deltaX,
          deltaY,
          width,
          height,
          PCTResizeInfo,
          pct: 1,
          freezeWidth,
          freezeHeight
        });
        if (size.width < minWidth || size.width > maxWidth || size.height < minHeight || size.height > maxHeight) {
          _this.dragHandler.cancelDrag();
          return;
        }
        _this.updatePosition({
          deltaX: freezeWidth ? 0 : size.deltaX,
          deltaY: freezeHeight ? 0 : size.deltaY
        });
        _this.nodeModel.width = size.width;
        _this.nodeModel.height = size.height;
        _this.nodeModel.setProperties({
          nodeSize: {
            width: size.width,
            height: size.height
          }
        });
        var edges = _this.getNodeEdges(id);
        var beforeNode = {
          x: x2,
          y: y2,
          width,
          height,
          radius
        };
        var afterNode = {
          x: _this.nodeModel.x,
          y: _this.nodeModel.y,
          width: _this.nodeModel.width,
          height: _this.nodeModel.height,
          radius
        };
        _this.updateEdgePointByAnchors();
        _this.eventEmit({ beforeNode, afterNode });
      };
      _this.updateEllipse = function(_a) {
        var deltaX = _a.deltaX, deltaY = _a.deltaY;
        var _b = _this.nodeModel, id = _b.id, rx = _b.rx, ry = _b.ry, x2 = _b.x, y2 = _b.y, PCTResizeInfo = _b.PCTResizeInfo;
        var index = _this.index;
        var _c = _this.nodeModel, minWidth = _c.minWidth, minHeight = _c.minHeight, maxWidth = _c.maxWidth, maxHeight = _c.maxHeight;
        var freezeWidth = minWidth === maxWidth;
        var freezeHeight = minHeight === maxHeight;
        var width = rx;
        var height = ry;
        var size = _this.getResize({
          index,
          deltaX,
          deltaY,
          width,
          height,
          PCTResizeInfo,
          pct: 1 / 2,
          freezeWidth,
          freezeHeight
        });
        if (size.width < minWidth / 2 || size.width > maxWidth / 2 || size.height < minHeight / 2 || size.height > maxHeight / 2) {
          _this.dragHandler.cancelDrag();
          return;
        }
        _this.updatePosition({
          deltaX: freezeWidth ? 0 : size.deltaX,
          deltaY: freezeHeight ? 0 : size.deltaY
        });
        _this.nodeModel.rx = size.width;
        _this.nodeModel.ry = size.height;
        _this.nodeModel.setProperties({
          nodeSize: {
            rx: size.width,
            ry: size.height
          }
        });
        var edges = _this.getNodeEdges(id);
        var beforeNode = { x: x2, y: y2 };
        var afterNode = {
          rx: size.width,
          ry: size.height,
          x: _this.nodeModel.x,
          y: _this.nodeModel.y
        };
        _this.updateEdgePointByAnchors();
        _this.eventEmit({ beforeNode: __assign14(__assign14({}, beforeNode), { rx, ry }), afterNode });
      };
      _this.updateDiamond = function(_a) {
        var deltaX = _a.deltaX, deltaY = _a.deltaY;
        var _b = _this.nodeModel, id = _b.id, rx = _b.rx, ry = _b.ry, x2 = _b.x, y2 = _b.y, PCTResizeInfo = _b.PCTResizeInfo;
        var index = _this.index;
        var _c = _this.nodeModel, minWidth = _c.minWidth, minHeight = _c.minHeight, maxWidth = _c.maxWidth, maxHeight = _c.maxHeight;
        var freezeWidth = minWidth === maxWidth;
        var freezeHeight = minHeight === maxHeight;
        var width = rx;
        var height = ry;
        var size = _this.getResize({
          index,
          deltaX,
          deltaY,
          width,
          height,
          PCTResizeInfo,
          pct: 1 / 2,
          freezeWidth,
          freezeHeight
        });
        if (size.width < minWidth / 2 || size.width > maxWidth / 2 || size.height < minHeight / 2 || size.height > maxHeight / 2) {
          _this.dragHandler.cancelDrag();
          return;
        }
        _this.updatePosition({
          deltaX: freezeWidth ? 0 : size.deltaX,
          deltaY: freezeHeight ? 0 : size.deltaY
        });
        _this.nodeModel.rx = size.width;
        _this.nodeModel.ry = size.height;
        _this.nodeModel.setProperties({
          nodeSize: {
            rx: size.width,
            ry: size.height
          }
        });
        var beforeNode = { x: x2, y: y2, rx, ry };
        var afterNode = {
          rx: size.width,
          ry: size.height,
          x: _this.nodeModel.x,
          y: _this.nodeModel.y
        };
        _this.updateEdgePointByAnchors();
        _this.eventEmit({ beforeNode, afterNode });
      };
      _this.eventEmit = function(_a) {
        var beforeNode = _a.beforeNode, afterNode = _a.afterNode;
        var _b = _this.nodeModel, id = _b.id, modelType = _b.modelType, type3 = _b.type;
        var oldNodeSize = __assign14({ id, modelType, type: type3 }, beforeNode);
        var newNodeSize = __assign14({ id, modelType, type: type3 }, afterNode);
        _this.graphModel.eventCenter.emit("node:resize", { oldNodeSize, newNodeSize });
      };
      _this.onDragging = function(_a) {
        var _b;
        var deltaX = _a.deltaX, deltaY = _a.deltaY;
        var transformModel = _this.graphModel.transformModel;
        var modelType = _this.nodeModel.modelType;
        _b = __read8(transformModel.fixDeltaXY(deltaX, deltaY), 2), deltaX = _b[0], deltaY = _b[1];
        if (modelType === ModelType.RECT_NODE || modelType === ModelType.HTML_NODE) {
          _this.updateRect({ deltaX, deltaY });
        } else if (modelType === ModelType.ELLIPSE_NODE) {
          _this.updateEllipse({ deltaX, deltaY });
        } else if (modelType === ModelType.DIAMOND_NODE) {
          _this.updateDiamond({ deltaX, deltaY });
        }
      };
      _this.onDragEnd = function() {
        var _a = _this.graphModel.gridSize, gridSize = _a === void 0 ? 1 : _a;
        var x2 = gridSize * Math.round(_this.nodeModel.x / gridSize);
        var y2 = gridSize * Math.round(_this.nodeModel.y / gridSize);
        _this.nodeModel.moveTo(x2, y2);
        _this.updateEdgePointByAnchors();
      };
      _this.index = props.index;
      _this.nodeModel = props.model;
      _this.graphModel = props.graphModel;
      _this.state = {};
      _this.dragHandler = new StepDrag({
        onDragging: _this.onDragging,
        onDragEnd: _this.onDragEnd,
        step: 1
      });
      return _this;
    }
    Control3.prototype.getNodeEdges = function(nodeId) {
      var graphModel = this.graphModel;
      var edges = graphModel.edges;
      var sourceEdges = [];
      var targetEdges = [];
      for (var i2 = 0; i2 < edges.length; i2++) {
        var edgeModel = edges[i2];
        if (edgeModel.sourceNodeId === nodeId) {
          sourceEdges.push(edgeModel);
        } else if (edges[i2].targetNodeId === nodeId) {
          targetEdges.push(edgeModel);
        }
      }
      return { sourceEdges, targetEdges };
    };
    Control3.prototype.render = function() {
      var _a = this.props, x2 = _a.x, y2 = _a.y, index = _a.index, model = _a.model;
      var style2 = model.getControlPointStyle();
      return _(
        "g",
        { className: "lf-resize-control-" + index },
        _(Rect, __assign14({ className: "lf-node-control" }, { x: x2, y: y2 }, style2, { onMouseDown: this.dragHandler.handleMouseDown }))
      );
    };
    return Control3;
  }(x)
);
var Control_default = Control;

// node_modules/.pnpm/@logicflow+extension@1.2.28/node_modules/@logicflow/extension/es/NodeResize/Control/ControlGroup.js
var __extends16 = function() {
  var extendStatics = function(d2, b) {
    extendStatics = Object.setPrototypeOf || { __proto__: [] } instanceof Array && function(d3, b2) {
      d3.__proto__ = b2;
    } || function(d3, b2) {
      for (var p2 in b2)
        if (b2.hasOwnProperty(p2))
          d3[p2] = b2[p2];
    };
    return extendStatics(d2, b);
  };
  return function(d2, b) {
    extendStatics(d2, b);
    function __() {
      this.constructor = d2;
    }
    d2.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
  };
}();
var __assign15 = function() {
  __assign15 = Object.assign || function(t2) {
    for (var s2, i2 = 1, n2 = arguments.length; i2 < n2; i2++) {
      s2 = arguments[i2];
      for (var p2 in s2)
        if (Object.prototype.hasOwnProperty.call(s2, p2))
          t2[p2] = s2[p2];
    }
    return t2;
  };
  return __assign15.apply(this, arguments);
};
var ControlGroup = (
  /** @class */
  function(_super) {
    __extends16(ControlGroup2, _super);
    function ControlGroup2() {
      var _this = _super.call(this) || this;
      _this.state = {};
      return _this;
    }
    ControlGroup2.prototype.getResizeControl = function() {
      var _a = this.props, model = _a.model, graphModel = _a.graphModel;
      var x2 = model.x, y2 = model.y, width = model.width, height = model.height;
      var box = {
        minX: x2 - width / 2,
        minY: y2 - height / 2,
        maxX: x2 + width / 2,
        maxY: y2 + height / 2
      };
      var minX = box.minX, minY = box.minY, maxX = box.maxX, maxY = box.maxY;
      var controlList = [
        // 左上角
        {
          x: minX,
          y: minY
        },
        // 右上角
        {
          x: maxX,
          y: minY
        },
        // 右下角
        {
          x: maxX,
          y: maxY
        },
        // 左下角
        {
          x: minX,
          y: maxY
        }
      ];
      return controlList.map(function(control, index) {
        return _(Control_default, __assign15({ index }, control, { model, graphModel }));
      });
    };
    ControlGroup2.prototype.getGroupSolid = function() {
      var model = this.props.model;
      var x2 = model.x, y2 = model.y, width = model.width, height = model.height;
      var style2 = model.getResizeOutlineStyle();
      return _(Rect, __assign15({ fill: "none" }, style2, { x: x2, y: y2, width, height }));
    };
    ControlGroup2.prototype.render = function() {
      return _(
        "g",
        { className: "lf-resize-control" },
        this.getGroupSolid(),
        this.getResizeControl()
      );
    };
    return ControlGroup2;
  }(x)
);
var ControlGroup_default = ControlGroup;

// node_modules/.pnpm/@logicflow+extension@1.2.28/node_modules/@logicflow/extension/es/NodeResize/Node/RectResize.js
var __extends17 = function() {
  var extendStatics = function(d2, b) {
    extendStatics = Object.setPrototypeOf || { __proto__: [] } instanceof Array && function(d3, b2) {
      d3.__proto__ = b2;
    } || function(d3, b2) {
      for (var p2 in b2)
        if (b2.hasOwnProperty(p2))
          d3[p2] = b2[p2];
    };
    return extendStatics(d2, b);
  };
  return function(d2, b) {
    extendStatics(d2, b);
    function __() {
      this.constructor = d2;
    }
    d2.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
  };
}();
var RectResizeModel = (
  /** @class */
  function(_super) {
    __extends17(RectResizeModel2, _super);
    function RectResizeModel2(data, graphModel) {
      var _this = _super.call(this, data, graphModel) || this;
      var nodeSize = _this.properties.nodeSize;
      if (nodeSize) {
        _this.width = nodeSize.width;
        _this.height = nodeSize.height;
      }
      return _this;
    }
    RectResizeModel2.prototype.initNodeData = function(data) {
      _super.prototype.initNodeData.call(this, data);
      this.minWidth = 30;
      this.minHeight = 30;
      this.maxWidth = 2e3;
      this.maxHeight = 2e3;
    };
    RectResizeModel2.prototype.getOutlineStyle = function() {
      var style2 = _super.prototype.getOutlineStyle.call(this);
      var isSilentMode = this.graphModel.editConfigModel.isSilentMode;
      if (isSilentMode)
        return style2;
      style2.stroke = "none";
      if (style2.hover) {
        style2.hover.stroke = "none";
      }
      return style2;
    };
    RectResizeModel2.prototype.getResizeOutlineStyle = function() {
      return {
        fill: "none",
        stroke: "transparent",
        strokeWidth: 1,
        strokeDasharray: "3,3"
      };
    };
    RectResizeModel2.prototype.getControlPointStyle = function() {
      return {
        width: 7,
        height: 7,
        fill: "#FFFFFF",
        stroke: "#000000"
      };
    };
    RectResizeModel2.prototype.resize = function(deltaX, deltaY) {
      console.log(deltaX, deltaY);
    };
    RectResizeModel2.prototype.enableProportionResize = function(turnOn) {
      if (turnOn === void 0) {
        turnOn = true;
      }
      if (turnOn) {
        var ResizePCT = { widthPCT: 100, hightPCT: 100 };
        var ResizeBasis = { basisWidth: this.width, basisHeight: this.height };
        var ScaleLimit = {
          maxScaleLimit: Math.min(this.maxWidth / this.width * 100, this.maxHeight / this.height * 100),
          minScaleLimit: Math.max(this.minWidth / this.width * 100, this.minHeight / this.height * 100)
        };
        this.PCTResizeInfo = { ResizePCT, ResizeBasis, ScaleLimit };
      } else {
        delete this.PCTResizeInfo;
      }
    };
    return RectResizeModel2;
  }(import_core17.RectNodeModel)
);
var RectResizeView = (
  /** @class */
  function(_super) {
    __extends17(RectResizeView2, _super);
    function RectResizeView2() {
      return _super !== null && _super.apply(this, arguments) || this;
    }
    RectResizeView2.prototype.getControlGroup = function() {
      var _a = this.props, model = _a.model, graphModel = _a.graphModel;
      return (0, import_core17.h)(ControlGroup_default, { model, graphModel });
    };
    RectResizeView2.prototype.getResizeShape = function() {
      return _super.prototype.getShape.call(this);
    };
    RectResizeView2.prototype.getShape = function() {
      var _a = this.props, isSelected = _a.model.isSelected, isSilentMode = _a.graphModel.editConfigModel.isSilentMode;
      return (0, import_core17.h)(
        "g",
        null,
        this.getResizeShape(),
        isSelected && !isSilentMode ? this.getControlGroup() : ""
      );
    };
    return RectResizeView2;
  }(import_core17.RectNode)
);
var RectResize = {
  type: "rect",
  view: RectResizeView,
  model: RectResizeModel
};
var RectResize_default = RectResize;

// node_modules/.pnpm/@logicflow+extension@1.2.28/node_modules/@logicflow/extension/es/NodeResize/Node/EllipseResize.js
var import_core18 = __toESM(require_logic_flow());
var __extends18 = function() {
  var extendStatics = function(d2, b) {
    extendStatics = Object.setPrototypeOf || { __proto__: [] } instanceof Array && function(d3, b2) {
      d3.__proto__ = b2;
    } || function(d3, b2) {
      for (var p2 in b2)
        if (b2.hasOwnProperty(p2))
          d3[p2] = b2[p2];
    };
    return extendStatics(d2, b);
  };
  return function(d2, b) {
    extendStatics(d2, b);
    function __() {
      this.constructor = d2;
    }
    d2.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
  };
}();
var EllipseResizeModel = (
  /** @class */
  function(_super) {
    __extends18(EllipseResizeModel2, _super);
    function EllipseResizeModel2(data, graphModel) {
      var _this = _super.call(this, data, graphModel) || this;
      var nodeSize = _this.properties.nodeSize;
      if (nodeSize) {
        _this.rx = nodeSize.rx;
        _this.ry = nodeSize.ry;
      }
      return _this;
    }
    EllipseResizeModel2.prototype.initNodeData = function(data) {
      _super.prototype.initNodeData.call(this, data);
      this.minWidth = 30;
      this.minHeight = 30;
      this.maxWidth = 2e3;
      this.maxHeight = 2e3;
    };
    EllipseResizeModel2.prototype.getOutlineStyle = function() {
      var style2 = _super.prototype.getOutlineStyle.call(this);
      var isSilentMode = this.graphModel.editConfigModel.isSilentMode;
      if (isSilentMode)
        return style2;
      style2.stroke = "none";
      if (style2.hover) {
        style2.hover.stroke = "none";
      }
      return style2;
    };
    EllipseResizeModel2.prototype.getResizeOutlineStyle = function() {
      return {
        stroke: "#000000",
        strokeWidth: 1,
        strokeDasharray: "3,3"
      };
    };
    EllipseResizeModel2.prototype.getControlPointStyle = function() {
      return {
        width: 7,
        height: 7,
        fill: "#FFFFFF",
        stroke: "#000000"
      };
    };
    EllipseResizeModel2.prototype.enableProportionResize = function(turnOn) {
      if (turnOn === void 0) {
        turnOn = true;
      }
      if (turnOn) {
        var ResizePCT = { widthPCT: 100, hightPCT: 100 };
        var ResizeBasis = { basisWidth: this.rx, basisHeight: this.ry };
        var ScaleLimit = {
          maxScaleLimit: Math.min(this.maxWidth / (this.rx * 2) * 100, this.maxHeight / (this.ry * 2) * 100),
          minScaleLimit: Math.max(this.minWidth / (this.rx * 2) * 100, this.minHeight / (this.ry * 2) * 100)
        };
        this.PCTResizeInfo = { ResizePCT, ResizeBasis, ScaleLimit };
      } else {
        delete this.PCTResizeInfo;
      }
    };
    return EllipseResizeModel2;
  }(import_core18.EllipseNodeModel)
);
var EllipseResizeView = (
  /** @class */
  function(_super) {
    __extends18(EllipseResizeView2, _super);
    function EllipseResizeView2() {
      return _super !== null && _super.apply(this, arguments) || this;
    }
    EllipseResizeView2.prototype.getControlGroup = function() {
      var _a = this.props, model = _a.model, graphModel = _a.graphModel;
      return (0, import_core18.h)(ControlGroup_default, { model, graphModel });
    };
    EllipseResizeView2.prototype.getResizeShape = function() {
      return _super.prototype.getShape.call(this);
    };
    EllipseResizeView2.prototype.getShape = function() {
      var _a = this.props, model = _a.model, isSilentMode = _a.graphModel.editConfigModel.isSilentMode;
      return (0, import_core18.h)(
        "g",
        null,
        this.getResizeShape(),
        model.isSelected && !isSilentMode ? this.getControlGroup() : ""
      );
    };
    return EllipseResizeView2;
  }(import_core18.EllipseNode)
);
var EllipseResize = {
  type: "ellipse",
  view: EllipseResizeView,
  model: EllipseResizeModel
};
var EllipseResize_default = EllipseResize;

// node_modules/.pnpm/@logicflow+extension@1.2.28/node_modules/@logicflow/extension/es/NodeResize/Node/DiamondResize.js
var import_core20 = __toESM(require_logic_flow());

// node_modules/.pnpm/@logicflow+extension@1.2.28/node_modules/@logicflow/extension/es/NodeResize/BasicShape/Polygon.js
var import_core19 = __toESM(require_logic_flow());
var __assign16 = function() {
  __assign16 = Object.assign || function(t2) {
    for (var s2, i2 = 1, n2 = arguments.length; i2 < n2; i2++) {
      s2 = arguments[i2];
      for (var p2 in s2)
        if (Object.prototype.hasOwnProperty.call(s2, p2))
          t2[p2] = s2[p2];
    }
    return t2;
  };
  return __assign16.apply(this, arguments);
};
function Polygon(_a) {
  var _b = _a.fillOpacity, fillOpacity = _b === void 0 ? 1 : _b, _c = _a.strokeWidth, strokeWidth = _c === void 0 ? 1 : _c, _d = _a.strokeOpacity, strokeOpacity = _d === void 0 ? 1 : _d, _e = _a.fill, fill = _e === void 0 ? "transparent" : _e, _f = _a.stroke, stroke = _f === void 0 ? "#000" : _f, points = _a.points, _g = _a.className, className = _g === void 0 ? "lf-basic-shape" : _g;
  var attrs = {
    fill,
    fillOpacity,
    strokeWidth,
    stroke,
    strokeOpacity,
    points: "",
    className
  };
  attrs.points = points.map(function(point) {
    return point.join(",");
  }).join(" ");
  return (0, import_core19.h)("polygon", __assign16({}, attrs));
}

// node_modules/.pnpm/@logicflow+extension@1.2.28/node_modules/@logicflow/extension/es/NodeResize/Node/DiamondResize.js
var __extends19 = function() {
  var extendStatics = function(d2, b) {
    extendStatics = Object.setPrototypeOf || { __proto__: [] } instanceof Array && function(d3, b2) {
      d3.__proto__ = b2;
    } || function(d3, b2) {
      for (var p2 in b2)
        if (b2.hasOwnProperty(p2))
          d3[p2] = b2[p2];
    };
    return extendStatics(d2, b);
  };
  return function(d2, b) {
    extendStatics(d2, b);
    function __() {
      this.constructor = d2;
    }
    d2.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
  };
}();
var __assign17 = function() {
  __assign17 = Object.assign || function(t2) {
    for (var s2, i2 = 1, n2 = arguments.length; i2 < n2; i2++) {
      s2 = arguments[i2];
      for (var p2 in s2)
        if (Object.prototype.hasOwnProperty.call(s2, p2))
          t2[p2] = s2[p2];
    }
    return t2;
  };
  return __assign17.apply(this, arguments);
};
var DiamondResizeModel = (
  /** @class */
  function(_super) {
    __extends19(DiamondResizeModel2, _super);
    function DiamondResizeModel2(data, graphModel) {
      var _this = _super.call(this, data, graphModel) || this;
      var nodeSize = _this.properties.nodeSize;
      if (nodeSize) {
        _this.rx = nodeSize.rx;
        _this.ry = nodeSize.ry;
      }
      return _this;
    }
    DiamondResizeModel2.prototype.initNodeData = function(data) {
      _super.prototype.initNodeData.call(this, data);
      this.minWidth = 30;
      this.minHeight = 30;
      this.maxWidth = 2e3;
      this.maxHeight = 2e3;
      this.gridSize = 1;
    };
    DiamondResizeModel2.prototype.getOutlineStyle = function() {
      var style2 = _super.prototype.getOutlineStyle.call(this);
      var isSilentMode = this.graphModel.editConfigModel.isSilentMode;
      if (isSilentMode)
        return style2;
      style2.stroke = "none";
      if (style2.hover) {
        style2.hover.stroke = "none";
      }
      return style2;
    };
    DiamondResizeModel2.prototype.getResizeOutlineStyle = function() {
      return {
        stroke: "#000000",
        strokeWidth: 1,
        strokeDasharray: "3,3"
      };
    };
    DiamondResizeModel2.prototype.getControlPointStyle = function() {
      return {
        width: 7,
        height: 7,
        fill: "#FFFFFF",
        stroke: "#000000"
      };
    };
    DiamondResizeModel2.prototype.enableProportionResize = function(turnOn) {
      if (turnOn === void 0) {
        turnOn = true;
      }
      if (turnOn) {
        var ResizePCT = { widthPCT: 100, hightPCT: 100 };
        var ResizeBasis = { basisWidth: this.rx, basisHeight: this.ry };
        var ScaleLimit = {
          maxScaleLimit: Math.min(this.maxWidth / (this.rx * 2) * 100, this.maxHeight / (this.ry * 2) * 100),
          minScaleLimit: Math.max(this.minWidth / (this.rx * 2) * 100, this.minHeight / (this.ry * 2) * 100)
        };
        this.PCTResizeInfo = { ResizePCT, ResizeBasis, ScaleLimit };
      } else {
        delete this.PCTResizeInfo;
      }
    };
    return DiamondResizeModel2;
  }(import_core20.DiamondNodeModel)
);
var DiamondResizeView = (
  /** @class */
  function(_super) {
    __extends19(DiamondResizeView2, _super);
    function DiamondResizeView2() {
      return _super !== null && _super.apply(this, arguments) || this;
    }
    DiamondResizeView2.prototype.getControlGroup = function() {
      var _a = this.props, model = _a.model, graphModel = _a.graphModel;
      return (0, import_core20.h)(ControlGroup_default, { model, graphModel });
    };
    DiamondResizeView2.prototype.getResizeShape = function() {
      var model = this.props.model;
      var points = model.points;
      var style2 = model.getNodeStyle();
      return (0, import_core20.h)(
        "g",
        null,
        (0, import_core20.h)(Polygon, __assign17({}, style2, { points }))
      );
    };
    DiamondResizeView2.prototype.getShape = function() {
      var _a = this.props, isSelected = _a.model.isSelected, isSilentMode = _a.graphModel.editConfigModel.isSilentMode;
      return (0, import_core20.h)(
        "g",
        null,
        this.getResizeShape(),
        isSelected && !isSilentMode ? this.getControlGroup() : ""
      );
    };
    return DiamondResizeView2;
  }(import_core20.DiamondNode)
);
var EllipseResize2 = {
  type: "diamond",
  view: DiamondResizeView,
  model: DiamondResizeModel
};
var DiamondResize_default = EllipseResize2;

// node_modules/.pnpm/@logicflow+extension@1.2.28/node_modules/@logicflow/extension/es/NodeResize/Node/HtmlResize.js
var import_core21 = __toESM(require_logic_flow());
var __extends20 = function() {
  var extendStatics = function(d2, b) {
    extendStatics = Object.setPrototypeOf || { __proto__: [] } instanceof Array && function(d3, b2) {
      d3.__proto__ = b2;
    } || function(d3, b2) {
      for (var p2 in b2)
        if (b2.hasOwnProperty(p2))
          d3[p2] = b2[p2];
    };
    return extendStatics(d2, b);
  };
  return function(d2, b) {
    extendStatics(d2, b);
    function __() {
      this.constructor = d2;
    }
    d2.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
  };
}();
var HtmlResizeModel = (
  /** @class */
  function(_super) {
    __extends20(HtmlResizeModel2, _super);
    function HtmlResizeModel2(data, graphModel) {
      var _this = _super.call(this, data, graphModel) || this;
      var nodeSize = _this.properties.nodeSize;
      if (nodeSize) {
        _this.width = nodeSize.width;
        _this.height = nodeSize.height;
      }
      return _this;
    }
    HtmlResizeModel2.prototype.initNodeData = function(data) {
      _super.prototype.initNodeData.call(this, data);
      this.minWidth = 30;
      this.minHeight = 30;
      this.maxWidth = 2e3;
      this.maxHeight = 2e3;
    };
    HtmlResizeModel2.prototype.getOutlineStyle = function() {
      var style2 = _super.prototype.getOutlineStyle.call(this);
      var isSilentMode = this.graphModel.editConfigModel.isSilentMode;
      if (isSilentMode)
        return style2;
      style2.stroke = "none";
      if (style2.hover) {
        style2.hover.stroke = "none";
      }
      return style2;
    };
    HtmlResizeModel2.prototype.getResizeOutlineStyle = function() {
      return {
        stroke: "#000000",
        strokeWidth: 1,
        strokeDasharray: "3,3"
      };
    };
    HtmlResizeModel2.prototype.getControlPointStyle = function() {
      return {
        width: 7,
        height: 7,
        fill: "#FFFFFF",
        stroke: "#000000"
      };
    };
    HtmlResizeModel2.prototype.enableProportionResize = function(turnOn) {
      if (turnOn === void 0) {
        turnOn = true;
      }
      if (turnOn) {
        var ResizePCT = { widthPCT: 100, hightPCT: 100 };
        var ResizeBasis = { basisWidth: this.width, basisHeight: this.height };
        var ScaleLimit = {
          maxScaleLimit: Math.min(this.maxWidth / this.width * 100, this.maxHeight / this.height * 100),
          minScaleLimit: Math.max(this.minWidth / this.width * 100, this.minHeight / this.height * 100)
        };
        this.PCTResizeInfo = { ResizePCT, ResizeBasis, ScaleLimit };
      } else {
        delete this.PCTResizeInfo;
      }
    };
    return HtmlResizeModel2;
  }(import_core21.HtmlNodeModel)
);
var HtmlResizeView = (
  /** @class */
  function(_super) {
    __extends20(HtmlResizeView2, _super);
    function HtmlResizeView2() {
      return _super !== null && _super.apply(this, arguments) || this;
    }
    HtmlResizeView2.prototype.getControlGroup = function() {
      var _a = this.props, model = _a.model, graphModel = _a.graphModel;
      return (0, import_core21.h)(ControlGroup_default, { model, graphModel });
    };
    HtmlResizeView2.prototype.getResizeShape = function() {
      return _super.prototype.getShape.call(this);
    };
    HtmlResizeView2.prototype.getShape = function() {
      var _a = this.props, isSelected = _a.model.isSelected, isSilentMode = _a.graphModel.editConfigModel.isSilentMode;
      return (0, import_core21.h)(
        "g",
        null,
        this.getResizeShape(),
        isSelected && !isSilentMode ? this.getControlGroup() : ""
      );
    };
    return HtmlResizeView2;
  }(import_core21.HtmlNode)
);
var HtmlResize = {
  type: "html",
  view: HtmlResizeView,
  model: HtmlResizeModel
};
var HtmlResize_default = HtmlResize;

// node_modules/.pnpm/@logicflow+extension@1.2.28/node_modules/@logicflow/extension/es/NodeResize/index.js
var NodeResize = {
  pluginName: "nodeResize",
  // 拖动step
  step: 0,
  install: function(lf) {
    lf.register({
      type: RectResize_default.type,
      view: RectResize_default.view,
      model: RectResize_default.model
    });
    lf.register({
      type: EllipseResize_default.type,
      view: EllipseResize_default.view,
      model: EllipseResize_default.model
    });
    lf.register({
      type: DiamondResize_default.type,
      view: DiamondResize_default.view,
      model: DiamondResize_default.model
    });
    lf.register({
      type: HtmlResize_default.type,
      view: HtmlResize_default.view,
      model: HtmlResize_default.model
    });
  }
};

// node_modules/.pnpm/@logicflow+extension@1.2.28/node_modules/@logicflow/extension/es/materials/group/GroupNode.js
var __extends21 = function() {
  var extendStatics = function(d2, b) {
    extendStatics = Object.setPrototypeOf || { __proto__: [] } instanceof Array && function(d3, b2) {
      d3.__proto__ = b2;
    } || function(d3, b2) {
      for (var p2 in b2)
        if (b2.hasOwnProperty(p2))
          d3[p2] = b2[p2];
    };
    return extendStatics(d2, b);
  };
  return function(d2, b) {
    extendStatics(d2, b);
    function __() {
      this.constructor = d2;
    }
    d2.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
  };
}();
var __assign18 = function() {
  __assign18 = Object.assign || function(t2) {
    for (var s2, i2 = 1, n2 = arguments.length; i2 < n2; i2++) {
      s2 = arguments[i2];
      for (var p2 in s2)
        if (Object.prototype.hasOwnProperty.call(s2, p2))
          t2[p2] = s2[p2];
    }
    return t2;
  };
  return __assign18.apply(this, arguments);
};
var __read9 = function(o2, n2) {
  var m2 = typeof Symbol === "function" && o2[Symbol.iterator];
  if (!m2)
    return o2;
  var i2 = m2.call(o2), r2, ar = [], e2;
  try {
    while ((n2 === void 0 || n2-- > 0) && !(r2 = i2.next()).done)
      ar.push(r2.value);
  } catch (error) {
    e2 = { error };
  } finally {
    try {
      if (r2 && !r2.done && (m2 = i2["return"]))
        m2.call(i2);
    } finally {
      if (e2)
        throw e2.error;
    }
  }
  return ar;
};
var __spread7 = function() {
  for (var ar = [], i2 = 0; i2 < arguments.length; i2++)
    ar = ar.concat(__read9(arguments[i2]));
  return ar;
};
var defaultWidth = 500;
var defaultHeight = 300;
var DEFAULT_BOTTOM_Z_INDEX = -1e4;
var GroupNodeModel = (
  /** @class */
  function(_super) {
    __extends21(GroupNodeModel2, _super);
    function GroupNodeModel2() {
      var _this = _super !== null && _super.apply(this, arguments) || this;
      _this.isGroup = true;
      _this.unfoldedWidth = defaultWidth;
      _this.unfoldedHight = defaultHeight;
      _this.childrenLastFoldStatus = {};
      return _this;
    }
    GroupNodeModel2.prototype.initNodeData = function(data) {
      var _this = this;
      _super.prototype.initNodeData.call(this, data);
      var children = [];
      if (Array.isArray(data.children)) {
        children = data.children;
      }
      this.children = new Set(children);
      this.width = defaultWidth;
      this.height = defaultHeight;
      this.foldedWidth = 80;
      this.foldedHeight = 60;
      this.zIndex = DEFAULT_BOTTOM_Z_INDEX;
      this.radius = 0;
      this.text.editable = false;
      this.text.draggable = false;
      this.isRestrict = false;
      this.resizable = false;
      this.autoToFront = false;
      this.foldable = false;
      if (this.properties.isFolded === void 0) {
        this.properties.isFolded = false;
      }
      this.isFolded = this.properties.isFolded;
      setTimeout(function() {
        _this.isFolded && _this.foldGroup(_this.isFolded);
      });
    };
    GroupNodeModel2.prototype.getResizeOutlineStyle = function() {
      var style2 = _super.prototype.getResizeOutlineStyle.call(this);
      style2.stroke = "none";
      return style2;
    };
    GroupNodeModel2.prototype.foldGroup = function(isFolded) {
      var _this = this;
      if (isFolded === this.isFolded) {
        return;
      }
      this.setProperty("isFolded", isFolded);
      this.isFolded = isFolded;
      if (isFolded) {
        this.x = this.x - this.width / 2 + this.foldedWidth / 2;
        this.y = this.y - this.height / 2 + this.foldedHeight / 2;
        this.unfoldedWidth = this.width;
        this.unfoldedHight = this.height;
        this.width = this.foldedWidth;
        this.height = this.foldedHeight;
      } else {
        this.width = this.unfoldedWidth;
        this.height = this.unfoldedHight;
        this.x = this.x + this.width / 2 - this.foldedWidth / 2;
        this.y = this.y + this.height / 2 - this.foldedHeight / 2;
      }
      var allEdges = this.incoming.edges.concat(this.outgoing.edges);
      this.children.forEach(function(elementId) {
        var nodeModel = _this.graphModel.getElement(elementId);
        var foldStatus = nodeModel.isFolded;
        if (nodeModel.isGroup && !nodeModel.isFolded) {
          nodeModel.foldGroup(isFolded);
        }
        if (nodeModel.isGroup && !isFolded) {
          var lastFoldStatus = _this.childrenLastFoldStatus[elementId];
          if (lastFoldStatus !== void 0 && lastFoldStatus !== nodeModel.isFolded) {
            nodeModel.foldGroup(lastFoldStatus);
          }
        }
        _this.childrenLastFoldStatus[elementId] = foldStatus;
        nodeModel.visible = !isFolded;
        allEdges = allEdges.concat(nodeModel.incoming.edges.concat(nodeModel.outgoing.edges));
      });
      this.foldEdge(isFolded, allEdges);
    };
    GroupNodeModel2.prototype.getAnchorStyle = function(anchorInfo) {
      var style2 = _super.prototype.getAnchorStyle.call(this, anchorInfo);
      style2.stroke = "transparent";
      style2.fill = "transparent";
      style2.hover.fill = "transparent";
      style2.hover.stroke = "transparent";
      return style2;
    };
    GroupNodeModel2.prototype.foldEdge = function(isFolded, allEdges) {
      var _this = this;
      allEdges.forEach(function(edgeModel, index) {
        var id = edgeModel.id, sourceNodeId = edgeModel.sourceNodeId, targetNodeId = edgeModel.targetNodeId, startPoint = edgeModel.startPoint, endPoint = edgeModel.endPoint, type3 = edgeModel.type, text = edgeModel.text;
        var properties = edgeModel.getProperties();
        var data = {
          id: id + "__" + index,
          sourceNodeId,
          targetNodeId,
          startPoint,
          endPoint,
          type: type3,
          properties,
          text: text === null || text === void 0 ? void 0 : text.value
        };
        if (edgeModel.virtual) {
          _this.graphModel.deleteEdgeById(edgeModel.id);
        }
        var targetNodeIdGroup = _this.graphModel.group.getNodeGroup(targetNodeId);
        if (!targetNodeIdGroup) {
          targetNodeIdGroup = _this.graphModel.getNodeModelById(targetNodeId);
        }
        var sourceNodeIdGroup = _this.graphModel.group.getNodeGroup(sourceNodeId);
        if (!sourceNodeIdGroup) {
          sourceNodeIdGroup = _this.graphModel.getNodeModelById(sourceNodeId);
        }
        if (isFolded && edgeModel.visible !== false) {
          if (_this.children.has(sourceNodeId) || _this.id === sourceNodeId) {
            data.startPoint = void 0;
            data.sourceNodeId = _this.id;
          } else {
            data.endPoint = void 0;
            data.targetNodeId = _this.id;
          }
          if (targetNodeIdGroup.id !== _this.id || sourceNodeIdGroup.id !== _this.id) {
            _this.createVirtualEdge(data);
          }
          edgeModel.visible = false;
        }
        if (!isFolded && edgeModel.visible === false) {
          if (targetNodeIdGroup && targetNodeIdGroup.isGroup && targetNodeIdGroup.isFolded) {
            data.targetNodeId = targetNodeIdGroup.id;
            data.endPoint = void 0;
            _this.createVirtualEdge(data);
          } else if (sourceNodeIdGroup && sourceNodeIdGroup.isGroup && sourceNodeIdGroup.isFolded) {
            data.sourceNodeId = sourceNodeIdGroup.id;
            data.startPoint = void 0;
            _this.createVirtualEdge(data);
          } else {
            edgeModel.visible = true;
          }
        }
      });
    };
    GroupNodeModel2.prototype.createVirtualEdge = function(edgeData) {
      edgeData.pointsList = void 0;
      var model = this.graphModel.addEdge(edgeData);
      model.virtual = true;
      model.text.editable = false;
      model.isFoldedEdge = true;
    };
    GroupNodeModel2.prototype.isInRange = function(_a) {
      var x1 = _a.x1, y1 = _a.y1, x2 = _a.x2, y2 = _a.y2;
      return x1 >= this.x - this.width / 2 && x2 <= this.x + this.width / 2 && y1 >= this.y - this.height / 2 && y2 <= this.y + this.height / 2;
    };
    GroupNodeModel2.prototype.isAllowMoveTo = function(_a) {
      var x1 = _a.x1, y1 = _a.y1, x2 = _a.x2, y2 = _a.y2;
      return {
        x: x1 >= this.x - this.width / 2 && x2 <= this.x + this.width / 2,
        y: y1 >= this.y - this.height / 2 && y2 <= this.y + this.height / 2
      };
    };
    GroupNodeModel2.prototype.setAllowAppendChild = function(isAllow) {
      this.setProperty("groupAddable", isAllow);
    };
    GroupNodeModel2.prototype.addChild = function(id) {
      this.children.add(id);
      this.graphModel.eventCenter.emit("group:add-node", { data: this.getData() });
    };
    GroupNodeModel2.prototype.removeChild = function(id) {
      this.children.delete(id);
      this.graphModel.eventCenter.emit("group:remove-node", { data: this.getData() });
    };
    GroupNodeModel2.prototype.getAddableOutlineStyle = function() {
      return {
        stroke: "#FEB663",
        strokeWidth: 2,
        strokeDasharray: "4 4",
        fill: "transparent"
      };
    };
    GroupNodeModel2.prototype.getData = function() {
      var _this = this;
      var data = _super.prototype.getData.call(this);
      data.children = [];
      this.children.forEach(function(childId) {
        var model = _this.graphModel.getNodeModelById(childId);
        if (model && !model.virtual) {
          data.children.push(childId);
        }
      });
      var properties = data.properties;
      delete properties.groupAddable;
      delete properties.isFolded;
      return data;
    };
    GroupNodeModel2.prototype.getHistoryData = function() {
      var data = _super.prototype.getData.call(this);
      data.children = __spread7(this.children);
      data.isGroup = true;
      var properties = data.properties;
      delete properties.groupAddable;
      if (properties.isFolded) {
        data.x = data.x + this.unfoldedWidth / 2 - this.foldedWidth / 2;
        data.y = data.y + this.unfoldedHight / 2 - this.foldedHeight / 2;
      }
      return data;
    };
    GroupNodeModel2.prototype.isAllowAppendIn = function(nodeData) {
      return true;
    };
    GroupNodeModel2.prototype.toBack = function() {
      this.zIndex--;
    };
    return GroupNodeModel2;
  }(RectResize_default.model)
);
var GroupNode = (
  /** @class */
  function(_super) {
    __extends21(GroupNode2, _super);
    function GroupNode2() {
      return _super !== null && _super.apply(this, arguments) || this;
    }
    GroupNode2.prototype.getControlGroup = function() {
      var _a = this.props.model, resizable = _a.resizable, properties = _a.properties;
      return resizable && !properties.isFolded ? _super.prototype.getControlGroup.call(this) : null;
    };
    GroupNode2.prototype.getAddableShape = function() {
      var _a = this.props.model, width = _a.width, height = _a.height, x2 = _a.x, y2 = _a.y, radius = _a.radius, properties = _a.properties;
      if (!properties.groupAddable)
        return null;
      var strokeWidth = this.props.model.getNodeStyle().strokeWidth;
      var style2 = this.props.model.getAddableOutlineStyle();
      var newWidth = width + strokeWidth + 8;
      var newHeight = height + strokeWidth + 8;
      return (0, import_core22.h)("rect", __assign18(__assign18({}, style2), { width: newWidth, height: newHeight, x: x2 - newWidth / 2, y: y2 - newHeight / 2, rx: radius, ry: radius }));
    };
    GroupNode2.prototype.getFoldIcon = function() {
      var model = this.props.model;
      var foldX = model.x - model.width / 2 + 5;
      var foldY = model.y - model.height / 2 + 5;
      if (!model.foldable)
        return null;
      var iconIcon = (0, import_core22.h)("path", {
        fill: "none",
        stroke: "#818281",
        strokeWidth: 2,
        "pointer-events": "none",
        d: model.properties.isFolded ? "M " + (foldX + 3) + "," + (foldY + 6) + " " + (foldX + 11) + "," + (foldY + 6) + " M" + (foldX + 7) + "," + (foldY + 2) + " " + (foldX + 7) + "," + (foldY + 10) : "M " + (foldX + 3) + "," + (foldY + 6) + " " + (foldX + 11) + "," + (foldY + 6) + " "
      });
      return (0, import_core22.h)("g", {}, [
        (0, import_core22.h)("rect", {
          height: 12,
          width: 14,
          rx: 2,
          ry: 2,
          strokeWidth: 1,
          fill: "#F4F5F6",
          stroke: "#CECECE",
          cursor: "pointer",
          x: model.x - model.width / 2 + 5,
          y: model.y - model.height / 2 + 5,
          onClick: function() {
            model.foldGroup(!model.properties.isFolded);
          }
        }),
        iconIcon
      ]);
    };
    GroupNode2.prototype.getResizeShape = function() {
      return (0, import_core22.h)("g", {}, [
        this.getAddableShape(),
        _super.prototype.getResizeShape.call(this),
        this.getFoldIcon()
      ]);
    };
    return GroupNode2;
  }(RectResize_default.view)
);
var GroupNode_default = {
  type: "group",
  view: GroupNode,
  model: GroupNodeModel
};

// node_modules/.pnpm/@logicflow+extension@1.2.28/node_modules/@logicflow/extension/es/materials/group/index.js
var __assign19 = function() {
  __assign19 = Object.assign || function(t2) {
    for (var s2, i2 = 1, n2 = arguments.length; i2 < n2; i2++) {
      s2 = arguments[i2];
      for (var p2 in s2)
        if (Object.prototype.hasOwnProperty.call(s2, p2))
          t2[p2] = s2[p2];
    }
    return t2;
  };
  return __assign19.apply(this, arguments);
};
var __rest = function(s2, e2) {
  var t2 = {};
  for (var p2 in s2)
    if (Object.prototype.hasOwnProperty.call(s2, p2) && e2.indexOf(p2) < 0)
      t2[p2] = s2[p2];
  if (s2 != null && typeof Object.getOwnPropertySymbols === "function")
    for (var i2 = 0, p2 = Object.getOwnPropertySymbols(s2); i2 < p2.length; i2++) {
      if (e2.indexOf(p2[i2]) < 0 && Object.prototype.propertyIsEnumerable.call(s2, p2[i2]))
        t2[p2[i2]] = s2[p2[i2]];
    }
  return t2;
};
var __read10 = function(o2, n2) {
  var m2 = typeof Symbol === "function" && o2[Symbol.iterator];
  if (!m2)
    return o2;
  var i2 = m2.call(o2), r2, ar = [], e2;
  try {
    while ((n2 === void 0 || n2-- > 0) && !(r2 = i2.next()).done)
      ar.push(r2.value);
  } catch (error) {
    e2 = { error };
  } finally {
    try {
      if (r2 && !r2.done && (m2 = i2["return"]))
        m2.call(i2);
    } finally {
      if (e2)
        throw e2.error;
    }
  }
  return ar;
};
var __spread8 = function() {
  for (var ar = [], i2 = 0; i2 < arguments.length; i2++)
    ar = ar.concat(__read10(arguments[i2]));
  return ar;
};
var DEFAULT_TOP_Z_INDEX = -1e3;
var DEFAULT_BOTTOM_Z_INDEX2 = -1e4;
var Group = (
  /** @class */
  function() {
    function Group2(_a) {
      var _this = this;
      var lf = _a.lf;
      this.topGroupZIndex = DEFAULT_BOTTOM_Z_INDEX2;
      this.nodeGroupMap = /* @__PURE__ */ new Map();
      this.graphRendered = function(data) {
        if (data && data.nodes) {
          data.nodes.forEach(function(node) {
            if (node.children) {
              node.children.forEach(function(nodeId) {
                _this.nodeGroupMap.set(nodeId, node.id);
              });
            }
          });
          _this.checkAndCorrectTopGroupZIndex(data.nodes);
        }
      };
      this.appendNodeToGroup = function(_a2) {
        var data = _a2.data;
        var preGroupId = _this.nodeGroupMap.get(data.id);
        if (preGroupId) {
          var preGroup = _this.lf.getNodeModelById(preGroupId);
          preGroup.removeChild(data.id);
          _this.nodeGroupMap.delete(data.id);
          preGroup.setAllowAppendChild(false);
        }
        var nodeModel = _this.lf.getNodeModelById(data.id);
        var bounds = nodeModel.getBounds();
        var group = _this.getGroup(bounds, data);
        if (nodeModel.isGroup) {
          data.children.forEach(function(nodeId) {
            _this.nodeGroupMap.set(nodeId, data.id);
          });
          _this.checkAndCorrectTopGroupZIndex([data]);
          _this.nodeSelected({ data, isSelected: false, isMultiple: false });
        }
        if (!group)
          return;
        var isAllowAppendIn = group.isAllowAppendIn(data);
        if (!isAllowAppendIn) {
          _this.lf.emit("group:not-allowed", {
            group: group.getData(),
            node: data
          });
          return;
        }
        group.addChild(data.id);
        _this.nodeGroupMap.set(data.id, group.id);
        group.setAllowAppendChild(false);
      };
      this.deleteGroupChild = function(_a2) {
        var data = _a2.data;
        if (data.children) {
          data.children.forEach(function(nodeId) {
            _this.nodeGroupMap.delete(nodeId);
            _this.lf.deleteNode(nodeId);
          });
        }
        var groupId = _this.nodeGroupMap.get(data.id);
        if (groupId) {
          var group = _this.lf.getNodeModelById(groupId);
          group.removeChild(data.id);
          _this.nodeGroupMap.delete(data.id);
        }
      };
      this.setActiveGroup = function(_a2) {
        var data = _a2.data;
        var nodeModel = _this.lf.getNodeModelById(data.id);
        var bounds = nodeModel.getBounds();
        var newGroup = _this.getGroup(bounds, data);
        if (_this.activeGroup) {
          _this.activeGroup.setAllowAppendChild(false);
        }
        if (!newGroup || nodeModel.isGroup && newGroup.id === data.id)
          return;
        var isAllowAppendIn = newGroup.isAllowAppendIn(data);
        if (!isAllowAppendIn) {
          return;
        }
        _this.activeGroup = newGroup;
        _this.activeGroup.setAllowAppendChild(true);
      };
      this.findNodeAndChildMaxZIndex = function(nodeModel) {
        var maxZIndex = DEFAULT_BOTTOM_Z_INDEX2;
        if (nodeModel.isGroup) {
          maxZIndex = nodeModel.zIndex > maxZIndex ? nodeModel.zIndex : maxZIndex;
        }
        if (nodeModel.children) {
          nodeModel.children.forEach(function(nodeId) {
            if (typeof nodeId === "object") {
              nodeId = nodeId.id;
            }
            var child = _this.lf.getNodeModelById(nodeId);
            if (child.isGroup) {
              var childMaxZIndex = _this.findNodeAndChildMaxZIndex(child);
              maxZIndex = childMaxZIndex > maxZIndex ? childMaxZIndex : maxZIndex;
            }
          });
        }
        return maxZIndex;
      };
      this.checkAndCorrectTopGroupZIndex = function(nodes) {
        var maxZIndex = DEFAULT_BOTTOM_Z_INDEX2;
        nodes.forEach(function(node) {
          var nodeModel = _this.lf.getNodeModelById(node.id);
          var currentNodeMaxZIndex = _this.findNodeAndChildMaxZIndex(nodeModel);
          if (currentNodeMaxZIndex > maxZIndex) {
            maxZIndex = currentNodeMaxZIndex;
          }
        });
        if (_this.topGroupZIndex >= maxZIndex) {
          return;
        }
        var allGroupNodes = _this.lf.graphModel.nodes.filter(function(node) {
          return node.isGroup;
        });
        var max = _this.topGroupZIndex;
        for (var i2 = 0; i2 < allGroupNodes.length; i2++) {
          var groupNode = allGroupNodes[i2];
          if (groupNode.zIndex > max) {
            max = groupNode.zIndex;
          }
        }
        _this.topGroupZIndex = max;
      };
      this.nodeSelected = function(_a2) {
        var data = _a2.data, isMultiple = _a2.isMultiple, isSelected = _a2.isSelected;
        var nodeModel = _this.lf.getNodeModelById(data.id);
        _this.toFrontGroup(nodeModel);
        if (_this.topGroupZIndex > DEFAULT_TOP_Z_INDEX) {
          _this.topGroupZIndex = DEFAULT_BOTTOM_Z_INDEX2;
          var allGroups = _this.lf.graphModel.nodes.filter(function(node) {
            return node.isGroup;
          }).sort(function(a2, b) {
            return a2.zIndex - b.zIndex;
          });
          var preZIndex = 0;
          for (var i2 = 0; i2 < allGroups.length; i2++) {
            var group = allGroups[i2];
            if (group.zIndex !== preZIndex) {
              _this.topGroupZIndex++;
              preZIndex = group.zIndex;
            }
            group.setZIndex(_this.topGroupZIndex);
          }
        }
        if (isMultiple && isSelected) {
          if (nodeModel.isGroup) {
            nodeModel.children.forEach(function(child) {
              var childModel = _this.lf.graphModel.getElement(child);
              childModel.setSelected(false);
            });
          } else {
            var groupId = _this.nodeGroupMap.get(data.id);
            if (groupId) {
              var groupModel = _this.lf.getNodeModelById(groupId);
              groupModel.isSelected && nodeModel.setSelected(false);
            }
          }
        }
      };
      this.toFrontGroup = function(model) {
        if (!model || !model.isGroup) {
          return;
        }
        _this.topGroupZIndex++;
        model.setZIndex(_this.topGroupZIndex);
        if (model.children) {
          model.children.forEach(function(nodeId) {
            var node = _this.lf.getNodeModelById(nodeId);
            _this.toFrontGroup(node);
          });
        }
      };
      lf.register(GroupNode_default);
      this.lf = lf;
      lf.graphModel.addNodeMoveRules(function(model, deltaX, deltaY) {
        if (model.isGroup) {
          var nodeIds = _this.getNodeAllChild(model);
          lf.graphModel.moveNodes(nodeIds, deltaX, deltaY, true);
          return true;
        }
        var groupModel = lf.getNodeModelById(_this.nodeGroupMap.get(model.id));
        if (groupModel && groupModel.isRestrict) {
          var _a2 = model.getBounds(), x1 = _a2.x1, y1 = _a2.y1, x2 = _a2.x2, y2 = _a2.y2;
          var r2 = groupModel.isAllowMoveTo({
            x1: x1 + deltaX,
            y1: y1 + deltaY,
            x2: x2 + deltaX,
            y2: y2 + deltaY
          });
          return r2;
        }
        return true;
      });
      lf.graphModel.group = this;
      lf.on("node:add,node:drop,node:dnd-add", this.appendNodeToGroup);
      lf.on("node:delete", this.deleteGroupChild);
      lf.on("node:dnd-drag,node:drag", this.setActiveGroup);
      lf.on("node:click", this.nodeSelected);
      lf.on("graph:rendered", this.graphRendered);
      lf.addElements = function(_a2, distance) {
        var selectedNodes = _a2.nodes, selectedEdges = _a2.edges;
        var nodeIdMap = {};
        var elements = {
          nodes: [],
          edges: []
        };
        var groupInnerEdges = [];
        for (var i2 = 0; i2 < selectedNodes.length; i2++) {
          var node = selectedNodes[i2];
          var preId = node.id;
          var children = node.children, rest = __rest(node, ["children"]);
          var nodeModel = lf.addNode(rest);
          if (!nodeModel)
            return { nodes: [], edges: [] };
          if (preId)
            nodeIdMap[preId] = nodeModel.id;
          elements.nodes.push(nodeModel);
          var edgesArray = _this.createAllChildNodes(nodeIdMap, children, nodeModel, distance).edgesArray;
          groupInnerEdges.push.apply(groupInnerEdges, __spread8(edgesArray));
        }
        groupInnerEdges.forEach(function(edge) {
          _this.createEdgeModel(edge, nodeIdMap, distance);
        });
        selectedEdges.forEach(function(edge) {
          var edgeModel = _this.createEdgeModel(edge, nodeIdMap, 0);
          elements.edges.push(edgeModel);
        });
        return elements;
      };
    }
    Group2.prototype.createAllChildNodes = function(nodeIdMap, children, current, distance) {
      var _this = this;
      var lf = this.lf;
      var edgesDataArray = [];
      var edgesNodeModelArray = [];
      var nodesArray = [];
      children === null || children === void 0 ? void 0 : children.forEach(function(childId) {
        var childNodeModel = lf.getNodeModelById(childId);
        var x2 = childNodeModel.x, y2 = childNodeModel.y, properties = childNodeModel.properties, type3 = childNodeModel.type, text = childNodeModel.text, rotate = childNodeModel.rotate, children2 = childNodeModel.children, incoming = childNodeModel.incoming, outgoing = childNodeModel.outgoing;
        var eventType = import_core23.EventType.NODE_GROUP_COPY || "node:group-copy-add";
        var newChildModel = lf.addNode({
          x: x2 + distance,
          y: y2 + distance,
          properties,
          type: type3,
          text: __assign19(__assign19({}, text), { x: text.x + distance, y: text.y + distance }),
          rotate
        }, eventType);
        current.addChild(newChildModel.id);
        nodeIdMap[childId] = newChildModel.id;
        nodesArray.push(newChildModel);
        childNodeModel.incoming.edges.forEach(function(edge) {
          edgesNodeModelArray.push(edge);
        });
        childNodeModel.outgoing.edges.forEach(function(edge) {
          edgesNodeModelArray.push(edge);
        });
        if (children2 instanceof Set) {
          var _a = _this.createAllChildNodes(nodeIdMap, children2, newChildModel, distance), childNodes = _a.nodesArray, childEdges = _a.edgesArray;
          nodesArray.push.apply(nodesArray, __spread8(childNodes));
          edgesDataArray.push.apply(edgesDataArray, __spread8(childEdges));
        }
      });
      var filterEdgesArray = edgesNodeModelArray.filter(function(edge) {
        return nodeIdMap[edge.sourceNodeId] && nodeIdMap[edge.targetNodeId];
      });
      var filterEdgesDataArray = filterEdgesArray.map(function(item) {
        return item.getData();
      });
      return {
        nodesArray,
        edgesArray: edgesDataArray.concat(filterEdgesDataArray)
      };
    };
    Group2.prototype.createEdgeModel = function(edge, nodeIdMap, distance) {
      var lf = this.lf;
      var sourceId = edge.sourceNodeId;
      var targetId = edge.targetNodeId;
      if (nodeIdMap[sourceId])
        sourceId = nodeIdMap[sourceId];
      if (nodeIdMap[targetId])
        targetId = nodeIdMap[targetId];
      var type3 = edge.type, startPoint = edge.startPoint, endPoint = edge.endPoint, pointsList = edge.pointsList, text = edge.text, rest = __rest(edge, ["type", "startPoint", "endPoint", "pointsList", "text"]);
      var newStartPoint = {
        x: startPoint.x + distance,
        y: startPoint.y + distance
      };
      var newEndPoint = {
        x: endPoint.x + distance,
        y: endPoint.y + distance
      };
      var newPointsList = [];
      if (pointsList && pointsList.length > 0) {
        newPointsList = pointsList.map(function(point) {
          point.x += distance;
          point.y += distance;
          return point;
        });
      }
      var newText = text;
      if (text && typeof text !== "string") {
        newText.x = text.x + distance;
        newText.y = text.y + distance;
      }
      var edgeModel = lf.graphModel.addEdge({
        type: type3,
        startPoint: newStartPoint,
        endPoint: newEndPoint,
        sourceNodeId: sourceId,
        targetNodeId: targetId,
        pointsList: newPointsList,
        text: newText
      });
      return edgeModel;
    };
    Group2.prototype.getNodeAllChild = function(model) {
      var _this = this;
      var nodeIds = [];
      if (model.children) {
        model.children.forEach(function(nodeId) {
          nodeIds.push(nodeId);
          var nodeModel = _this.lf.getNodeModelById(nodeId);
          if (nodeModel.isGroup) {
            nodeIds = nodeIds.concat(_this.getNodeAllChild(nodeModel));
          }
        });
      }
      return nodeIds;
    };
    Group2.prototype.getGroup = function(bounds, nodeData) {
      var nodes = this.lf.graphModel.nodes;
      var groups = nodes.filter(function(node) {
        return node.isGroup && node.isInRange(bounds) && node.id !== nodeData.id;
      });
      if (groups.length === 0)
        return;
      if (groups.length === 1)
        return groups[0];
      var topGroup = groups[groups.length - 1];
      for (var i2 = groups.length - 2; i2 >= 0; i2--) {
        if (groups[i2].zIndex > topGroup.zIndex) {
          topGroup = groups[i2];
        }
      }
      return topGroup;
    };
    Group2.prototype.getNodeGroup = function(nodeId) {
      var groupId = this.nodeGroupMap.get(nodeId);
      if (groupId) {
        return this.lf.getNodeModelById(groupId);
      }
    };
    Group2.pluginName = "group";
    return Group2;
  }()
);

// node_modules/.pnpm/@logicflow+extension@1.2.28/node_modules/@logicflow/extension/es/bpmn-elements/presets/Task/subProcess.js
var __extends22 = function() {
  var extendStatics = function(d2, b) {
    extendStatics = Object.setPrototypeOf || { __proto__: [] } instanceof Array && function(d3, b2) {
      d3.__proto__ = b2;
    } || function(d3, b2) {
      for (var p2 in b2)
        if (b2.hasOwnProperty(p2))
          d3[p2] = b2[p2];
    };
    return extendStatics(d2, b);
  };
  return function(d2, b) {
    extendStatics(d2, b);
    function __() {
      this.constructor = d2;
    }
    d2.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
  };
}();
var __assign20 = function() {
  __assign20 = Object.assign || function(t2) {
    for (var s2, i2 = 1, n2 = arguments.length; i2 < n2; i2++) {
      s2 = arguments[i2];
      for (var p2 in s2)
        if (Object.prototype.hasOwnProperty.call(s2, p2))
          t2[p2] = s2[p2];
    }
    return t2;
  };
  return __assign20.apply(this, arguments);
};
function SubProcessFactory() {
  var view = (
    /** @class */
    function(_super) {
      __extends22(view2, _super);
      function view2() {
        return _super !== null && _super.apply(this, arguments) || this;
      }
      view2.prototype.getFoldIcon = function() {
        var model2 = this.props.model;
        var x2 = model2.x, y2 = model2.y, width = model2.width, height = model2.height, properties = model2.properties, foldable = model2.foldable;
        var foldX = model2.x - model2.width / 2 + 5;
        var foldY = model2.y - model2.height / 2 + 5;
        if (!foldable)
          return null;
        var iconIcon = (0, import_core24.h)("path", {
          fill: "none",
          stroke: "#818281",
          strokeWidth: 2,
          "pointer-events": "none",
          d: properties.isFolded ? "M " + (foldX + 3) + "," + (foldY + 6) + " " + (foldX + 11) + "," + (foldY + 6) + " M" + (foldX + 7) + "," + (foldY + 2) + " " + (foldX + 7) + "," + (foldY + 10) : "M " + (foldX + 3) + "," + (foldY + 6) + " " + (foldX + 11) + "," + (foldY + 6) + " "
        });
        return (0, import_core24.h)("g", {}, [
          (0, import_core24.h)("rect", {
            height: 12,
            width: 14,
            rx: 2,
            ry: 2,
            strokeWidth: 1,
            fill: "#F4F5F6",
            stroke: "#CECECE",
            cursor: "pointer",
            x: x2 - width / 2 + 5,
            y: y2 - height / 2 + 5,
            onClick: function(e2) {
              e2.stopPropagation();
              model2.foldGroup(!properties.isFolded);
            }
          }),
          iconIcon
        ]);
      };
      view2.prototype.getResizeShape = function() {
        var model2 = this.props.model;
        var x2 = model2.x, y2 = model2.y, width = model2.width, height = model2.height;
        var style2 = model2.getNodeStyle();
        var foldRectAttrs = __assign20(__assign20({}, style2), {
          x: x2 - width / 2,
          y: y2 - height / 2,
          width,
          height,
          stroke: "black",
          strokeWidth: 2,
          strokeDasharray: "0 0"
        });
        return (0, import_core24.h)("g", {}, [
          // this.getAddAbleShape(),
          (0, import_core24.h)("rect", __assign20({}, foldRectAttrs)),
          this.getFoldIcon()
        ]);
      };
      return view2;
    }(GroupNode_default.view)
  );
  var model = (
    /** @class */
    function(_super) {
      __extends22(model2, _super);
      function model2() {
        return _super !== null && _super.apply(this, arguments) || this;
      }
      model2.prototype.initNodeData = function(data) {
        _super.prototype.initNodeData.call(this, data);
        this.foldable = true;
        this.resizable = true;
        this.width = 400;
        this.height = 200;
        this.resetWidthHeight();
        this.isTaskNode = true;
        this.boundaryEvents = [];
      };
      model2.prototype.resetWidthHeight = function() {
        var _a, _b;
        var width = (_a = this.properties.iniProp) === null || _a === void 0 ? void 0 : _a.width;
        var height = (_b = this.properties.iniProp) === null || _b === void 0 ? void 0 : _b.height;
        width && (this.width = width);
        height && (this.height = height);
      };
      model2.prototype.getNodeStyle = function() {
        var style2 = _super.prototype.getNodeStyle.call(this);
        style2.stroke = "#989891";
        style2.strokeWidth = 1;
        style2.strokeDasharray = "3 3";
        if (this.isSelected) {
          style2.stroke = "rgb(124, 15, 255)";
        }
        var isBoundaryEventTouchingTask = this.properties.isBoundaryEventTouchingTask;
        if (isBoundaryEventTouchingTask) {
          style2.stroke = "#00acff";
          style2.strokeWidth = 2;
        }
        return style2;
      };
      model2.prototype.addChild = function(id) {
        var model3 = this.graphModel.getElement(id);
        model3.setProperties({
          parent: this.id
        });
        _super.prototype.addChild.call(this, id);
      };
      model2.prototype.getAnchorStyle = function() {
        var style2 = _super.prototype.getAnchorStyle.call(this, {});
        style2.stroke = "#000";
        style2.fill = "#fff";
        style2.hover.stroke = "transparent";
        return style2;
      };
      model2.prototype.getOutlineStyle = function() {
        var style2 = _super.prototype.getOutlineStyle.call(this);
        style2.stroke = "transparent";
        !style2.hover && (style2.hover = {});
        style2.hover.stroke = "transparent";
        return style2;
      };
      model2.prototype.setTouching = function(flag) {
        this.setProperty("isBoundaryEventTouchingTask", flag);
      };
      model2.prototype.addBoundaryEvent = function(nodeId) {
        this.setTouching(false);
        if (this.boundaryEvents.find(function(item) {
          return item === nodeId;
        })) {
          return false;
        }
        var boundaryEvent2 = this.graphModel.getNodeModelById(nodeId);
        boundaryEvent2 === null || boundaryEvent2 === void 0 ? void 0 : boundaryEvent2.setProperties({
          attachedToRef: this.id
        });
        this.boundaryEvents.push(nodeId);
        return true;
      };
      model2.prototype.deleteBoundaryEvent = function(nodeId) {
        this.boundaryEvents = this.boundaryEvents.filter(function(item) {
          return item !== nodeId;
        });
      };
      return model2;
    }(GroupNode_default.model)
  );
  return {
    type: "bpmn:subProcess",
    view,
    model
  };
}

// node_modules/.pnpm/@logicflow+extension@1.2.28/node_modules/@logicflow/extension/es/bpmn-elements/presets/Task/index.js
var __values = function(o2) {
  var s2 = typeof Symbol === "function" && Symbol.iterator, m2 = s2 && o2[s2], i2 = 0;
  if (m2)
    return m2.call(o2);
  if (o2 && typeof o2.length === "number")
    return {
      next: function() {
        if (o2 && i2 >= o2.length)
          o2 = void 0;
        return { value: o2 && o2[i2++], done: !o2 };
      }
    };
  throw new TypeError(s2 ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
function boundaryEvent(lf) {
  lf.on("node:drag,node:dnd-drag", checkAppendBoundaryEvent);
  lf.on("node:drop,node:dnd-add", appendBoundaryEvent);
  lf.graphModel.addNodeMoveRules(function(model, deltaX, deltaY) {
    if (model.isTaskNode) {
      var nodeIds = model.boundaryEvents;
      lf.graphModel.moveNodes(nodeIds, deltaX, deltaY, true);
      return true;
    }
    return true;
  });
  function appendBoundaryEvent(_a) {
    var e_1, _b;
    var data = _a.data;
    var type3 = data.type, id = data.id;
    if (type3 !== "bpmn:boundaryEvent") {
      return;
    }
    var nodes = lf.graphModel.nodes;
    try {
      for (var nodes_1 = __values(nodes), nodes_1_1 = nodes_1.next(); !nodes_1_1.done; nodes_1_1 = nodes_1.next()) {
        var node = nodes_1_1.value;
        if (node.isTaskNode) {
          var nodeId = null;
          if ((nodeId = isBoundaryEventCloseToTask(node, data)) !== null) {
            var eventModel = lf.graphModel.getNodeModelById(id);
            var nodeModel = lf.graphModel.getNodeModelById(nodeId);
            var attachedToRef = eventModel.properties.attachedToRef;
            if (attachedToRef && attachedToRef !== nodeId) {
              lf.graphModel.getNodeModelById(attachedToRef).deleteBoundaryEvent(id);
            }
            nodeModel.addBoundaryEvent(id);
            return;
          }
        }
      }
    } catch (e_1_1) {
      e_1 = { error: e_1_1 };
    } finally {
      try {
        if (nodes_1_1 && !nodes_1_1.done && (_b = nodes_1.return))
          _b.call(nodes_1);
      } finally {
        if (e_1)
          throw e_1.error;
      }
    }
  }
  function checkAppendBoundaryEvent(_a) {
    var e_2, _b;
    var data = _a.data;
    var type3 = data.type;
    if (type3 !== "bpmn:boundaryEvent") {
      return;
    }
    var nodes = lf.graphModel.nodes;
    try {
      for (var nodes_2 = __values(nodes), nodes_2_1 = nodes_2.next(); !nodes_2_1.done; nodes_2_1 = nodes_2.next()) {
        var node = nodes_2_1.value;
        if (node.isTaskNode) {
          if (isBoundaryEventCloseToTask(node, data)) {
            node.setTouching(true);
          } else {
            node.setTouching(false);
          }
        }
      }
    } catch (e_2_1) {
      e_2 = { error: e_2_1 };
    } finally {
      try {
        if (nodes_2_1 && !nodes_2_1.done && (_b = nodes_2.return))
          _b.call(nodes_2);
      } finally {
        if (e_2)
          throw e_2.error;
      }
    }
  }
  function isBoundaryEventCloseToTask(task, event) {
    var offset = 5;
    var tx = task.x, ty = task.y, twidth = task.width, theight = task.height, id = task.id;
    var bbox = {
      minX: tx - twidth / 2,
      maxX: tx + twidth / 2,
      minY: ty - theight / 2,
      maxY: ty + theight / 2
    };
    var bx = event.x, by = event.y;
    var outerBBox = {
      minX: bbox.minX - offset,
      maxX: bbox.maxX + offset,
      minY: bbox.minY - offset,
      maxY: bbox.maxY + offset
    };
    var innerBBox = {
      minX: bbox.minX + offset,
      maxX: bbox.maxX - offset,
      minY: bbox.minY + offset,
      maxY: bbox.maxY - offset
    };
    if (bx > outerBBox.minX && bx < outerBBox.maxX && by > outerBBox.minY && by < outerBBox.maxY) {
      if (!(bx > innerBBox.minX && bx < innerBBox.maxX && by > innerBBox.minY && by < innerBBox.maxY)) {
        return id;
      }
    }
    return null;
  }
}
function registerTaskNodes(lf) {
  var ServiceTask2 = TaskNodeFactory("bpmn:serviceTask", serviceTaskIcon);
  var UserTask2 = TaskNodeFactory("bpmn:userTask", userTaskIcon);
  lf.register(ServiceTask2);
  lf.register(UserTask2);
  lf.register(SubProcessFactory());
  boundaryEvent(lf);
}

// node_modules/.pnpm/@logicflow+extension@1.2.28/node_modules/@logicflow/extension/es/bpmn-elements/presets/Flow/sequenceFlow.js
var import_core25 = __toESM(require_logic_flow());
var __extends23 = function() {
  var extendStatics = function(d2, b) {
    extendStatics = Object.setPrototypeOf || { __proto__: [] } instanceof Array && function(d3, b2) {
      d3.__proto__ = b2;
    } || function(d3, b2) {
      for (var p2 in b2)
        if (b2.hasOwnProperty(p2))
          d3[p2] = b2[p2];
    };
    return extendStatics(d2, b);
  };
  return function(d2, b) {
    extendStatics(d2, b);
    function __() {
      this.constructor = d2;
    }
    d2.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
  };
}();
var __assign21 = function() {
  __assign21 = Object.assign || function(t2) {
    for (var s2, i2 = 1, n2 = arguments.length; i2 < n2; i2++) {
      s2 = arguments[i2];
      for (var p2 in s2)
        if (Object.prototype.hasOwnProperty.call(s2, p2))
          t2[p2] = s2[p2];
    }
    return t2;
  };
  return __assign21.apply(this, arguments);
};
function sequenceFlowFactory(props) {
  var model = (
    /** @class */
    function(_super) {
      __extends23(model2, _super);
      function model2(data, graphModel) {
        var _this = this;
        if (!data.id) {
          data.id = "Flow_" + genBpmnId();
        }
        var properties = __assign21(__assign21(__assign21({}, props || {}), data.properties), {
          // panels: ['condition'],
          isDefaultFlow: false
        });
        data.properties = properties;
        _this = _super.call(this, data, graphModel) || this;
        return _this;
      }
      model2.extendKey = "SequenceFlowModel";
      return model2;
    }(import_core25.PolylineEdgeModel)
  );
  var view = (
    /** @class */
    function(_super) {
      __extends23(view2, _super);
      function view2() {
        return _super !== null && _super.apply(this, arguments) || this;
      }
      view2.prototype.getStartArrow = function() {
        var model2 = this.props.model;
        var isDefaultFlow = model2.properties.isDefaultFlow;
        return isDefaultFlow ? (0, import_core25.h)("path", {
          refX: 15,
          stroke: "#000000",
          strokeWidth: 2,
          d: "M 20 5 10 -5 z"
        }) : (0, import_core25.h)("path", {
          d: ""
        });
      };
      view2.extendKey = "SequenceFlowEdge";
      return view2;
    }(import_core25.PolylineEdge)
  );
  return {
    type: "bpmn:sequenceFlow",
    view,
    model
  };
}

// node_modules/.pnpm/@logicflow+extension@1.2.28/node_modules/@logicflow/extension/es/bpmn-elements/presets/Flow/index.js
var SequenceFlow2 = sequenceFlowFactory();
function registerFlows(lf) {
  lf.register(SequenceFlow2);
}

// node_modules/.pnpm/@logicflow+extension@1.2.28/node_modules/@logicflow/extension/es/bpmn-elements/index.js
var __read11 = function(o2, n2) {
  var m2 = typeof Symbol === "function" && o2[Symbol.iterator];
  if (!m2)
    return o2;
  var i2 = m2.call(o2), r2, ar = [], e2;
  try {
    while ((n2 === void 0 || n2-- > 0) && !(r2 = i2.next()).done)
      ar.push(r2.value);
  } catch (error) {
    e2 = { error };
  } finally {
    try {
      if (r2 && !r2.done && (m2 = i2["return"]))
        m2.call(i2);
    } finally {
      if (e2)
        throw e2.error;
    }
  }
  return ar;
};
var definitionConfig = [
  {
    nodes: ["startEvent", "intermediateCatchEvent", "boundaryEvent"],
    definition: [
      {
        type: "bpmn:timerEventDefinition",
        icon: timerIcon,
        properties: {
          definitionType: "bpmn:timerEventDefinition",
          timerValue: "",
          timerType: ""
        }
      }
    ]
  }
];
function useDefinition(definition) {
  function setDefinition(config) {
    function set(nodes, definitions) {
      nodes.forEach(function(name) {
        if (!(definition === null || definition === void 0 ? void 0 : definition[name])) {
          definition[name] = /* @__PURE__ */ new Map();
        }
        var map = definition === null || definition === void 0 ? void 0 : definition[name];
        definitions.forEach(function(define) {
          map.set(define.type, define);
        });
      });
      return definition;
    }
    config.forEach(function(define) {
      set(define.nodes, define.definition);
    });
  }
  return function() {
    return [definition, setDefinition];
  };
}
var BPMNElements = (
  /** @class */
  function() {
    function BPMNElements2(_a) {
      var lf = _a.lf;
      lf.definition = {};
      lf.useDefinition = useDefinition(lf.definition);
      var _b = __read11(lf.useDefinition(), 2), _definition = _b[0], setDefinition = _b[1];
      setDefinition(definitionConfig);
      registerEventNodes(lf);
      registerGatewayNodes(lf);
      registerFlows(lf);
      registerTaskNodes(lf);
      lf.setDefaultEdgeType("bpmn:sequenceFlow");
    }
    BPMNElements2.pluginName = "BpmnElementsPlugin";
    return BPMNElements2;
  }()
);

// node_modules/.pnpm/@logicflow+extension@1.2.28/node_modules/@logicflow/extension/es/bpmn-elements-adapter/constant.js
var StartEventConfig2 = {
  width: 40,
  height: 40
};
var EndEventConfig2 = {
  width: 40,
  height: 40
};
var BoundaryEventConfig = {
  width: 100,
  height: 80
};
var IntermediateEventConfig = {
  width: 100,
  height: 80
};
var ParallelGatewayConfig = {
  width: 100,
  height: 80
};
var InclusiveGatewayConfig = {
  width: 100,
  height: 80
};
var ExclusiveGatewayConfig2 = {
  width: 100,
  height: 80
};
var ServiceTaskConfig2 = {
  width: 100,
  height: 80
};
var UserTaskConfig2 = {
  width: 100,
  height: 80
};
var SubProcessConfig = {
  width: 100,
  height: 80
};

// node_modules/.pnpm/@logicflow+extension@1.2.28/node_modules/@logicflow/extension/es/bpmn-elements-adapter/xml2json.js
var XML2 = function() {
};
XML2.ObjTree = function() {
  return this;
};
XML2.ObjTree.VERSION = "0.23";
XML2.ObjTree.prototype.xmlDecl = '<?xml version="1.0" encoding="UTF-8" ?>\n';
XML2.ObjTree.prototype.attr_prefix = "-";
XML2.ObjTree.prototype.parseXML = function(xml) {
  var root;
  if (window.DOMParser) {
    var xmldom = new DOMParser();
    var dom = xmldom.parseFromString(xml, "application/xml");
    if (!dom)
      return;
    root = dom.documentElement;
  } else if (window.ActiveXObject) {
    xmldom = new ActiveXObject("Microsoft.XMLDOM");
    xmldom.async = false;
    xmldom.loadXML(xml);
    root = xmldom.documentElement;
  }
  if (!root)
    return;
  var data = this.parseDOM(root);
  return data;
};
XML2.ObjTree.prototype.parseHTTP = function(url, options, callback) {
  var myOpt = {};
  for (var key in options) {
    myOpt[key] = options[key];
  }
  if (!myOpt.method) {
    if (typeof myOpt.postBody === "undefined" && typeof myOpt.postbody === "undefined" && typeof myOpt.parameters === "undefined") {
      myOpt.method = "get";
    } else {
      myOpt.method = "post";
    }
  }
  if (callback) {
    myOpt.asynchronous = true;
    var __this_1 = this;
    var __func_1 = callback;
    var __save_1 = myOpt.onComplete;
    myOpt.onComplete = function(trans2) {
      var tree;
      if (trans2 && trans2.responseXML && trans2.responseXML.documentElement) {
        tree = __this_1.parseDOM(trans2.responseXML.documentElement);
      }
      __func_1(tree, trans2);
      if (__save_1)
        __save_1(trans2);
    };
  } else {
    myOpt.asynchronous = false;
  }
  var trans;
  if (typeof HTTP !== "undefined" && HTTP.Request) {
    myOpt.uri = url;
    var req = new HTTP.Request(myOpt);
    if (req)
      trans = req.transport;
  } else if (typeof Ajax !== "undefined" && Ajax.Request) {
    var req = new Ajax.Request(url, myOpt);
    if (req)
      trans = req.transport;
  }
  if (callback)
    return trans;
  if (trans && trans.responseXML && trans.responseXML.documentElement) {
    return this.parseDOM(trans.responseXML.documentElement);
  }
};
XML2.ObjTree.prototype.parseDOM = function(root) {
  if (!root)
    return;
  this.__force_array = {};
  if (this.force_array) {
    for (var i2 = 0; i2 < this.force_array.length; i2++) {
      this.__force_array[this.force_array[i2]] = 1;
    }
  }
  var json = this.parseElement(root);
  if (this.__force_array[root.nodeName]) {
    json = [json];
  }
  if (root.nodeType !== 11) {
    var tmp = {};
    tmp[root.nodeName] = json;
    json = tmp;
  }
  return json;
};
XML2.ObjTree.prototype.parseElement = function(elem) {
  if (elem.nodeType === 7) {
    return;
  }
  if (elem.nodeType === 3 || elem.nodeType === 4 || elem.nodeType === 8) {
    var bool = elem.nodeValue.match(/[^\x00-\x20]/);
    if (bool == null)
      return;
    return elem.nodeValue;
  }
  var retVal = null;
  var cnt = {};
  if (elem.attributes && elem.attributes.length) {
    retVal = {};
    for (var i2 = 0; i2 < elem.attributes.length; i2++) {
      var key = elem.attributes[i2].nodeName;
      if (typeof key !== "string")
        continue;
      var val = elem.attributes[i2].nodeValue;
      try {
        val = JSON.parse(elem.attributes[i2].nodeValue.replace(/'/g, '"'));
      } catch (error) {
        val = elem.attributes[i2].nodeValue;
      }
      if (val === null || val === void 0)
        continue;
      key = this.attr_prefix + key;
      if (typeof cnt[key] === "undefined")
        cnt[key] = 0;
      cnt[key]++;
      this.addNode(retVal, key, cnt[key], val);
    }
  }
  if (elem.childNodes && elem.childNodes.length) {
    var textOnly = true;
    if (retVal)
      textOnly = false;
    for (var i2 = 0; i2 < elem.childNodes.length && textOnly; i2++) {
      var nType = elem.childNodes[i2].nodeType;
      if (nType === 3 || nType === 4 || nType === 8)
        continue;
      textOnly = false;
    }
    if (textOnly) {
      if (!retVal)
        retVal = "";
      for (var i2 = 0; i2 < elem.childNodes.length; i2++) {
        retVal += elem.childNodes[i2].nodeValue;
      }
    } else {
      if (!retVal)
        retVal = {};
      for (var i2 = 0; i2 < elem.childNodes.length; i2++) {
        var key = elem.childNodes[i2].nodeName;
        if (typeof key !== "string")
          continue;
        var val = this.parseElement(elem.childNodes[i2]);
        if (!val)
          continue;
        if (typeof cnt[key] === "undefined")
          cnt[key] = 0;
        cnt[key]++;
        this.addNode(retVal, key, cnt[key], val);
      }
    }
  } else {
    retVal === null && (retVal = {});
  }
  return retVal;
};
XML2.ObjTree.prototype.addNode = function(hash, key, counts, val) {
  if (this.__force_array[key]) {
    if (counts === 1)
      hash[key] = [];
    hash[key][hash[key].length] = val;
  } else if (counts === 1) {
    hash[key] = val;
  } else if (counts === 2) {
    hash[key] = [hash[key], val];
  } else {
    hash[key][hash[key].length] = val;
  }
};
XML2.ObjTree.prototype.writeXML = function(tree) {
  var xml = this.hash_to_xml(null, tree);
  return this.xmlDecl + xml;
};
XML2.ObjTree.prototype.hash_to_xml = function(name, tree) {
  var elem = [];
  var attr = [];
  for (var key in tree) {
    if (!(tree === null || tree === void 0 ? void 0 : tree.hasOwnProperty(key)))
      continue;
    var val = tree[key];
    if (key.charAt(0) !== this.attr_prefix) {
      if (typeof val === "undefined" || val == null) {
        elem[elem.length] = "<" + key + " />";
      } else if (typeof val === "object" && val.constructor === Array) {
        elem[elem.length] = this.array_to_xml(key, val);
      } else if (typeof val === "object") {
        elem[elem.length] = this.hash_to_xml(key, val);
      } else {
        elem[elem.length] = this.scalar_to_xml(key, val);
      }
    } else {
      attr[attr.length] = " " + key.substring(1) + '="' + this.xml_escape(val) + '"';
    }
  }
  var jattr = attr.join("");
  var jelem = elem.join("");
  if (typeof name === "undefined" || name == null) {
  } else if (elem.length > 0) {
    if (jelem.match(/\n/)) {
      jelem = "<" + name + jattr + ">\n" + jelem + "</" + name + ">\n";
    } else {
      jelem = "<" + name + jattr + ">" + jelem + "</" + name + ">\n";
    }
  } else {
    jelem = "<" + name + jattr + " />\n";
  }
  return jelem;
};
XML2.ObjTree.prototype.array_to_xml = function(name, array) {
  var out = [];
  for (var i2 = 0; i2 < array.length; i2++) {
    var val = array[i2];
    if (typeof val === "undefined" || val == null) {
      out[out.length] = "<" + name + " />";
    } else if (typeof val === "object" && val.constructor === Array) {
      out[out.length] = this.array_to_xml(name, val);
    } else if (typeof val === "object") {
      out[out.length] = this.hash_to_xml(name, val);
    } else {
      out[out.length] = this.scalar_to_xml(name, val);
    }
  }
  return out.join("");
};
XML2.ObjTree.prototype.scalar_to_xml = function(name, text) {
  if (name === "#text") {
    return this.xml_escape(text);
  }
  return "<" + name + ">" + this.xml_escape(text) + "</" + name + ">\n";
};
XML2.ObjTree.prototype.xml_escape = function(text) {
  return text.replace(/&/g, "&").replace(/</g, "<").replace(/>/g, ">").replace(/"/g, '"');
};
var lfXml2Json2 = function(xmlData) {
  return new XML2.ObjTree().parseXML(xmlData);
};

// node_modules/.pnpm/@logicflow+extension@1.2.28/node_modules/@logicflow/extension/es/bpmn-elements-adapter/json2xml.js
function type2(obj) {
  return Object.prototype.toString.call(obj);
}
function addSpace2(depth) {
  return "  ".repeat(depth);
}
function handleAttributes2(obj) {
  if (type2(obj) === "[object Object]") {
    return Object.keys(obj).reduce(function(tmp, key) {
      var tmpKey = key;
      if (key.charAt(0) === "-") {
        tmpKey = key.substring(1);
      }
      tmp[tmpKey] = handleAttributes2(obj[key]);
      return tmp;
    }, {});
  }
  if (Array.isArray(obj)) {
    return obj.map(function(item) {
      return handleAttributes2(item);
    });
  }
  return obj;
}
function getAttributes2(obj) {
  var tmp = obj;
  try {
    if (typeof tmp !== "string") {
      tmp = JSON.parse(obj);
    }
  } catch (error) {
    tmp = JSON.stringify(handleAttributes2(obj)).replace(/"/g, "'");
  }
  return tmp;
}
var tn2 = "	\n";
function toXml2(obj, name, depth) {
  var frontSpace = addSpace2(depth);
  var str = "";
  var prefix = tn2 + frontSpace;
  if (name === "-json")
    return "";
  if (name === "#text") {
    return prefix + obj;
  }
  if (name === "#cdata-section") {
    return prefix + "<![CDATA[" + obj + "]]>";
  }
  if (name === "#comment") {
    return prefix + "<!--" + obj + "-->";
  }
  if (("" + name).charAt(0) === "-") {
    return " " + name.substring(1) + '="' + getAttributes2(obj) + '"';
  }
  if (Array.isArray(obj)) {
    str += obj.map(function(item) {
      return toXml2(item, name, depth + 1);
    }).join("");
  } else if (type2(obj) === "[object Object]") {
    var keys = Object.keys(obj);
    var attributes_1 = "";
    var children_1 = obj["-json"] ? tn2 + addSpace2(depth + 1) + obj["-json"] : "";
    str += (depth === 0 ? "" : prefix) + "<" + name;
    keys.forEach(function(k2) {
      k2.charAt(0) === "-" ? attributes_1 += toXml2(obj[k2], k2, depth + 1) : children_1 += toXml2(obj[k2], k2, depth + 1);
    });
    str += attributes_1 + (children_1 !== "" ? ">" + children_1 + prefix + "</" + name + ">" : " />");
  } else {
    str += prefix + "<" + name + ">" + obj.toString() + "</" + name + ">";
  }
  return str;
}
function lfJson2Xml2(obj) {
  var xmlStr = "";
  for (var key in obj) {
    xmlStr += toXml2(obj[key], key, 0);
  }
  return xmlStr;
}

// node_modules/.pnpm/@logicflow+extension@1.2.28/node_modules/@logicflow/extension/es/bpmn-elements-adapter/index.js
var __extends24 = function() {
  var extendStatics = function(d2, b) {
    extendStatics = Object.setPrototypeOf || { __proto__: [] } instanceof Array && function(d3, b2) {
      d3.__proto__ = b2;
    } || function(d3, b2) {
      for (var p2 in b2)
        if (b2.hasOwnProperty(p2))
          d3[p2] = b2[p2];
    };
    return extendStatics(d2, b);
  };
  return function(d2, b) {
    extendStatics(d2, b);
    function __() {
      this.constructor = d2;
    }
    d2.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
  };
}();
var __assign22 = function() {
  __assign22 = Object.assign || function(t2) {
    for (var s2, i2 = 1, n2 = arguments.length; i2 < n2; i2++) {
      s2 = arguments[i2];
      for (var p2 in s2)
        if (Object.prototype.hasOwnProperty.call(s2, p2))
          t2[p2] = s2[p2];
    }
    return t2;
  };
  return __assign22.apply(this, arguments);
};
var __rest2 = function(s2, e2) {
  var t2 = {};
  for (var p2 in s2)
    if (Object.prototype.hasOwnProperty.call(s2, p2) && e2.indexOf(p2) < 0)
      t2[p2] = s2[p2];
  if (s2 != null && typeof Object.getOwnPropertySymbols === "function")
    for (var i2 = 0, p2 = Object.getOwnPropertySymbols(s2); i2 < p2.length; i2++) {
      if (e2.indexOf(p2[i2]) < 0 && Object.prototype.propertyIsEnumerable.call(s2, p2[i2]))
        t2[p2[i2]] = s2[p2[i2]];
    }
  return t2;
};
var __values2 = function(o2) {
  var s2 = typeof Symbol === "function" && Symbol.iterator, m2 = s2 && o2[s2], i2 = 0;
  if (m2)
    return m2.call(o2);
  if (o2 && typeof o2.length === "number")
    return {
      next: function() {
        if (o2 && i2 >= o2.length)
          o2 = void 0;
        return { value: o2 && o2[i2++], done: !o2 };
      }
    };
  throw new TypeError(s2 ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
var __read12 = function(o2, n2) {
  var m2 = typeof Symbol === "function" && o2[Symbol.iterator];
  if (!m2)
    return o2;
  var i2 = m2.call(o2), r2, ar = [], e2;
  try {
    while ((n2 === void 0 || n2-- > 0) && !(r2 = i2.next()).done)
      ar.push(r2.value);
  } catch (error) {
    e2 = { error };
  } finally {
    try {
      if (r2 && !r2.done && (m2 = i2["return"]))
        m2.call(i2);
    } finally {
      if (e2)
        throw e2.error;
    }
  }
  return ar;
};
var __spread9 = function() {
  for (var ar = [], i2 = 0; i2 < arguments.length; i2++)
    ar = ar.concat(__read12(arguments[i2]));
  return ar;
};
var BpmnElements2;
(function(BpmnElements3) {
  BpmnElements3["START"] = "bpmn:startEvent";
  BpmnElements3["END"] = "bpmn:endEvent";
  BpmnElements3["INTERMEDIATE_CATCH"] = "bpmn:intermediateCatchEvent";
  BpmnElements3["INTERMEDIATE_THROW"] = "bpmn:intermediateThrowEvent";
  BpmnElements3["BOUNDARY"] = "bpmn:boundaryEvent";
  BpmnElements3["PARALLEL_GATEWAY"] = "bpmn:parallelGateway";
  BpmnElements3["INCLUSIVE_GATEWAY"] = "bpmn:inclusiveGateway";
  BpmnElements3["EXCLUSIVE_GATEWAY"] = "bpmn:exclusiveGateway";
  BpmnElements3["USER"] = "bpmn:userTask";
  BpmnElements3["SYSTEM"] = "bpmn:serviceTask";
  BpmnElements3["FLOW"] = "bpmn:sequenceFlow";
  BpmnElements3["SUBPROCESS"] = "bpmn:subProcess";
})(BpmnElements2 || (BpmnElements2 = {}));
var defaultAttrsForInput = [
  "-name",
  "-id",
  "bpmn:incoming",
  "bpmn:outgoing",
  "-sourceRef",
  "-targetRef",
  "-children"
];
var defaultRetainedProperties = [
  "properties",
  "startPoint",
  "endPoint",
  "pointsList"
];
var defaultExcludeFields = {
  in: [],
  out: [
    "properties.panels",
    "properties.nodeSize",
    "properties.definitionId",
    "properties.timerValue",
    "properties.timerType",
    "properties.definitionType",
    "properties.parent",
    "properties.isBoundaryEventTouchingTask"
  ]
};
var mergeInNOutObject = function(target, source) {
  var sourceKeys = Object.keys(source || {});
  sourceKeys.forEach(function(key) {
    if (target[key]) {
      var _a = source[key], fnIn = _a.in, fnOut = _a.out;
      if (fnIn) {
        target[key].in = fnIn;
      }
      if (fnOut) {
        target[key].out = fnOut;
      }
    } else {
      target[key] = source[key];
    }
  });
  return target;
};
var defaultTransformer = {
  "bpmn:startEvent": {
    out: function(data) {
      var _a, _b;
      var properties = data.properties;
      return ((_b = (_a = defaultTransformer[properties.definitionType]) === null || _a === void 0 ? void 0 : _a.out) === null || _b === void 0 ? void 0 : _b.call(_a, data)) || {};
    }
  },
  // 'bpmn:endEvent': undefined,
  "bpmn:intermediateCatchEvent": {
    out: function(data) {
      var _a, _b;
      var properties = data.properties;
      return ((_b = (_a = defaultTransformer[properties.definitionType]) === null || _a === void 0 ? void 0 : _a.out) === null || _b === void 0 ? void 0 : _b.call(_a, data)) || {};
    }
  },
  "bpmn:intermediateThrowEvent": {
    out: function(data) {
      var _a, _b;
      var properties = data.properties;
      return ((_b = (_a = defaultTransformer[properties.definitionType]) === null || _a === void 0 ? void 0 : _a.out) === null || _b === void 0 ? void 0 : _b.call(_a, data)) || {};
    }
  },
  "bpmn:boundaryEvent": {
    out: function(data) {
      var _a, _b;
      var properties = data.properties;
      return ((_b = (_a = defaultTransformer[properties.definitionType]) === null || _a === void 0 ? void 0 : _a.out) === null || _b === void 0 ? void 0 : _b.call(_a, data)) || {};
    }
  },
  // 'bpmn:userTask': undefined,
  "bpmn:sequenceFlow": {
    out: function(data) {
      var _a = data.properties, expressionType = _a.expressionType, condition = _a.condition;
      if (condition) {
        if (expressionType === "cdata") {
          return {
            json: '<bpmn:conditionExpression xsi:type="bpmn2:tFormalExpression"><![CDATA[${' + condition + "}]]></bpmn:conditionExpression>"
          };
        }
        return {
          json: '<bpmn:conditionExpression xsi:type="bpmn2:tFormalExpression">' + condition + "</bpmn:conditionExpression>"
        };
      }
      return {
        json: ""
      };
    }
  },
  // 'bpmn:subProcess': undefined,
  // 'bpmn:participant': undefined,
  "bpmn:timerEventDefinition": {
    out: function(data) {
      var _a = data.properties, timerType = _a.timerType, timerValue = _a.timerValue, definitionId = _a.definitionId;
      var typeFunc = function() {
        return "<bpmn:" + timerType + ' xsi:type="bpmn:tFormalExpression">' + timerValue + "</bpmn:" + timerType + ">";
      };
      return {
        json: '<bpmn:timerEventDefinition id="' + definitionId + '"' + (timerType && timerValue ? ">" + typeFunc() + "</bpmn:timerEventDefinition>" : "/>")
      };
    },
    in: function(key, data) {
      var e_1, _a, _b;
      var _c;
      var definitionType = key;
      var definitionId = data["-id"];
      var timerType = "";
      var timerValue = "";
      try {
        for (var _d = __values2(Object.keys(data)), _e = _d.next(); !_e.done; _e = _d.next()) {
          var key_1 = _e.value;
          if (key_1.includes("bpmn:")) {
            _b = __read12(key_1.split(":"), 2), timerType = _b[1];
            timerValue = (_c = data[key_1]) === null || _c === void 0 ? void 0 : _c["#text"];
          }
        }
      } catch (e_1_1) {
        e_1 = { error: e_1_1 };
      } finally {
        try {
          if (_e && !_e.done && (_a = _d.return))
            _a.call(_d);
        } finally {
          if (e_1)
            throw e_1.error;
        }
      }
      return {
        "-definitionId": definitionId,
        "-definitionType": definitionType,
        "-timerType": timerType,
        "-timerValue": timerValue
      };
    }
  },
  "bpmn:conditionExpression": {
    in: function(_key, data) {
      var _a;
      var condition = "";
      var expressionType = "";
      if (data["#cdata-section"]) {
        expressionType = "cdata";
        condition = ((_a = /^\$\{(.*)\}$/g.exec(data["#cdata-section"])) === null || _a === void 0 ? void 0 : _a[1]) || "";
      } else if (data["#text"]) {
        expressionType = "normal";
        condition = data["#text"];
      }
      return {
        "-condition": condition,
        "-expressionType": expressionType
      };
    }
  }
};
function convertNormalToXml(other) {
  var _a = other !== null && other !== void 0 ? other : {}, retainedAttrsFields = _a.retainedAttrsFields, excludeFields = _a.excludeFields, transformer = _a.transformer;
  var retainedAttrsSet = new Set(__spread9(defaultRetainedProperties, retainedAttrsFields || []));
  var excludeFieldsSet = {
    in: new Set(__spread9(defaultExcludeFields.in, (excludeFields === null || excludeFields === void 0 ? void 0 : excludeFields.in) || [])),
    out: new Set(__spread9(defaultExcludeFields.out, (excludeFields === null || excludeFields === void 0 ? void 0 : excludeFields.out) || []))
  };
  defaultTransformer = mergeInNOutObject(defaultTransformer, transformer);
  return function(object) {
    var nodes = object.nodes;
    var edges = object.edges;
    function ToXmlJson(obj, path) {
      var e_2, _a2;
      var _b;
      if ((obj === null || obj === void 0 ? void 0 : obj.flag) === 1) {
        return;
      }
      var fn;
      if ((fn = defaultTransformer[obj.type]) && fn.out) {
        var output_1 = fn.out(obj);
        var keys = Object.keys(output_1);
        if (keys.length > 0) {
          keys.forEach(function(key2) {
            obj[key2] = output_1[key2];
          });
        }
      }
      if (obj === null || obj === void 0 ? void 0 : obj.children) {
        obj.children = obj.children.map(function(key2) {
          var target = nodes.find(function(item) {
            return item.id === key2;
          }) || edges.find(function(item) {
            return item.id === key2;
          });
          return target || {};
        });
      }
      var xmlJson = {};
      if (typeof obj === "string") {
        return obj;
      }
      if (Array.isArray(obj)) {
        return obj.map(function(item) {
          return ToXmlJson(item, "");
        }).filter(function(item) {
          return item != void 0;
        });
      }
      try {
        for (var _c = __values2(Object.entries(obj)), _d = _c.next(); !_d.done; _d = _c.next()) {
          var _e = __read12(_d.value, 2), key = _e[0], value = _e[1];
          if (((_b = value) === null || _b === void 0 ? void 0 : _b["flag"]) === 1) {
            return;
          }
          var newPath = [path, key].filter(function(item) {
            return item;
          }).join(".");
          if (excludeFieldsSet.out.has(newPath)) {
            continue;
          } else if (typeof value !== "object") {
            if (key.indexOf("-") === 0 || ["#text", "#cdata-section", "#comment"].includes(key)) {
              xmlJson[key] = value;
            } else {
              xmlJson["-" + key] = value;
            }
          } else if (retainedAttrsSet.has(newPath)) {
            xmlJson["-" + key] = ToXmlJson(value, newPath);
          } else {
            xmlJson[key] = ToXmlJson(value, newPath);
          }
        }
      } catch (e_2_1) {
        e_2 = { error: e_2_1 };
      } finally {
        try {
          if (_d && !_d.done && (_a2 = _c.return))
            _a2.call(_c);
        } finally {
          if (e_2)
            throw e_2.error;
        }
      }
      return xmlJson;
    }
    return ToXmlJson(object, "");
  };
}
function convertXmlToNormal(xmlJson) {
  var e_3, _a;
  var json = {};
  try {
    for (var _b = __values2(Object.entries(xmlJson)), _c = _b.next(); !_c.done; _c = _b.next()) {
      var _d = __read12(_c.value, 2), key = _d[0], value = _d[1];
      if (key.indexOf("-") === 0) {
        json[key.substring(1)] = handleAttributes2(value);
      } else if (typeof value === "string") {
        json[key] = value;
      } else if (Object.prototype.toString.call(value) === "[object Object]") {
        json[key] = convertXmlToNormal(value);
      } else if (Array.isArray(value)) {
        json[key] = value.map(function(v2) {
          return convertXmlToNormal(v2);
        });
      } else {
        json[key] = value;
      }
    }
  } catch (e_3_1) {
    e_3 = { error: e_3_1 };
  } finally {
    try {
      if (_c && !_c.done && (_a = _b.return))
        _a.call(_b);
    } finally {
      if (e_3)
        throw e_3.error;
    }
  }
  return json;
}
function convertLf2ProcessData2(bpmnData, data, other) {
  var _a;
  var nodeIdMap = /* @__PURE__ */ new Map();
  var xmlJsonData = convertNormalToXml(other)(data);
  xmlJsonData.nodes.forEach(function(node) {
    var nodeId = node["-id"], nodeType = node["-type"], text = node.text, children = node.children, otherProps = __rest2(node, ["-id", "-type", "text", "children"]);
    var processNode = { "-id": nodeId };
    if (text === null || text === void 0 ? void 0 : text["-value"]) {
      processNode["-name"] = text["-value"];
    }
    if (otherProps["-json"]) {
      processNode["-json"] = otherProps["-json"];
    }
    if (otherProps["-properties"]) {
      Object.assign(processNode, otherProps["-properties"]);
    }
    if (children) {
      processNode.children = children;
    }
    if (!bpmnData[nodeType]) {
      bpmnData[nodeType] = [];
    }
    bpmnData[nodeType].push(processNode);
    nodeIdMap.set(nodeId, processNode);
  });
  var sequenceFlow = xmlJsonData.edges.map(function(edge) {
    var id = edge["-id"], type3 = edge["-type"], sourceNodeId = edge["-sourceNodeId"], targetNodeId = edge["-targetNodeId"], text = edge.text, otherProps = __rest2(edge, ["-id", "-type", "-sourceNodeId", "-targetNodeId", "text"]);
    var targetNode = nodeIdMap.get(targetNodeId);
    if (!targetNode["bpmn:incoming"]) {
      targetNode["bpmn:incoming"] = [];
    }
    targetNode["bpmn:incoming"].push(id);
    var edgeConfig = {
      "-id": id,
      "-sourceRef": sourceNodeId,
      "-targetRef": targetNodeId
    };
    if (text === null || text === void 0 ? void 0 : text["-value"]) {
      edgeConfig["-name"] = text["-value"];
    }
    if (otherProps["-json"]) {
      edgeConfig["-json"] = otherProps["-json"];
    }
    if (otherProps["-properties"]) {
      Object.assign(edgeConfig, otherProps["-properties"]);
    }
    return edgeConfig;
  });
  data.edges.forEach(function(_a2) {
    var sourceNodeId = _a2.sourceNodeId, id = _a2.id;
    var sourceNode = nodeIdMap.get(sourceNodeId);
    if (!sourceNode["bpmn:outgoing"]) {
      sourceNode["bpmn:outgoing"] = [];
    }
    sourceNode["bpmn:outgoing"].push(id);
  });
  (_a = bpmnData["bpmn:subProcess"]) === null || _a === void 0 ? void 0 : _a.forEach(function(item) {
    var setMap = {
      "bpmn:incoming": /* @__PURE__ */ new Set(),
      "bpmn:outgoing": /* @__PURE__ */ new Set()
    };
    var edgesInSubProcess = [];
    item.children.forEach(function(child) {
      var _a2;
      var target = nodeIdMap.get(child["-id"]);
      ["bpmn:incoming", "bpmn:outgoing"].forEach(function(key) {
        target[key] && target[key].forEach(function(value) {
          setMap[key].add(value);
        });
      });
      var index = (_a2 = bpmnData[child["-type"]]) === null || _a2 === void 0 ? void 0 : _a2.findIndex(function(_item) {
        return _item["-id"] === child["-id"];
      });
      if (index >= 0) {
        bpmnData[child["-type"]].splice(index, 1);
      }
      nodeIdMap.delete(child["-id"]);
      if (!item[child["-type"]]) {
        item[child["-type"]] = [];
      }
      item[child["-type"]].push(target);
    });
    var incomingSet = setMap["bpmn:incoming"], outgoingSet = setMap["bpmn:outgoing"];
    outgoingSet.forEach(function(value) {
      incomingSet.has(value) && edgesInSubProcess.push(value);
    });
    var _loop_1 = function(i3) {
      var index = sequenceFlow.findIndex(function(item2) {
        return item2["-id"] === edgesInSubProcess[i3];
      });
      if (index >= 0) {
        if (!item["bpmn:sequenceFlow"]) {
          item["bpmn:sequenceFlow"] = [];
        }
        item["bpmn:sequenceFlow"].push(sequenceFlow[index]);
        sequenceFlow.splice(index, 1);
      } else {
        i3++;
      }
      out_i_1 = i3;
    };
    var out_i_1;
    for (var i2 = 0; i2 < edgesInSubProcess.length; ) {
      _loop_1(i2);
      i2 = out_i_1;
    }
    delete item.children;
  });
  bpmnData[BpmnElements2.FLOW] = sequenceFlow;
  return bpmnData;
}
function convertLf2DiagramData2(bpmnDiagramData, data) {
  bpmnDiagramData["bpmndi:BPMNEdge"] = data.edges.map(function(edge) {
    var _a;
    var edgeId = edge.id;
    var pointsList = edge.pointsList.map(function(_a2) {
      var x2 = _a2.x, y2 = _a2.y;
      return {
        "-x": x2,
        "-y": y2
      };
    });
    var diagramData = {
      "-id": edgeId + "_di",
      "-bpmnElement": edgeId,
      "di:waypoint": pointsList
    };
    if ((_a = edge.text) === null || _a === void 0 ? void 0 : _a.value) {
      diagramData["bpmndi:BPMNLabel"] = {
        "dc:Bounds": {
          "-x": edge.text.x - edge.text.value.length * 10 / 2,
          "-y": edge.text.y - 7,
          "-width": edge.text.value.length * 10,
          "-height": 14
        }
      };
    }
    return diagramData;
  });
  bpmnDiagramData["bpmndi:BPMNShape"] = data.nodes.map(function(node) {
    var _a;
    var nodeId = node.id;
    var width = 100;
    var height = 80;
    var x2 = node.x, y2 = node.y;
    var shapeConfig = BPMNBaseAdapter.shapeConfigMap.get(node.type);
    if (shapeConfig) {
      width = shapeConfig.width;
      height = shapeConfig.height;
    }
    x2 -= width / 2;
    y2 -= height / 2;
    var diagramData = {
      "-id": nodeId + "_di",
      "-bpmnElement": nodeId,
      "dc:Bounds": {
        "-x": x2,
        "-y": y2,
        "-width": width,
        "-height": height
      }
    };
    if ((_a = node.text) === null || _a === void 0 ? void 0 : _a.value) {
      diagramData["bpmndi:BPMNLabel"] = {
        "dc:Bounds": {
          "-x": node.text.x - node.text.value.length * 10 / 2,
          "-y": node.text.y - 7,
          "-width": node.text.value.length * 10,
          "-height": 14
        }
      };
    }
    return diagramData;
  });
}
var ignoreType = ["bpmn:incoming", "bpmn:outgoing"];
function convertBpmn2LfData2(bpmnData, other) {
  var nodes = [];
  var edges = [];
  var eleMap = /* @__PURE__ */ new Map();
  var _a = other !== null && other !== void 0 ? other : {}, transformer = _a.transformer, excludeFields = _a.excludeFields;
  var excludeFieldsSet = {
    in: new Set(__spread9(defaultExcludeFields.in, (excludeFields === null || excludeFields === void 0 ? void 0 : excludeFields.in) || [])),
    out: new Set(__spread9(defaultExcludeFields.out, (excludeFields === null || excludeFields === void 0 ? void 0 : excludeFields.out) || []))
  };
  defaultTransformer = mergeInNOutObject(defaultTransformer, transformer);
  var definitions = bpmnData["bpmn:definitions"];
  if (definitions) {
    var process_1 = definitions["bpmn:process"];
    (function(data, callbacks) {
      callbacks.forEach(function(callback) {
        try {
          Object.keys(data).forEach(function(key) {
            try {
              callback(key);
            } catch (error) {
              console.error(error);
            }
          });
        } catch (error) {
          console.error(error);
        }
      });
    })(process_1, [
      function(key) {
        function subProcessProcessing(data2) {
          if (!data2["-children"]) {
            data2["-children"] = [];
          }
          Object.keys(data2).forEach(function(key2) {
            var _a2;
            if (key2.indexOf("bpmn:") === 0 && !ignoreType.includes(key2)) {
              if (!process_1[key2]) {
                process_1[key2] = [];
              }
              !Array.isArray(process_1[key2]) && (process_1[key2] = [process_1[key2]]);
              Array.isArray(data2[key2]) ? (_a2 = process_1[key2]).push.apply(_a2, __spread9(data2[key2])) : process_1[key2].push(data2[key2]);
              if (Array.isArray(data2[key2])) {
                data2[key2].forEach(function(item) {
                  !key2.includes("Flow") && data2["-children"].push(item["-id"]);
                });
              } else {
                !key2.includes("Flow") && data2["-children"].push(data2[key2]["-id"]);
              }
              delete data2[key2];
            }
          });
        }
        if (key === "bpmn:subProcess") {
          var data = process_1[key];
          if (Array.isArray(data)) {
            data.forEach(function(item) {
              key === "bpmn:subProcess" && subProcessProcessing(item);
            });
          } else {
            subProcessProcessing(data);
          }
        }
      },
      function(key) {
        var fn = function(obj) {
          Object.keys(obj).forEach(function(key2) {
            var _a2, _b;
            if (key2.includes("bpmn:")) {
              var props_1 = {};
              if (defaultTransformer[key2] && defaultTransformer[key2].in) {
                props_1 = (_b = (_a2 = defaultTransformer[key2]).in) === null || _b === void 0 ? void 0 : _b.call(_a2, key2, lodash_default_default.cloneDeep(obj[key2]));
                delete obj[key2];
              } else {
                func(obj[key2]);
              }
              var keys = void 0;
              if ((keys = Reflect.ownKeys(props_1)).length > 0) {
                keys.forEach(function(key3) {
                  Reflect.set(obj, key3, props_1[key3]);
                });
              }
            }
          });
        };
        function func(data) {
          eleMap.set(data["-id"], data);
          if (Array.isArray(data)) {
            data.forEach(function(item) {
              func(item);
            });
          } else if (typeof data === "object") {
            fn(data);
          }
        }
        func(process_1[key]);
      },
      function(key) {
        if (key.indexOf("bpmn:") === 0) {
          var value = process_1[key];
          if (key === "bpmn:sequenceFlow") {
            var bpmnEdges = definitions["bpmndi:BPMNDiagram"]["bpmndi:BPMNPlane"]["bpmndi:BPMNEdge"];
            edges = getLfEdges2(value, bpmnEdges);
          } else {
            var shapes = definitions["bpmndi:BPMNDiagram"]["bpmndi:BPMNPlane"]["bpmndi:BPMNShape"];
            if (key === "bpmn:boundaryEvent") {
              var data = process_1[key];
              var fn_1 = function(item) {
                var attachedToRef = item["-attachedToRef"];
                var attachedToNode = eleMap.get(attachedToRef);
                if (!attachedToNode["-boundaryEvents"]) {
                  attachedToNode["-boundaryEvents"] = [];
                }
                attachedToNode["-boundaryEvents"].push(item["-id"]);
              };
              if (Array.isArray(data)) {
                data.forEach(function(item) {
                  fn_1(item);
                });
              } else {
                fn_1(data);
              }
            }
            nodes = nodes.concat(getLfNodes2(value, shapes, key));
          }
        }
      }
    ]);
  }
  var ignoreFields = function(obj, filterSet, path) {
    Object.keys(obj).forEach(function(key) {
      var tmpPath = path ? path + "." + key : key;
      if (filterSet.has(tmpPath)) {
        delete obj[key];
      } else if (typeof obj[key] === "object") {
        ignoreFields(obj[key], filterSet, tmpPath);
      }
    });
  };
  nodes.forEach(function(node) {
    var _a2, _b;
    if ((_a2 = other === null || other === void 0 ? void 0 : other.mapping) === null || _a2 === void 0 ? void 0 : _a2.in) {
      var mapping = (_b = other === null || other === void 0 ? void 0 : other.mapping) === null || _b === void 0 ? void 0 : _b.in;
      var type3 = node.type;
      if (mapping[type3]) {
        node.type = mapping[type3];
      }
    }
    ignoreFields(node, excludeFieldsSet.in, "");
  });
  edges.forEach(function(edge) {
    var _a2, _b;
    if ((_a2 = other === null || other === void 0 ? void 0 : other.mapping) === null || _a2 === void 0 ? void 0 : _a2.in) {
      var mapping = (_b = other === null || other === void 0 ? void 0 : other.mapping) === null || _b === void 0 ? void 0 : _b.in;
      var type3 = edge.type;
      if (mapping[type3]) {
        edge.type = mapping[type3];
      }
    }
    ignoreFields(edge, excludeFieldsSet.in, "");
  });
  return {
    nodes,
    edges
  };
}
function getLfNodes2(value, shapes, key) {
  var nodes = [];
  if (Array.isArray(value)) {
    value.forEach(function(val) {
      var shapeValue2;
      if (Array.isArray(shapes)) {
        shapeValue2 = shapes.find(function(shape) {
          return shape["-bpmnElement"] === val["-id"];
        });
      } else {
        shapeValue2 = shapes;
      }
      var node2 = getNodeConfig2(shapeValue2, key, val);
      nodes.push(node2);
    });
  } else {
    var shapeValue = void 0;
    if (Array.isArray(shapes)) {
      shapeValue = shapes.find(function(shape) {
        return shape["-bpmnElement"] === value["-id"];
      });
    } else {
      shapeValue = shapes;
    }
    var node = getNodeConfig2(shapeValue, key, value);
    nodes.push(node);
  }
  return nodes;
}
function getNodeConfig2(shapeValue, type3, processValue) {
  var x2 = Number(shapeValue["dc:Bounds"]["-x"]);
  var y2 = Number(shapeValue["dc:Bounds"]["-y"]);
  var children = processValue["-children"];
  var name = processValue["-name"];
  var shapeConfig = BPMNBaseAdapter.shapeConfigMap.get(type3);
  if (shapeConfig) {
    x2 += shapeConfig.width / 2;
    y2 += shapeConfig.height / 2;
  }
  var properties = {};
  Object.entries(processValue).forEach(function(_a) {
    var _b = __read12(_a, 2), key = _b[0], value = _b[1];
    if (!defaultAttrsForInput.includes(key)) {
      properties[key] = value;
    }
  });
  properties = convertXmlToNormal(properties);
  var text;
  if (name) {
    text = {
      x: x2,
      y: y2,
      value: name
    };
    if (shapeValue["bpmndi:BPMNLabel"] && shapeValue["bpmndi:BPMNLabel"]["dc:Bounds"]) {
      var textBounds = shapeValue["bpmndi:BPMNLabel"]["dc:Bounds"];
      text.x = Number(textBounds["-x"]) + Number(textBounds["-width"]) / 2;
      text.y = Number(textBounds["-y"]) + Number(textBounds["-height"]) / 2;
    }
  }
  var nodeConfig = {
    id: shapeValue["-bpmnElement"],
    type: type3,
    x: x2,
    y: y2,
    properties
  };
  children && (nodeConfig.children = children);
  if (text) {
    nodeConfig.text = text;
  }
  return nodeConfig;
}
function getLfEdges2(value, bpmnEdges) {
  var edges = [];
  if (Array.isArray(value)) {
    value.forEach(function(val) {
      var edgeValue2;
      if (Array.isArray(bpmnEdges)) {
        edgeValue2 = bpmnEdges.find(function(edge) {
          return edge["-bpmnElement"] === val["-id"];
        });
      } else {
        edgeValue2 = bpmnEdges;
      }
      edges.push(getEdgeConfig2(edgeValue2, val));
    });
  } else {
    var edgeValue = void 0;
    if (Array.isArray(bpmnEdges)) {
      edgeValue = bpmnEdges.find(function(edge) {
        return edge["-bpmnElement"] === value["-id"];
      });
    } else {
      edgeValue = bpmnEdges;
    }
    edges.push(getEdgeConfig2(edgeValue, value));
  }
  return edges;
}
function getEdgeConfig2(edgeValue, processValue) {
  var text;
  var textVal = processValue["-name"];
  if (textVal) {
    var textBounds = edgeValue["bpmndi:BPMNLabel"]["dc:Bounds"];
    var textLength_1 = 0;
    textVal.split("\n").forEach(function(textSpan) {
      if (textLength_1 < textSpan.length) {
        textLength_1 = textSpan.length;
      }
    });
    text = {
      value: textVal,
      x: Number(textBounds["-x"]) + textLength_1 * 10 / 2,
      y: Number(textBounds["-y"]) + 7
    };
  }
  var properties = {};
  Object.entries(processValue).forEach(function(_a) {
    var _b = __read12(_a, 2), key = _b[0], value = _b[1];
    if (!defaultAttrsForInput.includes(key)) {
      properties[key] = value;
    }
  });
  properties = convertXmlToNormal(properties);
  var pointsList = edgeValue["di:waypoint"].map(function(point) {
    return {
      x: Number(point["-x"]),
      y: Number(point["-y"])
    };
  });
  var edge = {
    id: processValue["-id"],
    type: BpmnElements2.FLOW,
    pointsList,
    sourceNodeId: processValue["-sourceRef"],
    targetNodeId: processValue["-targetRef"],
    properties
  };
  if (text) {
    edge.text = text;
  }
  return edge;
}
var BPMNBaseAdapter = (
  /** @class */
  function() {
    function BPMNBaseAdapter2(_a) {
      var _this = this;
      var lf = _a.lf;
      this.adapterOut = function(data, other) {
        var _a2, _b;
        var bpmnProcessData = __assign22({}, _this.processAttributes);
        convertLf2ProcessData2(bpmnProcessData, data, other);
        var bpmnDiagramData = {
          "-id": "BPMNPlane_1",
          "-bpmnElement": bpmnProcessData["-id"]
        };
        convertLf2DiagramData2(bpmnDiagramData, data);
        var definitions = _this.definitionAttributes;
        definitions["bpmn:process"] = bpmnProcessData;
        definitions["bpmndi:BPMNDiagram"] = {
          "-id": "BPMNDiagram_1",
          "bpmndi:BPMNPlane": bpmnDiagramData
        };
        var bpmnData = {
          "bpmn:definitions": definitions
        };
        if ((_a2 = other === null || other === void 0 ? void 0 : other.mapping) === null || _a2 === void 0 ? void 0 : _a2.out) {
          var mapping_1 = (_b = other === null || other === void 0 ? void 0 : other.mapping) === null || _b === void 0 ? void 0 : _b.out;
          var nameMapping_1 = function(obj) {
            if (Array.isArray(obj)) {
              obj.forEach(function(item) {
                return nameMapping_1(item);
              });
            }
            if (typeof obj === "object") {
              Object.keys(obj).forEach(function(key) {
                var mappingName;
                if (mappingName = mapping_1[key]) {
                  obj[mappingName] = lodash_default_default.cloneDeep(obj[key]);
                  delete obj[key];
                  nameMapping_1(obj[mappingName]);
                } else {
                  nameMapping_1(obj[key]);
                }
              });
            }
          };
          nameMapping_1(bpmnData);
        }
        return bpmnData;
      };
      this.adapterIn = function(bpmnData, other) {
        if (bpmnData) {
          return convertBpmn2LfData2(bpmnData, other);
        }
      };
      lf.adapterIn = this.adapterIn;
      lf.adapterOut = this.adapterOut;
      this.processAttributes = {
        "-isExecutable": "true",
        "-id": "Process"
      };
      this.definitionAttributes = {
        "-id": "Definitions",
        "-xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance",
        "-xmlns:bpmn": "http://www.omg.org/spec/BPMN/20100524/MODEL",
        "-xmlns:bpmndi": "http://www.omg.org/spec/BPMN/20100524/DI",
        "-xmlns:dc": "http://www.omg.org/spec/DD/20100524/DC",
        "-xmlns:di": "http://www.omg.org/spec/DD/20100524/DI",
        "-targetNamespace": "http://logic-flow.org",
        "-exporter": "logicflow",
        "-exporterVersion": "1.2.10"
      };
    }
    BPMNBaseAdapter2.prototype.setCustomShape = function(key, val) {
      BPMNBaseAdapter2.shapeConfigMap.set(key, val);
    };
    BPMNBaseAdapter2.pluginName = "bpmn-adapter";
    BPMNBaseAdapter2.shapeConfigMap = /* @__PURE__ */ new Map();
    return BPMNBaseAdapter2;
  }()
);
BPMNBaseAdapter.shapeConfigMap.set(BpmnElements2.START, {
  width: StartEventConfig2.width,
  height: StartEventConfig2.height
});
BPMNBaseAdapter.shapeConfigMap.set(BpmnElements2.END, {
  width: EndEventConfig2.width,
  height: EndEventConfig2.height
});
BPMNBaseAdapter.shapeConfigMap.set(BpmnElements2.INTERMEDIATE_CATCH, {
  width: IntermediateEventConfig.width,
  height: IntermediateEventConfig.height
});
BPMNBaseAdapter.shapeConfigMap.set(BpmnElements2.INTERMEDIATE_THROW, {
  width: IntermediateEventConfig.width,
  height: IntermediateEventConfig.height
});
BPMNBaseAdapter.shapeConfigMap.set(BpmnElements2.BOUNDARY, {
  width: BoundaryEventConfig.width,
  height: BoundaryEventConfig.height
});
BPMNBaseAdapter.shapeConfigMap.set(BpmnElements2.PARALLEL_GATEWAY, {
  width: ParallelGatewayConfig.width,
  height: ParallelGatewayConfig.height
});
BPMNBaseAdapter.shapeConfigMap.set(BpmnElements2.INCLUSIVE_GATEWAY, {
  width: InclusiveGatewayConfig.width,
  height: InclusiveGatewayConfig.height
});
BPMNBaseAdapter.shapeConfigMap.set(BpmnElements2.EXCLUSIVE_GATEWAY, {
  width: ExclusiveGatewayConfig2.width,
  height: ExclusiveGatewayConfig2.height
});
BPMNBaseAdapter.shapeConfigMap.set(BpmnElements2.SYSTEM, {
  width: ServiceTaskConfig2.width,
  height: ServiceTaskConfig2.height
});
BPMNBaseAdapter.shapeConfigMap.set(BpmnElements2.USER, {
  width: UserTaskConfig2.width,
  height: UserTaskConfig2.height
});
BPMNBaseAdapter.shapeConfigMap.set(BpmnElements2.SUBPROCESS, {
  width: SubProcessConfig.width,
  height: SubProcessConfig.height
});
var BPMNAdapter = (
  /** @class */
  function(_super) {
    __extends24(BPMNAdapter2, _super);
    function BPMNAdapter2(data) {
      var _this = _super.call(this, data) || this;
      _this.adapterXmlIn = function(bpmnData) {
        var json = lfXml2Json2(bpmnData);
        return _this.adapterIn(json, _this.props);
      };
      _this.adapterXmlOut = function(data2) {
        var outData = _this.adapterOut(data2, _this.props);
        return lfJson2Xml2(outData);
      };
      var lf = data.lf, props = data.props;
      lf.adapterIn = _this.adapterXmlIn;
      lf.adapterOut = _this.adapterXmlOut;
      _this.props = props;
      return _this;
    }
    BPMNAdapter2.pluginName = "BPMNAdapter";
    return BPMNAdapter2;
  }(BPMNBaseAdapter)
);

// node_modules/.pnpm/@logicflow+extension@1.2.28/node_modules/@logicflow/extension/es/tools/snapshot/index.js
var Snapshot = (
  /** @class */
  function() {
    function Snapshot2(_a) {
      var _this = this;
      var lf = _a.lf;
      this.lf = lf;
      this.customCssRules = "";
      this.useGlobalRules = true;
      lf.getSnapshot = function(fileName, backgroundColor) {
        _this.getSnapshot(fileName, backgroundColor);
      };
      lf.getSnapshotBlob = function(backgroundColor) {
        return _this.getSnapshotBlob(backgroundColor);
      };
      lf.getSnapshotBase64 = function(backgroundColor) {
        return _this.getSnapshotBase64(backgroundColor);
      };
    }
    Snapshot2.prototype.getSvgRootElement = function(lf) {
      var svgRootElement = lf.container.querySelector(".lf-canvas-overlay");
      return svgRootElement;
    };
    Snapshot2.prototype.triggerDownload = function(imgURI) {
      var evt = new MouseEvent("click", {
        view: document.defaultView,
        bubbles: false,
        cancelable: true
      });
      var a2 = document.createElement("a");
      a2.setAttribute("download", this.fileName);
      a2.setAttribute("href", imgURI);
      a2.setAttribute("target", "_blank");
      a2.dispatchEvent(evt);
    };
    Snapshot2.prototype.removeAnchor = function(element) {
      var childNodes = element.childNodes;
      var childLength = element.childNodes && element.childNodes.length;
      for (var i2 = 0; i2 < childLength; i2++) {
        var child = childNodes[i2];
        var classList = child.classList && Array.from(child.classList) || [];
        if (classList.indexOf("lf-anchor") > -1) {
          element.removeChild(element.childNodes[i2]);
          childLength--;
          i2--;
        }
      }
    };
    Snapshot2.prototype.removeRotateControl = function(element) {
      var childNodes = element.childNodes;
      var childLength = element.childNodes && element.childNodes.length;
      for (var i2 = 0; i2 < childLength; i2++) {
        var child = childNodes[i2];
        var classList = child.classList && Array.from(child.classList) || [];
        if (classList.indexOf("lf-rotate-control") > -1) {
          element.removeChild(element.childNodes[i2]);
          childLength--;
          i2--;
        }
      }
    };
    Snapshot2.prototype.getSnapshot = function(fileName, backgroundColor) {
      var _this = this;
      this.fileName = fileName || "logic-flow." + Date.now() + ".png";
      var svg = this.getSvgRootElement(this.lf);
      this.getCanvasData(svg, backgroundColor).then(function(canvas) {
        var imgURI = canvas.toDataURL("image/png").replace("image/png", "image/octet-stream");
        _this.triggerDownload(imgURI);
      });
    };
    Snapshot2.prototype.getSnapshotBase64 = function(backgroundColor) {
      var _this = this;
      var svg = this.getSvgRootElement(this.lf);
      return new Promise(function(resolve) {
        _this.getCanvasData(svg, backgroundColor).then(function(canvas) {
          var base64 = canvas.toDataURL("image/png");
          resolve({ data: base64, width: canvas.width, height: canvas.height });
        });
      });
    };
    Snapshot2.prototype.getSnapshotBlob = function(backgroundColor) {
      var _this = this;
      var svg = this.getSvgRootElement(this.lf);
      return new Promise(function(resolve) {
        _this.getCanvasData(svg, backgroundColor).then(function(canvas) {
          canvas.toBlob(function(blob) {
            resolve({ data: blob, width: canvas.width, height: canvas.height });
          }, "image/png");
        });
      });
    };
    Snapshot2.prototype.getClassRules = function() {
      var rules = "";
      if (this.useGlobalRules) {
        var styleSheets = document.styleSheets;
        for (var i2 = 0; i2 < styleSheets.length; i2++) {
          var sheet = styleSheets[i2];
          for (var j2 = 0; j2 < sheet.cssRules.length; j2++) {
            rules += sheet.cssRules[j2].cssText;
          }
        }
      }
      if (this.customCssRules) {
        rules += this.customCssRules;
      }
      return rules;
    };
    Snapshot2.prototype.getCanvasData = function(svg, backgroundColor) {
      var _this = this;
      var copy = svg.cloneNode(true);
      var graph = copy.lastChild;
      var childLength = graph.childNodes && graph.childNodes.length;
      if (childLength) {
        for (var i2 = 0; i2 < childLength; i2++) {
          var lfLayer = graph.childNodes[i2];
          var layerClassList = lfLayer.classList && Array.from(lfLayer.classList);
          if (layerClassList && layerClassList.indexOf("lf-base") < 0) {
            graph.removeChild(graph.childNodes[i2]);
            childLength--;
            i2--;
          } else {
            var lfBase = graph.childNodes[i2];
            lfBase && lfBase.childNodes.forEach(function(item) {
              var element = item;
              _this.removeAnchor(element.firstChild);
              _this.removeRotateControl(element.firstChild);
            });
          }
        }
      }
      var dpr = window.devicePixelRatio || 1;
      if (dpr < 1) {
        dpr = 1;
      }
      var canvas = document.createElement("canvas");
      var base = this.lf.graphModel.rootEl.querySelector(".lf-base");
      var bbox = base.getBoundingClientRect();
      var layout = this.lf.container.querySelector(".lf-canvas-overlay").getBoundingClientRect();
      var offsetX = bbox.x - layout.x;
      var offsetY = bbox.y - layout.y;
      var graphModel = this.lf.graphModel;
      var transformModel = graphModel.transformModel;
      var SCALE_X = transformModel.SCALE_X, SCALE_Y = transformModel.SCALE_Y, TRANSLATE_X = transformModel.TRANSLATE_X, TRANSLATE_Y = transformModel.TRANSLATE_Y;
      copy.lastChild.style.transform = "matrix(1, 0, 0, 1, " + ((-offsetX + TRANSLATE_X) * (1 / SCALE_X) + 10) + ", " + ((-offsetY + TRANSLATE_Y) * (1 / SCALE_Y) + 10) + ")";
      var bboxWidth = Math.ceil(bbox.width / SCALE_X);
      var bboxHeight = Math.ceil(bbox.height / SCALE_Y);
      canvas.style.width = bboxWidth + "px";
      canvas.style.height = bboxHeight + "px";
      canvas.width = bboxWidth * dpr + 80;
      canvas.height = bboxHeight * dpr + 80;
      var ctx = canvas.getContext("2d");
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      ctx.scale(dpr, dpr);
      if (backgroundColor) {
        ctx.fillStyle = backgroundColor;
        ctx.fillRect(0, 0, bboxWidth * dpr + 80, bboxHeight * dpr + 80);
      } else {
        ctx.clearRect(0, 0, bboxWidth, bboxHeight);
      }
      var img = new Image();
      var style2 = document.createElement("style");
      style2.innerHTML = this.getClassRules();
      var foreignObject = document.createElement("foreignObject");
      foreignObject.appendChild(style2);
      copy.appendChild(foreignObject);
      return new Promise(function(resolve) {
        img.onload = function() {
          var isFirefox = navigator.userAgent.indexOf("Firefox") > -1;
          try {
            if (isFirefox) {
              createImageBitmap(img, {
                resizeWidth: canvas.width,
                resizeHeight: canvas.height
              }).then(function(imageBitmap) {
                ctx.drawImage(imageBitmap, 0, 0);
                resolve(canvas);
              });
            } else {
              ctx.drawImage(img, 0, 0);
              resolve(canvas);
            }
          } catch (e2) {
            ctx.drawImage(img, 0, 0);
            resolve(canvas);
          }
        };
        var svg2Img = "data:image/svg+xml;charset=utf-8," + new XMLSerializer().serializeToString(copy);
        var imgSrc = svg2Img.replace(/\n/g, "").replace(/\t/g, "").replace(/#/g, "%23");
        img.src = imgSrc;
      });
    };
    Snapshot2.pluginName = "snapshot";
    return Snapshot2;
  }()
);

// node_modules/.pnpm/@logicflow+extension@1.2.28/node_modules/@logicflow/extension/es/turbo-adapter/index.js
var __assign23 = function() {
  __assign23 = Object.assign || function(t2) {
    for (var s2, i2 = 1, n2 = arguments.length; i2 < n2; i2++) {
      s2 = arguments[i2];
      for (var p2 in s2)
        if (Object.prototype.hasOwnProperty.call(s2, p2))
          t2[p2] = s2[p2];
    }
    return t2;
  };
  return __assign23.apply(this, arguments);
};
var __read13 = function(o2, n2) {
  var m2 = typeof Symbol === "function" && o2[Symbol.iterator];
  if (!m2)
    return o2;
  var i2 = m2.call(o2), r2, ar = [], e2;
  try {
    while ((n2 === void 0 || n2-- > 0) && !(r2 = i2.next()).done)
      ar.push(r2.value);
  } catch (error) {
    e2 = { error };
  } finally {
    try {
      if (r2 && !r2.done && (m2 = i2["return"]))
        m2.call(i2);
    } finally {
      if (e2)
        throw e2.error;
    }
  }
  return ar;
};
var TurboType = {
  SEQUENCE_FLOW: 1,
  START_EVENT: 2,
  END_EVENT: 3,
  USER_TASK: 4,
  // SERVICE_TASK = 5, 暂不支持
  EXCLUSIVE_GATEWAY: 6
};
var TurboTypeMap = {
  1: "bpmn:sequenceFlow",
  2: "bpmn:startEvent",
  3: "bpmn:endEvent",
  4: "bpmn:userTask",
  6: "bpmn:exclusiveGateway"
};
function getTurboType(type3) {
  switch (type3) {
    case "bpmn:sequenceFlow":
      return TurboType.SEQUENCE_FLOW;
    case "bpmn:startEvent":
      return TurboType.START_EVENT;
    case "bpmn:endEvent":
      return TurboType.END_EVENT;
    case "bpmn:userTask":
      return TurboType.USER_TASK;
    case "bpmn:exclusiveGateway":
      return TurboType.EXCLUSIVE_GATEWAY;
    default:
      return type3;
  }
}
function convertNodeToTurboElement(node) {
  var id = node.id, type3 = node.type, x2 = node.x, y2 = node.y, _a = node.text, text = _a === void 0 ? "" : _a, properties = node.properties;
  return {
    incoming: [],
    outgoing: [],
    dockers: [],
    type: getTurboType(node.type),
    properties: __assign23(__assign23({}, properties), {
      name: text && text.value || "",
      x: x2,
      y: y2,
      text
    }),
    key: id
  };
}
function convertEdgeToTurboElement(edge) {
  var id = edge.id, type3 = edge.type, sourceNodeId = edge.sourceNodeId, targetNodeId = edge.targetNodeId, startPoint = edge.startPoint, endPoint = edge.endPoint, pointsList = edge.pointsList, _a = edge.text, text = _a === void 0 ? "" : _a, properties = edge.properties;
  return {
    incoming: [sourceNodeId],
    outgoing: [targetNodeId],
    type: getTurboType(type3),
    dockers: [],
    properties: __assign23(__assign23({}, properties), { name: text && text.value || "", text, startPoint: JSON.stringify(startPoint), endPoint: JSON.stringify(endPoint), pointsList: JSON.stringify(pointsList) }),
    key: id
  };
}
function toTurboData(data) {
  var nodeMap = /* @__PURE__ */ new Map();
  var turboData = {
    flowElementList: []
  };
  data.nodes.forEach(function(node) {
    var flowElement = convertNodeToTurboElement(node);
    turboData.flowElementList.push(flowElement);
    nodeMap.set(node.id, flowElement);
  });
  data.edges.forEach(function(edge) {
    var flowElement = convertEdgeToTurboElement(edge);
    var sourceElement = nodeMap.get(edge.sourceNodeId);
    sourceElement.outgoing.push(flowElement.key);
    var targetElement = nodeMap.get(edge.targetNodeId);
    targetElement.incoming.push(flowElement.key);
    turboData.flowElementList.push(flowElement);
  });
  return turboData;
}
function convertFlowElementToEdge(element) {
  var incoming = element.incoming, outgoing = element.outgoing, properties = element.properties, key = element.key, type3 = element.type;
  var text = properties.text, name = properties.name, startPoint = properties.startPoint, endPoint = properties.endPoint, pointsList = properties.pointsList;
  var edge = {
    id: key,
    type: TurboTypeMap[type3],
    sourceNodeId: incoming[0],
    targetNodeId: outgoing[0],
    text: text || name,
    properties: {}
  };
  if (startPoint) {
    edge.startPoint = JSON.parse(startPoint);
  }
  if (endPoint) {
    edge.endPoint = JSON.parse(endPoint);
  }
  if (pointsList) {
    edge.pointsList = JSON.parse(pointsList);
  }
  var excludeProperties = ["startPoint", "endPoint", "pointsList", "text"];
  Object.keys(element.properties).forEach(function(property) {
    if (excludeProperties.indexOf(property) === -1) {
      edge.properties[property] = element.properties[property];
    }
  });
  return edge;
}
function convertFlowElementToNode(element) {
  var properties = element.properties, key = element.key, type3 = element.type, bounds = element.bounds;
  var x2 = properties.x, y2 = properties.y;
  var text = properties.text;
  if (x2 === void 0) {
    var _a = __read13(bounds, 2), _b = _a[0], x1 = _b.x, y1 = _b.y, _c = _a[1], x22 = _c.x, y22 = _c.y;
    x2 = (x1 + x22) / 2;
    y2 = (y1 + y22) / 2;
  }
  var node = {
    id: key,
    type: TurboTypeMap[type3],
    x: x2,
    y: y2,
    text,
    properties: {}
  };
  var excludeProperties = ["x", "y", "text"];
  Object.keys(element.properties).forEach(function(property) {
    if (excludeProperties.indexOf(property) === -1) {
      node.properties[property] = element.properties[property];
    }
  });
  return node;
}
function toLogicflowData(data) {
  var lfData = {
    nodes: [],
    edges: []
  };
  var list = data.flowElementList;
  list && list.length > 0 && list.forEach(function(element) {
    if (element.type === TurboType.SEQUENCE_FLOW) {
      var edge = convertFlowElementToEdge(element);
      lfData.edges.push(edge);
    } else {
      var node = convertFlowElementToNode(element);
      lfData.nodes.push(node);
    }
  });
  return lfData;
}
var TurboAdapter = (
  /** @class */
  function() {
    function TurboAdapter2(_a) {
      var lf = _a.lf;
      lf.adapterIn = this.adapterIn;
      lf.adapterOut = this.adapterOut;
    }
    TurboAdapter2.prototype.adapterOut = function(logicflowData) {
      if (logicflowData) {
        return toTurboData(logicflowData);
      }
    };
    TurboAdapter2.prototype.adapterIn = function(turboData) {
      if (turboData) {
        return toLogicflowData(turboData);
      }
    };
    TurboAdapter2.pluginName = "turboAdapter";
    return TurboAdapter2;
  }()
);

// node_modules/.pnpm/@logicflow+extension@1.2.28/node_modules/@logicflow/extension/es/insert-node-in-polyline/index.js
var import_core26 = __toESM(require_logic_flow());

// node_modules/.pnpm/@logicflow+extension@1.2.28/node_modules/@logicflow/extension/es/insert-node-in-polyline/edge.js
var SegmentDirection;
(function(SegmentDirection2) {
  SegmentDirection2["HORIZONTAL"] = "horizontal";
  SegmentDirection2["VERTICAL"] = "vertical";
})(SegmentDirection || (SegmentDirection = {}));
var isInSegment = function(point, start, end, deviation) {
  if (deviation === void 0) {
    deviation = 0;
  }
  var distance = distToSegment(point, start, end);
  return distance <= deviation;
};
function sqr(x2) {
  return x2 * x2;
}
function dist2(v2, w2) {
  return sqr(v2.x - w2.x) + sqr(v2.y - w2.y);
}
var distToSegmentSquared = function(p2, v2, w2) {
  var l2 = dist2(v2, w2);
  if (l2 === 0)
    return dist2(p2, v2);
  var t2 = ((p2.x - v2.x) * (w2.x - v2.x) + (p2.y - v2.y) * (w2.y - v2.y)) / l2;
  t2 = Math.max(0, Math.min(1, t2));
  return dist2(p2, {
    x: v2.x + t2 * (w2.x - v2.x),
    y: v2.y + t2 * (w2.y - v2.y)
  });
};
var distToSegment = function(point, start, end) {
  return Math.sqrt(distToSegmentSquared(point, start, end));
};
var getNodeBBox = function(node) {
  var x2 = node.x, y2 = node.y, width = node.width, height = node.height;
  var bBox = {
    minX: x2 - width / 2,
    minY: y2 - height / 2,
    maxX: x2 + width / 2,
    maxY: y2 + height / 2,
    x: x2,
    y: y2,
    width,
    height,
    centerX: x2,
    centerY: y2
  };
  return bBox;
};
var segmentDirection = function(start, end) {
  var direction;
  if (start.x === end.x) {
    direction = SegmentDirection.VERTICAL;
  } else if (start.y === end.y) {
    direction = SegmentDirection.HORIZONTAL;
  }
  return direction;
};
var crossPointInSegment = function(node, start, end) {
  var bBox = getNodeBBox(node);
  var direction = segmentDirection(start, end);
  var maxX = Math.max(start.x, end.x);
  var minX = Math.min(start.x, end.x);
  var maxY = Math.max(start.y, end.y);
  var minY = Math.min(start.y, end.y);
  var x2 = node.x, y2 = node.y, width = node.width, height = node.height;
  if (direction === SegmentDirection.HORIZONTAL) {
    if (maxX >= bBox.maxX && minX <= bBox.minX) {
      return {
        startCrossPoint: {
          x: start.x > end.x ? x2 + width / 2 : x2 - width / 2,
          y: start.y
        },
        endCrossPoint: {
          x: start.x > end.x ? x2 - width / 2 : x2 + width / 2,
          y: start.y
        }
      };
    }
  } else if (direction === SegmentDirection.VERTICAL) {
    if (maxY >= bBox.maxY && minY <= bBox.minY) {
      return {
        startCrossPoint: {
          x: start.x,
          y: start.y > end.y ? y2 + height / 2 : y2 - height / 2
        },
        endCrossPoint: {
          x: start.x,
          y: start.y > end.y ? y2 - height / 2 : y2 + height / 2
        }
      };
    }
  }
};
var isNodeInSegment = function(node, polyline, deviation) {
  if (deviation === void 0) {
    deviation = 0;
  }
  var x2 = node.x, y2 = node.y;
  var pointsList = polyline.pointsList;
  for (var i2 = 0; i2 < pointsList.length - 1; i2++) {
    if (isInSegment({ x: x2, y: y2 }, pointsList[i2], pointsList[i2 + 1], deviation)) {
      var bBoxCross = crossPointInSegment(node, pointsList[i2], pointsList[i2 + 1]);
      if (bBoxCross) {
        return {
          crossIndex: i2 + 1,
          crossPoints: bBoxCross
        };
      }
    }
  }
  return {
    crossIndex: -1,
    crossPoints: {
      startCrossPoint: { x: 0, y: 0 },
      endCrossPoint: { x: 0, y: 0 }
    }
  };
};

// node_modules/.pnpm/@logicflow+extension@1.2.28/node_modules/@logicflow/extension/es/insert-node-in-polyline/index.js
var __read14 = function(o2, n2) {
  var m2 = typeof Symbol === "function" && o2[Symbol.iterator];
  if (!m2)
    return o2;
  var i2 = m2.call(o2), r2, ar = [], e2;
  try {
    while ((n2 === void 0 || n2-- > 0) && !(r2 = i2.next()).done)
      ar.push(r2.value);
  } catch (error) {
    e2 = { error };
  } finally {
    try {
      if (r2 && !r2.done && (m2 = i2["return"]))
        m2.call(i2);
    } finally {
      if (e2)
        throw e2.error;
    }
  }
  return ar;
};
var __spread10 = function() {
  for (var ar = [], i2 = 0; i2 < arguments.length; i2++)
    ar = ar.concat(__read14(arguments[i2]));
  return ar;
};
var InsertNodeInPolyline = (
  /** @class */
  function() {
    function InsertNodeInPolyline2(_a) {
      var lf = _a.lf;
      this._lf = lf;
      this.deviation = 20;
      this.dndAdd = true;
      this.dropAdd = true;
      this.eventHandler();
    }
    InsertNodeInPolyline2.prototype.eventHandler = function() {
      var _this = this;
      if (this.dndAdd) {
        this._lf.on("node:dnd-add", function(_a) {
          var data = _a.data;
          _this.insetNode(data);
        });
      }
      if (this.dropAdd) {
        this._lf.on("node:drop", function(_a) {
          var data = _a.data;
          var edges = _this._lf.graphModel.edges;
          var id = data.id;
          var pureNode = true;
          for (var i2 = 0; i2 < edges.length; i2++) {
            if (edges[i2].sourceNodeId === id || edges[i2].targetNodeId === id) {
              pureNode = false;
              break;
            }
          }
          if (pureNode) {
            _this.insetNode(data);
          }
        });
      }
    };
    InsertNodeInPolyline2.prototype.checkRuleBeforeInsetNode = function(sourceNodeId, targetNodeId, sourceAnchorId, targetAnchorId, nodeData) {
      var sourceNodeModel = this._lf.getNodeModelById(sourceNodeId);
      var targetNodeModel = this._lf.getNodeModelById(targetNodeId);
      var sourceAnchorInfo = sourceNodeModel.getAnchorInfo(sourceAnchorId);
      var targetAnchorInfo = targetNodeModel.getAnchorInfo(targetAnchorId);
      var sourceRuleResultData = sourceNodeModel.isAllowConnectedAsSource(nodeData, sourceAnchorInfo, targetAnchorInfo);
      var targetRuleResultData = targetNodeModel.isAllowConnectedAsTarget(nodeData, sourceAnchorInfo, targetAnchorInfo);
      var _a = (0, import_core26.formateAnchorConnectValidateData)(sourceRuleResultData), isSourcePass = _a.isAllPass, sourceMsg = _a.msg;
      var _b = (0, import_core26.formateAnchorConnectValidateData)(targetRuleResultData), isTargetPass = _b.isAllPass, targetMsg = _b.msg;
      return {
        isPass: isSourcePass && isTargetPass,
        sourceMsg,
        targetMsg
      };
    };
    InsertNodeInPolyline2.prototype.insetNode = function(nodeData) {
      var _this = this;
      var edges = this._lf.graphModel.edges;
      var nodeModel = this._lf.getNodeModelById(nodeData.id);
      var anchorArray = nodeModel.getDefaultAnchor();
      var isNotAllowConnect = !anchorArray || anchorArray.length === 0;
      if (isNotAllowConnect) {
        this._lf.graphModel.eventCenter.emit(import_core26.EventType.CONNECTION_NOT_ALLOWED, {
          data: nodeData,
          msg: "自定义类型节点不显示锚点，也不允许其他节点连接到此节点"
        });
        return;
      }
      for (var i2 = 0; i2 < edges.length; i2++) {
        var _a = isNodeInSegment(nodeModel, edges[i2], this.deviation), crossIndex = _a.crossIndex, crossPoints = _a.crossPoints;
        if (crossIndex >= 0) {
          var _b = edges[i2], sourceNodeId = _b.sourceNodeId, targetNodeId = _b.targetNodeId, id = _b.id, type3 = _b.type, pointsList = _b.pointsList, sourceAnchorId = _b.sourceAnchorId, targetAnchorId = _b.targetAnchorId;
          var startPoint = cloneDeep_default(pointsList[0]);
          var endPoint = cloneDeep_default(crossPoints.startCrossPoint);
          this._lf.deleteEdge(id);
          var checkResult = this.checkRuleBeforeInsetNode(sourceNodeId, targetNodeId, sourceAnchorId, targetAnchorId, nodeData);
          this._lf.addEdge({
            type: type3,
            sourceNodeId,
            targetNodeId: nodeData.id,
            startPoint,
            endPoint,
            pointsList: __spread10(pointsList.slice(0, crossIndex), [
              crossPoints.startCrossPoint
            ])
          });
          this._lf.addEdge({
            type: type3,
            sourceNodeId: nodeData.id,
            targetNodeId,
            startPoint: cloneDeep_default(crossPoints.endCrossPoint),
            endPoint: cloneDeep_default(pointsList[pointsList.length - 1]),
            pointsList: __spread10([
              crossPoints.endCrossPoint
            ], pointsList.slice(crossIndex))
          });
          if (!checkResult.isPass) {
            this._lf.graphModel.eventCenter.emit(import_core26.EventType.CONNECTION_NOT_ALLOWED, {
              data: nodeData,
              msg: checkResult.targetMsg || checkResult.sourceMsg
            });
            setTimeout(function() {
              _this._lf.undo();
            }, 200);
            break;
          } else {
            break;
          }
        }
      }
    };
    InsertNodeInPolyline2.pluginName = "insertNodeInPolyline";
    return InsertNodeInPolyline2;
  }()
);

// node_modules/.pnpm/@logicflow+extension@1.2.28/node_modules/@logicflow/extension/es/components/control/index.js
var __read15 = function(o2, n2) {
  var m2 = typeof Symbol === "function" && o2[Symbol.iterator];
  if (!m2)
    return o2;
  var i2 = m2.call(o2), r2, ar = [], e2;
  try {
    while ((n2 === void 0 || n2-- > 0) && !(r2 = i2.next()).done)
      ar.push(r2.value);
  } catch (error) {
    e2 = { error };
  } finally {
    try {
      if (r2 && !r2.done && (m2 = i2["return"]))
        m2.call(i2);
    } finally {
      if (e2)
        throw e2.error;
    }
  }
  return ar;
};
var __spread11 = function() {
  for (var ar = [], i2 = 0; i2 < arguments.length; i2++)
    ar = ar.concat(__read15(arguments[i2]));
  return ar;
};
var Control2 = (
  /** @class */
  function() {
    function Control3(_a) {
      var _this = this;
      var lf = _a.lf;
      this.controlItems = [
        {
          key: "zoom-out",
          iconClass: "lf-control-zoomOut",
          title: "缩小流程图",
          text: "缩小",
          onClick: function() {
            _this.lf.zoom(false);
          }
        },
        {
          key: "zoom-in",
          iconClass: "lf-control-zoomIn",
          title: "放大流程图",
          text: "放大",
          onClick: function() {
            _this.lf.zoom(true);
          }
        },
        {
          key: "reset",
          iconClass: "lf-control-fit",
          title: "恢复流程原有尺寸",
          text: "适应",
          onClick: function() {
            _this.lf.resetZoom();
          }
        },
        {
          key: "undo",
          iconClass: "lf-control-undo",
          title: "回到上一步",
          text: "上一步",
          onClick: function() {
            _this.lf.undo();
          }
        },
        {
          key: "redo",
          iconClass: "lf-control-redo",
          title: "移到下一步",
          text: "下一步",
          onClick: function() {
            _this.lf.redo();
          }
        }
      ];
      this.lf = lf;
    }
    Control3.prototype.render = function(lf, domContainer) {
      this.destroy();
      var toolEl = this.getControlTool();
      this.toolEl = toolEl;
      domContainer.appendChild(toolEl);
      this.domContainer = domContainer;
    };
    Control3.prototype.destroy = function() {
      if (this.domContainer && this.toolEl && this.domContainer.contains(this.toolEl)) {
        this.domContainer.removeChild(this.toolEl);
      }
    };
    Control3.prototype.addItem = function(item) {
      this.controlItems.push(item);
    };
    Control3.prototype.removeItem = function(key) {
      var index = this.controlItems.findIndex(function(item) {
        return item.key === key;
      });
      return index == -1 ? null : this.controlItems.splice(index, 1)[0];
    };
    Control3.prototype.getControlTool = function() {
      var _this = this;
      var NORMAL = "lf-control-item";
      var DISABLED = "lf-control-item disabled";
      var controlTool = document.createElement("div");
      var controlElements = [];
      controlTool.className = "lf-control";
      this.controlItems.forEach(function(item) {
        var itemContainer = document.createElement("div");
        var icon = document.createElement("i");
        var text = document.createElement("span");
        itemContainer.className = DISABLED;
        item.onClick && (itemContainer.onclick = item.onClick.bind(null, _this.lf));
        item.onMouseEnter && (itemContainer.onmouseenter = item.onMouseEnter.bind(null, _this.lf));
        item.onMouseLeave && (itemContainer.onmouseleave = item.onMouseLeave.bind(null, _this.lf));
        icon.className = item.iconClass;
        text.className = "lf-control-text";
        text.title = item.title;
        text.innerText = item.text;
        itemContainer.append(icon, text);
        switch (item.text) {
          case "上一步":
            _this.lf.on("history:change", function(_a) {
              var undoAble = _a.data.undoAble;
              itemContainer.className = undoAble ? NORMAL : DISABLED;
            });
            break;
          case "下一步":
            _this.lf.on("history:change", function(_a) {
              var redoAble = _a.data.redoAble;
              itemContainer.className = redoAble ? NORMAL : DISABLED;
            });
            break;
          default:
            itemContainer.className = NORMAL;
            break;
        }
        controlElements.push(itemContainer);
      });
      controlTool.append.apply(controlTool, __spread11(controlElements));
      return controlTool;
    };
    Control3.pluginName = "control";
    return Control3;
  }()
);

// node_modules/.pnpm/@logicflow+extension@1.2.28/node_modules/@logicflow/extension/es/components/menu/index.js
var __read16 = function(o2, n2) {
  var m2 = typeof Symbol === "function" && o2[Symbol.iterator];
  if (!m2)
    return o2;
  var i2 = m2.call(o2), r2, ar = [], e2;
  try {
    while ((n2 === void 0 || n2-- > 0) && !(r2 = i2.next()).done)
      ar.push(r2.value);
  } catch (error) {
    e2 = { error };
  } finally {
    try {
      if (r2 && !r2.done && (m2 = i2["return"]))
        m2.call(i2);
    } finally {
      if (e2)
        throw e2.error;
    }
  }
  return ar;
};
var __spread12 = function() {
  for (var ar = [], i2 = 0; i2 < arguments.length; i2++)
    ar = ar.concat(__read16(arguments[i2]));
  return ar;
};
var DefaultNodeMenuKey = "lf:defaultNodeMenu";
var DefaultEdgeMenuKey = "lf:defaultEdgeMenu";
var DefaultGraphMenuKey = "lf:defaultGraphMenu";
var DefaultSelectionMenuKey = "lf:defaultSelectionMenu";
var Menu = (
  /** @class */
  function() {
    function Menu2(_a) {
      var _this = this;
      var lf = _a.lf;
      this.lf = lf;
      var isSilentMode = lf.options.isSilentMode;
      if (!isSilentMode) {
        this.__menuDOM = document.createElement("ul");
        this.menuTypeMap = /* @__PURE__ */ new Map();
        this.init();
        this.lf.setMenuConfig = function(config) {
          _this.setMenuConfig(config);
        };
        this.lf.addMenuConfig = function(config) {
          _this.addMenuConfig(config);
        };
        this.lf.setMenuByType = function(config) {
          _this.setMenuByType(config);
        };
      }
    }
    Menu2.prototype.init = function() {
      var _this = this;
      var defaultNodeMenu = [
        {
          text: "删除",
          callback: function(node) {
            _this.lf.deleteNode(node.id);
          }
        },
        {
          text: "编辑文本",
          callback: function(node) {
            _this.lf.graphModel.editText(node.id);
          }
        },
        {
          text: "复制",
          callback: function(node) {
            _this.lf.cloneNode(node.id);
          }
        }
      ];
      this.menuTypeMap.set(DefaultNodeMenuKey, defaultNodeMenu);
      var defaultEdgeMenu = [
        {
          text: "删除",
          callback: function(edge) {
            _this.lf.deleteEdge(edge.id);
          }
        },
        {
          text: "编辑文本",
          callback: function(edge) {
            _this.lf.graphModel.editText(edge.id);
          }
        }
      ];
      this.menuTypeMap.set(DefaultEdgeMenuKey, defaultEdgeMenu);
      this.menuTypeMap.set(DefaultGraphMenuKey, []);
      var DefaultSelectionMenu = [
        {
          text: "删除",
          callback: function(elements) {
            _this.lf.clearSelectElements();
            elements.edges.forEach(function(edge) {
              return _this.lf.deleteEdge(edge.id);
            });
            elements.nodes.forEach(function(node) {
              return _this.lf.deleteNode(node.id);
            });
          }
        }
      ];
      this.menuTypeMap.set(DefaultSelectionMenuKey, DefaultSelectionMenu);
    };
    Menu2.prototype.render = function(lf, container) {
      var _this = this;
      if (lf.options.isSilentMode)
        return;
      this.__container = container;
      this.__currentData = null;
      this.__menuDOM.className = "lf-menu";
      container.appendChild(this.__menuDOM);
      this.__menuDOM.addEventListener("click", function(event) {
        event.stopPropagation();
        var target = event.target;
        while (Array.from(target.classList).indexOf("lf-menu-item") === -1 && Array.from(target.classList).indexOf("lf-menu") === -1) {
          target = target.parentElement;
        }
        if (Array.from(target.classList).indexOf("lf-menu-item") > -1) {
          target.onclickCallback(_this.__currentData);
          _this.__menuDOM.style.display = "none";
          _this.__currentData = null;
        } else {
          console.warn("点击区域不在菜单项内，请检查代码！");
        }
      }, true);
      this.lf.on("node:contextmenu", function(_a) {
        var data = _a.data, position = _a.position, e2 = _a.e;
        var _b = position.domOverlayPosition, x2 = _b.x, y2 = _b.y;
        var id = data.id;
        var model = _this.lf.graphModel.getNodeModelById(id);
        var menuList = [];
        var typeMenus = _this.menuTypeMap.get(model.type);
        if (model && model.menu && Array.isArray(model.menu)) {
          menuList = model.menu;
        } else if (typeMenus) {
          menuList = typeMenus;
        } else {
          menuList = _this.menuTypeMap.get(DefaultNodeMenuKey);
        }
        _this.__currentData = data;
        _this.showMenu(x2, y2, menuList, {
          width: model.width,
          height: model.height,
          clientX: e2.clientX,
          clientY: e2.clientY
        });
      });
      this.lf.on("edge:contextmenu", function(_a) {
        var data = _a.data, position = _a.position, e2 = _a.e;
        var _b = position.domOverlayPosition, x2 = _b.x, y2 = _b.y;
        var id = data.id;
        var model = _this.lf.graphModel.getEdgeModelById(id);
        var menuList = [];
        var typeMenus = _this.menuTypeMap.get(model.type);
        if (model && model.menu && Array.isArray(model.menu)) {
          menuList = model.menu;
        } else if (typeMenus) {
          menuList = typeMenus;
        } else {
          menuList = _this.menuTypeMap.get(DefaultEdgeMenuKey);
        }
        _this.__currentData = data;
        _this.showMenu(x2, y2, menuList, {
          width: model.width,
          height: model.height,
          clientX: e2.clientX,
          clientY: e2.clientY
        });
      });
      this.lf.on("blank:contextmenu", function(_a) {
        var position = _a.position;
        var menuList = _this.menuTypeMap.get(DefaultGraphMenuKey);
        var _b = position.domOverlayPosition, x2 = _b.x, y2 = _b.y;
        _this.showMenu(x2, y2, menuList);
      });
      this.lf.on("selection:contextmenu", function(_a) {
        var data = _a.data, position = _a.position;
        var menuList = _this.menuTypeMap.get(DefaultSelectionMenuKey);
        var _b = position.domOverlayPosition, x2 = _b.x, y2 = _b.y;
        _this.__currentData = data;
        _this.showMenu(x2, y2, menuList);
      });
      this.lf.on("node:mousedown", function() {
        _this.__menuDOM.style.display = "none";
      });
      this.lf.on("edge:click", function() {
        _this.__menuDOM.style.display = "none";
      });
      this.lf.on("blank:click", function() {
        _this.__menuDOM.style.display = "none";
      });
    };
    Menu2.prototype.destroy = function() {
      var _a;
      (_a = this === null || this === void 0 ? void 0 : this.__container) === null || _a === void 0 ? void 0 : _a.removeChild(this.__menuDOM);
      this.__menuDOM = null;
    };
    Menu2.prototype.showMenu = function(x2, y2, menuList, options) {
      if (!menuList || !menuList.length)
        return;
      var menu = this.__menuDOM;
      menu.innerHTML = "";
      menu.append.apply(menu, __spread12(this.__getMenuDom(menuList)));
      if (!menu.children.length)
        return;
      menu.style.display = "block";
      if (!options) {
        menu.style.top = y2 + "px";
        menu.style.left = x2 + "px";
        return;
      }
      var width = options.width, height = options.height, clientX = options.clientX, clientY = options.clientY;
      var graphModel = this.lf.graphModel;
      var menuWidth = menu.offsetWidth;
      var menuIsRightShow = true;
      var windowMaxX = window.innerWidth || document.documentElement.clientWidth || document.body.clientWidth;
      var rightDistance = windowMaxX - clientX;
      var graphRect = graphModel.rootEl.getBoundingClientRect();
      var graphMaxX = graphRect.left + graphRect.width;
      if (graphMaxX < windowMaxX) {
        rightDistance = graphMaxX - clientX;
      }
      if (rightDistance < menuWidth) {
        menuIsRightShow = false;
      }
      if (menuIsRightShow) {
        menu.style.left = x2 + "px";
      } else {
        menu.style.left = x2 - width + "px";
      }
      var menuHeight = menu.offsetHeight;
      var menuIsBottomShow = true;
      var windowMaxY = window.innerHeight || document.documentElement.clientHeight || document.body.clientHeight;
      var bottomDistance = windowMaxY - clientY;
      var graphMaxY = graphRect.top + graphRect.height;
      if (graphMaxY < windowMaxY) {
        bottomDistance = graphMaxY - clientY;
      }
      if (bottomDistance < menuHeight) {
        menuIsBottomShow = false;
      }
      if (menuIsBottomShow) {
        menu.style.top = y2 + "px";
      } else {
        menu.style.top = y2 - height + "px";
      }
    };
    Menu2.prototype.setMenuByType = function(config) {
      if (!config.type || !config.menu) {
        return;
      }
      this.menuTypeMap.set(config.type, config.menu);
    };
    Menu2.prototype.__getMenuDom = function(list) {
      var menuList = [];
      list && list.length > 0 && list.forEach(function(item) {
        var element = document.createElement("li");
        if (item.className) {
          element.className = "lf-menu-item " + item.className;
        } else {
          element.className = "lf-menu-item";
        }
        if (item.icon === true) {
          var icon = document.createElement("span");
          icon.className = "lf-menu-item-icon";
          element.appendChild(icon);
        }
        var text = document.createElement("span");
        text.className = "lf-menu-item-text";
        if (item.text) {
          text.innerText = item.text;
        }
        element.appendChild(text);
        element.onclickCallback = item.callback;
        menuList.push(element);
      });
      return menuList;
    };
    Menu2.prototype.setMenuConfig = function(config) {
      if (!config) {
        return;
      }
      config.nodeMenu !== void 0 && this.menuTypeMap.set(DefaultNodeMenuKey, config.nodeMenu ? config.nodeMenu : []);
      config.edgeMenu !== void 0 && this.menuTypeMap.set(DefaultEdgeMenuKey, config.edgeMenu ? config.edgeMenu : []);
      config.graphMenu !== void 0 && this.menuTypeMap.set(DefaultGraphMenuKey, config.graphMenu ? config.graphMenu : []);
    };
    Menu2.prototype.addMenuConfig = function(config) {
      if (!config) {
        return;
      }
      if (Array.isArray(config.nodeMenu)) {
        var menuList = this.menuTypeMap.get(DefaultNodeMenuKey);
        this.menuTypeMap.set(DefaultNodeMenuKey, menuList.concat(config.nodeMenu));
      }
      if (Array.isArray(config.edgeMenu)) {
        var menuList = this.menuTypeMap.get(DefaultEdgeMenuKey);
        this.menuTypeMap.set(DefaultEdgeMenuKey, menuList.concat(config.edgeMenu));
      }
      if (Array.isArray(config.graphMenu)) {
        var menuList = this.menuTypeMap.get(DefaultGraphMenuKey);
        this.menuTypeMap.set(DefaultGraphMenuKey, menuList.concat(config.graphMenu));
      }
    };
    Menu2.prototype.changeMenuItem = function(type3, config) {
      if (type3 === "add")
        this.addMenuConfig(config);
      else if (type3 === "reset")
        this.setMenuConfig(config);
      else {
        throw new Error("The first parameter of changeMenuConfig should be 'add' or 'reset'");
      }
    };
    Menu2.pluginName = "menu";
    return Menu2;
  }()
);

// node_modules/.pnpm/@logicflow+extension@1.2.28/node_modules/@logicflow/extension/es/components/context-menu/index.js
var __read17 = function(o2, n2) {
  var m2 = typeof Symbol === "function" && o2[Symbol.iterator];
  if (!m2)
    return o2;
  var i2 = m2.call(o2), r2, ar = [], e2;
  try {
    while ((n2 === void 0 || n2-- > 0) && !(r2 = i2.next()).done)
      ar.push(r2.value);
  } catch (error) {
    e2 = { error };
  } finally {
    try {
      if (r2 && !r2.done && (m2 = i2["return"]))
        m2.call(i2);
    } finally {
      if (e2)
        throw e2.error;
    }
  }
  return ar;
};
var COMMON_TYPE_KEY = "menu-common";
var NEXT_X_DISTANCE = 200;
var NEXT_Y_DISTANCE = 100;
var ContextMenu = (
  /** @class */
  function() {
    function ContextMenu2(_a) {
      var _this = this;
      var lf = _a.lf;
      this.menuTypeMap = /* @__PURE__ */ new Map();
      this.listenDelete = function() {
        _this.hideContextMenu();
      };
      this.lf = lf;
      this.__menuDOM = document.createElement("div");
      this.__menuDOM.className = "lf-inner-context";
      this.menuTypeMap.set(COMMON_TYPE_KEY, []);
      this.lf.setContextMenuByType = function(type3, menus) {
        _this.setContextMenuByType(type3, menus);
      };
      this.lf.setContextMenuItems = function(menus) {
        _this.setContextMenuItems(menus);
      };
      this.lf.showContextMenu = function(data) {
        _this.showContextMenu(data);
      };
      this.lf.hideContextMenu = function() {
        _this.hideContextMenu();
      };
    }
    ContextMenu2.prototype.render = function(lf, container) {
      var _this = this;
      this.container = container;
      lf.on("node:click", function(_a) {
        var data = _a.data;
        _this._activeData = data;
        _this.createContextMenu();
      });
      lf.on("edge:click", function(_a) {
        var data = _a.data;
        _this._activeData = data;
        _this.createContextMenu();
      });
      lf.on("blank:click", function() {
        _this.hideContextMenu();
      });
    };
    ContextMenu2.prototype.setContextMenuByType = function(type3, menus) {
      this.menuTypeMap.set(type3, menus);
    };
    ContextMenu2.prototype.hideContextMenu = function() {
      this.__menuDOM.innerHTML = "";
      this.__menuDOM.style.display = "none";
      if (this.isShow) {
        this.container.removeChild(this.__menuDOM);
      }
      this.lf.off("node:delete,edge:delete,node:drag,graph:transform", this.listenDelete);
      this.isShow = false;
    };
    ContextMenu2.prototype.showContextMenu = function(data) {
      if (!data || !data.id) {
        console.warn("请检查传入的参数");
        return;
      }
      this._activeData = data;
      this.createContextMenu();
    };
    ContextMenu2.prototype.setContextMenuItems = function(menus) {
      this.menuTypeMap.set(COMMON_TYPE_KEY, menus);
    };
    ContextMenu2.prototype.getContextMenuPosition = function() {
      var data = this._activeData;
      var Model = this.lf.graphModel.getElement(data.id);
      if (!Model) {
        console.warn("找不到元素" + data.id);
        return;
      }
      var x2;
      var y2;
      if (Model.BaseType === "edge") {
        x2 = Number.MIN_SAFE_INTEGER;
        y2 = Number.MAX_SAFE_INTEGER;
        var edgeData = Model.getData();
        x2 = Math.max(edgeData.startPoint.x, x2);
        y2 = Math.min(edgeData.startPoint.y, y2);
        x2 = Math.max(edgeData.endPoint.x, x2);
        y2 = Math.min(edgeData.endPoint.y, y2);
        if (edgeData.pointsList) {
          edgeData.pointsList.forEach(function(point) {
            x2 = Math.max(point.x, x2);
            y2 = Math.min(point.y, y2);
          });
        }
      }
      if (Model.BaseType === "node") {
        x2 = data.x + Model.width / 2;
        y2 = data.y - Model.height / 2;
      }
      return this.lf.graphModel.transformModel.CanvasPointToHtmlPoint([x2, y2]);
    };
    ContextMenu2.prototype.createContextMenu = function() {
      var _this = this;
      var isSilentMode = this.lf.options.isSilentMode;
      if (isSilentMode) {
        return;
      }
      var items = this.menuTypeMap.get(this._activeData.type) || [];
      items = items.concat(this.menuTypeMap.get(COMMON_TYPE_KEY));
      var menus = document.createDocumentFragment();
      items.forEach(function(item) {
        var menuItem = document.createElement("div");
        menuItem.className = "lf-context-item";
        var img = document.createElement("img");
        img.src = item.icon;
        img.className = "lf-context-img";
        if (item.className) {
          menuItem.className = menuItem.className + " " + item.className;
        }
        img.addEventListener("click", function() {
          _this.hideContextMenu();
          if (item.callback) {
            item.callback(_this._activeData);
          } else {
            _this.addNode({
              sourceId: _this._activeData.id,
              x: _this._activeData.x,
              y: _this._activeData.y,
              properties: item.properties,
              type: item.type
            });
          }
        });
        menuItem.appendChild(img);
        menus.appendChild(menuItem);
      });
      this.__menuDOM.innerHTML = "";
      this.__menuDOM.appendChild(menus);
      this.showMenu();
    };
    ContextMenu2.prototype.addNode = function(node, y2) {
      var isDeep = y2 !== void 0;
      if (y2 === void 0) {
        y2 = node.y;
      }
      var nodeModel = this.lf.getNodeModelById(node.sourceId);
      var leftTopX = node.x - nodeModel.width + NEXT_X_DISTANCE;
      var leftTopY = y2 - node.y / 2 - 20;
      var rightBottomX = node.x + nodeModel.width + NEXT_X_DISTANCE;
      var rightBottomY = y2 + node.y / 2 + 20;
      var existElements = this.lf.getAreaElement([leftTopX, leftTopY], [rightBottomX, rightBottomY]);
      if (existElements.length) {
        y2 = y2 + NEXT_Y_DISTANCE;
        this.addNode(node, y2);
        return;
      }
      var newNode = this.lf.addNode({
        type: node.type,
        x: node.x + 200,
        y: y2,
        properties: node.properties
      });
      var startPoint;
      var endPoint;
      if (isDeep) {
        startPoint = {
          x: node.x,
          y: node.y + nodeModel.height / 2
        };
        endPoint = {
          x: newNode.x - newNode.width / 2,
          y: newNode.y
        };
      }
      this.lf.addEdge({
        sourceNodeId: node.sourceId,
        targetNodeId: newNode.id,
        startPoint,
        endPoint
      });
    };
    ContextMenu2.prototype.showMenu = function() {
      var _a = __read17(this.getContextMenuPosition(), 2), x2 = _a[0], y2 = _a[1];
      this.__menuDOM.style.display = "flex";
      this.__menuDOM.style.top = y2 + "px";
      this.__menuDOM.style.left = x2 + 10 + "px";
      this.container.appendChild(this.__menuDOM);
      !this.isShow && this.lf.on("node:delete,edge:delete,node:drag,graph:transform", this.listenDelete);
      this.isShow = true;
    };
    ContextMenu2.pluginName = "contextMenu";
    return ContextMenu2;
  }()
);

// node_modules/.pnpm/@logicflow+extension@1.2.28/node_modules/@logicflow/extension/es/components/dnd-panel/index.js
var DndPanel = (
  /** @class */
  function() {
    function DndPanel2(_a) {
      var _this = this;
      var lf = _a.lf;
      this.lf = lf;
      this.lf.setPatternItems = function(shapeList) {
        _this.setPatternItems(shapeList);
      };
    }
    DndPanel2.prototype.render = function(lf, domContainer) {
      var _this = this;
      this.destroy();
      if (!this.shapeList || this.shapeList.length === 0) {
        this.domContainer = domContainer;
        return;
      }
      this.panelEl = document.createElement("div");
      this.panelEl.className = "lf-dndpanel";
      this.shapeList.forEach(function(shapeItem) {
        _this.panelEl.appendChild(_this.createDndItem(shapeItem));
      });
      domContainer.appendChild(this.panelEl);
      this.domContainer = domContainer;
    };
    DndPanel2.prototype.destroy = function() {
      if (this.domContainer && this.panelEl && this.domContainer.contains(this.panelEl)) {
        this.domContainer.removeChild(this.panelEl);
      }
    };
    DndPanel2.prototype.setPatternItems = function(shapeList) {
      this.shapeList = shapeList;
      if (this.domContainer) {
        this.render(this.lf, this.domContainer);
      }
    };
    DndPanel2.prototype.createDndItem = function(shapeItem) {
      var _this = this;
      var el = document.createElement("div");
      el.className = shapeItem.className ? "lf-dnd-item " + shapeItem.className : "lf-dnd-item";
      var shape = document.createElement("div");
      shape.className = "lf-dnd-shape";
      if (shapeItem.icon) {
        shape.style.backgroundImage = "url(" + shapeItem.icon + ")";
      }
      el.appendChild(shape);
      if (shapeItem.label) {
        var text = document.createElement("div");
        text.innerText = shapeItem.label;
        text.className = "lf-dnd-text";
        el.appendChild(text);
      }
      if (shapeItem.disabled) {
        el.classList.add("disabled");
        el.onmousedown = function() {
          if (shapeItem.callback) {
            shapeItem.callback(_this.lf, _this.domContainer);
          }
        };
        return el;
      }
      el.onmousedown = function() {
        if (shapeItem.type) {
          _this.lf.dnd.startDrag({
            type: shapeItem.type,
            properties: shapeItem.properties,
            text: shapeItem.text
          });
        }
        if (shapeItem.callback) {
          shapeItem.callback(_this.lf, _this.domContainer);
        }
      };
      el.ondblclick = function(e2) {
        _this.lf.graphModel.eventCenter.emit("dnd:panel-dbclick", {
          e: e2,
          data: shapeItem
        });
      };
      el.onclick = function(e2) {
        _this.lf.graphModel.eventCenter.emit("dnd:panel-click", {
          e: e2,
          data: shapeItem
        });
      };
      el.oncontextmenu = function(e2) {
        _this.lf.graphModel.eventCenter.emit("dnd:panel-contextmenu", {
          e: e2,
          data: shapeItem
        });
      };
      return el;
    };
    DndPanel2.pluginName = "dndPanel";
    return DndPanel2;
  }()
);

// node_modules/.pnpm/@logicflow+extension@1.2.28/node_modules/@logicflow/extension/es/components/selection-select/index.js
var SelectionSelect = (
  /** @class */
  function() {
    function SelectionSelect2(_a) {
      var _this = this;
      var lf = _a.lf;
      this.__disabled = false;
      this.isDefaultStopMoveGraph = false;
      this.isWholeNode = true;
      this.isWholeEdge = true;
      this.__draw = function(ev) {
        var _a2 = _this.lf.getPointByClient(ev.clientX, ev.clientY).domOverlayPosition, x1 = _a2.x, y1 = _a2.y;
        _this.endPoint = { x: x1, y: y1 };
        var _b = _this.startPoint, x2 = _b.x, y2 = _b.y;
        var style2 = _this.wrapper.style;
        var left = x2;
        var top = y2;
        var width = x1 - x2;
        var height = y1 - y2;
        if (x1 < x2) {
          left = x1;
          width = x2 - x1;
        }
        if (y1 < y2) {
          top = y1;
          height = y2 - y1;
        }
        style2.left = left + "px";
        style2.top = top + "px";
        style2.width = width + "px";
        style2.height = height + "px";
      };
      this.__drawOff = function() {
        document.removeEventListener("mousemove", _this.__draw);
        document.removeEventListener("mouseup", _this.__drawOff);
        _this.wrapper.oncontextmenu = null;
        _this.__domContainer.removeChild(_this.wrapper);
        var _a2 = _this.startPoint, x2 = _a2.x, y2 = _a2.y;
        var _b = _this.endPoint, x1 = _b.x, y1 = _b.y;
        if (Math.abs(x1 - x2) < 10 && Math.abs(y1 - y2) < 10) {
          return;
        }
        var lt = [Math.min(x2, x1), Math.min(y2, y1)];
        var rt = [Math.max(x2, x1), Math.max(y2, y1)];
        var elements = _this.lf.graphModel.getAreaElement(lt, rt, _this.isWholeEdge, _this.isWholeNode, true);
        var group = _this.lf.graphModel.group;
        elements.forEach(function(element) {
          if (!group || !group.getNodeGroup(element.id)) {
            _this.lf.selectElementById(element.id, true);
          }
        });
        _this.lf.emit("selection:selected", elements);
      };
      this.lf = lf;
      var stopMoveGraph = lf.getEditConfig().stopMoveGraph;
      this.isDefaultStopMoveGraph = stopMoveGraph;
      lf.openSelectionSelect = function() {
        _this.openSelectionSelect();
      };
      lf.closeSelectionSelect = function() {
        _this.closeSelectionSelect();
      };
    }
    SelectionSelect2.prototype.render = function(lf, domContainer) {
      var _this = this;
      this.__domContainer = domContainer;
      lf.on("blank:mousedown", function(_a) {
        var e2 = _a.e;
        var config = lf.getEditConfig();
        if (!config.stopMoveGraph || _this.__disabled) {
          return;
        }
        var isRightClick = e2.button === 2;
        if (isRightClick) {
          return;
        }
        var _b = lf.getPointByClient(e2.clientX, e2.clientY).domOverlayPosition, x2 = _b.x, y2 = _b.y;
        _this.startPoint = { x: x2, y: y2 };
        _this.endPoint = { x: x2, y: y2 };
        var wrapper = document.createElement("div");
        wrapper.className = "lf-selection-select";
        wrapper.oncontextmenu = function prevent(ev) {
          ev.preventDefault();
        };
        wrapper.style.top = _this.startPoint.y + "px";
        wrapper.style.left = _this.startPoint.x + "px";
        domContainer.appendChild(wrapper);
        _this.wrapper = wrapper;
        document.addEventListener("mousemove", _this.__draw);
        document.addEventListener("mouseup", _this.__drawOff);
      });
    };
    SelectionSelect2.prototype.setSelectionSense = function(isWholeEdge, isWholeNode) {
      if (isWholeEdge === void 0) {
        isWholeEdge = true;
      }
      if (isWholeNode === void 0) {
        isWholeNode = true;
      }
      this.isWholeEdge = isWholeEdge;
      this.isWholeNode = isWholeNode;
    };
    SelectionSelect2.prototype.openSelectionSelect = function() {
      var stopMoveGraph = this.lf.getEditConfig().stopMoveGraph;
      if (!stopMoveGraph) {
        this.isDefaultStopMoveGraph = false;
        this.lf.updateEditConfig({
          stopMoveGraph: true
        });
      }
      this.open();
    };
    SelectionSelect2.prototype.closeSelectionSelect = function() {
      if (!this.isDefaultStopMoveGraph) {
        this.lf.updateEditConfig({
          stopMoveGraph: false
        });
      }
      this.close();
    };
    SelectionSelect2.prototype.open = function() {
      this.__disabled = false;
    };
    SelectionSelect2.prototype.close = function() {
      this.__disabled = true;
    };
    SelectionSelect2.pluginName = "selectionSelect";
    return SelectionSelect2;
  }()
);

// node_modules/.pnpm/@logicflow+extension@1.2.28/node_modules/@logicflow/extension/es/components/mini-map/index.js
var __values3 = function(o2) {
  var s2 = typeof Symbol === "function" && Symbol.iterator, m2 = s2 && o2[s2], i2 = 0;
  if (m2)
    return m2.call(o2);
  if (o2 && typeof o2.length === "number")
    return {
      next: function() {
        if (o2 && i2 >= o2.length)
          o2 = void 0;
        return { value: o2 && o2[i2++], done: !o2 };
      }
    };
  throw new TypeError(s2 ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
var MiniMap = (
  /** @class */
  function() {
    function MiniMap2(_a) {
      var _this = this;
      var lf = _a.lf, LogicFlow = _a.LogicFlow, options = _a.options;
      this.lf = null;
      this.container = null;
      this.miniMapWrap = null;
      this.miniMapContainer = null;
      this.lfMap = null;
      this.viewport = null;
      this.width = 150;
      this.height = 220;
      this.leftPosition = void 0;
      this.topPosition = void 0;
      this.rightPosition = void 0;
      this.bottomPosition = void 0;
      this.miniMapWidth = 450;
      this.miniMapHeight = 660;
      this.viewPortTop = 0;
      this.viewPortLeft = 0;
      this.startPosition = null;
      this.viewPortScale = 1;
      this.viewPortWidth = 150;
      this.viewPortHeight = 75;
      this.resetDataX = 0;
      this.resetDataY = 0;
      this.LogicFlow = null;
      this.isShow = false;
      this.isShowHeader = true;
      this.isShowCloseIcon = true;
      this.dragging = false;
      this.disabledPlugins = ["miniMap", "control", "selectionSelect"];
      this.show = function(leftPosition, topPosition) {
        _this.setView();
        if (!_this.isShow) {
          _this.createMiniMap(leftPosition, topPosition);
        }
        _this.isShow = true;
      };
      this.hide = function() {
        if (_this.isShow) {
          _this.removeMiniMap();
        }
        _this.isShow = false;
      };
      this.reset = function() {
        _this.lf.resetTranslate();
        _this.lf.resetZoom();
        _this.hide();
        _this.show();
      };
      this.startDrag = function(e2) {
        document.addEventListener("mousemove", _this.drag);
        document.addEventListener("mouseup", _this.drop);
        _this.startPosition = {
          x: e2.x,
          y: e2.y
        };
      };
      this.moveViewport = function(top, left) {
        var viewStyle = _this.viewport.style;
        _this.viewPortTop = top;
        _this.viewPortLeft = left;
        viewStyle.top = _this.viewPortTop + "px";
        viewStyle.left = _this.viewPortLeft + "px";
      };
      this.drag = function(e2) {
        _this.dragging = true;
        var top = _this.viewPortTop + e2.y - _this.startPosition.y;
        var left = _this.viewPortLeft + e2.x - _this.startPosition.x;
        _this.moveViewport(top, left);
        _this.startPosition = {
          x: e2.x,
          y: e2.y
        };
        var centerX = (_this.viewPortLeft + _this.viewPortWidth / 2) / _this.viewPortScale;
        var centerY = (_this.viewPortTop + _this.viewPortHeight / 2) / _this.viewPortScale;
        _this.lf.focusOn({
          coordinate: {
            x: centerX + _this.resetDataX,
            y: centerY + _this.resetDataY
          }
        });
      };
      this.drop = function() {
        document.removeEventListener("mousemove", _this.drag);
        document.removeEventListener("mouseup", _this.drop);
        var top = _this.viewPortTop;
        var left = _this.viewPortLeft;
        if (_this.viewPortLeft > _this.width) {
          left = _this.width - _this.viewPortWidth;
        }
        if (_this.viewPortTop > _this.height) {
          top = _this.height - _this.viewPortHeight;
        }
        if (_this.viewPortLeft < -_this.width) {
          left = 0;
        }
        if (_this.viewPortTop < -_this.height) {
          top = 0;
        }
        _this.moveViewport(top, left);
      };
      this.mapClick = function(e2) {
        if (_this.dragging) {
          _this.dragging = false;
        } else {
          var layerX = e2.layerX, layerY = e2.layerY;
          var ViewPortCenterX = layerX;
          var ViewPortCenterY = layerY;
          var graphData = _this.lf.getGraphRawData();
          var _a2 = _this.getBounds(graphData), left = _a2.left, top_1 = _a2.top;
          var resetGraphX = left + ViewPortCenterX / _this.viewPortScale;
          var resetGraphY = top_1 + ViewPortCenterY / _this.viewPortScale;
          _this.lf.focusOn({ coordinate: { x: resetGraphX, y: resetGraphY } });
        }
      };
      this.lf = lf;
      if (options && options.MiniMap) {
        this.setOption(options);
      }
      this.miniMapWidth = lf.graphModel.width;
      this.miniMapHeight = lf.graphModel.width * this.height / this.width;
      this.LogicFlow = LogicFlow;
      this.initMiniMap();
    }
    MiniMap2.prototype.render = function(lf, container) {
      var _this = this;
      this.container = container;
      this.lf.on("history:change", function() {
        if (_this.isShow) {
          _this.setView();
        }
      });
      this.lf.on("graph:transform", throttle_default(function() {
        if (_this.isShow && !_this.dragging) {
          _this.setView();
        }
      }, 300));
    };
    MiniMap2.prototype.init = function(option) {
      this.disabledPlugins = this.disabledPlugins.concat(option.disabledPlugins || []);
    };
    MiniMap2.prototype.setOption = function(options) {
      var _a = options.MiniMap, _b = _a.width, width = _b === void 0 ? 150 : _b, _c = _a.height, height = _c === void 0 ? 220 : _c, _d = _a.isShowHeader, isShowHeader = _d === void 0 ? true : _d, _e = _a.isShowCloseIcon, isShowCloseIcon = _e === void 0 ? true : _e, _f = _a.leftPosition, leftPosition = _f === void 0 ? 0 : _f, _g = _a.topPosition, topPosition = _g === void 0 ? 0 : _g, rightPosition = _a.rightPosition, bottomPosition = _a.bottomPosition;
      this.width = width;
      this.height = height;
      this.isShowHeader = isShowHeader;
      this.isShowCloseIcon = isShowCloseIcon;
      this.viewPortWidth = width;
      this.leftPosition = leftPosition;
      this.topPosition = topPosition;
      this.rightPosition = rightPosition;
      this.bottomPosition = bottomPosition;
    };
    MiniMap2.prototype.initMiniMap = function() {
      var miniMapWrap = document.createElement("div");
      miniMapWrap.className = "lf-mini-map-graph";
      miniMapWrap.style.width = this.width + 4 + "px";
      miniMapWrap.style.height = this.height + "px";
      this.lfMap = new this.LogicFlow({
        container: miniMapWrap,
        isSilentMode: true,
        stopZoomGraph: true,
        stopScrollGraph: true,
        stopMoveGraph: true,
        hideAnchors: true,
        hoverOutline: false,
        disabledPlugins: this.disabledPlugins
      });
      this.lfMap.adapterIn = function(a2) {
        return a2;
      };
      this.lfMap.adapterOut = function(a2) {
        return a2;
      };
      this.miniMapWrap = miniMapWrap;
      this.createViewPort();
      miniMapWrap.addEventListener("click", this.mapClick);
    };
    MiniMap2.prototype.createMiniMap = function(left, top) {
      var miniMapContainer = document.createElement("div");
      miniMapContainer.appendChild(this.miniMapWrap);
      if (typeof left !== "undefined" || typeof top !== "undefined") {
        miniMapContainer.style.left = (left || 0) + "px";
        miniMapContainer.style.top = (top || 0) + "px";
      } else {
        if (typeof this.rightPosition !== "undefined") {
          miniMapContainer.style.right = this.rightPosition + "px";
        } else if (typeof this.leftPosition !== "undefined") {
          miniMapContainer.style.left = this.leftPosition + "px";
        }
        if (typeof this.bottomPosition !== "undefined") {
          miniMapContainer.style.bottom = this.bottomPosition + "px";
        } else if (typeof this.topPosition !== "undefined") {
          miniMapContainer.style.top = this.topPosition + "px";
        }
      }
      miniMapContainer.style.position = "absolute";
      miniMapContainer.className = "lf-mini-map";
      if (!this.isShowCloseIcon) {
        miniMapContainer.classList.add("lf-mini-map-no-close-icon");
      }
      if (!this.isShowHeader) {
        miniMapContainer.classList.add("lf-mini-map-no-header");
      }
      this.container.appendChild(miniMapContainer);
      this.miniMapWrap.appendChild(this.viewport);
      var header = document.createElement("div");
      header.className = "lf-mini-map-header";
      header.innerText = MiniMap2.headerTitle;
      miniMapContainer.appendChild(header);
      var close = document.createElement("span");
      close.className = "lf-mini-map-close";
      close.addEventListener("click", this.hide);
      miniMapContainer.appendChild(close);
      this.miniMapContainer = miniMapContainer;
    };
    MiniMap2.prototype.removeMiniMap = function() {
      this.container.removeChild(this.miniMapContainer);
    };
    MiniMap2.prototype.getBounds = function(data) {
      var left = 0;
      var right = this.miniMapWidth;
      var top = 0;
      var bottom = this.miniMapHeight;
      var nodes = data.nodes;
      if (nodes && nodes.length > 0) {
        nodes.forEach(function(_a) {
          var x2 = _a.x, y2 = _a.y, _b = _a.width, width = _b === void 0 ? 200 : _b, _c = _a.height, height = _c === void 0 ? 200 : _c;
          var nodeLeft = x2 - width / 2;
          var nodeRight = x2 + width / 2;
          var nodeTop = y2 - height / 2;
          var nodeBottom = y2 + height / 2;
          left = nodeLeft < left ? nodeLeft : left;
          right = nodeRight > right ? nodeRight : right;
          top = nodeTop < top ? nodeTop : top;
          bottom = nodeBottom > bottom ? nodeBottom : bottom;
        });
      }
      return {
        left,
        top,
        bottom,
        right
      };
    };
    MiniMap2.prototype.resetData = function(data) {
      var nodes = data.nodes, edges = data.edges;
      var left = 0;
      var top = 0;
      if (nodes && nodes.length > 0) {
        nodes.forEach(function(_a) {
          var x2 = _a.x, y2 = _a.y, _b = _a.width, width = _b === void 0 ? 200 : _b, _c = _a.height, height = _c === void 0 ? 200 : _c;
          var nodeLeft = x2 - width / 2;
          var nodeTop = y2 - height / 2;
          left = nodeLeft < left ? nodeLeft : left;
          top = nodeTop < top ? nodeTop : top;
        });
        if (left < 0 || top < 0) {
          this.resetDataX = left;
          this.resetDataY = top;
          nodes.forEach(function(node) {
            node.x = node.x - left;
            node.y = node.y - top;
            if (node.text) {
              node.text.x = node.text.x - left;
              node.text.y = node.text.y - top;
            }
          });
          edges.forEach(function(edge) {
            if (edge.startPoint) {
              edge.startPoint.x = edge.startPoint.x - left;
              edge.startPoint.y = edge.startPoint.y - top;
            }
            if (edge.endPoint) {
              edge.endPoint.x = edge.endPoint.x - left;
              edge.endPoint.y = edge.endPoint.y - top;
            }
            if (edge.text) {
              edge.text.x = edge.text.x - left;
              edge.text.y = edge.text.y - top;
            }
            if (edge.pointsList) {
              edge.pointsList.forEach(function(point) {
                point.x = point.x - left;
                point.y = point.y - top;
              });
            }
          });
        }
      }
      return data;
    };
    MiniMap2.prototype.setView = function() {
      var e_1, _a;
      var graphData = this.lf.getGraphRawData();
      var data = this.resetData(graphData);
      var viewMap = this.lf.viewMap;
      var modelMap = this.lf.graphModel.modelMap;
      var minimapViewMap = this.lfMap.viewMap;
      try {
        for (var _b = __values3(viewMap.keys()), _c = _b.next(); !_c.done; _c = _b.next()) {
          var key = _c.value;
          if (!minimapViewMap.has(key)) {
            this.lfMap.setView(key, viewMap.get(key));
            this.lfMap.graphModel.modelMap.set(key, modelMap.get(key));
          }
        }
      } catch (e_1_1) {
        e_1 = { error: e_1_1 };
      } finally {
        try {
          if (_c && !_c.done && (_a = _b.return))
            _a.call(_b);
        } finally {
          if (e_1)
            throw e_1.error;
        }
      }
      this.lfMap.render(data);
      var _d = this.getBounds(data), left = _d.left, top = _d.top, right = _d.right, bottom = _d.bottom;
      var realWidthScale = this.width / (right - left);
      var realHeightScale = this.height / (bottom - top);
      var innerStyle = this.miniMapWrap.firstChild.style;
      var scale = Math.min(realWidthScale, realHeightScale);
      innerStyle.pointerEvents = "none";
      innerStyle.transform = "matrix(" + scale + ", 0, 0, " + scale + ", 0, 0)";
      innerStyle.transformOrigin = "left top";
      innerStyle.height = bottom - Math.min(top, 0) + "px";
      innerStyle.width = right - Math.min(left, 0) + "px";
      this.viewPortScale = scale;
      this.setViewPort(scale, {
        left,
        top,
        right,
        bottom
      });
    };
    MiniMap2.prototype.setViewPort = function(scale, _a) {
      var left = _a.left, right = _a.right, top = _a.top, bottom = _a.bottom;
      var viewStyle = this.viewport.style;
      viewStyle.width = this.viewPortWidth + "px";
      viewStyle.height = this.viewPortWidth / (this.lf.graphModel.width / this.lf.graphModel.height) + "px";
      var _b = this.lf.getTransform(), TRANSLATE_X = _b.TRANSLATE_X, TRANSLATE_Y = _b.TRANSLATE_Y, SCALE_X = _b.SCALE_X, SCALE_Y = _b.SCALE_Y;
      var realWidth = right - left;
      var viewPortWidth = this.width / (realWidth / this.lf.graphModel.width);
      var realViewPortWidth = this.width * (viewPortWidth / this.width);
      var graphRatio = this.lf.graphModel.width / this.lf.graphModel.height;
      var realViewPortHeight = realViewPortWidth / graphRatio;
      var graphData = this.lf.getGraphRawData();
      var _c = this.getBounds(graphData), graphLeft = _c.left, graphTop = _c.top;
      var viewportLeft = graphLeft;
      var viewportTop = graphTop;
      viewportLeft += TRANSLATE_X / SCALE_X;
      viewportTop += TRANSLATE_Y / SCALE_Y;
      this.viewPortTop = viewportTop > 0 ? 0 : -viewportTop * scale;
      this.viewPortLeft = viewportLeft > 0 ? 0 : -viewportLeft * scale;
      this.viewPortWidth = realViewPortWidth;
      this.viewPortHeight = realViewPortHeight;
      viewStyle.top = this.viewPortTop + "px";
      viewStyle.left = this.viewPortLeft + "px";
      viewStyle.width = realViewPortWidth / SCALE_X + "px";
      viewStyle.height = realViewPortHeight / SCALE_Y + "px";
    };
    MiniMap2.prototype.createViewPort = function() {
      var div = document.createElement("div");
      div.className = "lf-minimap-viewport";
      div.addEventListener("mousedown", this.startDrag);
      this.viewport = div;
    };
    MiniMap2.pluginName = "miniMap";
    MiniMap2.width = 150;
    MiniMap2.height = 220;
    MiniMap2.viewPortWidth = 150;
    MiniMap2.viewPortHeight = 75;
    MiniMap2.isShowHeader = true;
    MiniMap2.isShowCloseIcon = true;
    MiniMap2.leftPosition = 0;
    MiniMap2.topPosition = 0;
    MiniMap2.rightPosition = null;
    MiniMap2.bottomPosition = null;
    MiniMap2.headerTitle = "导航";
    return MiniMap2;
  }()
);

// node_modules/.pnpm/@logicflow+extension@1.2.28/node_modules/@logicflow/extension/es/materials/curved-edge/index.js
var import_core27 = __toESM(require_logic_flow());
var __extends25 = function() {
  var extendStatics = function(d2, b) {
    extendStatics = Object.setPrototypeOf || { __proto__: [] } instanceof Array && function(d3, b2) {
      d3.__proto__ = b2;
    } || function(d3, b2) {
      for (var p2 in b2)
        if (b2.hasOwnProperty(p2))
          d3[p2] = b2[p2];
    };
    return extendStatics(d2, b);
  };
  return function(d2, b) {
    extendStatics(d2, b);
    function __() {
      this.constructor = d2;
    }
    d2.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
  };
}();
var __assign24 = function() {
  __assign24 = Object.assign || function(t2) {
    for (var s2, i2 = 1, n2 = arguments.length; i2 < n2; i2++) {
      s2 = arguments[i2];
      for (var p2 in s2)
        if (Object.prototype.hasOwnProperty.call(s2, p2))
          t2[p2] = s2[p2];
    }
    return t2;
  };
  return __assign24.apply(this, arguments);
};
var __read18 = function(o2, n2) {
  var m2 = typeof Symbol === "function" && o2[Symbol.iterator];
  if (!m2)
    return o2;
  var i2 = m2.call(o2), r2, ar = [], e2;
  try {
    while ((n2 === void 0 || n2-- > 0) && !(r2 = i2.next()).done)
      ar.push(r2.value);
  } catch (error) {
    e2 = { error };
  } finally {
    try {
      if (r2 && !r2.done && (m2 = i2["return"]))
        m2.call(i2);
    } finally {
      if (e2)
        throw e2.error;
    }
  }
  return ar;
};
var directionMap = {
  tr: "tl",
  lb: "tl",
  tl: "tr",
  rb: "tr",
  br: "bl",
  lt: "bl",
  bl: "br",
  rt: "br"
};
function pointFilter(points) {
  var all = points;
  var i2 = 1;
  while (i2 < all.length - 1) {
    var _a = __read18(all[i2 - 1], 2), x2 = _a[0], y2 = _a[1];
    var _b = __read18(all[i2], 2), x1 = _b[0], y1 = _b[1];
    var _c = __read18(all[i2 + 1], 2), x22 = _c[0], y22 = _c[1];
    if (x2 === x1 && x1 === x22 || y2 === y1 && y1 === y22) {
      all.splice(i2, 1);
    } else {
      i2++;
    }
  }
  return all;
}
function getMidPoints(cur, key, orientation, radius) {
  var mid1 = [cur[0], cur[1]];
  var mid2 = [cur[0], cur[1]];
  switch (orientation) {
    case "tl": {
      if (key === "tr") {
        mid1[1] += radius;
        mid2[0] += radius;
      } else if (key === "lb") {
        mid1[0] += radius;
        mid2[1] += radius;
      }
      return [mid1, mid2];
    }
    case "tr": {
      if (key === "tl") {
        mid1[1] += radius;
        mid2[0] -= radius;
      } else if (key === "rb") {
        mid1[0] -= radius;
        mid2[1] += radius;
      }
      return [mid1, mid2];
    }
    case "bl": {
      if (key === "br") {
        mid1[1] -= radius;
        mid2[0] += radius;
      } else if (key === "lt") {
        mid1[0] += radius;
        mid2[1] -= radius;
      }
      return [mid1, mid2];
    }
    case "br": {
      if (key === "bl") {
        mid1[1] -= radius;
        mid2[0] -= radius;
      } else if (key === "rt") {
        mid1[0] -= radius;
        mid2[1] -= radius;
      }
      return [mid1, mid2];
    }
    default:
      return [];
  }
}
function getPartialPath(prev, cur, next, radius) {
  var _a;
  var dir1 = "";
  var dir2 = "";
  if (prev[0] === cur[0]) {
    dir1 = prev[1] > cur[1] ? "t" : "b";
  } else if (prev[1] === cur[1]) {
    dir1 = prev[0] > cur[0] ? "l" : "r";
  }
  if (cur[0] === next[0]) {
    dir2 = cur[1] > next[1] ? "t" : "b";
  } else if (cur[1] === next[1]) {
    dir2 = cur[0] > next[0] ? "l" : "r";
  }
  var r2 = Math.min(Math.hypot(cur[0] - prev[0], cur[1] - prev[1]) / 2, Math.hypot(next[0] - cur[0], next[1] - cur[1]) / 2, radius) || 1 / 5 * radius;
  var key = "" + dir1 + dir2;
  var orientation = directionMap[key] || "-";
  var path = "L " + prev[0] + " " + prev[1];
  if (orientation === "-") {
    path += "L " + cur[0] + " " + cur[1] + " L " + next[0] + " " + next[1];
  } else {
    var _b = __read18(getMidPoints(cur, key, orientation, r2), 2), mid1 = _b[0], mid2 = _b[1];
    if (mid1 && mid2) {
      path += "L " + mid1[0] + " " + mid1[1] + " Q " + cur[0] + " " + cur[1] + " " + mid2[0] + " " + mid2[1];
      _a = __read18(mid2, 2), cur[0] = _a[0], cur[1] = _a[1];
    }
  }
  return path;
}
function getCurvedEdgePath(points, radius) {
  var i2 = 0;
  var d2 = "";
  if (points.length === 2) {
    d2 += "M" + points[i2][0] + " " + points[i2++][1] + " L " + points[i2][0] + " " + points[i2][1];
  } else {
    d2 += "M" + points[i2][0] + " " + points[i2++][1];
    for (; i2 + 1 < points.length; ) {
      var prev = points[i2 - 1];
      var cur = points[i2];
      var next = points[i2++ + 1];
      d2 += getPartialPath(prev, cur, next, radius);
    }
    d2 += "L " + points[i2][0] + " " + points[i2][1];
  }
  return d2;
}
var CurvedEdge = (
  /** @class */
  function(_super) {
    __extends25(CurvedEdge2, _super);
    function CurvedEdge2() {
      return _super !== null && _super.apply(this, arguments) || this;
    }
    CurvedEdge2.prototype.getEdge = function() {
      var model = this.props.model;
      var pointsStr = model.points, isAnimation = model.isAnimation, arrowConfig = model.arrowConfig, _a = model.radius, radius = _a === void 0 ? 5 : _a;
      var style2 = model.getEdgeStyle();
      var animationStyle = model.getEdgeAnimationStyle();
      var points = pointFilter(pointsStr.split(" ").map(function(p2) {
        return p2.split(",").map(function(a2) {
          return +a2;
        });
      }));
      var d2 = getCurvedEdgePath(points, radius);
      var attrs = __assign24(__assign24(__assign24({ style: isAnimation ? animationStyle : {} }, style2), arrowConfig), { fill: "none" });
      return (0, import_core27.h)("path", __assign24({ d: d2 }, attrs));
    };
    return CurvedEdge2;
  }(import_core27.PolylineEdge)
);
var CurvedEdgeModel = (
  /** @class */
  function(_super) {
    __extends25(CurvedEdgeModel2, _super);
    function CurvedEdgeModel2() {
      return _super !== null && _super.apply(this, arguments) || this;
    }
    return CurvedEdgeModel2;
  }(import_core27.PolylineEdgeModel)
);

// node_modules/.pnpm/@logicflow+extension@1.2.28/node_modules/@logicflow/extension/es/tools/flow-path/index.js
var __read19 = function(o2, n2) {
  var m2 = typeof Symbol === "function" && o2[Symbol.iterator];
  if (!m2)
    return o2;
  var i2 = m2.call(o2), r2, ar = [], e2;
  try {
    while ((n2 === void 0 || n2-- > 0) && !(r2 = i2.next()).done)
      ar.push(r2.value);
  } catch (error) {
    e2 = { error };
  } finally {
    try {
      if (r2 && !r2.done && (m2 = i2["return"]))
        m2.call(i2);
    } finally {
      if (e2)
        throw e2.error;
    }
  }
  return ar;
};
var __spread13 = function() {
  for (var ar = [], i2 = 0; i2 < arguments.length; i2++)
    ar = ar.concat(__read19(arguments[i2]));
  return ar;
};
var FlowPath = (
  /** @class */
  function() {
    function FlowPath2(_a) {
      var _this = this;
      var lf = _a.lf;
      this.lf = lf;
      this.pathes = [];
      lf.getPathes = function() {
        if (!_this.startNodeType) {
          throw new Error("需要预先指定开始节点类型");
        }
        return _this.getPathes();
      };
      lf.setRawPathes = function(pathes) {
        _this.setPathes(pathes);
      };
      lf.getRawPathes = function() {
        return _this.pathes;
      };
      lf.setStartNodeType = function(type3) {
        _this.startNodeType = type3;
      };
    }
    FlowPath2.prototype.setPathes = function(pathes) {
      this.pathes = pathes.map(function(_a) {
        var routeId = _a.routeId, name = _a.name, elements = _a.elements, type3 = _a.type;
        return {
          routeId,
          name,
          elements,
          type: type3,
          similarElement: null,
          weight: 0
        };
      });
    };
    FlowPath2.prototype.getPathes = function() {
      var _this = this;
      var graphData = this.lf.getGraphRawData();
      var nodesMap = /* @__PURE__ */ new Map();
      var startNodeIds = [];
      graphData.nodes.forEach(function(node) {
        nodesMap.set(node.id, {
          id: node.id,
          data: node,
          nextNodes: []
        });
        if (node.type === _this.startNodeType) {
          startNodeIds.push(node.id);
        }
      });
      graphData.edges.forEach(function(edge) {
        var node = nodesMap.get(edge.sourceNodeId);
        node.nextNodes.push(edge.targetNodeId);
      });
      var pathElements = [];
      startNodeIds.forEach(function(id) {
        pathElements = pathElements.concat(_this.findPathElements(nodesMap.get(id), nodesMap, []));
      });
      return this.getNewPathes(pathElements);
    };
    FlowPath2.prototype.findPathElements = function(node, nodesMap, elements) {
      if (elements === void 0) {
        elements = [];
      }
      var newPathes = __spread13(elements);
      newPathes.push(node.id);
      if (node.nextNodes.length === 0) {
        return [newPathes];
      }
      var subPath = [];
      for (var i2 = 0; i2 < node.nextNodes.length; i2++) {
        var n2 = nodesMap.get(node.nextNodes[i2]);
        var p2 = void 0;
        var idx = newPathes.indexOf(n2.id);
        if (idx !== -1) {
          p2 = [__spread13(newPathes.slice(idx), [n2.id])];
        } else {
          p2 = this.findPathElements(n2, nodesMap, __spread13(newPathes));
        }
        subPath = subPath.concat(p2);
      }
      return subPath;
    };
    FlowPath2.prototype.getNewPathes = function(pathElements) {
      var _this = this;
      var pathes = [];
      var LoopSet = /* @__PURE__ */ new Set();
      pathElements.forEach(function(elements) {
        var routeId = _this.getNewId("path");
        var name = _this.getNewId("路径");
        var isLoop = _this.isLoopPath(elements);
        var elementStr = elements.join(",");
        if (!LoopSet.has(elementStr)) {
          LoopSet.add(elementStr);
          pathes.push({
            routeId,
            name,
            elements,
            type: isLoop,
            weight: 0,
            similarElement: ""
          });
        }
      });
      var oldPathes = JSON.parse(JSON.stringify(this.pathes));
      pathes.forEach(function(newPath) {
        for (var i2 = 0; i2 < oldPathes.length; i2++) {
          var oldPath = oldPathes[i2];
          var weight = _this.similar2Path(__spread13(newPath.elements), __spread13(oldPath.elements));
          if (weight > newPath.weight && oldPath.weight <= weight) {
            newPath.weight = weight;
            newPath.similarElement = oldPath;
            if (weight === oldPath.weight && oldPath.similarElement) {
              oldPath.similarElement.similarElement = null;
              oldPath.similarElement.weight = 0;
              oldPath.similarElement = null;
              oldPath.weight = 0;
            } else {
              oldPath.similarElement = newPath;
              oldPath.weight = weight;
            }
          }
        }
      });
      pathes.forEach(function(newPath) {
        if (newPath.similarElement && newPath.similarElement.similarElement === newPath) {
          newPath.routeId = newPath.similarElement.routeId;
          newPath.name = newPath.similarElement.name;
        }
        delete newPath.similarElement;
        delete newPath.weight;
      });
      this.setPathes(pathes);
      return pathes;
    };
    FlowPath2.prototype.similar2Path = function(x2, y2) {
      var z2 = 0;
      var s2 = x2.length + y2.length;
      x2.sort();
      y2.sort();
      var a2 = x2.shift();
      var b = y2.shift();
      while (a2 !== void 0 && b !== void 0) {
        if (a2 === b) {
          z2++;
          a2 = x2.shift();
          b = y2.shift();
        } else if (a2 < b) {
          a2 = x2.shift();
        } else if (a2 > b) {
          b = y2.shift();
        }
      }
      return z2 / s2 * 200;
    };
    FlowPath2.prototype.getNewId = function(prefix) {
      return prefix + "_" + getBpmnId();
    };
    FlowPath2.prototype.isLoopPath = function(elements) {
      var length = elements.length;
      return elements.indexOf(elements[length - 1]) !== length - 1 ? 1 : 0;
    };
    FlowPath2.pluginName = "flowPath";
    return FlowPath2;
  }()
);

// node_modules/.pnpm/@logicflow+extension@1.2.28/node_modules/@logicflow/extension/es/tools/auto-layout/index.js
var __assign25 = function() {
  __assign25 = Object.assign || function(t2) {
    for (var s2, i2 = 1, n2 = arguments.length; i2 < n2; i2++) {
      s2 = arguments[i2];
      for (var p2 in s2)
        if (Object.prototype.hasOwnProperty.call(s2, p2))
          t2[p2] = s2[p2];
    }
    return t2;
  };
  return __assign25.apply(this, arguments);
};
var POSITION_TYPE = {
  LEFT_TOP: -1,
  LEFT: 0,
  LEFT_BOTTOM: 1
};
var AutoLayout = (
  /** @class */
  function() {
    function AutoLayout2(_a) {
      var _this = this;
      var lf = _a.lf;
      this.lf = lf;
      this.trunk = [];
      lf.layout = function(startNodeType) {
        var data = _this.lf.getGraphRawData();
        _this.lf.setStartNodeType(startNodeType);
        var path = _this.lf.getPathes();
        _this.levelHeight = [];
        _this.newNodeMap = /* @__PURE__ */ new Map();
        return _this.layout(data, path);
      };
    }
    AutoLayout2.prototype.layout = function(data, path) {
      var _this = this;
      var trunk = [];
      path.forEach(function(p2) {
        var elements = p2.elements;
        if (elements.length > trunk.length) {
          trunk = elements;
        } else if (elements.length === trunk.length) {
          if (JSON.stringify(elements) === JSON.stringify(_this.trunk)) {
            trunk = _this.trunk;
          }
        }
      });
      this.trunk = trunk;
      var nodeMap = this.formatData(data);
      var newGraphData = {
        nodes: [],
        edges: []
      };
      for (var i2 = trunk.length - 1; i2 >= 0; i2--) {
        this.setNodePosition(trunk[i2], nodeMap, newGraphData, i2, 1);
      }
      this.lf.graphModel.graphDataToModel(newGraphData);
    };
    AutoLayout2.prototype.setNodePosition = function(nodeId, nodeMap, newGraphData, xLevel, yLevel) {
      var _this = this;
      var n2 = nodeMap[nodeId];
      var text = n2.text, type3 = n2.type, next = n2.next, properties = n2.properties;
      var x2 = xLevel * 160 + 40;
      var y2 = yLevel * 120;
      var nodeData = {
        id: nodeId,
        x: x2,
        text,
        y: y2,
        type: type3,
        properties
      };
      if (text && typeof text === "object") {
        nodeData.text = __assign25(__assign25({}, text), { x: x2 + text.x, y: y2 + text.y });
      }
      this.newNodeMap.set(nodeData.id, {
        x: nodeData.x,
        y: nodeData.y,
        type: type3
      });
      newGraphData.nodes.push(nodeData);
      n2.isFixed = true;
      this.addLevelHeight(xLevel, 1);
      if (next && next.length > 0) {
        next.forEach(function(nextInfo) {
          var n1 = nodeMap[nextInfo.nodeId];
          if (!n1.isFixed) {
            var nextYLevel = _this.getLevelHeight(xLevel + 1);
            _this.addLevelHeight(xLevel, 1);
            _this.setNodePosition(nextInfo.nodeId, nodeMap, newGraphData, xLevel + 1, nextYLevel + 1);
          } else {
          }
          newGraphData.edges.push(__assign25({ id: nextInfo.edgeId, type: nextInfo.edgeType, sourceNodeId: nodeId, targetNodeId: nextInfo.nodeId, properties: nextInfo.properties, text: nextInfo.text }, _this.getEdgeDataPoints(nodeId, nextInfo.nodeId)));
        });
      }
      return nodeData;
    };
    AutoLayout2.prototype.getEdgeDataPoints = function(sourceNodeId, targetNodeId) {
      var source = this.newNodeMap.get(sourceNodeId);
      var target = this.newNodeMap.get(targetNodeId);
      var _a = this.getShape(sourceNodeId), width = _a.width, height = _a.height;
      var _b = this.getShape(targetNodeId), targetWidth = _b.width, targetHeight = _b.height;
      var positionType = this.getRelativePosition(source, target);
      var startPoint = {
        x: source.x,
        y: source.y
      };
      var endPoint = {
        x: target.x,
        y: target.y
      };
      switch (positionType) {
        case POSITION_TYPE.LEFT:
          startPoint.x = source.x + width / 2;
          endPoint.x = target.x - targetWidth / 2;
          break;
        case POSITION_TYPE.LEFT_TOP:
          startPoint.y = source.y + height / 2;
          endPoint.x = target.x - targetWidth / 2;
          break;
        case POSITION_TYPE.LEFT_BOTTOM:
          startPoint.x = source.x + width / 2;
          endPoint.y = target.y + targetHeight / 2;
          break;
        default:
          break;
      }
      return {
        startPoint,
        endPoint
      };
    };
    AutoLayout2.prototype.getRelativePosition = function(source, target) {
      var y2 = source.y;
      var y1 = target.y;
      var positionType;
      if (y2 < y1) {
        positionType = -1;
      } else if (y2 === y1) {
        positionType = 0;
      } else {
        positionType = 1;
      }
      return positionType;
    };
    AutoLayout2.prototype.getShape = function(nodeId) {
      var nodeModel = this.lf.getNodeModelById(nodeId);
      return {
        height: nodeModel.height,
        width: nodeModel.width
      };
    };
    AutoLayout2.prototype.formatData = function(data) {
      var nodeMap = data.nodes.reduce(function(nMap, node) {
        var type3 = node.type, properties = node.properties, text = node.text, x2 = node.x, y2 = node.y;
        if (text && typeof text === "object") {
          text.x = text.x - x2;
          text.y = text.y - y2;
        }
        nMap[node.id] = {
          type: type3,
          properties,
          text,
          prev: [],
          next: []
        };
        return nMap;
      }, {});
      data.edges.forEach(function(edge) {
        var sourceNodeId = edge.sourceNodeId, targetNodeId = edge.targetNodeId, id = edge.id, properties = edge.properties, text = edge.text;
        var newText = text;
        if (typeof text === "object") {
          newText = text.value;
        }
        nodeMap[sourceNodeId].next.push({
          edgeId: id,
          nodeId: targetNodeId,
          edgeType: edge.type,
          properties,
          text: newText
        });
        nodeMap[targetNodeId].prev.push({
          edgeId: id,
          nodeId: sourceNodeId,
          properties,
          text: newText
        });
      });
      return nodeMap;
    };
    AutoLayout2.prototype.addLevelHeight = function(level, height, isNegative) {
      if (height === void 0) {
        height = 1;
      }
      if (isNegative === void 0) {
        isNegative = false;
      }
      var l2 = this.levelHeight[level];
      if (!l2) {
        l2 = {
          positiveHeight: 0,
          negativeHeight: 0
        };
        this.levelHeight[level] = l2;
      }
      isNegative ? l2.negativeHeight -= height : l2.positiveHeight += height;
    };
    AutoLayout2.prototype.getLevelHeight = function(level, isNegative) {
      if (isNegative === void 0) {
        isNegative = false;
      }
      var val = this.levelHeight[level];
      if (!val) {
        return 0;
      }
      return isNegative ? val.negativeHeight : val.positiveHeight;
    };
    AutoLayout2.pluginName = "AutoLayout";
    return AutoLayout2;
  }()
);

// node_modules/.pnpm/@logicflow+extension@1.2.28/node_modules/@logicflow/extension/es/components/highlight/index.js
var __assign26 = function() {
  __assign26 = Object.assign || function(t2) {
    for (var s2, i2 = 1, n2 = arguments.length; i2 < n2; i2++) {
      s2 = arguments[i2];
      for (var p2 in s2)
        if (Object.prototype.hasOwnProperty.call(s2, p2))
          t2[p2] = s2[p2];
    }
    return t2;
  };
  return __assign26.apply(this, arguments);
};
var __read20 = function(o2, n2) {
  var m2 = typeof Symbol === "function" && o2[Symbol.iterator];
  if (!m2)
    return o2;
  var i2 = m2.call(o2), r2, ar = [], e2;
  try {
    while ((n2 === void 0 || n2-- > 0) && !(r2 = i2.next()).done)
      ar.push(r2.value);
  } catch (error) {
    e2 = { error };
  } finally {
    try {
      if (r2 && !r2.done && (m2 = i2["return"]))
        m2.call(i2);
    } finally {
      if (e2)
        throw e2.error;
    }
  }
  return ar;
};
var __spread14 = function() {
  for (var ar = [], i2 = 0; i2 < arguments.length; i2++)
    ar = ar.concat(__read20(arguments[i2]));
  return ar;
};
var getPath = function(id, lf) {
  var el = lf.getModelById(id);
  return getNodePath(el.BaseType === "node" ? el : el.targetNode, lf);
};
var getNodePath = function(node, lf) {
  var incomingPaths = [];
  var outgoingPaths = [];
  var getIncomingPaths = function(curNode, path, prevNode) {
    if (prevNode === void 0) {
      prevNode = null;
    }
    if (prevNode) {
      path.unshift.apply(path, __spread14(lf.getEdgeModels({
        sourceNodeId: curNode.id,
        targetNodeId: prevNode.id
      }).map(function(item) {
        return item.id;
      })));
    }
    if (path.includes(curNode.id)) {
      incomingPaths.push(path);
      return;
    }
    path.unshift(curNode.id);
    if (!curNode.incoming.nodes.length) {
      incomingPaths.push(path);
      return;
    }
    curNode.incoming.nodes.forEach(function(nextNode) {
      getIncomingPaths(nextNode, path.slice(), curNode);
    });
  };
  var getOutgoingPaths = function(curNode, path, prevNode) {
    if (prevNode === void 0) {
      prevNode = null;
    }
    if (prevNode) {
      path.push.apply(path, __spread14(lf.getEdgeModels({
        sourceNodeId: prevNode.id,
        targetNodeId: curNode.id
      }).map(function(item) {
        return item.id;
      })));
    }
    if (path.includes(curNode.id)) {
      outgoingPaths.push(path);
      return;
    }
    path.push(curNode.id);
    if (!curNode.outgoing.nodes.length) {
      outgoingPaths.push(path);
      return;
    }
    curNode.outgoing.nodes.forEach(function(nextNode) {
      getOutgoingPaths(nextNode, path.slice(), curNode);
    });
  };
  getIncomingPaths(node, []);
  getOutgoingPaths(node, []);
  return __spread14(new Set(__spread14(incomingPaths.flat(), outgoingPaths.flat())));
};
var Highlight = (
  /** @class */
  function() {
    function Highlight2(_a) {
      var lf = _a.lf;
      this.mode = "path";
      this.manual = false;
      this.tempStyles = {};
      this.lf = lf;
    }
    Highlight2.prototype.setMode = function(mode) {
      this.mode = mode;
    };
    Highlight2.prototype.setManual = function(manual) {
      this.manual = manual;
    };
    Highlight2.prototype.highlightSingle = function(id) {
      var model = this.lf.getModelById(id);
      if (model.BaseType === "node") {
        model.updateStyles(this.tempStyles[id]);
      } else if (model.BaseType === "edge") {
        model.updateStyles(this.tempStyles[id]);
        model.sourceNode.updateStyles(this.tempStyles[model.sourceNode.id]);
        model.targetNode.updateStyles(this.tempStyles[model.targetNode.id]);
      }
    };
    Highlight2.prototype.highlightPath = function(id) {
      var _this = this;
      var path = getPath(id, this.lf);
      path.forEach(function(_id) {
        _this.lf.getModelById(_id).updateStyles(_this.tempStyles[_id]);
      });
    };
    Highlight2.prototype.highlight = function(id, mode) {
      var _this = this;
      if (mode === void 0) {
        mode = this.mode;
      }
      if (this.manual)
        return;
      if (Object.keys(this.tempStyles).length) {
        this.restoreHighlight();
      }
      Object.values(this.lf.graphModel.modelsMap).forEach(function(item) {
        var oStyle = item.BaseType === "node" ? item.getNodeStyle() : item.getEdgeStyle();
        _this.tempStyles[item.id] = __assign26({}, oStyle);
        item.setStyles({ opacity: 0.1 });
      });
      var modeTrigger = {
        single: this.highlightSingle.bind(this),
        path: this.highlightPath.bind(this)
      };
      modeTrigger[mode](id);
    };
    Highlight2.prototype.restoreHighlight = function() {
      var _this = this;
      if (!Object.keys(this.tempStyles).length)
        return;
      Object.values(this.lf.graphModel.modelsMap).forEach(function(item) {
        var _a;
        var oStyle = (_a = _this.tempStyles[item.id]) !== null && _a !== void 0 ? _a : {};
        item.updateStyles(__assign26({}, oStyle));
      });
      this.tempStyles = {};
    };
    Highlight2.prototype.render = function(lf, domContainer) {
      var _this = this;
      this.lf.on("node:mouseenter", function(_a) {
        var data = _a.data;
        return _this.highlight(data.id);
      });
      this.lf.on("edge:mouseenter", function(_a) {
        var data = _a.data;
        return _this.highlight(data.id);
      });
      this.lf.on("node:mouseleave", this.restoreHighlight.bind(this));
      this.lf.on("edge:mouseleave", this.restoreHighlight.bind(this));
      this.lf.on("history:change", this.restoreHighlight.bind(this));
    };
    Highlight2.prototype.destroy = function() {
    };
    Highlight2.pluginName = "highlight";
    return Highlight2;
  }()
);
export {
  AutoLayout,
  BPMNAdapter,
  BPMNBaseAdapter,
  BPMNElements,
  BoundaryEventFactory,
  BpmnAdapter,
  BpmnElement,
  BpmnXmlAdapter,
  ContextMenu,
  Control2 as Control,
  CurvedEdge,
  CurvedEdgeModel,
  DiamondResize_default as DiamondResize,
  DndPanel,
  EllipseResize_default as EllipseResize,
  EndEventFactory,
  EndEventModel,
  EndEventView,
  ExclusiveGatewayModel,
  ExclusiveGatewayView,
  FlowPath,
  GatewayNodeFactory,
  Group,
  GroupNode_default as GroupNode,
  Highlight,
  HtmlResize_default as HtmlResize,
  InsertNodeInPolyline,
  IntermediateCatchEventFactory,
  IntermediateThrowEventFactory,
  Menu,
  MiniMap,
  NodeResize,
  RectResize_default as RectResize,
  SelectionSelect,
  SequenceFlowModel,
  SequenceFlowView,
  ServiceTaskModel,
  ServiceTaskView,
  Snapshot,
  StartEventFactory,
  StartEventModel,
  StartEventView,
  SubProcessFactory,
  TaskNodeFactory,
  UserTaskModel,
  UserTaskView,
  utils_exports as bpmnUtils,
  convertNormalToXml,
  convertXmlToNormal,
  gatewayComposable,
  getCurvedEdgePath,
  handleAttributes,
  icons_exports as icons,
  lfJson2Xml,
  lfXml2Json,
  multiInstanceIcon,
  sequenceFlowFactory,
  toLogicflowData,
  toNormalJson,
  toTurboData,
  toXmlJson,
  useDefinition
};
//# sourceMappingURL=@logicflow_extension.js.map

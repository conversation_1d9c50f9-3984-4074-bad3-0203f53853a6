{"version": 3, "sources": ["../../.pnpm/path-browserify@1.0.1/node_modules/path-browserify/index.js"], "sourcesContent": ["// 'path' module extracted from Node.js v8.11.1 (only the posix part)\n// transplited with Babel\n\n// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\n'use strict';\n\nfunction assertPath(path) {\n  if (typeof path !== 'string') {\n    throw new TypeError('Path must be a string. Received ' + JSON.stringify(path));\n  }\n}\n\n// Resolves . and .. elements in a path with directory names\nfunction normalizeStringPosix(path, allowAboveRoot) {\n  var res = '';\n  var lastSegmentLength = 0;\n  var lastSlash = -1;\n  var dots = 0;\n  var code;\n  for (var i = 0; i <= path.length; ++i) {\n    if (i < path.length)\n      code = path.charCodeAt(i);\n    else if (code === 47 /*/*/)\n      break;\n    else\n      code = 47 /*/*/;\n    if (code === 47 /*/*/) {\n      if (lastSlash === i - 1 || dots === 1) {\n        // NOOP\n      } else if (lastSlash !== i - 1 && dots === 2) {\n        if (res.length < 2 || lastSegmentLength !== 2 || res.charCodeAt(res.length - 1) !== 46 /*.*/ || res.charCodeAt(res.length - 2) !== 46 /*.*/) {\n          if (res.length > 2) {\n            var lastSlashIndex = res.lastIndexOf('/');\n            if (lastSlashIndex !== res.length - 1) {\n              if (lastSlashIndex === -1) {\n                res = '';\n                lastSegmentLength = 0;\n              } else {\n                res = res.slice(0, lastSlashIndex);\n                lastSegmentLength = res.length - 1 - res.lastIndexOf('/');\n              }\n              lastSlash = i;\n              dots = 0;\n              continue;\n            }\n          } else if (res.length === 2 || res.length === 1) {\n            res = '';\n            lastSegmentLength = 0;\n            lastSlash = i;\n            dots = 0;\n            continue;\n          }\n        }\n        if (allowAboveRoot) {\n          if (res.length > 0)\n            res += '/..';\n          else\n            res = '..';\n          lastSegmentLength = 2;\n        }\n      } else {\n        if (res.length > 0)\n          res += '/' + path.slice(lastSlash + 1, i);\n        else\n          res = path.slice(lastSlash + 1, i);\n        lastSegmentLength = i - lastSlash - 1;\n      }\n      lastSlash = i;\n      dots = 0;\n    } else if (code === 46 /*.*/ && dots !== -1) {\n      ++dots;\n    } else {\n      dots = -1;\n    }\n  }\n  return res;\n}\n\nfunction _format(sep, pathObject) {\n  var dir = pathObject.dir || pathObject.root;\n  var base = pathObject.base || (pathObject.name || '') + (pathObject.ext || '');\n  if (!dir) {\n    return base;\n  }\n  if (dir === pathObject.root) {\n    return dir + base;\n  }\n  return dir + sep + base;\n}\n\nvar posix = {\n  // path.resolve([from ...], to)\n  resolve: function resolve() {\n    var resolvedPath = '';\n    var resolvedAbsolute = false;\n    var cwd;\n\n    for (var i = arguments.length - 1; i >= -1 && !resolvedAbsolute; i--) {\n      var path;\n      if (i >= 0)\n        path = arguments[i];\n      else {\n        if (cwd === undefined)\n          cwd = process.cwd();\n        path = cwd;\n      }\n\n      assertPath(path);\n\n      // Skip empty entries\n      if (path.length === 0) {\n        continue;\n      }\n\n      resolvedPath = path + '/' + resolvedPath;\n      resolvedAbsolute = path.charCodeAt(0) === 47 /*/*/;\n    }\n\n    // At this point the path should be resolved to a full absolute path, but\n    // handle relative paths to be safe (might happen when process.cwd() fails)\n\n    // Normalize the path\n    resolvedPath = normalizeStringPosix(resolvedPath, !resolvedAbsolute);\n\n    if (resolvedAbsolute) {\n      if (resolvedPath.length > 0)\n        return '/' + resolvedPath;\n      else\n        return '/';\n    } else if (resolvedPath.length > 0) {\n      return resolvedPath;\n    } else {\n      return '.';\n    }\n  },\n\n  normalize: function normalize(path) {\n    assertPath(path);\n\n    if (path.length === 0) return '.';\n\n    var isAbsolute = path.charCodeAt(0) === 47 /*/*/;\n    var trailingSeparator = path.charCodeAt(path.length - 1) === 47 /*/*/;\n\n    // Normalize the path\n    path = normalizeStringPosix(path, !isAbsolute);\n\n    if (path.length === 0 && !isAbsolute) path = '.';\n    if (path.length > 0 && trailingSeparator) path += '/';\n\n    if (isAbsolute) return '/' + path;\n    return path;\n  },\n\n  isAbsolute: function isAbsolute(path) {\n    assertPath(path);\n    return path.length > 0 && path.charCodeAt(0) === 47 /*/*/;\n  },\n\n  join: function join() {\n    if (arguments.length === 0)\n      return '.';\n    var joined;\n    for (var i = 0; i < arguments.length; ++i) {\n      var arg = arguments[i];\n      assertPath(arg);\n      if (arg.length > 0) {\n        if (joined === undefined)\n          joined = arg;\n        else\n          joined += '/' + arg;\n      }\n    }\n    if (joined === undefined)\n      return '.';\n    return posix.normalize(joined);\n  },\n\n  relative: function relative(from, to) {\n    assertPath(from);\n    assertPath(to);\n\n    if (from === to) return '';\n\n    from = posix.resolve(from);\n    to = posix.resolve(to);\n\n    if (from === to) return '';\n\n    // Trim any leading backslashes\n    var fromStart = 1;\n    for (; fromStart < from.length; ++fromStart) {\n      if (from.charCodeAt(fromStart) !== 47 /*/*/)\n        break;\n    }\n    var fromEnd = from.length;\n    var fromLen = fromEnd - fromStart;\n\n    // Trim any leading backslashes\n    var toStart = 1;\n    for (; toStart < to.length; ++toStart) {\n      if (to.charCodeAt(toStart) !== 47 /*/*/)\n        break;\n    }\n    var toEnd = to.length;\n    var toLen = toEnd - toStart;\n\n    // Compare paths to find the longest common path from root\n    var length = fromLen < toLen ? fromLen : toLen;\n    var lastCommonSep = -1;\n    var i = 0;\n    for (; i <= length; ++i) {\n      if (i === length) {\n        if (toLen > length) {\n          if (to.charCodeAt(toStart + i) === 47 /*/*/) {\n            // We get here if `from` is the exact base path for `to`.\n            // For example: from='/foo/bar'; to='/foo/bar/baz'\n            return to.slice(toStart + i + 1);\n          } else if (i === 0) {\n            // We get here if `from` is the root\n            // For example: from='/'; to='/foo'\n            return to.slice(toStart + i);\n          }\n        } else if (fromLen > length) {\n          if (from.charCodeAt(fromStart + i) === 47 /*/*/) {\n            // We get here if `to` is the exact base path for `from`.\n            // For example: from='/foo/bar/baz'; to='/foo/bar'\n            lastCommonSep = i;\n          } else if (i === 0) {\n            // We get here if `to` is the root.\n            // For example: from='/foo'; to='/'\n            lastCommonSep = 0;\n          }\n        }\n        break;\n      }\n      var fromCode = from.charCodeAt(fromStart + i);\n      var toCode = to.charCodeAt(toStart + i);\n      if (fromCode !== toCode)\n        break;\n      else if (fromCode === 47 /*/*/)\n        lastCommonSep = i;\n    }\n\n    var out = '';\n    // Generate the relative path based on the path difference between `to`\n    // and `from`\n    for (i = fromStart + lastCommonSep + 1; i <= fromEnd; ++i) {\n      if (i === fromEnd || from.charCodeAt(i) === 47 /*/*/) {\n        if (out.length === 0)\n          out += '..';\n        else\n          out += '/..';\n      }\n    }\n\n    // Lastly, append the rest of the destination (`to`) path that comes after\n    // the common path parts\n    if (out.length > 0)\n      return out + to.slice(toStart + lastCommonSep);\n    else {\n      toStart += lastCommonSep;\n      if (to.charCodeAt(toStart) === 47 /*/*/)\n        ++toStart;\n      return to.slice(toStart);\n    }\n  },\n\n  _makeLong: function _makeLong(path) {\n    return path;\n  },\n\n  dirname: function dirname(path) {\n    assertPath(path);\n    if (path.length === 0) return '.';\n    var code = path.charCodeAt(0);\n    var hasRoot = code === 47 /*/*/;\n    var end = -1;\n    var matchedSlash = true;\n    for (var i = path.length - 1; i >= 1; --i) {\n      code = path.charCodeAt(i);\n      if (code === 47 /*/*/) {\n          if (!matchedSlash) {\n            end = i;\n            break;\n          }\n        } else {\n        // We saw the first non-path separator\n        matchedSlash = false;\n      }\n    }\n\n    if (end === -1) return hasRoot ? '/' : '.';\n    if (hasRoot && end === 1) return '//';\n    return path.slice(0, end);\n  },\n\n  basename: function basename(path, ext) {\n    if (ext !== undefined && typeof ext !== 'string') throw new TypeError('\"ext\" argument must be a string');\n    assertPath(path);\n\n    var start = 0;\n    var end = -1;\n    var matchedSlash = true;\n    var i;\n\n    if (ext !== undefined && ext.length > 0 && ext.length <= path.length) {\n      if (ext.length === path.length && ext === path) return '';\n      var extIdx = ext.length - 1;\n      var firstNonSlashEnd = -1;\n      for (i = path.length - 1; i >= 0; --i) {\n        var code = path.charCodeAt(i);\n        if (code === 47 /*/*/) {\n            // If we reached a path separator that was not part of a set of path\n            // separators at the end of the string, stop now\n            if (!matchedSlash) {\n              start = i + 1;\n              break;\n            }\n          } else {\n          if (firstNonSlashEnd === -1) {\n            // We saw the first non-path separator, remember this index in case\n            // we need it if the extension ends up not matching\n            matchedSlash = false;\n            firstNonSlashEnd = i + 1;\n          }\n          if (extIdx >= 0) {\n            // Try to match the explicit extension\n            if (code === ext.charCodeAt(extIdx)) {\n              if (--extIdx === -1) {\n                // We matched the extension, so mark this as the end of our path\n                // component\n                end = i;\n              }\n            } else {\n              // Extension does not match, so our result is the entire path\n              // component\n              extIdx = -1;\n              end = firstNonSlashEnd;\n            }\n          }\n        }\n      }\n\n      if (start === end) end = firstNonSlashEnd;else if (end === -1) end = path.length;\n      return path.slice(start, end);\n    } else {\n      for (i = path.length - 1; i >= 0; --i) {\n        if (path.charCodeAt(i) === 47 /*/*/) {\n            // If we reached a path separator that was not part of a set of path\n            // separators at the end of the string, stop now\n            if (!matchedSlash) {\n              start = i + 1;\n              break;\n            }\n          } else if (end === -1) {\n          // We saw the first non-path separator, mark this as the end of our\n          // path component\n          matchedSlash = false;\n          end = i + 1;\n        }\n      }\n\n      if (end === -1) return '';\n      return path.slice(start, end);\n    }\n  },\n\n  extname: function extname(path) {\n    assertPath(path);\n    var startDot = -1;\n    var startPart = 0;\n    var end = -1;\n    var matchedSlash = true;\n    // Track the state of characters (if any) we see before our first dot and\n    // after any path separator we find\n    var preDotState = 0;\n    for (var i = path.length - 1; i >= 0; --i) {\n      var code = path.charCodeAt(i);\n      if (code === 47 /*/*/) {\n          // If we reached a path separator that was not part of a set of path\n          // separators at the end of the string, stop now\n          if (!matchedSlash) {\n            startPart = i + 1;\n            break;\n          }\n          continue;\n        }\n      if (end === -1) {\n        // We saw the first non-path separator, mark this as the end of our\n        // extension\n        matchedSlash = false;\n        end = i + 1;\n      }\n      if (code === 46 /*.*/) {\n          // If this is our first dot, mark it as the start of our extension\n          if (startDot === -1)\n            startDot = i;\n          else if (preDotState !== 1)\n            preDotState = 1;\n      } else if (startDot !== -1) {\n        // We saw a non-dot and non-path separator before our dot, so we should\n        // have a good chance at having a non-empty extension\n        preDotState = -1;\n      }\n    }\n\n    if (startDot === -1 || end === -1 ||\n        // We saw a non-dot character immediately before the dot\n        preDotState === 0 ||\n        // The (right-most) trimmed path component is exactly '..'\n        preDotState === 1 && startDot === end - 1 && startDot === startPart + 1) {\n      return '';\n    }\n    return path.slice(startDot, end);\n  },\n\n  format: function format(pathObject) {\n    if (pathObject === null || typeof pathObject !== 'object') {\n      throw new TypeError('The \"pathObject\" argument must be of type Object. Received type ' + typeof pathObject);\n    }\n    return _format('/', pathObject);\n  },\n\n  parse: function parse(path) {\n    assertPath(path);\n\n    var ret = { root: '', dir: '', base: '', ext: '', name: '' };\n    if (path.length === 0) return ret;\n    var code = path.charCodeAt(0);\n    var isAbsolute = code === 47 /*/*/;\n    var start;\n    if (isAbsolute) {\n      ret.root = '/';\n      start = 1;\n    } else {\n      start = 0;\n    }\n    var startDot = -1;\n    var startPart = 0;\n    var end = -1;\n    var matchedSlash = true;\n    var i = path.length - 1;\n\n    // Track the state of characters (if any) we see before our first dot and\n    // after any path separator we find\n    var preDotState = 0;\n\n    // Get non-dir info\n    for (; i >= start; --i) {\n      code = path.charCodeAt(i);\n      if (code === 47 /*/*/) {\n          // If we reached a path separator that was not part of a set of path\n          // separators at the end of the string, stop now\n          if (!matchedSlash) {\n            startPart = i + 1;\n            break;\n          }\n          continue;\n        }\n      if (end === -1) {\n        // We saw the first non-path separator, mark this as the end of our\n        // extension\n        matchedSlash = false;\n        end = i + 1;\n      }\n      if (code === 46 /*.*/) {\n          // If this is our first dot, mark it as the start of our extension\n          if (startDot === -1) startDot = i;else if (preDotState !== 1) preDotState = 1;\n        } else if (startDot !== -1) {\n        // We saw a non-dot and non-path separator before our dot, so we should\n        // have a good chance at having a non-empty extension\n        preDotState = -1;\n      }\n    }\n\n    if (startDot === -1 || end === -1 ||\n    // We saw a non-dot character immediately before the dot\n    preDotState === 0 ||\n    // The (right-most) trimmed path component is exactly '..'\n    preDotState === 1 && startDot === end - 1 && startDot === startPart + 1) {\n      if (end !== -1) {\n        if (startPart === 0 && isAbsolute) ret.base = ret.name = path.slice(1, end);else ret.base = ret.name = path.slice(startPart, end);\n      }\n    } else {\n      if (startPart === 0 && isAbsolute) {\n        ret.name = path.slice(1, startDot);\n        ret.base = path.slice(1, end);\n      } else {\n        ret.name = path.slice(startPart, startDot);\n        ret.base = path.slice(startPart, end);\n      }\n      ret.ext = path.slice(startDot, end);\n    }\n\n    if (startPart > 0) ret.dir = path.slice(0, startPart - 1);else if (isAbsolute) ret.dir = '/';\n\n    return ret;\n  },\n\n  sep: '/',\n  delimiter: ':',\n  win32: null,\n  posix: null\n};\n\nposix.posix = posix;\n\nmodule.exports = posix;\n"], "mappings": ";;;;;AAAA;AAAA;AA0BA,aAAS,WAAW,MAAM;AACxB,UAAI,OAAO,SAAS,UAAU;AAC5B,cAAM,IAAI,UAAU,qCAAqC,KAAK,UAAU,IAAI,CAAC;AAAA,MAC/E;AAAA,IACF;AAGA,aAAS,qBAAqB,MAAM,gBAAgB;AAClD,UAAI,MAAM;AACV,UAAI,oBAAoB;AACxB,UAAI,YAAY;AAChB,UAAI,OAAO;AACX,UAAI;AACJ,eAAS,IAAI,GAAG,KAAK,KAAK,QAAQ,EAAE,GAAG;AACrC,YAAI,IAAI,KAAK;AACX,iBAAO,KAAK,WAAW,CAAC;AAAA,iBACjB,SAAS;AAChB;AAAA;AAEA,iBAAO;AACT,YAAI,SAAS,IAAU;AACrB,cAAI,cAAc,IAAI,KAAK,SAAS,GAAG;AAAA,UAEvC,WAAW,cAAc,IAAI,KAAK,SAAS,GAAG;AAC5C,gBAAI,IAAI,SAAS,KAAK,sBAAsB,KAAK,IAAI,WAAW,IAAI,SAAS,CAAC,MAAM,MAAY,IAAI,WAAW,IAAI,SAAS,CAAC,MAAM,IAAU;AAC3I,kBAAI,IAAI,SAAS,GAAG;AAClB,oBAAI,iBAAiB,IAAI,YAAY,GAAG;AACxC,oBAAI,mBAAmB,IAAI,SAAS,GAAG;AACrC,sBAAI,mBAAmB,IAAI;AACzB,0BAAM;AACN,wCAAoB;AAAA,kBACtB,OAAO;AACL,0BAAM,IAAI,MAAM,GAAG,cAAc;AACjC,wCAAoB,IAAI,SAAS,IAAI,IAAI,YAAY,GAAG;AAAA,kBAC1D;AACA,8BAAY;AACZ,yBAAO;AACP;AAAA,gBACF;AAAA,cACF,WAAW,IAAI,WAAW,KAAK,IAAI,WAAW,GAAG;AAC/C,sBAAM;AACN,oCAAoB;AACpB,4BAAY;AACZ,uBAAO;AACP;AAAA,cACF;AAAA,YACF;AACA,gBAAI,gBAAgB;AAClB,kBAAI,IAAI,SAAS;AACf,uBAAO;AAAA;AAEP,sBAAM;AACR,kCAAoB;AAAA,YACtB;AAAA,UACF,OAAO;AACL,gBAAI,IAAI,SAAS;AACf,qBAAO,MAAM,KAAK,MAAM,YAAY,GAAG,CAAC;AAAA;AAExC,oBAAM,KAAK,MAAM,YAAY,GAAG,CAAC;AACnC,gCAAoB,IAAI,YAAY;AAAA,UACtC;AACA,sBAAY;AACZ,iBAAO;AAAA,QACT,WAAW,SAAS,MAAY,SAAS,IAAI;AAC3C,YAAE;AAAA,QACJ,OAAO;AACL,iBAAO;AAAA,QACT;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAEA,aAAS,QAAQ,KAAK,YAAY;AAChC,UAAI,MAAM,WAAW,OAAO,WAAW;AACvC,UAAI,OAAO,WAAW,SAAS,WAAW,QAAQ,OAAO,WAAW,OAAO;AAC3E,UAAI,CAAC,KAAK;AACR,eAAO;AAAA,MACT;AACA,UAAI,QAAQ,WAAW,MAAM;AAC3B,eAAO,MAAM;AAAA,MACf;AACA,aAAO,MAAM,MAAM;AAAA,IACrB;AAEA,QAAI,QAAQ;AAAA;AAAA,MAEV,SAAS,SAAS,UAAU;AAC1B,YAAI,eAAe;AACnB,YAAI,mBAAmB;AACvB,YAAI;AAEJ,iBAAS,IAAI,UAAU,SAAS,GAAG,KAAK,MAAM,CAAC,kBAAkB,KAAK;AACpE,cAAI;AACJ,cAAI,KAAK;AACP,mBAAO,UAAU,CAAC;AAAA,eACf;AACH,gBAAI,QAAQ;AACV,oBAAM,QAAQ,IAAI;AACpB,mBAAO;AAAA,UACT;AAEA,qBAAW,IAAI;AAGf,cAAI,KAAK,WAAW,GAAG;AACrB;AAAA,UACF;AAEA,yBAAe,OAAO,MAAM;AAC5B,6BAAmB,KAAK,WAAW,CAAC,MAAM;AAAA,QAC5C;AAMA,uBAAe,qBAAqB,cAAc,CAAC,gBAAgB;AAEnE,YAAI,kBAAkB;AACpB,cAAI,aAAa,SAAS;AACxB,mBAAO,MAAM;AAAA;AAEb,mBAAO;AAAA,QACX,WAAW,aAAa,SAAS,GAAG;AAClC,iBAAO;AAAA,QACT,OAAO;AACL,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,MAEA,WAAW,SAAS,UAAU,MAAM;AAClC,mBAAW,IAAI;AAEf,YAAI,KAAK,WAAW;AAAG,iBAAO;AAE9B,YAAI,aAAa,KAAK,WAAW,CAAC,MAAM;AACxC,YAAI,oBAAoB,KAAK,WAAW,KAAK,SAAS,CAAC,MAAM;AAG7D,eAAO,qBAAqB,MAAM,CAAC,UAAU;AAE7C,YAAI,KAAK,WAAW,KAAK,CAAC;AAAY,iBAAO;AAC7C,YAAI,KAAK,SAAS,KAAK;AAAmB,kBAAQ;AAElD,YAAI;AAAY,iBAAO,MAAM;AAC7B,eAAO;AAAA,MACT;AAAA,MAEA,YAAY,SAAS,WAAW,MAAM;AACpC,mBAAW,IAAI;AACf,eAAO,KAAK,SAAS,KAAK,KAAK,WAAW,CAAC,MAAM;AAAA,MACnD;AAAA,MAEA,MAAM,SAAS,OAAO;AACpB,YAAI,UAAU,WAAW;AACvB,iBAAO;AACT,YAAI;AACJ,iBAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,EAAE,GAAG;AACzC,cAAI,MAAM,UAAU,CAAC;AACrB,qBAAW,GAAG;AACd,cAAI,IAAI,SAAS,GAAG;AAClB,gBAAI,WAAW;AACb,uBAAS;AAAA;AAET,wBAAU,MAAM;AAAA,UACpB;AAAA,QACF;AACA,YAAI,WAAW;AACb,iBAAO;AACT,eAAO,MAAM,UAAU,MAAM;AAAA,MAC/B;AAAA,MAEA,UAAU,SAAS,SAAS,MAAM,IAAI;AACpC,mBAAW,IAAI;AACf,mBAAW,EAAE;AAEb,YAAI,SAAS;AAAI,iBAAO;AAExB,eAAO,MAAM,QAAQ,IAAI;AACzB,aAAK,MAAM,QAAQ,EAAE;AAErB,YAAI,SAAS;AAAI,iBAAO;AAGxB,YAAI,YAAY;AAChB,eAAO,YAAY,KAAK,QAAQ,EAAE,WAAW;AAC3C,cAAI,KAAK,WAAW,SAAS,MAAM;AACjC;AAAA,QACJ;AACA,YAAI,UAAU,KAAK;AACnB,YAAI,UAAU,UAAU;AAGxB,YAAI,UAAU;AACd,eAAO,UAAU,GAAG,QAAQ,EAAE,SAAS;AACrC,cAAI,GAAG,WAAW,OAAO,MAAM;AAC7B;AAAA,QACJ;AACA,YAAI,QAAQ,GAAG;AACf,YAAI,QAAQ,QAAQ;AAGpB,YAAI,SAAS,UAAU,QAAQ,UAAU;AACzC,YAAI,gBAAgB;AACpB,YAAI,IAAI;AACR,eAAO,KAAK,QAAQ,EAAE,GAAG;AACvB,cAAI,MAAM,QAAQ;AAChB,gBAAI,QAAQ,QAAQ;AAClB,kBAAI,GAAG,WAAW,UAAU,CAAC,MAAM,IAAU;AAG3C,uBAAO,GAAG,MAAM,UAAU,IAAI,CAAC;AAAA,cACjC,WAAW,MAAM,GAAG;AAGlB,uBAAO,GAAG,MAAM,UAAU,CAAC;AAAA,cAC7B;AAAA,YACF,WAAW,UAAU,QAAQ;AAC3B,kBAAI,KAAK,WAAW,YAAY,CAAC,MAAM,IAAU;AAG/C,gCAAgB;AAAA,cAClB,WAAW,MAAM,GAAG;AAGlB,gCAAgB;AAAA,cAClB;AAAA,YACF;AACA;AAAA,UACF;AACA,cAAI,WAAW,KAAK,WAAW,YAAY,CAAC;AAC5C,cAAI,SAAS,GAAG,WAAW,UAAU,CAAC;AACtC,cAAI,aAAa;AACf;AAAA,mBACO,aAAa;AACpB,4BAAgB;AAAA,QACpB;AAEA,YAAI,MAAM;AAGV,aAAK,IAAI,YAAY,gBAAgB,GAAG,KAAK,SAAS,EAAE,GAAG;AACzD,cAAI,MAAM,WAAW,KAAK,WAAW,CAAC,MAAM,IAAU;AACpD,gBAAI,IAAI,WAAW;AACjB,qBAAO;AAAA;AAEP,qBAAO;AAAA,UACX;AAAA,QACF;AAIA,YAAI,IAAI,SAAS;AACf,iBAAO,MAAM,GAAG,MAAM,UAAU,aAAa;AAAA,aAC1C;AACH,qBAAW;AACX,cAAI,GAAG,WAAW,OAAO,MAAM;AAC7B,cAAE;AACJ,iBAAO,GAAG,MAAM,OAAO;AAAA,QACzB;AAAA,MACF;AAAA,MAEA,WAAW,SAAS,UAAU,MAAM;AAClC,eAAO;AAAA,MACT;AAAA,MAEA,SAAS,SAAS,QAAQ,MAAM;AAC9B,mBAAW,IAAI;AACf,YAAI,KAAK,WAAW;AAAG,iBAAO;AAC9B,YAAI,OAAO,KAAK,WAAW,CAAC;AAC5B,YAAI,UAAU,SAAS;AACvB,YAAI,MAAM;AACV,YAAI,eAAe;AACnB,iBAAS,IAAI,KAAK,SAAS,GAAG,KAAK,GAAG,EAAE,GAAG;AACzC,iBAAO,KAAK,WAAW,CAAC;AACxB,cAAI,SAAS,IAAU;AACnB,gBAAI,CAAC,cAAc;AACjB,oBAAM;AACN;AAAA,YACF;AAAA,UACF,OAAO;AAEP,2BAAe;AAAA,UACjB;AAAA,QACF;AAEA,YAAI,QAAQ;AAAI,iBAAO,UAAU,MAAM;AACvC,YAAI,WAAW,QAAQ;AAAG,iBAAO;AACjC,eAAO,KAAK,MAAM,GAAG,GAAG;AAAA,MAC1B;AAAA,MAEA,UAAU,SAAS,SAAS,MAAM,KAAK;AACrC,YAAI,QAAQ,UAAa,OAAO,QAAQ;AAAU,gBAAM,IAAI,UAAU,iCAAiC;AACvG,mBAAW,IAAI;AAEf,YAAI,QAAQ;AACZ,YAAI,MAAM;AACV,YAAI,eAAe;AACnB,YAAI;AAEJ,YAAI,QAAQ,UAAa,IAAI,SAAS,KAAK,IAAI,UAAU,KAAK,QAAQ;AACpE,cAAI,IAAI,WAAW,KAAK,UAAU,QAAQ;AAAM,mBAAO;AACvD,cAAI,SAAS,IAAI,SAAS;AAC1B,cAAI,mBAAmB;AACvB,eAAK,IAAI,KAAK,SAAS,GAAG,KAAK,GAAG,EAAE,GAAG;AACrC,gBAAI,OAAO,KAAK,WAAW,CAAC;AAC5B,gBAAI,SAAS,IAAU;AAGnB,kBAAI,CAAC,cAAc;AACjB,wBAAQ,IAAI;AACZ;AAAA,cACF;AAAA,YACF,OAAO;AACP,kBAAI,qBAAqB,IAAI;AAG3B,+BAAe;AACf,mCAAmB,IAAI;AAAA,cACzB;AACA,kBAAI,UAAU,GAAG;AAEf,oBAAI,SAAS,IAAI,WAAW,MAAM,GAAG;AACnC,sBAAI,EAAE,WAAW,IAAI;AAGnB,0BAAM;AAAA,kBACR;AAAA,gBACF,OAAO;AAGL,2BAAS;AACT,wBAAM;AAAA,gBACR;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAEA,cAAI,UAAU;AAAK,kBAAM;AAAA,mBAA0B,QAAQ;AAAI,kBAAM,KAAK;AAC1E,iBAAO,KAAK,MAAM,OAAO,GAAG;AAAA,QAC9B,OAAO;AACL,eAAK,IAAI,KAAK,SAAS,GAAG,KAAK,GAAG,EAAE,GAAG;AACrC,gBAAI,KAAK,WAAW,CAAC,MAAM,IAAU;AAGjC,kBAAI,CAAC,cAAc;AACjB,wBAAQ,IAAI;AACZ;AAAA,cACF;AAAA,YACF,WAAW,QAAQ,IAAI;AAGvB,6BAAe;AACf,oBAAM,IAAI;AAAA,YACZ;AAAA,UACF;AAEA,cAAI,QAAQ;AAAI,mBAAO;AACvB,iBAAO,KAAK,MAAM,OAAO,GAAG;AAAA,QAC9B;AAAA,MACF;AAAA,MAEA,SAAS,SAAS,QAAQ,MAAM;AAC9B,mBAAW,IAAI;AACf,YAAI,WAAW;AACf,YAAI,YAAY;AAChB,YAAI,MAAM;AACV,YAAI,eAAe;AAGnB,YAAI,cAAc;AAClB,iBAAS,IAAI,KAAK,SAAS,GAAG,KAAK,GAAG,EAAE,GAAG;AACzC,cAAI,OAAO,KAAK,WAAW,CAAC;AAC5B,cAAI,SAAS,IAAU;AAGnB,gBAAI,CAAC,cAAc;AACjB,0BAAY,IAAI;AAChB;AAAA,YACF;AACA;AAAA,UACF;AACF,cAAI,QAAQ,IAAI;AAGd,2BAAe;AACf,kBAAM,IAAI;AAAA,UACZ;AACA,cAAI,SAAS,IAAU;AAEnB,gBAAI,aAAa;AACf,yBAAW;AAAA,qBACJ,gBAAgB;AACvB,4BAAc;AAAA,UACpB,WAAW,aAAa,IAAI;AAG1B,0BAAc;AAAA,UAChB;AAAA,QACF;AAEA,YAAI,aAAa,MAAM,QAAQ;AAAA,QAE3B,gBAAgB;AAAA,QAEhB,gBAAgB,KAAK,aAAa,MAAM,KAAK,aAAa,YAAY,GAAG;AAC3E,iBAAO;AAAA,QACT;AACA,eAAO,KAAK,MAAM,UAAU,GAAG;AAAA,MACjC;AAAA,MAEA,QAAQ,SAAS,OAAO,YAAY;AAClC,YAAI,eAAe,QAAQ,OAAO,eAAe,UAAU;AACzD,gBAAM,IAAI,UAAU,qEAAqE,OAAO,UAAU;AAAA,QAC5G;AACA,eAAO,QAAQ,KAAK,UAAU;AAAA,MAChC;AAAA,MAEA,OAAO,SAAS,MAAM,MAAM;AAC1B,mBAAW,IAAI;AAEf,YAAI,MAAM,EAAE,MAAM,IAAI,KAAK,IAAI,MAAM,IAAI,KAAK,IAAI,MAAM,GAAG;AAC3D,YAAI,KAAK,WAAW;AAAG,iBAAO;AAC9B,YAAI,OAAO,KAAK,WAAW,CAAC;AAC5B,YAAI,aAAa,SAAS;AAC1B,YAAI;AACJ,YAAI,YAAY;AACd,cAAI,OAAO;AACX,kBAAQ;AAAA,QACV,OAAO;AACL,kBAAQ;AAAA,QACV;AACA,YAAI,WAAW;AACf,YAAI,YAAY;AAChB,YAAI,MAAM;AACV,YAAI,eAAe;AACnB,YAAI,IAAI,KAAK,SAAS;AAItB,YAAI,cAAc;AAGlB,eAAO,KAAK,OAAO,EAAE,GAAG;AACtB,iBAAO,KAAK,WAAW,CAAC;AACxB,cAAI,SAAS,IAAU;AAGnB,gBAAI,CAAC,cAAc;AACjB,0BAAY,IAAI;AAChB;AAAA,YACF;AACA;AAAA,UACF;AACF,cAAI,QAAQ,IAAI;AAGd,2BAAe;AACf,kBAAM,IAAI;AAAA,UACZ;AACA,cAAI,SAAS,IAAU;AAEnB,gBAAI,aAAa;AAAI,yBAAW;AAAA,qBAAW,gBAAgB;AAAG,4BAAc;AAAA,UAC9E,WAAW,aAAa,IAAI;AAG5B,0BAAc;AAAA,UAChB;AAAA,QACF;AAEA,YAAI,aAAa,MAAM,QAAQ;AAAA,QAE/B,gBAAgB;AAAA,QAEhB,gBAAgB,KAAK,aAAa,MAAM,KAAK,aAAa,YAAY,GAAG;AACvE,cAAI,QAAQ,IAAI;AACd,gBAAI,cAAc,KAAK;AAAY,kBAAI,OAAO,IAAI,OAAO,KAAK,MAAM,GAAG,GAAG;AAAA;AAAO,kBAAI,OAAO,IAAI,OAAO,KAAK,MAAM,WAAW,GAAG;AAAA,UAClI;AAAA,QACF,OAAO;AACL,cAAI,cAAc,KAAK,YAAY;AACjC,gBAAI,OAAO,KAAK,MAAM,GAAG,QAAQ;AACjC,gBAAI,OAAO,KAAK,MAAM,GAAG,GAAG;AAAA,UAC9B,OAAO;AACL,gBAAI,OAAO,KAAK,MAAM,WAAW,QAAQ;AACzC,gBAAI,OAAO,KAAK,MAAM,WAAW,GAAG;AAAA,UACtC;AACA,cAAI,MAAM,KAAK,MAAM,UAAU,GAAG;AAAA,QACpC;AAEA,YAAI,YAAY;AAAG,cAAI,MAAM,KAAK,MAAM,GAAG,YAAY,CAAC;AAAA,iBAAW;AAAY,cAAI,MAAM;AAEzF,eAAO;AAAA,MACT;AAAA,MAEA,KAAK;AAAA,MACL,WAAW;AAAA,MACX,OAAO;AAAA,MACP,OAAO;AAAA,IACT;AAEA,UAAM,QAAQ;AAEd,WAAO,UAAU;AAAA;AAAA;", "names": []}
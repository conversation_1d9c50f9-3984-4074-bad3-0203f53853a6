import {
  ElConfigProvider,
  ElPagination,
  ElTable,
  ElTableColumn,
  vLoading
} from "./chunk-Y4EDKVC2.js";
import "./chunk-4N46QFUN.js";
import "./chunk-EGVUO3GM.js";
import "./chunk-WIMLKF2O.js";
import {
  Fragment,
  computed,
  createVNode,
  defineComponent,
  getCurrentInstance,
  inject,
  isVNode,
  mergeProps,
  nextTick,
  onBeforeUnmount,
  onMounted,
  ref,
  resolveDirective,
  toRefs,
  unref,
  vue_runtime_esm_bundler_exports,
  warn,
  withDirectives
} from "./chunk-JBQXOB42.js";
import "./chunk-PRH6DGNM.js";

// node_modules/.pnpm/@pureadmin+table@3.2.1_elem_ae453a41636c68c0f900aa993e84fc0f/node_modules/@pureadmin/table/dist/index.es.js
var He = Object.prototype.hasOwnProperty;
var Y = (e, n) => He.call(e, n);
var Z = (e) => e !== null && typeof e == "object";
var ee = "__epPropKey";
var Me = ((e, n) => {
  if (!Z(e) || Z(r = e) && r[ee])
    return e;
  var r;
  const { values: p, required: u, default: c, type: l, validator: O } = e, h = p || O ? (v) => {
    let d = false, y = [];
    if (p && (y = Array.from(p), Y(e, "default") && y.push(c), d || (d = y.includes(v))), O && (d || (d = O(v))), !d && y.length > 0) {
      const s = [...new Set(y)].map((k) => JSON.stringify(k)).join(", ");
      warn(`Invalid prop: validation failed${n ? ` for prop "${n}"` : ""}. Expected one of [${s}], got value ${JSON.stringify(v)}.`);
    }
    return d;
  } : void 0, B = { type: l, required: !!u, validator: h, [ee]: true };
  return Y(e, "default") && (B.default = c), B;
})({ type: String, values: ["", "default", "small", "large"], required: false });
var We = { data: { type: Array, default: () => [] }, size: Me, width: [String, Number], height: [String, Number], maxHeight: [String, Number], fit: { type: Boolean, default: true }, stripe: Boolean, border: Boolean, rowKey: [String, Function], showHeader: { type: Boolean, default: true }, showSummary: Boolean, sumText: String, summaryMethod: Function, rowClassName: [String, Function], rowStyle: [Object, Function], cellClassName: [String, Function], cellStyle: [Object, Function], headerRowClassName: [String, Function], headerRowStyle: [Object, Function], headerCellClassName: [String, Function], headerCellStyle: [Object, Function], highlightCurrentRow: Boolean, currentRowKey: [String, Number], emptyText: String, expandRowKeys: Array, defaultExpandAll: Boolean, defaultSort: Object, tooltipEffect: String, tooltipOptions: Object, spanMethod: Function, selectOnIndeterminate: { type: Boolean, default: true }, indent: { type: Number, default: 16 }, treeProps: { type: Object, default: () => ({ hasChildren: "hasChildren", children: "children", checkStrictly: false }) }, lazy: Boolean, load: Function, style: { type: Object, default: () => ({}) }, className: { type: String, default: "" }, tableLayout: { type: String, default: "fixed" }, scrollbarAlwaysOn: Boolean, flexible: Boolean, showOverflowTooltip: [Boolean, Object] };
var Ie = { tableKey: { type: [String, Number], default: "0" }, columns: { type: Array, default: [] }, loading: { type: Boolean, default: false }, loadingConfig: { type: Object, default: () => {
} }, alignWhole: { type: String, default: "left" }, headerAlign: { type: String, default: "" }, showOverflowTooltip: { type: Boolean, default: false }, rowHoverBgColor: { type: String, default: "" }, pagination: { type: Object, default: { total: 0, pageSize: 5, align: "right", size: "default", background: false, pageSizes: [5, 10, 15, 20], layout: "total, sizes, prev, pager, next, jumper" } }, adaptive: { type: Boolean, default: false }, adaptiveConfig: { type: Object, default: { offsetBottom: 96, fixHeader: true, timeout: 60, zIndex: 3 } }, locale: { type: [String, Object], default: "" }, ...We };
var te = defineComponent({ name: "Renderer", props: { render: { type: Function }, params: { type: Object } }, setup: (e) => () => createVNode(Fragment, null, [e.render(e.params)]) });
var ae = { name: "en", el: { select: { loading: "Loading", noMatch: "No matching data", noData: "No data", placeholder: "Select" }, pagination: { goto: "Go to", pagesize: "/page", total: "Total {total}", pageClassifier: "", page: "Page", prev: "Go to previous page", next: "Go to next page", currentPage: "page {pager}", prevPages: "Previous {pager} pages", nextPages: "Next {pager} pages", deprecationWarning: "Deprecated usages detected, please refer to the el-pagination documentation for more details" }, table: { emptyText: "No Data", confirmFilter: "Confirm", resetFilter: "Reset", clearFilter: "All", sumText: "Sum" } } };
var ne = { name: "zh-cn", el: { select: { loading: "加载中", noMatch: "无匹配数据", noData: "无数据", placeholder: "请选择" }, pagination: { goto: "前往", pagesize: "条/页", total: "共 {total} 条", pageClassifier: "页", page: "页", prev: "上一页", next: "下一页", currentPage: "第 {pager} 页", prevPages: "向前 {pager} 页", nextPages: "向后 {pager} 页", deprecationWarning: "你使用了一些已被废弃的用法，请参考 el-pagination 的官方文档" }, table: { emptyText: "暂无数据", confirmFilter: "筛选", resetFilter: "重置", clearFilter: "全部", sumText: "合计" } } };
var oe = { name: "zh-tw", el: { select: { loading: "載入中", noMatch: "無相符資料", noData: "無資料", placeholder: "請選擇" }, pagination: { goto: "前往", pagesize: "項/頁", total: "共 {total} 項", pageClassifier: "頁", page: "頁", prev: "上一頁", next: "下一頁", currentPage: "第 {pager} 頁", prevPages: "向前 {pager} 頁", nextPages: "向后 {pager} 頁", deprecationWarning: "偵測到已過時的使用方式，請參閱 el-pagination 說明文件以了解更多資訊" }, table: { emptyText: "暫無資料", confirmFilter: "篩選", resetFilter: "重置", clearFilter: "全部", sumText: "合計" } } };
var fe = Object.defineProperty;
var Ke = Object.getOwnPropertyDescriptor;
var qe = Object.getOwnPropertyNames;
var Ve = Object.prototype.hasOwnProperty;
var re = (e, n, r, p) => {
  if (n && typeof n == "object" || typeof n == "function")
    for (let u of qe(n))
      !Ve.call(e, u) && u !== r && fe(e, u, { get: () => n[u], enumerable: !(p = Ke(n, u)) || p.enumerable });
  return e;
};
var Ge = Object.prototype.toString;
function me(e, n) {
  return Ge.call(e) === `[object ${n}]`;
}
function le(e) {
  return me(e, "String");
}
function ie(e) {
  return typeof e == "function";
}
var se;
var pe;
var ue = (e) => e.replace(/\B([A-Z])/g, "-$1").toLowerCase();
var S = {};
((e, n) => {
  for (var r in n)
    fe(e, r, { get: n[r], enumerable: true });
})(S, { Vue: () => vue_runtime_esm_bundler_exports }), re(S, se = vue_runtime_esm_bundler_exports, "default"), pe && re(pe, se, "default");
var Je = (e) => {
  let n, r = (e == null ? void 0 : e.className) ?? "dark", p = (0, S.shallowRef)(false), u = () => {
    let c = e != null && e.selector ? e.selector === "html" ? document.documentElement : document.body : document.documentElement;
    p.value = c.classList.contains(r);
  };
  return function(c) {
    (0, S.getCurrentInstance)() && (0, S.onUnmounted)(c);
  }(() => {
    n && (n.takeRecords(), n.disconnect());
  }), (0, S.onBeforeMount)(() => {
    let c = e != null && e.selector ? e.selector === "html" ? document.documentElement : document.body : document.documentElement;
    u(), n = new MutationObserver(u), n.observe(c, { attributes: true, attributeFilter: ["class"] });
  }), { isDark: p, toggleDark: () => {
    (e != null && e.selector ? e.selector === "html" ? document.documentElement : document.body : document.documentElement).classList.toggle(r);
  } };
};
function ce(e) {
  return typeof e == "function" || Object.prototype.toString.call(e) === "[object Object]" && !isVNode(e);
}
var M = defineComponent({ name: "PureTable", props: Ie, directives: { Loading: vLoading }, emits: ["page-size-change", "page-current-change"], setup(e, { slots: n, attrs: r, emit: p, expose: u }) {
  const { locale: c, i18n: l, ssr: O } = inject("locale", { locale: null, i18n: null, ssr: false }), { locale: h, columns: B, loading: v, tableKey: d, adaptive: y, pagination: s, alignWhole: k, headerAlign: ye, loadingConfig: P, adaptiveConfig: C, rowHoverBgColor: W, showOverflowTooltip: be } = toRefs(e), I = ref(false), { isDark: he } = Je(), A = getCurrentInstance();
  let ve = unref(s) && unref(s).currentPage && unref(s).pageSize, K = computed(() => {
    var o, f, i, b;
    if (!unref(l))
      return;
    const a = ((i = (f = l == null ? void 0 : l.global) == null ? void 0 : f.getLocaleMessage(unref((o = l == null ? void 0 : l.global) == null ? void 0 : o.locale))) == null ? void 0 : i.el) || ((b = l == null ? void 0 : l.getLocaleMessage(unref(l == null ? void 0 : l.locale))) == null ? void 0 : b.el);
    return a ? { el: a } : null;
  }), q = computed(() => le(c) ? [ae, ne, oe].filter((a) => a.name === ue(c))[0] : c), $ = computed(() => {
    if (unref(h))
      return le(unref(h)) ? [ae, ne, oe].filter((a) => a.name === ue(unref(h)))[0] : unref(h);
  }), xe = computed(() => {
    if (!unref(P))
      return;
    let { text: a, spinner: o, svg: f, viewBox: i } = unref(P);
    return { "element-loading-text": a, "element-loading-spinner": o, "element-loading-svg": f, "element-loading-svg-view-box": i };
  });
  const we = computed(() => {
    var a, o;
    if (unref(v))
      return { "element-loading-background": (a = unref(P)) != null && a.background ? (o = unref(P)) == null ? void 0 : o.background : he.value ? "rgba(0, 0, 0, 0.45)" : "rgba(255, 255, 255, 0.45)" };
  }), Se = computed(() => Object.assign({ width: "100%", margin: "16px 0", display: "flex", flexWrap: "wrap", justifyContent: unref(s).align === "left" ? "flex-start" : unref(s).align === "center" ? "center" : "flex-end" }, unref(s).style ?? {})), V = (a, o) => {
    const { cellRenderer: f, slot: i, headerRenderer: b, headerSlot: j, hide: x, children: F, prop: N, ...Oe } = a;
    if (ie(x) && x(r))
      return x(r);
    if (function(g) {
      return me(g, "Boolean");
    }(x) && x)
      return x;
    const L = { default: (g) => {
      var T;
      return f ? createVNode(te, { render: f, params: Object.assign(g, { index: g.$index, props: e, attrs: r }) }, null) : i ? (T = n == null ? void 0 : n[i]) == null ? void 0 : T.call(n, Object.assign(g, { index: g.$index, props: e, attrs: r })) : void 0;
    } };
    let R = b ? { header: (g) => createVNode(te, { render: b, params: Object.assign(g, { index: g.$index, props: e, attrs: r }) }, null), ...L } : n != null && n[j] ? { header: (g) => {
      var T;
      return (T = n == null ? void 0 : n[j]) == null ? void 0 : T.call(n, Object.assign(g, { index: g.$index, props: e, attrs: r }));
    }, ...L } : L;
    return (F == null ? void 0 : F.length) > 0 && (R.default = () => F.map(V)), createVNode(ElTableColumn, mergeProps({ key: o }, Oe, { prop: ie(N) && N(o) ? N(o) : N, align: a != null && a.align ? a.align : unref(k), headerAlign: a != null && a.headerAlign ? a.headerAlign : unref(ye), showOverflowTooltip: a != null && a.showOverflowTooltip ? a.showOverflowTooltip : unref(be) }), ce(R) ? R : { default: () => [R] });
  }, G = () => {
    var a;
    return (a = A == null ? void 0 : A.proxy) == null ? void 0 : a.$refs[`TableRef${unref(d)}`];
  }, z = () => G().$refs, D = async () => {
    await nextTick();
    const a = z().tableWrapper, o = unref(C).offsetBottom ?? 96;
    a.style.height = window.innerHeight - a.getBoundingClientRect().top - o + "px";
  }, J = ((a, o = 200, f = false) => {
    let i, b, j = o;
    return function() {
      i && clearTimeout(i), f ? (i || a.call(b, ...arguments), i = setTimeout(() => i = null, j)) : i = setTimeout(() => a.call(b, ...arguments), j);
    };
  })(D, unref(C).timeout ?? 60), U = async (a = 3) => {
    await nextTick();
    const o = z().tableHeaderRef.$el.style;
    o.position = "sticky", o.top = 0, o.zIndex = a;
  };
  onMounted(() => {
    I.value = true, nextTick(() => {
      if (unref(W) && z().tableWrapper.style.setProperty("--el-table-row-hover-bg-color", unref(W), "important"), unref(y)) {
        if (D(), window.addEventListener("resize", J), Reflect.has(unref(C), "fixHeader") && !unref(C).fixHeader)
          return;
        U(unref(C).zIndex ?? 3);
      }
    });
  }), onBeforeUnmount(() => {
    unref(y) && window.removeEventListener("resize", J);
  }), u({ getTableRef: G, getTableDoms: z, setAdaptive: D, setHeaderSticky: U });
  let _ = () => createVNode(Fragment, null, [createVNode(ElTable, mergeProps(e, r, { ref: `TableRef${unref(d)}` }), { default: () => unref(B).map(V), append: () => n.append && n.append(), empty: () => n.empty && n.empty() }), ve ? createVNode(ElPagination, mergeProps(r, { class: "pure-pagination", style: unref(Se) }, unref(s), { layout: unref(s).layout ?? "total, sizes, prev, pager, next, jumper", pageSizes: unref(s).pageSizes ?? [5, 10, 15, 20], onSizeChange: (a) => ((o) => {
    unref(s).pageSize = o, p("page-size-change", o);
  })(a), onCurrentChange: (a) => ((o) => {
    unref(s).currentPage = o, p("page-current-change", o);
  })(a) }), null) : null]), Q = () => {
    let a;
    return withDirectives(createVNode("div", mergeProps({ class: "pure-table", style: "width:100%" }, unref(we), unref(xe)), [unref(K) || unref(q) || unref($) ? createVNode(ElConfigProvider, { locale: unref($) ? unref($) : unref(K) || unref(q) }, ce(a = _()) ? a : { default: () => [a] }) : _()]), [[resolveDirective("loading"), unref(v)]]);
  };
  return () => O ? I.value && Q() : Q();
} });
var Qe = Object.assign(M, { install: (e, n) => {
  e.component(M.name, M), e.provide("locale", n ?? { locale: null, i18n: null, ssr: false });
} });
export {
  Qe as PureTable,
  Qe as default
};
/*! Bundled license information:

@pureadmin/table/dist/index.es.js:
  (**
  * @vue/shared v3.4.37
  * (c) 2018-present Yuxi (Evan) You and Vue contributors
  * @license MIT
  **)
*/
//# sourceMappingURL=@pureadmin_table.js.map
